/// Cursor 工作区清理工具
/// 
/// 对应原版 Python 的 augment_tools/cursor/augutils/workspace_cleaner.py
/// 清理 Cursor 工作区存储目录

use std::fs;
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};
use zip::{ZipWriter, write::FileOptions};
use std::io::Write;
use super::paths::get_workspace_storage_path;

/// 清理结果结构体
/// 对应原版 Python 函数的返回字典
#[derive(Debug, Clone, serde::Serialize)]
pub struct CleanWorkspaceResult {
    pub backup_path: Option<String>,
    pub deleted_files_count: usize,
    pub failed_operations: Vec<FailedOperation>,
    pub failed_compressions: Vec<FailedCompression>,
}

/// 失败的操作记录
#[derive(Debug, Clone, serde::Serialize)]
pub struct FailedOperation {
    pub operation_type: String, // "file" 或 "directory"
    pub path: String,
    pub error: String,
}

/// 失败的压缩记录
#[derive(Debug, <PERSON><PERSON>, serde::Serialize)]
pub struct FailedCompression {
    pub file: String,
    pub error: String,
}

/// 强制删除目录及其所有内容
/// 对应原版 Python 的 force_delete_directory 函数
/// 
/// Returns:
///     bool: 成功返回 true，失败返回 false
fn force_delete_directory(path: &Path) -> bool {
    if !path.exists() {
        return true;
    }

    #[cfg(target_os = "windows")]
    {
        // Windows: 处理只读文件并使用长路径
        let path_str = format!("\\\\?\\{}", path.display());
        match std::fs::remove_dir_all(&path_str) {
            Ok(_) => true,
            Err(_) => {
                // 如果失败，尝试逐个删除
                remove_dir_contents_recursive(path)
            }
        }
    }

    #[cfg(not(target_os = "windows"))]
    {
        match std::fs::remove_dir_all(path) {
            Ok(_) => true,
            Err(_) => {
                // 如果失败，尝试逐个删除
                remove_dir_contents_recursive(path)
            }
        }
    }
}

/// 递归删除目录内容
fn remove_dir_contents_recursive(path: &Path) -> bool {
    let entries = match fs::read_dir(path) {
        Ok(entries) => entries,
        Err(_) => return false,
    };

    let mut all_success = true;

    for entry in entries {
        if let Ok(entry) = entry {
            let entry_path = entry.path();
            
            if entry_path.is_dir() {
                if !force_delete_directory(&entry_path) {
                    all_success = false;
                }
            } else {
                #[cfg(target_os = "windows")]
                {
                    // 清除只读属性
                    if let Ok(mut perms) = fs::metadata(&entry_path).map(|m| m.permissions()) {
                        perms.set_readonly(false);
                        let _ = fs::set_permissions(&entry_path, perms);
                    }
                }
                
                if fs::remove_file(&entry_path).is_err() {
                    all_success = false;
                }
            }
        }
    }

    // 最后删除目录本身
    if all_success {
        fs::remove_dir(path).is_ok()
    } else {
        false
    }
}

/// 清理工作区存储目录
/// 对应原版 Python 的 clean_workspace_storage 函数
/// 
/// 此函数：
/// 1. 获取工作区存储路径
/// 2. 创建所有文件的 zip 备份
/// 3. 删除目录中的所有文件
/// 
/// Returns:
///     Result<CleanWorkspaceResult, String>: 包含操作结果的结构体或错误信息
pub fn clean_workspace_storage() -> Result<CleanWorkspaceResult, String> {
    let workspace_path = get_workspace_storage_path();
    let workspace_path = Path::new(&workspace_path);

    if !workspace_path.exists() {
        // 如果工作区目录不存在，返回成功结果（没有文件需要清理）
        return Ok(CleanWorkspaceResult {
            backup_path: None,
            deleted_files_count: 0,
            failed_operations: Vec::new(),
            failed_compressions: Vec::new(),
        });
    }

    // 创建带时间戳的备份文件名
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    let backup_path = format!("{}_backup_{}.zip", workspace_path.display(), timestamp);

    // 创建 zip 备份
    let mut failed_compressions = Vec::new();
    
    {
        let backup_file = fs::File::create(&backup_path)
            .map_err(|e| format!("Failed to create backup file: {}", e))?;
        
        let mut zip = ZipWriter::new(backup_file);
        let options = FileOptions::default()
            .compression_method(zip::CompressionMethod::Deflated);

        // 递归遍历所有文件
        if let Err(e) = add_dir_to_zip(&mut zip, workspace_path, workspace_path, &options, &mut failed_compressions) {
            return Err(format!("Failed to create backup: {}", e));
        }

        zip.finish().map_err(|e| format!("Failed to finalize backup: {}", e))?;
    }

    // 计算删除前的文件数量
    let total_files = count_files_recursive(workspace_path);

    // 删除目录中的所有文件
    let mut failed_operations = Vec::new();

    // 首先尝试整体删除
    if !force_delete_directory(workspace_path) {
        // 如果整体删除失败，尝试逐个删除
        delete_files_individually(workspace_path, &mut failed_operations);
    }

    Ok(CleanWorkspaceResult {
        backup_path: Some(backup_path),
        deleted_files_count: total_files,
        failed_operations,
        failed_compressions,
    })
}

/// 将目录添加到 zip 文件
fn add_dir_to_zip<W: Write + std::io::Seek>(
    zip: &mut ZipWriter<W>,
    dir_path: &Path,
    base_path: &Path,
    options: &FileOptions,
    failed_compressions: &mut Vec<FailedCompression>,
) -> Result<(), Box<dyn std::error::Error>> {
    let entries = fs::read_dir(dir_path)?;

    for entry in entries {
        let entry = entry?;
        let path = entry.path();
        
        if path.is_file() {
            let relative_path = path.strip_prefix(base_path)?;
            
            match fs::File::open(&path) {
                Ok(mut file) => {
                    match zip.start_file(relative_path.to_string_lossy(), *options) {
                        Ok(_) => {
                            if let Err(e) = std::io::copy(&mut file, zip) {
                                failed_compressions.push(FailedCompression {
                                    file: path.to_string_lossy().to_string(),
                                    error: e.to_string(),
                                });
                            }
                        }
                        Err(e) => {
                            failed_compressions.push(FailedCompression {
                                file: path.to_string_lossy().to_string(),
                                error: e.to_string(),
                            });
                        }
                    }
                }
                Err(e) => {
                    failed_compressions.push(FailedCompression {
                        file: path.to_string_lossy().to_string(),
                        error: e.to_string(),
                    });
                }
            }
        } else if path.is_dir() {
            add_dir_to_zip(zip, &path, base_path, options, failed_compressions)?;
        }
    }

    Ok(())
}

/// 递归计算文件数量
fn count_files_recursive(dir_path: &Path) -> usize {
    let mut count = 0;
    
    if let Ok(entries) = fs::read_dir(dir_path) {
        for entry in entries {
            if let Ok(entry) = entry {
                let path = entry.path();
                if path.is_file() {
                    count += 1;
                } else if path.is_dir() {
                    count += count_files_recursive(&path);
                }
            }
        }
    }
    
    count
}

/// 逐个删除文件
fn delete_files_individually(dir_path: &Path, failed_operations: &mut Vec<FailedOperation>) {
    if let Ok(entries) = fs::read_dir(dir_path) {
        for entry in entries {
            if let Ok(entry) = entry {
                let path = entry.path();
                
                if path.is_file() {
                    #[cfg(target_os = "windows")]
                    {
                        // 清除只读属性
                        if let Ok(mut perms) = fs::metadata(&path).map(|m| m.permissions()) {
                            perms.set_readonly(false);
                            let _ = fs::set_permissions(&path, perms);
                        }
                    }
                    
                    if let Err(e) = fs::remove_file(&path) {
                        failed_operations.push(FailedOperation {
                            operation_type: "file".to_string(),
                            path: path.to_string_lossy().to_string(),
                            error: e.to_string(),
                        });
                    }
                } else if path.is_dir() {
                    delete_files_individually(&path, failed_operations);
                    
                    if let Err(e) = fs::remove_dir(&path) {
                        failed_operations.push(FailedOperation {
                            operation_type: "directory".to_string(),
                            path: path.to_string_lossy().to_string(),
                            error: e.to_string(),
                        });
                    }
                }
            }
        }
    }
}
