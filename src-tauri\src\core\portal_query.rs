/// Portal Token查询模块
/// 
/// 实现account_info_fetcher.py的逻辑，使用Portal Token查询Orb API获取账号信息
/// 包括订阅信息获取、余额查询、时间转换等功能

use serde::{Deserialize, Serialize};
use serde_json::Value;
use reqwest;
use std::time::Duration;
use chrono::DateTime;
use anyhow::Result;

/// 账号数据结构体
/// 对应account_info_fetcher.py中的OrbAccountInfo类的数据字段
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountData {
    /// 邮箱地址
    pub email: Option<String>,
    /// 计划名称
    pub plan_name: Option<String>,
    /// 账号状态
    pub status: Option<String>,
    /// 到期时间（北京时间格式）
    pub end_date: Option<String>,
    /// 当前余额
    pub credits_balance: Option<f64>,
    /// 总额度
    pub total_credits: Option<f64>,
    /// 有效性
    pub validity: String,
    /// 使用量显示
    pub usage_display: String,
}

impl Default for AccountData {
    fn default() -> Self {
        Self {
            email: None,
            plan_name: None,
            status: None,
            end_date: None,
            credits_balance: None,
            total_credits: None,
            validity: "❌ 无效".to_string(),
            usage_display: "N/A".to_string(),
        }
    }
}

/// Orb账号信息查询器
/// 对应account_info_fetcher.py中的OrbAccountInfo类
pub struct OrbAccountInfo {
    /// Portal Token
    token: String,
    /// 基础URL
    base_url: String,
    /// HTTP客户端
    http_client: reqwest::Client,
    /// 客户ID
    customer_id: Option<String>,
    /// 定价单元ID
    pricing_unit_id: Option<String>,
    /// 订阅ID
    subscription_id: Option<String>,
    /// Price ID (用于usage API)
    price_id: Option<String>,
    /// Ledger总额度（基础额度）
    ledger_total_credits: f64,

}

impl OrbAccountInfo {
    /// 创建新的Orb账号信息查询器
    /// 对应account_info_fetcher.py的__init__方法
    pub fn new(token: String) -> Result<Self> {
        // 设置HTTP headers - 完全按照account_info_fetcher.py
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert("accept", "application/json, text/plain, */*".parse()?);
        headers.insert("accept-language", "zh-CN,zh;q=0.9".parse()?);
        headers.insert("priority", "u=1, i".parse()?);
        headers.insert("referer", format!("https://portal.withorb.com/view?token={}", token).parse()?);
        headers.insert("sec-ch-ua", r#""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138""#.parse()?);
        headers.insert("sec-ch-ua-mobile", "?0".parse()?);
        headers.insert("sec-ch-ua-platform", r#""Windows""#.parse()?);
        headers.insert("sec-fetch-dest", "empty".parse()?);
        headers.insert("sec-fetch-mode", "cors".parse()?);
        headers.insert("sec-fetch-site", "same-origin".parse()?);
        headers.insert("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36".parse()?);

        // 创建HTTP客户端 - 复用现有模式
        let http_client = reqwest::Client::builder()
            .default_headers(headers)
            .timeout(Duration::from_secs(30))
            .build()?;

        Ok(Self {
            token,
            base_url: "https://portal.withorb.com/api/v1".to_string(),
            http_client,
            customer_id: None,
            pricing_unit_id: None,
            subscription_id: None,
            price_id: None,
            ledger_total_credits: 0.0,
        })
    }

    /// 将UTC时间转换为北京时间
    /// 对应account_info_fetcher.py的convert_to_beijing_time方法
    pub fn convert_to_beijing_time(&self, utc_time_str: &str) -> String {
        if utc_time_str.is_empty() || utc_time_str == "N/A" {
            return "N/A".to_string();
        }

        // 处理时间字符串格式
        let mut time_str = utc_time_str.to_string();
        if time_str.ends_with('Z') {
            time_str = time_str.replace('Z', "+00:00");
        }

        // 解析UTC时间
        match DateTime::parse_from_rfc3339(&time_str) {
            Ok(dt) => {
                // 转换为北京时间（UTC+8）
                let beijing_dt = dt.with_timezone(&chrono::FixedOffset::east_opt(8 * 3600).unwrap());
                // 格式化为中文易读格式
                beijing_dt.format("%Y年%m月%d日 %H:%M:%S (北京时间)").to_string()
            }
            Err(_) => {
                // 如果解析失败，尝试其他格式
                match DateTime::parse_from_str(&time_str, "%Y-%m-%dT%H:%M:%S%.f%z") {
                    Ok(dt) => {
                        let beijing_dt = dt.with_timezone(&chrono::FixedOffset::east_opt(8 * 3600).unwrap());
                        beijing_dt.format("%Y年%m月%d日 %H:%M:%S (北京时间)").to_string()
                    }
                    Err(_) => {
                        eprintln!("⚠️ 时间转换失败: {}", utc_time_str);
                        utc_time_str.to_string()
                    }
                }
            }
        }
    }

    /// 获取订阅信息
    /// 对应account_info_fetcher.py的get_subscriptions方法
    pub async fn get_subscriptions(&mut self) -> Result<(Option<String>, Option<String>, Option<String>, Option<String>)> {
        let url = format!("{}/subscriptions_from_link", self.base_url);

        let response = match self.http_client
            .get(&url)
            .query(&[("token", &self.token)])
            .send()
            .await {
                Ok(resp) => resp,
                Err(e) => {
                    // 简化网络错误信息，不暴露URL
                    if e.is_timeout() {
                        return Err(anyhow::anyhow!("网络超时"));
                    } else if e.is_connect() {
                        return Err(anyhow::anyhow!("网络连接失败"));
                    } else {
                        return Err(anyhow::anyhow!("网络请求失败"));
                    }
                }
            };

        if response.status().is_success() {
            let data: Value = response.json().await?;
            
            if let Some(subscriptions) = data.get("data").and_then(|d| d.as_array()) {
                if let Some(subscription) = subscriptions.first() {
                    // 提取客户信息
                    let customer = subscription.get("customer").unwrap_or(&Value::Null);
                    self.customer_id = customer.get("id").and_then(|v| v.as_str()).map(|s| s.to_string());
                    let email = customer.get("name").and_then(|v| v.as_str()).map(|s| s.to_string());

                    // 提取订阅信息
                    self.subscription_id = subscription.get("id").and_then(|v| v.as_str()).map(|s| s.to_string());
                    let plan_name = subscription.get("name").and_then(|v| v.as_str()).map(|s| s.to_string());
                    let status = subscription.get("status").and_then(|v| v.as_str()).map(|s| s.to_string());


                    
                    // 获取并转换到期时间
                    let end_date_str = subscription.get("end_date")
                        .and_then(|v| v.as_str())
                        .unwrap_or("N/A");
                    let end_date = Some(self.convert_to_beijing_time(end_date_str));
                    
                    // 提取pricing_unit_id用于获取余额，同时保存price_id
                    if let Some(price_intervals) = subscription.get("price_intervals").and_then(|v| v.as_array()) {
                        for interval in price_intervals {
                            if let Some(allocation) = interval.get("allocation") {
                                if let Some(pricing_unit) = allocation.get("pricing_unit") {
                                    if let Some(id) = pricing_unit.get("id").and_then(|v| v.as_str()) {
                                        self.pricing_unit_id = Some(id.to_string());
                                        break;
                                    }
                                }
                            }
                        }

                        // 查找User Message相关的price_id (用于usage API)
                        for interval in price_intervals {
                            if let Some(price) = interval.get("price") {
                                if let Some(price_detail) = price.get("price") {
                                    if let Some(name) = price_detail.get("name").and_then(|v| v.as_str()) {
                                        if name == "User Message" {
                                            self.price_id = price.get("id").and_then(|v| v.as_str()).map(|s| s.to_string());
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    println!("✅ 订阅信息获取成功");
                    if let Some(ref email) = email {
                        println!("✅ 邮箱: {}", email);
                    }
                    if let (Some(ref plan), Some(ref status)) = (&plan_name, &status) {
                        println!("✅ 计划: {}, 状态: {}", plan, status);
                    }
                    
                    return Ok((email, plan_name, status, end_date));
                }
            }
            
            println!("❌ 订阅数据为空");
            return Err(anyhow::anyhow!("订阅数据为空"));
        }
        
        println!("❌ 获取订阅信息失败，状态码: {}", response.status());
        // 简化错误信息，不暴露具体状态码
        if response.status().is_client_error() {
            if response.status() == 401 || response.status() == 403 {
                Err(anyhow::anyhow!("Token无效或已过期"))
            } else if response.status() == 404 {
                Err(anyhow::anyhow!("资源不存在"))
            } else {
                Err(anyhow::anyhow!("请求参数错误"))
            }
        } else if response.status().is_server_error() {
            Err(anyhow::anyhow!("服务器错误"))
        } else {
            Err(anyhow::anyhow!("获取订阅信息失败"))
        }
    }

    /// 获取账户余额信息
    /// 对应account_info_fetcher.py的get_ledger_summary方法
    pub async fn get_ledger_summary(&mut self) -> Result<(Option<f64>, Option<f64>)> {
        if self.customer_id.is_none() || self.pricing_unit_id.is_none() {
            println!("⚠️ 无法获取余额信息（缺少必要参数）");
            return Ok((None, None));
        }

        let customer_id = self.customer_id.as_ref().unwrap();
        let pricing_unit_id = self.pricing_unit_id.as_ref().unwrap();
        
        let url = format!("{}/customers/{}/ledger_summary", self.base_url, customer_id);
        
        let response = match self.http_client
            .get(&url)
            .query(&[
                ("pricing_unit_id", pricing_unit_id),
                ("token", &self.token)
            ])
            .send()
            .await {
                Ok(resp) => resp,
                Err(e) => {
                    // 简化网络错误信息，不暴露URL
                    if e.is_timeout() {
                        return Err(anyhow::anyhow!("网络超时"));
                    } else if e.is_connect() {
                        return Err(anyhow::anyhow!("网络连接失败"));
                    } else {
                        return Err(anyhow::anyhow!("网络请求失败"));
                    }
                }
            };

        if response.status().is_success() {
            let data: Value = response.json().await?;

            // 提取剩余额度
            let credits_balance = data.get("credits_balance")
                .and_then(|v| v.as_str())
                .and_then(|s| s.parse::<f64>().ok());

            // 🔧 修复：计算基础额度需要从credit_blocks获取所有有效的非allocation blocks
            self.ledger_total_credits = 0.0;

            if let Some(credit_blocks) = data.get("credit_blocks").and_then(|v| v.as_array()) {
                for block in credit_blocks {
                    let max_balance = block.get("maximum_initial_balance")
                        .and_then(|v| v.as_str())
                        .and_then(|s| s.parse::<f64>().ok());
                    let is_active = block.get("is_active").and_then(|v| v.as_bool()).unwrap_or(false);
                    let allocation_id = block.get("allocation_id");

                    // 只计算有实际余额且不是allocation产生的blocks
                    // allocation_id为null表示这是基础额度，不是allocation产生的
                    if let Some(max_bal) = max_balance {
                        let is_base_credit = allocation_id.map_or(true, |v| v.is_null());
                        if is_active && is_base_credit {
                            self.ledger_total_credits += max_bal;
                        }
                    }
                }
            }

            // 🔧 修复：获取真实的总额度和使用量
            let real_total_credits = self.get_real_total_credits().await.unwrap_or(self.ledger_total_credits);

            println!("✅ 余额获取成功");
            return Ok((credits_balance, Some(real_total_credits)));
        }
        
        println!("❌ 获取余额失败，状态码: {}", response.status());
        // 简化错误信息，不暴露具体状态码
        if response.status().is_client_error() {
            if response.status() == 401 || response.status() == 403 {
                Err(anyhow::anyhow!("Token无效或已过期"))
            } else if response.status() == 404 {
                Err(anyhow::anyhow!("资源不存在"))
            } else {
                Err(anyhow::anyhow!("请求参数错误"))
            }
        } else if response.status().is_server_error() {
            Err(anyhow::anyhow!("服务器错误"))
        } else {
            Err(anyhow::anyhow!("获取余额失败"))
        }
    }

    /// 获取完整的账号信息
    /// 对应account_info_fetcher.py的fetch_account_info方法
    pub async fn fetch_account_info(&mut self) -> Result<AccountData> {
        println!("🚀 开始获取账号信息...");
        
        // 获取订阅信息（包含邮箱、计划、状态、到期时间）
        let (email, plan_name, status, end_date) = self.get_subscriptions().await?;
        
        // 获取余额信息
        let (credits_balance, total_credits) = if self.pricing_unit_id.is_some() {
            self.get_ledger_summary().await.unwrap_or((None, None))
        } else {
            (None, None)
        };
        
        // 判断有效性
        let validity = if status.as_deref() == Some("active") {
            "✅ 有效".to_string()
        } else {
            "❌ 无效".to_string()
        };
        
        // 🔧 修复：使用真实使用量计算
        let usage_display = if let (Some(balance), Some(total)) = (credits_balance, total_credits) {
            let used_credits = total - balance;
            if total > 0.0 {
                let usage_percent = (used_credits / total) * 100.0;
                format!("{:.0}/{:.0} ({:.1}%)", used_credits, total, usage_percent)
            } else {
                format!("{:.0}/{:.0} (0%)", used_credits, total)
            }
        } else {
            "N/A".to_string()
        };
        
        let account_data = AccountData {
            email,
            plan_name,
            status,
            end_date,
            credits_balance,
            total_credits,
            validity,
            usage_display,
        };
        
        println!("🎉 账号信息获取完成!");
        Ok(account_data)
    }

    /// 获取真实总额度 - 基于当前有效期内的所有credit blocks
    /// 对应account_info_fetcher.py的get_real_total_credits方法
    async fn get_real_total_credits(&self) -> Result<f64> {
        use chrono::{DateTime, Utc};

        if self.customer_id.is_none() || self.pricing_unit_id.is_none() {
            return Ok(0.0);
        }

        let current_time = Utc::now();

        // 重新获取credit_blocks数据以确保准确性
        let url = format!("{}/customers/{}/ledger_summary",
                         self.base_url,
                         self.customer_id.as_ref().unwrap());

        let response = self.http_client
            .get(&url)
            .query(&[
                ("pricing_unit_id", self.pricing_unit_id.as_ref().unwrap()),
                ("token", &self.token)
            ])
            .send()
            .await?;

        if response.status().is_success() {
            let data: Value = response.json().await?;
            let empty_vec = vec![];
            let credit_blocks = data.get("credit_blocks").and_then(|v| v.as_array()).unwrap_or(&empty_vec);

            let mut total_credits = 0.0;

            for (_i, block) in credit_blocks.iter().enumerate() {
                let max_balance = block.get("maximum_initial_balance")
                    .and_then(|v| v.as_str())
                    .and_then(|s| s.parse::<f64>().ok());
                let effective_date = block.get("effective_date").and_then(|v| v.as_str());
                let expiry_date = block.get("expiry_date").and_then(|v| v.as_str());
                let is_active = block.get("is_active").and_then(|v| v.as_bool()).unwrap_or(false);

                // 检查是否应该计入总额度
                if let Some(max_bal) = max_balance {
                    if is_active {
                        let mut should_include = false;

                        match (effective_date, expiry_date) {
                            (Some(effective), Some(expiry)) => {
                                // 尝试解析日期
                                match (
                                    DateTime::parse_from_rfc3339(&effective.replace('Z', "+00:00")),
                                    DateTime::parse_from_rfc3339(&expiry.replace('Z', "+00:00"))
                                ) {
                                    (Ok(effective_dt), Ok(expiry_dt)) => {
                                        let effective_utc = effective_dt.with_timezone(&Utc);
                                        let expiry_utc = expiry_dt.with_timezone(&Utc);

                                        // 只计算当前有效期内的credit blocks
                                        if effective_utc <= current_time && current_time <= expiry_utc {
                                            should_include = true;
                                        }
                                    }
                                    _ => {
                                        // 如果日期解析失败，保守处理，认为有效
                                        should_include = true;
                                    }
                                }
                            }
                            _ => {
                                // 如果缺少日期信息，保守处理，认为有效
                                should_include = true;
                            }
                        }

                        if should_include {
                            total_credits += max_bal;
                        }
                    }
                }
            }

            Ok(total_credits)
        } else {
            Ok(0.0)
        }
    }

    /// 获取账号创建时间
    /// 调用subscriptions_from_link API获取详细的订阅信息，其中包含创建时间
    pub async fn get_customer_creation_time(&self) -> Result<Option<String>> {
        let url = format!("{}/subscriptions_from_link", self.base_url);

        let response = self.http_client
            .get(&url)
            .query(&[("token", &self.token)])
            .send()
            .await?;

        if response.status().is_success() {
            let data: Value = response.json().await?;

            // 提取订阅创建时间
            if let Some(subscriptions) = data.get("data").and_then(|d| d.as_array()) {
                if let Some(subscription) = subscriptions.first() {
                    if let Some(creation_time_str) = subscription.get("creation_time").and_then(|t| t.as_str()) {
                        // 将ISO 8601格式转换为我们需要的格式
                        let formatted_time = self.format_iso_timestamp(creation_time_str);
                        Ok(Some(formatted_time))
                    } else {
                        Ok(None)
                    }
                } else {
                    Ok(None)
                }
            } else {
                Ok(None)
            }
        } else {
            Err(anyhow::anyhow!("获取订阅信息失败: HTTP {}", response.status()))
        }
    }

    /// 将ISO 8601格式时间转换为可读格式
    fn format_iso_timestamp(&self, iso_time: &str) -> String {
        use chrono::DateTime;

        // 解析ISO 8601格式的时间字符串
        if let Ok(datetime) = DateTime::parse_from_rfc3339(iso_time) {
            datetime.format("%Y-%m-%d %H:%M:%S UTC").to_string()
        } else {
            // 如果解析失败，返回原始字符串
            iso_time.to_string()
        }
    }
}
