use tauri::{State, AppHandle, Emitter};
use serde_json::Value;
use crate::app_state::AppState;

/// 获取配置
/// 对应原版 Python 的 get_config 方法
#[tauri::command]
pub async fn get_config(state: State<'_, AppState>) -> Result<Value, String> {
    let config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    Ok(config_manager.get_config().clone())
}

/// 获取配置值
/// 对应原版 Python 的 get 方法
#[tauri::command]
pub async fn get_config_value(
    key_path: String,
    default: Option<Value>,
    state: State<'_, AppState>
) -> Result<Value, String> {
    let config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    Ok(config_manager.get(&key_path, default))
}

/// 设置配置值
/// 对应原版 Python 的 set 方法
#[tauri::command]
pub async fn set_config_value(
    key_path: String,
    value: Value,
    state: State<'_, AppState>
) -> Result<bool, String> {
    let mut config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    config_manager.set(&key_path, value).map_err(|e| e.to_string())
}

/// 保存配置
/// 对应原版 Python 的 save_config 方法
#[tauri::command]
pub async fn save_config(config: Value, state: State<'_, AppState>, app_handle: AppHandle) -> Result<bool, String> {
    let mut config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    // 更新配置
    config_manager.config = config.clone();
    let save_result = config_manager.save_config().map_err(|e| e.to_string())?;

    if save_result {
        // 释放配置管理器锁
        drop(config_manager);

        // 更新EmailManager配置 - 与原版Python行为一致
        {
            let mut email_manager = state.email_manager.lock().map_err(|e| e.to_string())?;
            email_manager.update_config(config.clone()).map_err(|e| e.to_string())?;
        }

        // 发送配置更新事件 - 与原版Python行为一致
        let _ = app_handle.emit("config_updated", serde_json::json!({ "config": config }));
    }

    Ok(save_result)
}

/// 获取编辑器类型
/// 对应原版 Python 的 get_editor_type 方法
#[tauri::command]
pub async fn get_editor_type(state: State<'_, AppState>) -> Result<Option<String>, String> {
    let config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    Ok(config_manager.get_editor_type())
}

/// 设置编辑器类型
/// 对应原版 Python 的 set_editor_type 方法
#[tauri::command]
pub async fn set_editor_type(
    editor_type: String,
    state: State<'_, AppState>
) -> Result<bool, String> {
    let mut config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    config_manager.set_editor_type(&editor_type).map_err(|e| e.to_string())
}

/// 检查是否是首次运行
/// 对应原版 Python 的 is_first_run 方法
#[tauri::command]
pub async fn is_first_run(state: State<'_, AppState>) -> Result<bool, String> {
    let config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    Ok(config_manager.is_first_run())
}

/// 重置配置
/// 对应原版 Python 的 reset_config 方法
#[tauri::command]
pub async fn reset_config(state: State<'_, AppState>) -> Result<bool, String> {
    let mut config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    config_manager.reset_config().map_err(|e| e.to_string())
}

/// 导出配置
/// 对应原版 Python 的 export_config 方法
#[tauri::command]
pub async fn export_config(
    export_path: String,
    state: State<'_, AppState>
) -> Result<bool, String> {
    let config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    config_manager.export_config(&export_path).map_err(|e| e.to_string())
}

/// 导入配置
/// 对应原版 Python 的 import_config 方法
#[tauri::command]
pub async fn import_config(
    import_path: String,
    state: State<'_, AppState>
) -> Result<bool, String> {
    let mut config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
    config_manager.import_config(&import_path).map_err(|e| e.to_string())
}
