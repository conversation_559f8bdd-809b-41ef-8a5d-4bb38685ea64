use rand::RngCore;

/// 生成机器ID
/// 对应原版 Python device_codes.py 的 generate_machine_id 函数
/// 完全按照原版逻辑：使用 secrets.token_bytes(32) 生成64字符的十六进制字符串
///
/// Generate a random 64-character hex string for machine ID.
/// Similar to using /dev/urandom in bash but using Python's cryptographic functions.
///
/// Returns:
///     Result<String, String>: A 64-character hexadecimal string
pub fn generate_machine_id() -> Result<String, String> {
    // 生成32个随机字节（将变成64个十六进制字符）
    // 对应原版 Python: random_bytes = secrets.token_bytes(32)
    let mut random_bytes = [0u8; 32];
    rand::thread_rng().fill_bytes(&mut random_bytes);

    // 转换为十六进制字符串
    // 对应原版 Python: return random_bytes.hex()
    Ok(hex::encode(random_bytes))
}

/// 生成设备ID
/// 对应原版 Python device_codes.py 的 generate_device_id 函数
/// 完全按照原版逻辑：生成随机 UUID v4 并转换为小写
///
/// Generate a random UUID v4 for device ID.
///
/// Returns:
///     Result<String, String>: A lowercase UUID v4 string in the format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
///     where x is any hexadecimal digit and y is one of 8, 9, A, or B
pub fn generate_device_id() -> Result<String, String> {
    // 生成随机 UUID v4
    // 对应原版 Python: device_id = str(uuid.uuid4())
    let device_id = uuid::Uuid::new_v4().to_string();

    // 转换为小写
    // 对应原版 Python: return device_id.lower()
    Ok(device_id.to_lowercase())
}
