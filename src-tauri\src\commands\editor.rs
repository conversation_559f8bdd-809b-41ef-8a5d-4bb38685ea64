use tauri::State;
use serde_json::Value;
use crate::app_state::AppState;
// use crate::core::editor::EditorDetector;

/// 检测编辑器
/// 对应原版 Python 中的编辑器检测功能
#[tauri::command]
pub async fn detect_editors(state: State<'_, AppState>) -> Result<Value, String> {
    let detector = &*state.editor_detector;
    let result = detector.detect_all();
    Ok(result)
}

/// 获取选中的编辑器
/// 从配置中获取当前选中的编辑器类型
#[tauri::command]
pub async fn get_selected_editor(state: State<'_, AppState>) -> Result<String, String> {
    let config_manager = state.config_manager.lock().unwrap();
    match config_manager.get_editor_type() {
        Some(editor_type) => Ok(editor_type),
        None => Ok("".to_string())
    }
}

/// 选择编辑器
/// 设置当前选中的编辑器类型并保存到配置
#[tauri::command]
pub async fn select_editor(editor_type: String, state: State<'_, AppState>) -> Result<bool, String> {
    let mut config_manager = state.config_manager.lock().unwrap();

    // 检查set_editor_type的返回值
    match config_manager.set_editor_type(&editor_type) {
        Ok(true) => {
            println!("Editor selected: {}", editor_type);
            Ok(true)
        },
        Ok(false) => {
            eprintln!("Invalid editor type: {}", editor_type);
            Err(format!("Invalid editor type: {}. Only 'vscode' and 'cursor' are supported.", editor_type))
        },
        Err(e) => {
            eprintln!("Failed to save editor selection: {}", e);
            Err(format!("Failed to save editor selection: {}", e))
        }
    }
}

/// 获取编辑器状态
/// 返回编辑器检测结果和当前选中的编辑器
#[tauri::command]
pub async fn get_editor_status(state: State<'_, AppState>) -> Result<Value, String> {
    let detector = &*state.editor_detector;
    let config_manager = state.config_manager.lock().unwrap();

    let detection_result = detector.detect_all();
    let selected_editor = config_manager.get_editor_type().unwrap_or_default();

    Ok(serde_json::json!({
        "detection": detection_result,
        "selected": selected_editor
    }))
}

// is_first_run 命令已移至 config.rs，因为它属于配置管理功能
