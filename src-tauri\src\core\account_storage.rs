/// 账号存储管理器
/// 
/// 复用ConfigManager的JSON管理模式，管理YAugment/account/accounts.json文件
/// 实现账号数据的保存、读取、去重和查询功能

use serde::{Deserialize, Serialize};
// use serde_json::Value; // 暂时未使用
use std::path::PathBuf;
use anyhow::Result;
use crate::utils::paths::get_app_data_dir;

/// 账号记录结构体
/// 简化版本，只保留账号真实创建时间
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountRecord {
    /// 邮箱地址，用作唯一标识
    pub email: String,
    /// Portal Token
    pub portal_token: String,
    /// 账号真实创建时间（从API获取的creation_time）
    pub account_creation_time: Option<String>,
}

impl AccountRecord {
    /// 创建新的账号记录
    pub fn new(email: String, portal_token: String) -> Self {
        Self {
            email,
            portal_token,
            account_creation_time: None,
        }
    }

    /// 创建新的账号记录（包含真实创建时间）
    pub fn new_with_creation_time(email: String, portal_token: String, account_creation_time: Option<String>) -> Self {
        Self {
            email,
            portal_token,
            account_creation_time,
        }
    }

    /// 设置账号真实创建时间
    pub fn set_account_creation_time(&mut self, creation_time: String) {
        self.account_creation_time = Some(creation_time);
    }

    /// 获取显示时间（显示真实创建时间）
    pub fn get_display_time(&self) -> Option<&str> {
        self.account_creation_time.as_deref()
    }
}

/// 账号存储管理器
/// 复用ConfigManager模式，管理accounts.json文件
#[derive(Debug)]
pub struct AccountStorageManager {
    /// 账号列表
    accounts: Vec<AccountRecord>,
    /// 存储目录
    #[allow(dead_code)]
    storage_dir: PathBuf,
    /// 存储文件路径
    storage_file: PathBuf,
}

impl AccountStorageManager {
    /// 创建新的账号存储管理器
    /// 复用ConfigManager的初始化模式
    pub fn new() -> Result<Self> {
        let storage_dir = Self::get_storage_dir()?;
        let storage_file = storage_dir.join("accounts.json");
        let accounts = Self::load_accounts_from_file(&storage_file)?;

        Ok(Self {
            accounts,
            storage_dir,
            storage_file,
        })
    }

    /// 获取存储目录路径
    /// 对应任务要求：{app_data_dir}/account/
    fn get_storage_dir() -> Result<PathBuf> {
        let app_data_dir = get_app_data_dir()
            .map_err(|e| anyhow::anyhow!("获取应用数据目录失败: {}", e))?;
        let storage_dir = app_data_dir.join("account");
        
        // 确保目录存在
        if !storage_dir.exists() {
            std::fs::create_dir_all(&storage_dir)
                .map_err(|e| anyhow::anyhow!("创建存储目录失败: {}", e))?;
        }
        
        Ok(storage_dir)
    }

    /// 从文件加载账号数据
    /// 复用ConfigManager的load_config_from_file模式
    fn load_accounts_from_file(storage_file: &PathBuf) -> Result<Vec<AccountRecord>> {
        if storage_file.exists() {
            match std::fs::read_to_string(storage_file) {
                Ok(content) => {
                    match serde_json::from_str::<Vec<AccountRecord>>(&content) {
                        Ok(accounts) => {
                            // println!("成功加载 {} 个账号记录", accounts.len());
                            Ok(accounts)
                        }
                        Err(e) => {
                            eprintln!("解析账号文件失败: {}", e);
                            Ok(Vec::new())
                        }
                    }
                }
                Err(e) => {
                    eprintln!("读取账号文件失败: {}", e);
                    Ok(Vec::new())
                }
            }
        } else {
            // 文件不存在，创建空的账号文件
            println!("账号文件不存在，创建空的账号文件: {:?}", storage_file);
            let empty_accounts: Vec<AccountRecord> = Vec::new();
            match std::fs::write(
                storage_file,
                serde_json::to_string_pretty(&empty_accounts)?,
            ) {
                Ok(_) => println!("空账号文件创建成功"),
                Err(_) => eprintln!("创建空账号文件失败"),
            }
            Ok(empty_accounts)
        }
    }

    /// 保存账号到存储
    /// 实现基于email的去重功能
    /// 返回值：Ok(true) = 新增账号, Ok(false) = 更新现有账号, Err = 保存失败
    pub fn save_account(&mut self, email: String, portal_token: String) -> Result<bool> {
        // 检查是否已存在相同邮箱的账号
        if let Some(existing_account) = self.accounts.iter_mut().find(|acc| acc.email == email) {
            // 更新现有账号的Portal Token
            existing_account.portal_token = portal_token;
            println!("更新现有账号: {}", email);

            // 保存到文件，返回false表示是更新现有账号
            match self.save_to_file_internal() {
                Ok(_) => Ok(false), // false表示更新现有账号
                Err(e) => Err(e),
            }
        } else {
            // 添加新账号
            let new_account = AccountRecord::new(email.clone(), portal_token);
            self.accounts.push(new_account);
            println!("添加新账号: {}", email);

            // 保存到文件，返回true表示是新增账号
            match self.save_to_file_internal() {
                Ok(_) => Ok(true), // true表示新增账号
                Err(e) => Err(e),
            }
        }
    }

    /// 保存账号列表到文件（内部方法）
    /// 复用ConfigManager的save_config模式
    fn save_to_file_internal(&self) -> Result<()> {
        match std::fs::write(
            &self.storage_file,
            serde_json::to_string_pretty(&self.accounts)?,
        ) {
            Ok(_) => {
                println!("账号数据保存成功");
                Ok(())
            }
            Err(e) => {
                eprintln!("保存账号数据失败: {}", e);
                Err(anyhow::anyhow!("保存失败: {}", e))
            }
        }
    }

    /// 保存账号列表到文件（向后兼容方法）
    pub fn save_to_file(&self) -> Result<bool> {
        match self.save_to_file_internal() {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }

    /// 获取所有保存的账号
    pub fn get_accounts(&self) -> &Vec<AccountRecord> {
        &self.accounts
    }

    /// 根据邮箱获取账号
    pub fn get_account_by_email(&self, email: &str) -> Option<&AccountRecord> {
        self.accounts.iter().find(|acc| acc.email == email)
    }

    /// 根据邮箱获取可变账号引用（用于更新最后使用时间）
    pub fn get_account_by_email_mut(&mut self, email: &str) -> Option<&mut AccountRecord> {
        self.accounts.iter_mut().find(|acc| acc.email == email)
    }





    /// 删除账号
    pub fn remove_account(&mut self, email: &str) -> Result<bool> {
        let original_len = self.accounts.len();
        self.accounts.retain(|acc| acc.email != email);
        
        if self.accounts.len() < original_len {
            println!("删除账号: {}", email);
            self.save_to_file()
        } else {
            Ok(false)
        }
    }

    /// 获取账号数量
    pub fn count(&self) -> usize {
        self.accounts.len()
    }

    /// 检查是否存在指定邮箱的账号
    pub fn has_account(&self, email: &str) -> bool {
        self.accounts.iter().any(|acc| acc.email == email)
    }
}


