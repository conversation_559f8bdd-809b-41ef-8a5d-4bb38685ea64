/// 窗口控制命令
/// 
/// 对应原版 Python 的窗口控制功能

use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Window, Emitter};



#[tauri::command]
pub async fn minimize_window(window: Window) -> Result<(), String> {
    window.minimize().map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn toggle_maximize(window: Window) -> Result<(), String> {
    if window.is_maximized().map_err(|e| e.to_string())? {
        window.unmaximize().map_err(|e| e.to_string())
    } else {
        window.maximize().map_err(|e| e.to_string())
    }
}

#[tauri::command]
pub async fn close_window(app: AppHandle) -> Result<(), String> {
    // 应用关闭前清理时效检查资源
    let state = app.state::<crate::AppState>();
    crate::core::expiry_checker::ExpiryChecker::stop_expiry_check_for_cleanup(&state).await;

    app.exit(0);
    Ok(())
}

#[tauri::command]
pub async fn start_window_drag(window: Window, _x: f64, _y: f64) -> Result<(), String> {
    // Tauri 会自动处理窗口拖拽，这里主要是为了兼容性
    window.start_dragging().map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn update_window_drag(_window: Window, _x: f64, _y: f64) -> Result<(), String> {
    // 在 Tauri 中，拖拽是自动处理的，这个命令主要用于兼容性
    Ok(())
}

#[tauri::command]
pub async fn end_window_drag(_window: Window) -> Result<(), String> {
    // 在 Tauri 中，拖拽结束是自动处理的，这个命令主要用于兼容性
    Ok(())
}

#[tauri::command]
pub async fn log_message(message: String) -> Result<(), String> {
    println!("[Frontend Log] {}", message);
    Ok(())
}

#[tauri::command]
pub async fn resize_window(window: Window, width: f64, height: f64) -> Result<(), String> {
    use tauri::Size;
    let size = Size::Logical(tauri::LogicalSize {
        width,
        height,
    });
    window.set_size(size).map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn center_window(window: Window) -> Result<(), String> {
    #[cfg(target_os = "macos")]
    {
        // Mac平台优化的居中逻辑
        return center_window_mac_optimized(window).await;
    }

    #[cfg(not(target_os = "macos"))]
    {
        // 其他平台使用标准居中
        window.center().map_err(|e| e.to_string())
    }
}

/// Mac平台优化的窗口居中函数
#[cfg(target_os = "macos")]
async fn center_window_mac_optimized(window: Window) -> Result<(), String> {
    use tauri::Position;

    println!("Mac平台：开始优化窗口居中");

    // 步骤1：获取主显示器信息
    let monitor = window.primary_monitor().map_err(|e| e.to_string())?;
    let monitor = monitor.ok_or("无法获取主显示器信息")?;

    // 步骤2：获取当前窗口大小
    let window_size = window.inner_size().map_err(|e| e.to_string())?;

    // 步骤3：计算居中位置
    let monitor_size = monitor.size();
    let monitor_pos = monitor.position();

    // 考虑Mac的菜单栏高度（通常是25-28像素）
    let menu_bar_height = 28.0;
    let available_height = monitor_size.height as f64 - menu_bar_height;

    let center_x = monitor_pos.x as f64 + (monitor_size.width as f64 - window_size.width as f64) / 2.0;
    let center_y = monitor_pos.y as f64 + menu_bar_height + (available_height - window_size.height as f64) / 2.0;

    println!("Mac平台：计算居中位置 - 显示器: {}x{}, 窗口: {}x{}, 目标位置: ({}, {})",
             monitor_size.width, monitor_size.height,
             window_size.width, window_size.height,
             center_x, center_y);

    // 步骤4：设置窗口位置
    let position = Position::Physical(tauri::PhysicalPosition {
        x: center_x as i32,
        y: center_y as i32,
    });

    window.set_position(position).map_err(|e| e.to_string())?;

    // 步骤5：等待位置设置完成
    tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

    // 步骤6：验证位置是否正确设置
    let final_position = window.outer_position().map_err(|e| e.to_string())?;
    println!("Mac平台：窗口居中完成，最终位置: ({}, {})", final_position.x, final_position.y);

    Ok(())
}

/// 强制居中窗口（Mac平台专用，解决居中失效问题）
#[tauri::command]
pub async fn force_center_window(window: Window) -> Result<(), String> {
    #[cfg(target_os = "macos")]
    {
        println!("Mac平台：开始强制居中窗口");

        // 多次尝试居中，确保成功
        for attempt in 1..=3 {
            println!("Mac平台：第{}次尝试居中", attempt);

            match center_window_mac_optimized(window.clone()).await {
                Ok(_) => {
                    // 验证居中是否成功
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

                    let position = window.outer_position().map_err(|e| e.to_string())?;
                    let monitor = window.primary_monitor().map_err(|e| e.to_string())?;

                    if let Some(monitor) = monitor {
                        let monitor_size = monitor.size();
                        let window_size = window.inner_size().map_err(|e| e.to_string())?;

                        // 检查窗口是否大致居中（允许一定误差）
                        let expected_x = (monitor_size.width as f64 - window_size.width as f64) / 2.0;
                        let actual_x = position.x as f64;
                        let x_diff = (expected_x - actual_x).abs();

                        if x_diff < 50.0 { // 允许50像素的误差
                            println!("Mac平台：窗口居中成功，位置验证通过");
                            return Ok(());
                        } else {
                            println!("Mac平台：窗口居中位置不准确，期望x: {}, 实际x: {}, 差异: {}",
                                   expected_x, actual_x, x_diff);
                        }
                    }
                }
                Err(e) => {
                    println!("Mac平台：第{}次居中尝试失败: {}", attempt, e);
                }
            }

            // 等待后重试
            if attempt < 3 {
                tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;
            }
        }

        println!("Mac平台：强制居中完成（可能未完全成功）");
        Ok(())
    }

    #[cfg(not(target_os = "macos"))]
    {
        // 非Mac平台使用标准居中
        window.center().map_err(|e| e.to_string())
    }
}

#[tauri::command]
pub async fn hide_window(window: Window) -> Result<(), String> {
    #[cfg(target_os = "macos")]
    {
        // Mac平台特殊处理：先最小化再隐藏，避免窗口状态不一致
        let _ = window.minimize();
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    }

    window.hide().map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn show_window(window: Window) -> Result<(), String> {
    #[cfg(target_os = "macos")]
    {
        // Mac平台特殊处理：确保窗口状态正确
        // 先尝试取消最小化，然后显示窗口
        let _ = window.unminimize();
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
    }

    let result = window.show().map_err(|e| e.to_string());

    #[cfg(target_os = "macos")]
    {
        // Mac平台：显示后确保窗口获得焦点
        if result.is_ok() {
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            let _ = window.set_focus();
        }
    }

    result
}

/// 强制显示窗口（Mac平台优化版本）
/// 专门用于解决Mac上窗口隐藏后无法显示的问题
#[tauri::command]
pub async fn force_show_window(window: Window) -> Result<(), String> {
    #[cfg(target_os = "macos")]
    {
        // Mac平台强制显示策略
        println!("Mac平台：开始强制显示窗口");

        // 步骤1：确保窗口不是最小化状态
        let _ = window.unminimize();
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        // 步骤2：尝试显示窗口
        let show_result = window.show();
        if let Err(e) = &show_result {
            println!("Mac平台：首次显示失败: {}", e);
        }
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        // 步骤3：设置窗口为前台
        let _ = window.set_focus();
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

        // 步骤4：确保窗口在最前面
        let _ = window.set_always_on_top(true);
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;
        let _ = window.set_always_on_top(false);

        println!("Mac平台：强制显示窗口完成");
        show_result.map_err(|e| e.to_string())
    }

    #[cfg(not(target_os = "macos"))]
    {
        // 非Mac平台使用标准显示方法
        window.show().map_err(|e| e.to_string())
    }
}

/// 检查窗口是否可见
#[tauri::command]
pub async fn is_window_visible(window: Window) -> Result<bool, String> {
    window.is_visible().map_err(|e| e.to_string())
}

/// 获取窗口和屏幕信息，包括 DPI 缩放因子
#[tauri::command]
pub async fn get_window_info(window: Window) -> Result<serde_json::Value, String> {
    use serde_json::json;

    // 获取窗口的缩放因子
    let scale_factor = window.scale_factor().map_err(|e| e.to_string())?;

    // 获取当前窗口大小（逻辑像素）
    let logical_size = window.inner_size().map_err(|e| e.to_string())?;

    // 获取当前窗口位置
    let position = window.outer_position().map_err(|e| e.to_string())?;

    // 获取主显示器信息
    let monitor = window.primary_monitor().map_err(|e| e.to_string())?;
    let monitor_info = if let Some(monitor) = monitor {
        json!({
            "name": monitor.name(),
            "size": {
                "width": monitor.size().width,
                "height": monitor.size().height
            },
            "position": {
                "x": monitor.position().x,
                "y": monitor.position().y
            },
            "scale_factor": monitor.scale_factor()
        })
    } else {
        json!(null)
    };

    Ok(json!({
        "scale_factor": scale_factor,
        "logical_size": {
            "width": logical_size.width,
            "height": logical_size.height
        },
        "position": {
            "x": position.x,
            "y": position.y
        },
        "monitor": monitor_info
    }))
}

/// 带动画效果的窗口大小调整
#[tauri::command]
pub async fn resize_window_animated(window: Window, width: f64, height: f64, duration_ms: Option<u64>) -> Result<(), String> {
    use tauri::Size;
    use tokio::time::{sleep, Duration};

    let duration = duration_ms.unwrap_or(300); // 默认 300ms 动画
    let steps = 20; // 动画步数
    let step_duration = duration / steps;

    // 获取当前窗口大小
    let current_size = window.inner_size().map_err(|e| e.to_string())?;
    let current_width = current_size.width as f64;
    let current_height = current_size.height as f64;

    // 计算每步的增量
    let width_step = (width - current_width) / steps as f64;
    let height_step = (height - current_height) / steps as f64;

    // 执行动画
    for i in 1..=steps {
        let new_width = current_width + width_step * i as f64;
        let new_height = current_height + height_step * i as f64;

        let size = Size::Logical(tauri::LogicalSize {
            width: new_width,
            height: new_height,
        });

        window.set_size(size).map_err(|e| e.to_string())?;

        if i < steps {
            sleep(Duration::from_millis(step_duration)).await;
        }
    }

    Ok(())
}

/// 退出应用程序
/// 对应原版Python的sys.exit()调用
#[tauri::command]
pub async fn exit_app(app: AppHandle) -> Result<(), String> {
    // 应用关闭前清理时效检查资源
    let state = app.state::<crate::AppState>();
    crate::core::expiry_checker::ExpiryChecker::stop_expiry_check_for_cleanup(&state).await;

    app.exit(0);
    Ok(())
}

/// 打开URL
/// 对应原版Python的webbrowser.open()调用
#[tauri::command]
pub async fn open_url(url: String) -> Result<(), String> {

    #[cfg(target_os = "windows")]
    {
        // Windows: 使用Windows API直接打开URL，避免命令行特殊字符问题
        use std::ffi::CString;
        use std::ptr;

        let url_cstring = CString::new(url.as_bytes())
            .map_err(|e| format!("URL转换失败: {}", e))?;

        unsafe {
            let result = winapi::um::shellapi::ShellExecuteA(
                ptr::null_mut(),
                CString::new("open").unwrap().as_ptr(),
                url_cstring.as_ptr(),
                ptr::null(),
                ptr::null(),
                winapi::um::winuser::SW_SHOWNORMAL,
            );

            if result as isize <= 32 {
                return Err("使用Windows API打开URL失败".to_string());
            }
        }
    }

    #[cfg(target_os = "macos")]
    {
        std::process::Command::new("open")
            .arg(&url)
            .spawn()
            .map_err(|e| format!("打开URL失败: {}", e))?;
    }

    #[cfg(target_os = "linux")]
    {
        std::process::Command::new("xdg-open")
            .arg(&url)
            .spawn()
            .map_err(|e| format!("打开URL失败: {}", e))?;
    }

    Ok(())
}

/// 启动主程序
/// 版本验证通过后调用，关闭验证窗口并显示主程序窗口
#[tauri::command]
pub async fn start_main_app(app: AppHandle) -> Result<(), String> {
    // 🔧 修复：启动主程序前检查并启动时效检查（如果尚未启动）
    let state = app.state::<crate::AppState>();

    // 检查是否已经有时效检查在运行
    let expiry_enabled = {
        let expiry_status = state.expiry_status.lock().await;
        expiry_status.enabled
    };

    // 只有在没有时效检查运行时才启动
    if !expiry_enabled {
        // 获取版本检查器实例
        let version_checker = {
            let checker_guard = state.version_checker.lock().await;
            checker_guard.clone()
        };

        if let Some(checker) = version_checker {
            // 检查现有验证状态并启动时效检查
            let app_clone = app.clone();
            tokio::spawn(async move {
                if let Err(e) = crate::core::expiry_checker::ExpiryChecker::start_from_verification_data(app_clone, &checker).await {
                    eprintln!("启动主程序时启动时效检查失败: {}", e);
                }
            });
        }
    }

    // 获取版本验证窗口
    if let Some(version_window) = app.get_webview_window("version_check") {
        // 关闭版本验证窗口
        version_window.close().map_err(|e| format!("关闭版本验证窗口失败: {}", e))?;
    }

    // 显示主程序窗口
    if let Some(main_window) = app.get_webview_window("main") {
        main_window.show().map_err(|e| format!("显示主程序窗口失败: {}", e))?;
        main_window.set_focus().map_err(|e| format!("聚焦主程序窗口失败: {}", e))?;

        // 向主程序窗口发送重新初始化事件
        // 延迟一点时间确保窗口完全显示后再发送事件
        let main_window_clone = main_window.clone();
        tokio::spawn(async move {
            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
            if let Err(e) = main_window_clone.emit("version_check_completed", ()) {
                eprintln!("发送重新初始化事件失败: {}", e);
            }
        });
    }

    Ok(())
}
