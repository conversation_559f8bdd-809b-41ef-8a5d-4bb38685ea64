{"$schema": "https://schema.tauri.app/config/2", "productName": "YAugment", "version": "2.4.1", "identifier": "com.yan.yaugment", "build": {"beforeDevCommand": "", "beforeBuildCommand": "", "frontendDist": "../src"}, "app": {"withGlobalTauri": true, "security": {"csp": null, "capabilities": [{"identifier": "main-capability", "description": "Main application capabilities", "windows": ["version_check", "main"], "permissions": ["core:default", "core:window:allow-set-size", "core:window:allow-center", "core:window:allow-set-position", "core:window:allow-set-resizable", "core:window:allow-set-maximizable", "core:window:allow-set-minimizable", "core:window:allow-set-closable", "core:window:allow-set-title", "core:window:allow-maximize", "core:window:allow-unmaximize", "core:window:allow-minimize", "core:window:allow-unminimize", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-close", "core:window:allow-set-decorations", "core:window:allow-set-always-on-top", "core:window:allow-set-content-protected", "core:window:allow-set-size-constraints", "core:window:allow-set-icon", "core:window:allow-set-skip-taskbar", "core:window:allow-set-cursor-grab", "core:window:allow-set-cursor-visible", "core:window:allow-set-cursor-icon", "core:window:allow-set-cursor-position", "core:window:allow-set-ignore-cursor-events", "core:window:allow-start-dragging"]}]}, "windows": [{"label": "version_check", "title": "YAugment 版本验证", "url": "version_check.html", "width": 450, "height": 250, "center": true, "decorations": false, "transparent": true, "resizable": false, "maximizable": false, "minimizable": false, "closable": true, "alwaysOnTop": true, "skipTaskbar": false, "visible": false, "shadow": true, "windowEffects": {"effects": ["mica"], "state": "active", "radius": 14.0}}, {"label": "main", "title": "YAugment", "url": "index.html", "width": 1100, "height": 750, "minWidth": 1100, "minHeight": 750, "center": true, "decorations": false, "transparent": true, "resizable": true, "maximizable": true, "minimizable": true, "closable": true, "alwaysOnTop": false, "skipTaskbar": false, "visible": false, "shadow": true, "windowEffects": {"effects": ["mica"], "state": "active", "radius": 28}}]}, "bundle": {"active": true, "targets": ["msi"], "icon": ["icons/icon.png", "icons/icon.icns", "icons/icon.ico"], "createUpdaterArtifacts": false, "resources": ["icons/gzh.jpg"], "publisher": "Yan", "copyright": "Copyright © 2025 Yan. All rights reserved.", "category": "DeveloperTool", "shortDescription": "YAugment - 高效开发工具", "longDescription": "YAugment 是一个功能强大的开发工具，提供网络优化、邮箱生成等多种实用功能。", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": "", "tsp": false, "wix": {"language": "zh-CN", "template": null, "fragmentPaths": [], "componentRefs": [], "featureRefs": [], "mergeRefs": [], "enableElevatedUpdateTask": false, "bannerPath": null, "dialogImagePath": null, "upgradeCode": "A7B8C9D0-E1F2-4A5B-8C9D-0E1F2A3B4C5D"}, "webviewInstallMode": {"type": "downloadBootstrapper", "silent": true}, "nsis": {"template": null, "headerImage": null, "sidebarImage": null, "installerIcon": "icons/icon.ico", "installMode": "perMachine", "languages": ["SimpChinese"], "displayLanguageSelector": false, "customLanguageFiles": {}}}}}