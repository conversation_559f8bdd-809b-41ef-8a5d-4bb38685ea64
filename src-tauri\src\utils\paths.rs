use std::path::PathBuf;
use std::env;

/// 获取应用数据目录
/// 对应原版 Python utils.py 的 get_app_data_dir 函数
/// 完全按照原版逻辑实现跨平台路径获取
///
/// Platform specific paths:
/// - Windows: %APPDATA%/YAugment (typically C:\Users\<USER>\AppData\Roaming\YAugment)
/// - macOS: ~/Library/Application Support/YAugment
/// - Linux: ~/.local/share/YAugment
pub fn get_app_data_dir() -> Result<PathBuf, String> {
    if cfg!(target_os = "windows") {
        // Windows: %APPDATA%/YAugment
        match env::var("APPDATA") {
            Ok(app_data) => {
                if app_data.is_empty() {
                    Err("APPDATA environment variable is empty".to_string())
                } else {
                    Ok(PathBuf::from(app_data).join("YAugment"))
                }
            }
            Err(_) => Err("APPDATA environment variable not found".to_string()),
        }
    } else if cfg!(target_os = "macos") {
        // macOS: ~/Library/Application Support/YAugment
        match dirs::home_dir() {
            Some(home) => Ok(home.join("Library").join("Application Support").join("YAugment")),
            None => Err("Unable to determine home directory".to_string()),
        }
    } else {
        // Linux and other Unix-like systems: ~/.local/share/YAugment
        match dirs::home_dir() {
            Some(home) => Ok(home.join(".local").join("share").join("YAugment")),
            None => Err("Unable to determine home directory".to_string()),
        }
    }
}

/// 获取配置目录
/// 对应原版 Python ConfigManager 的配置目录逻辑
/// 配置目录与应用数据目录相同
pub fn get_config_dir() -> Result<PathBuf, String> {
    get_app_data_dir()
}

/// 获取用户主目录
/// 对应原版 Python utils.py 的 get_home_dir 函数
pub fn get_home_dir() -> Result<PathBuf, String> {
    match dirs::home_dir() {
        Some(home) => Ok(home),
        None => Err("Unable to determine home directory".to_string()),
    }
}
