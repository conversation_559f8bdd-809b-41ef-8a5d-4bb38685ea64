// 全局变量
let bridge = null;
let currentPage = 'editorSelect';
let selectedEditor = '';
let resetStatusCheckInterval = null;

// 全局错误处理 - 确保所有错误都会显示toast
window.addEventListener('error', function(event) {
    // 过滤掉一些不需要显示toast的错误
    const errorMessage = event.error?.message || event.message || '';

    // 跳过某些类型的错误，避免误报
    if (errorMessage.includes('save_config failed') ||
        errorMessage.includes('save_config error') ||
        errorMessage.includes('ResizeObserver') ||
        errorMessage.includes('Non-Error promise rejection') ||
        event.filename?.includes('tauri')) {
        console.warn('Filtered global error:', errorMessage);
        return;
    }

    if (typeof showToast === 'function') {
        console.error('Global error caught:', event.error || event.message);
        showToast('操作失败', 'error', 6000);
    }
});

// 处理未捕获的Promise rejection
window.addEventListener('unhandledrejection', function(event) {
    // 过滤掉一些不需要显示toast的错误
    const errorMessage = event.reason?.message || event.reason || '';

    // 跳过某些类型的错误，避免误报
    if (errorMessage.includes('save_config failed') ||
        errorMessage.includes('save_config error') ||
        errorMessage.includes('ResizeObserver') ||
        errorMessage.includes('Non-Error promise rejection')) {
        console.warn('Filtered unhandled rejection:', errorMessage);
        event.preventDefault(); // 阻止默认的错误处理
        return;
    }

    if (typeof showToast === 'function') {
        console.error('Unhandled promise rejection:', event.reason);
        showToast('操作失败', 'error', 6000);
    }
    event.preventDefault(); // 阻止默认的错误处理
});

window.addEventListener('unhandledrejection', function(event) {
    const errorMessage = `Promise错误: ${event.reason?.message || event.reason}`;
    if (typeof showToast === 'function') {
        showToast(errorMessage, 'error', 6000);
    }
    console.error('Unhandled promise rejection:', event.reason);
});

// 包装所有异步函数调用，确保错误被捕获
function safeCall(fn, ...args) {
    try {
        const result = fn(...args);
        if (result && typeof result.catch === 'function') {
            result.catch(error => {
                // 过滤掉配置保存相关的错误，避免误报
                if (error.message && (error.message.includes('save_config') || error.message.includes('config'))) {
                    console.warn('Config-related error filtered in safeCall:', error.message);
                    return;
                }

                const errorMessage = `操作失败: ${error.message || error}`;
                showToast(errorMessage, 'error', 5000);
                console.error('Async operation failed:', error);
            });
        }
        return result;
    } catch (error) {
        // 过滤掉配置保存相关的错误，避免误报
        if (error.message && (error.message.includes('save_config') || error.message.includes('config'))) {
            console.warn('Config-related error filtered in safeCall:', error.message);
            throw error; // 重新抛出错误，但不显示toast
        }

        const errorMessage = `操作失败: ${error.message || error}`;
        showToast(errorMessage, 'error', 5000);
        console.error('Sync operation failed:', error);
        throw error;
    }
}

// 通用invoke函数，用于调用Tauri命令
async function invoke(command, args = {}) {
    if (window.__TAURI__ && window.__TAURI__.core) {
        return await window.__TAURI__.core.invoke(command, args);
    } else {
        throw new Error('Tauri API not available');
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    try {
        detectEnvironment();
        initWebChannel();
        setupEventListeners();

        // 启动版本检查和应用初始化流程
        initializeApp();
    } catch (error) {
        console.error('Initialization failed:', error);
        if (typeof showToast === 'function') {
            showToast('应用初始化失败', 'error', 6000);
        }
    }
});

// 环境检测函数
function detectEnvironment() {
    const env = {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        currentUrl: window.location.href,
        referrer: document.referrer,
        timestamp: new Date().toISOString()
    };

    return env;
}

// 初始化 Tauri Bridge
function initWebChannel() {
    console.log('开始初始化 Tauri Bridge...');

    // 检测运行环境
    detectEnvironment();

    // 检查 Tauri API 是否可用
    if (typeof window.__TAURI__ === 'undefined') {
        console.error('Tauri API 不可用');
        return;
    }

    // 创建 Tauri bridge 对象
    bridge = {
        // 配置管理
        get_config: (callback) => {
            window.__TAURI__.core.invoke('get_config').then(callback).catch(err => {
                console.error('get_config failed:', err);
                callback(null);
            });
        },
        set_config: async (key, value) => await window.__TAURI__.core.invoke('set_config_value', { keyPath: key, value }),
        save_config: (config, callback) => {
            window.__TAURI__.core.invoke('save_config', { config }).then(result => {
                callback(result);
            }).catch(err => {
                console.warn('save_config error (handled):', err);
                callback(false);
            });
        },

        // 编辑器检测
        detect_editors: async () => await window.__TAURI__.core.invoke('detect_editors'),
        select_editor: (editorType, callback) => {
            window.__TAURI__.core.invoke('select_editor', { editorType }).then(result => {
                callback(result);
            }).catch(err => {
                console.error('select_editor failed:', err);
                callback(false);
            });
        },
        set_selected_editor: async (editor) => await window.__TAURI__.core.invoke('select_editor', { editorType: editor }),
        get_editor_status: (callback) => {
            window.__TAURI__.core.invoke('get_editor_status').then(callback).catch(err => {
                console.error('get_editor_status failed:', err);
                callback({});
            });
        },
        get_selected_editor: (callback) => {
            window.__TAURI__.core.invoke('get_selected_editor').then(callback).catch(err => {
                console.error('get_selected_editor failed:', err);
                callback('');
            });
        },
        is_first_run: (callback) => {
            window.__TAURI__.core.invoke('is_first_run').then(callback).catch(err => {
                console.error('is_first_run failed:', err);
                callback(false);
            });
        },

        // 窗口控制
        minimize_window: () => window.__TAURI__.core.invoke('minimize_window'),
        toggle_maximize: () => window.__TAURI__.core.invoke('toggle_maximize'),
        close_window: () => window.__TAURI__.core.invoke('close_window'),
        start_window_drag: (x, y) => window.__TAURI__.core.invoke('start_window_drag', { x, y }),
        update_window_drag: (x, y) => window.__TAURI__.core.invoke('update_window_drag', { x, y }),
        end_window_drag: () => window.__TAURI__.core.invoke('end_window_drag'),

        // 日志
        log_message: async (message) => await window.__TAURI__.core.invoke('log_message', { message }),

        // 邮箱功能
        generate_email: (callback) => {
            window.__TAURI__.core.invoke('generate_email').then(result => {
                console.log('generate_email result:', result);
                callback(result);
            }).catch(err => {
                console.error('generate_email failed:', err);
                callback(null); // 使用null而不是空字符串，更明确表示失败
            });
        },
        get_verification_code: () => window.__TAURI__.core.invoke('get_verification_code'),
        get_verification_status: (callback) => {
            if (callback) {
                // 回调模式
                window.__TAURI__.core.invoke('get_verification_status').then(callback).catch(err => {
                    console.error('get_verification_status failed:', err);
                    callback('{}');
                });
            } else {
                // 直接返回Promise模式
                return window.__TAURI__.core.invoke('get_verification_status');
            }
        },
        test_email_connection: () => window.__TAURI__.core.invoke('test_email_connection'),
        start_verification_code: async () => await window.__TAURI__.core.invoke('start_verification_code'),
        stop_verification_code: async () => await window.__TAURI__.core.invoke('stop_verification_code'),

        // 账户管理
        get_account_info: async () => await window.__TAURI__.core.invoke('get_account_info'),
        set_account_info: async (info) => await window.__TAURI__.core.invoke('set_account_info', { info }),

        // 重置功能
        reset_augment: () => window.__TAURI__.core.invoke('reset_augment'),
        get_reset_status: (callback) => {
            window.__TAURI__.core.invoke('get_reset_status').then(callback).catch(err => {
                console.error('get_reset_status failed:', err);
                callback('{}');
            });
        },
        reset_all: async () => await window.__TAURI__.core.invoke('reset_all'),

        // 版本检查
        check_version: async () => await window.__TAURI__.core.invoke('check_version'),
        agree_disclaimer: async () => await window.__TAURI__.core.invoke('agree_disclaimer'),
        verify_code: async (code) => await window.__TAURI__.core.invoke('verify_code', { code }),
        verify_vip_qq: async (qq_number) => await window.__TAURI__.core.invoke('verify_vip_qq', { qq_number }),
        get_about_info: async () => await window.__TAURI__.core.invoke('get_about_info'),
        get_config_help_url: () => window.__TAURI__.core.invoke('get_config_help_url'),
        get_token_help_url: () => window.__TAURI__.core.invoke('get_token_help_url'),
        get_version_info: async () => await window.__TAURI__.core.invoke('get_version_info'),

        // 账号管理
        query_augment_account: async (token) => await window.__TAURI__.core.invoke('query_augment_account', { token }),

        // 工具相关
        get_cursor_tools: async () => await window.__TAURI__.core.invoke('get_cursor_tools'),
        get_vscode_tools: async () => await window.__TAURI__.core.invoke('get_vscode_tools'),
        execute_cursor_tool: async (tool, params) => await window.__TAURI__.core.invoke('execute_cursor_tool', { tool, params }),
        execute_vscode_tool: async (tool, params) => await window.__TAURI__.core.invoke('execute_vscode_tool', { tool, params }),

        // 剪贴板
        copy_text_to_clipboard: async (text) => await window.__TAURI__.core.invoke('copy_text_to_clipboard', { text }),

        // 打开外部链接
        open_external_url: async (url) => await window.__TAURI__.core.invoke('open_url', { url })
    };

    window.bridge = bridge;  // 设置为全局变量

    console.log('Tauri bridge 初始化成功');
    console.log('Bridge 对象:', bridge);

    // 延迟设置Tauri事件监听器，确保Tauri完全初始化
    setTimeout(() => {
        setupTauriEventListeners();
    }, 100);

    // 初始化应用
    initializeApp();
}

// 设置Tauri事件监听器
function setupTauriEventListeners() {
    // 检查Tauri API是否可用 (Tauri 2.0)
    if (!window.__TAURI__ || !window.__TAURI__.event || !window.__TAURI__.event.listen) {
        console.error('Tauri 2.0 event API 不可用，跳过事件监听器设置');
        return;
    }

    // 监听来自Rust后端的事件 (Tauri 2.0 API)
    window.__TAURI__.event.listen('show_toast', (event) => {
        const { message, type } = event.payload;
        showToast(message, type);
    });

    window.__TAURI__.event.listen('update_progress', (event) => {
        const { taskId, progress, message } = event.payload;
        updateProgress(taskId, progress, message);
    });

    window.__TAURI__.event.listen('update_editor_status', (event) => {
        const { status } = event.payload;
        updateEditorStatus(status);
    });

    window.__TAURI__.event.listen('show_email_result', (event) => {
        const { email } = event.payload;
        showEmailResultDirect(email);
    });

    window.__TAURI__.event.listen('show_verification_code', (event) => {
        const { code } = event.payload;
        console.log('收到验证码事件:', code);

        // 停止轮询
        if (verificationStatusCheckInterval) {
            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;
        }

        // 显示验证码
        showVerificationCodeInWorkflow(code);
        updateWorkspaceStatus('success', '验证码获取成功');
        updateWorkflowProgress(2, true); // 标记第二步完成
        updateVerificationDetailedStatus('success', '验证码获取成功', `验证码: ${code}`);

        // 强制显示成功toast - 确保在所有环境下都能显示
        console.log('准备显示验证码获取成功toast:', code);
        const toastResult = showToast(`验证码获取成功: ${code}`, 'success', 6000);
        console.log('showToast 返回结果:', toastResult);

        // 恢复按钮状态
        setTimeout(() => {
            updateWorkflowButtonState('getCodeBtn', 'ready', '获取验证码');

            // 重新启用生成邮箱按钮
            const generateEmailBtn = document.getElementById('generateEmailBtn');
            if (generateEmailBtn) {
                generateEmailBtn.disabled = false;
                console.log('验证码获取成功：重新启用生成邮箱按钮');
            }
        }, 100);
    });

    window.__TAURI__.event.listen('verification_complete', (event) => {
        const { success, message } = event.payload;
        console.log('收到验证码完成事件:', success, message);

        // 停止轮询
        if (verificationStatusCheckInterval) {
            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;
        }

        // verification_complete 信号只处理失败情况
        // 成功情况由 show_verification_code 信号处理
        if (success) {
            updateVerificationStatus('success', message);
            // 更新实时状态显示为绿色
            updateWorkspaceStatus('success', '验证码获取成功');
            // 成功时不显示toast，等待show_verification_code信号
        } else {
            updateVerificationStatus('error', message);
            // 失败时显示toast并恢复按钮状态 - 这是唯一的失败toast
            showToast(message || '验证码获取失败', 'error');
            updateWorkspaceStatus('error', message || '验证码获取失败');
            // 恢复按钮状态
            setTimeout(() => {
                updateWorkflowButtonState('getCodeBtn', 'ready', '获取验证码');

                // 重新启用生成邮箱按钮
                const generateEmailBtn = document.getElementById('generateEmailBtn');
                if (generateEmailBtn) {
                    generateEmailBtn.disabled = false;
                    console.log('验证码获取失败：重新启用生成邮箱按钮');
                }
            }, 100);
        }
    });

    window.__TAURI__.event.listen('verification_progress', (event) => {
        const { message, progress } = event.payload;
        updateVerificationDetailedStatus('running', message, '');
        // 只更新进度条，不显示数字进度格式（与原版一致）
        const progressBar = document.getElementById('verificationProgressBar');
        if (progressBar && progress !== undefined) {
            const percentage = progress * 100;
            progressBar.style.width = `${percentage}%`;
        }
    });

    window.__TAURI__.event.listen('reset_complete', (event) => {
        const { success, message } = event.payload;
        // 停止轮询
        if (resetStatusCheckInterval) {
            clearInterval(resetStatusCheckInterval);
            resetStatusCheckInterval = null;
        }
        // 直接处理重置完成
        handleResetComplete(success, message);
    });

    window.__TAURI__.event.listen('config_updated', (event) => {
        const { config } = event.payload;
        handleConfigUpdate(config);
    });
}

// 设置事件监听器
function setupEventListeners() {
    // 优化的鼠标跟踪效果，使用节流避免过度触发
    let mouseTrackingThrottle = false;
    document.querySelectorAll('.feature-card').forEach(card => {
        card.addEventListener('mousemove', (e) => {
            if (mouseTrackingThrottle) return;
            mouseTrackingThrottle = true;

            requestAnimationFrame(() => {
                const hoverEffect = card.querySelector('.card-hover-effect');
                if (hoverEffect) {
                    const rect = card.getBoundingClientRect();
                    const x = ((e.clientX - rect.left) / rect.width) * 100;
                    const y = ((e.clientY - rect.top) / rect.height) * 100;
                    hoverEffect.style.setProperty('--mouse-x', `${x}%`);
                    hoverEffect.style.setProperty('--mouse-y', `${y}%`);
                }
                mouseTrackingThrottle = false;
            });
        });
    });

    // 设置标题栏事件（双击最大化等）
    setupTitlebarEvents();
}

// 设置标题栏事件（双击最大化等）
function setupTitlebarEvents() {
    const titlebar = document.getElementById('titlebar');
    const windowControls = titlebar.querySelector('.window-controls');

    // 双击标题栏最大化/还原窗口
    titlebar.addEventListener('dblclick', (e) => {
        // 如果双击的是窗口控制按钮区域，不触发最大化
        if (windowControls.contains(e.target) || e.target.closest('.window-controls')) {
            return;
        }
        e.preventDefault();
        e.stopPropagation();
        toggleMaximize();
    });

    // 阻止标题栏上的文本选择
    titlebar.addEventListener('selectstart', (e) => {
        e.preventDefault();
    });

    // 阻止标题栏上的右键菜单
    titlebar.addEventListener('contextmenu', (e) => {
        e.preventDefault();
    });
}

// 初始化应用
async function initializeApp() {
    try {
        console.log('开始初始化应用...');

        // 确保最小加载时间，避免闪烁
        const minLoadingTime = 1200; // 1.2秒最小加载时间
        const startTime = Date.now();

        // 显示加载页面
        showPage('loadingPage');

        // 检查 bridge 是否可用
        if (!bridge) {
            console.error('Bridge 未初始化');
            setTimeout(() => showPage('editorSelectPage'), minLoadingTime);
            return;
        }

        // 版本检查已在独立的version_check.html中完成
        // 主程序直接开始初始化流程
        console.log('✅ 版本检查已通过，开始主程序初始化');
        continueAppInitialization(startTime, minLoadingTime);

    } catch (error) {
        console.error('应用初始化失败:', error);
        // 出错时显示编辑器选择页面
        setTimeout(() => {
            showPage('editorSelectPage');
        }, 1200);
    }
}



// 继续应用初始化流程（版本检查通过后）
function continueAppInitialization(startTime, minLoadingTime) {
    bridge.is_first_run((isFirstRun) => {
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

        setTimeout(() => {
            if (!isFirstRun) {
                // 不是首次运行，直接显示主页面
                bridge.get_selected_editor((editor) => {
                    selectedEditor = editor;
                    console.log('已配置编辑器:', selectedEditor);

                    // 获取配置并更新显示
                    bridge.get_config((config) => {
                        console.log('获取到配置:', config);

                        // 更新浮动卡片信息
                        updateFloatingCards(config);

                        // 更新编辑器显示
                        updateEditorDisplay();

                        // 初始化关于页面
                        initializeAboutPage().catch(error => {
                            console.error('关于页面初始化失败:', error);
                        });

                        hideLoadingPageAndShow('main');
                    });
                });
            } else {
                // 首次运行，显示编辑器选择页面
                console.log('首次运行，显示编辑器选择页面');
                hideLoadingPageAndShow('editorSelect');
            }
        }, remainingTime);
    });
}

// 更新浮动卡片信息
function updateFloatingCards(config) {
    const domainElement = document.getElementById('floatingDomain');
    const modeElement = document.getElementById('floatingMode');

    if (config && config.email) {
        const emailConfig = config.email;

        // 更新域名
        if (domainElement) {
            domainElement.textContent = emailConfig.domain || '未配置';
        }

        // 更新模式
        if (modeElement) {
            const mode = emailConfig.use_temp_mail ? '临时邮箱' : 'IMAP';
            modeElement.textContent = mode;
        }
    } else {
        // 如果没有配置，显示默认值
        if (domainElement) {
            domainElement.textContent = '未配置';
        }
        if (modeElement) {
            modeElement.textContent = 'IMAP';
        }
    }
}

// 初始化关于页面信息
async function initializeAboutPage() {
    if (!bridge || !bridge.get_about_info) {
        console.warn('Bridge或get_about_info方法不可用');
        return;
    }

    try {
        console.log('获取关于页面信息...');
        const aboutInfo = await bridge.get_about_info();

        if (aboutInfo) {
            console.log('关于信息获取成功:', aboutInfo);
            updateAboutPageInfo(aboutInfo);
        } else {
            console.warn('关于信息为空');
        }
    } catch (error) {
        console.error('获取关于信息失败:', error);
    }
}

// 更新关于页面信息
function updateAboutPageInfo(aboutInfo) {
    console.log('更新关于页面信息:', aboutInfo);

    // 更新应用名称和版本
    const appNameElement = document.querySelector('.app-name');
    if (appNameElement) {
        // 使用固定的应用名称和版本，或从aboutInfo获取
        const appName = aboutInfo.app_name || 'YAugment';
        const appVersion = aboutInfo.app_version || '1.0.0';
        appNameElement.textContent = `${appName} v${appVersion}`;
        console.log('应用名称已更新:', appNameElement.textContent);
    }

    // 更新版权信息
    const copyrightElement = document.querySelector('.copyright-item .info-value');
    if (copyrightElement && aboutInfo.copyright) {
        copyrightElement.textContent = aboutInfo.copyright;
        console.log('版权信息已更新:', aboutInfo.copyright);
    }

    // 更新邮箱信息
    const emailElement = document.querySelector('.email-item .info-value');
    if (emailElement && aboutInfo.email) {
        emailElement.textContent = aboutInfo.email;
        console.log('邮箱信息已更新:', aboutInfo.email);
    }

    // 更新QQ群信息
    const qqElement = document.querySelector('.qq-item .info-value');
    if (qqElement && aboutInfo.qq_group_1) {
        let qqText = aboutInfo.qq_group_1;

        // 如果有QQ群2前缀，组合显示
        if (aboutInfo.qq_group_2_prefix && aboutInfo.qq_group_2 && aboutInfo.qq_group_2_suffix) {
            qqText += ` <span class="group-note">${aboutInfo.qq_group_2_prefix}${aboutInfo.qq_group_2}${aboutInfo.qq_group_2_suffix}</span>`;
        }

        qqElement.innerHTML = qqText;
        console.log('QQ群信息已更新:', qqText);
    }
}

// 页面显示函数
function showPage(pageId) {
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => page.classList.add('hidden'));

    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        targetPage.classList.remove('hidden');
        currentPage = pageId.replace('Page', '');
        console.log('显示页面:', pageId);
    }
}

// 隐藏加载页面并显示目标页面（带平滑过渡）
function hideLoadingPageAndShow(targetPage) {
    const loadingPage = document.getElementById('loadingPage');
    const loadingContainer = loadingPage.querySelector('.loading-container');

    console.log('切换页面:', targetPage);

    // 开始加载页面淡出动画
    if (loadingContainer) {
        loadingContainer.classList.add('fade-out');
    }

    // 等待淡出动画完成后切换页面
    setTimeout(() => {
        loadingPage.classList.add('hidden');

        if (targetPage === 'main') {
            showMainPageDirectly();
        } else if (targetPage === 'editorSelect') {
            showEditorSelectPage();
        }
    }, 800); // 等待淡出动画完成
}

// 显示编辑器选择页面
function showEditorSelectPage() {
    const editorSelectPage = document.getElementById('editorSelectPage');
    const mainPage = document.getElementById('mainPage');

    // 确保主页面隐藏
    mainPage.classList.add('hidden');

    // 显示编辑器选择页面并添加淡入动画
    editorSelectPage.classList.remove('hidden');
    editorSelectPage.classList.add('fade-in');

    // 更新当前页面状态
    currentPage = 'editorSelect';

    // 动画完成后移除动画类
    setTimeout(() => {
        editorSelectPage.classList.remove('fade-in');
    }, 800);

    // 初始化关于页面信息（确保首次运行时也能获取正确的关于信息）
    initializeAboutPage();

    // 获取编辑器状态
    bridge.get_editor_status((status) => {
        updateEditorStatus(status);
    });
}

// 直接显示主页面（带淡入动画）
function showMainPageDirectly() {
    const editorSelectPage = document.getElementById('editorSelectPage');
    const mainPage = document.getElementById('mainPage');

    // 确保编辑器选择页面隐藏
    editorSelectPage.classList.add('hidden');

    // 显示主页面并添加淡入动画
    mainPage.classList.remove('hidden');
    mainPage.classList.add('fade-in');

    // 更新当前页面状态
    currentPage = 'main';

    // 更新显示的编辑器
    updateEditorDisplay();

    // 缩短动画时间并立即触发页面动画
    setTimeout(() => {
        mainPage.classList.remove('fade-in');
        // 立即触发动画
        initPageAnimations();
    }, 400);
}

// 更新编辑器状态
function updateEditorStatus(status) {
    if (!status || typeof status !== 'object') {
        console.warn('updateEditorStatus: 无效的状态对象', status);
        return;
    }

    // VS Code状态
    const vscodeStatus = document.getElementById('vscodeStatus');
    if (vscodeStatus) {
        try {
            const vscodeInstalled = status.vscode?.installed || false;
            const indicator = vscodeStatus.querySelector('.status-indicator');
            const text = vscodeStatus.querySelector('.status-text');

            if (indicator && text) {
                indicator.className = `status-indicator ${vscodeInstalled ? 'installed' : 'not-installed'}`;
                text.className = `status-text ${vscodeInstalled ? 'installed' : 'not-installed'}`;
                text.textContent = vscodeInstalled ? '已安装' : '未安装';

                // 检查文字滚动
                checkAndEnableTextScrolling(text);
            }
        } catch (error) {
            console.error('更新VS Code状态时出错:', error);
        }
    }

    // Cursor状态
    const cursorStatus = document.getElementById('cursorStatus');
    if (cursorStatus) {
        try {
            const cursorInstalled = status.cursor?.installed || false;
            const indicator = cursorStatus.querySelector('.status-indicator');
            const text = cursorStatus.querySelector('.status-text');

            if (indicator && text) {
                indicator.className = `status-indicator ${cursorInstalled ? 'installed' : 'not-installed'}`;
                text.className = `status-text ${cursorInstalled ? 'installed' : 'not-installed'}`;
                text.textContent = cursorInstalled ? '已安装' : '未安装';

                // 检查文字滚动
                checkAndEnableTextScrolling(text);
            }
        } catch (error) {
            console.error('更新Cursor状态时出错:', error);
        }
    }
}

// 选择编辑器
function selectEditor(editorType) {
    try {
        // 添加选择动画效果
        const selectedCard = document.getElementById(`${editorType}Card`);
        if (selectedCard) {
            selectedCard.style.transform = 'scale(0.95)';
            selectedCard.style.opacity = '0.7';

            setTimeout(() => {
                selectedCard.style.transform = '';
                selectedCard.style.opacity = '';
            }, 150);
        }

        if (!bridge || !bridge.set_selected_editor) {
            throw new Error('Bridge未初始化或set_selected_editor方法不可用');
        }

        // 使用async/await而不是回调
        bridge.set_selected_editor(editorType).then((success) => {
            try {
                console.log('编辑器选择结果:', success, '类型:', typeof success);
                if (success) {
                    selectedEditor = editorType;
                    // 移除toast通知，直接显示视觉反馈

                    // 添加成功选择的视觉反馈
                    if (selectedCard) {
                        selectedCard.style.borderColor = 'rgba(34, 197, 94, 0.5)';
                        selectedCard.style.boxShadow = '0 0 30px rgba(34, 197, 94, 0.3)';
                    }

                    setTimeout(() => {
                        showMainPage();
                    }, 800);
                } else {
                    showToast('选择编辑器失败', 'error');

                    // 添加失败的视觉反馈
                    if (selectedCard) {
                        selectedCard.style.borderColor = 'rgba(239, 68, 68, 0.5)';
                        selectedCard.style.boxShadow = '0 0 30px rgba(239, 68, 68, 0.3)';

                        setTimeout(() => {
                            selectedCard.style.borderColor = '';
                            selectedCard.style.boxShadow = '';
                        }, 2000);
                    }
                }
            } catch (error) {
                showToast(`选择编辑器时发生错误: ${error.message}`, 'error');
                console.error('Error in selectEditor callback:', error);
            }
        });
    } catch (error) {
        showToast(`选择编辑器失败: ${error.message}`, 'error');
        console.error('Error in selectEditor:', error);
    }
}

// 显示主页面（带动画，用于编辑器选择后）
function showMainPage() {
    const editorSelectPage = document.getElementById('editorSelectPage');
    const mainPage = document.getElementById('mainPage');
    const editorSelectContainer = editorSelectPage.querySelector('.editor-select-container');

    // 添加出场动画类
    if (editorSelectContainer) {
        editorSelectContainer.classList.add('fade-out');
    }

    // 缩短等待时间，让主页更快出现
    setTimeout(() => {
        editorSelectPage.classList.add('hidden');
        mainPage.classList.remove('hidden');

        // 更新当前页面状态
        currentPage = 'main';

        // 更新显示的编辑器
        updateEditorDisplay();

        // 立即淡入主页面并触发动画
        mainPage.style.opacity = '1';
        initPageAnimations();
    }, 400); // 缩短等待时间
}

// 窗口控制
function minimizeWindow() {
    bridge.minimize_window();
}

function toggleMaximize() {
    bridge.toggle_maximize();
}

function closeWindow() {
    bridge.close_window();
}

// 更新编辑器显示
function updateEditorDisplay() {
    // 更新主页面的编辑器显示
    const selectedEditorEl = document.getElementById('selectedEditor');
    if (selectedEditorEl) {
        selectedEditorEl.textContent = `当前选择的编辑器: ${selectedEditor.toUpperCase()}`;
    }

    // 更新重置区域的编辑器名称
    const currentEditorNameEl = document.getElementById('currentEditorName');
    if (currentEditorNameEl && selectedEditor) {
        currentEditorNameEl.textContent = selectedEditor.toUpperCase();
    }
}

// Toast提示系统 - 符合项目主题风格
let toastHistory = [];
let toastIdCounter = 0;

function showToast(message, type = 'info', duration = 4000) {
    console.log('showToast 被调用:', { message, type, duration });

    const container = document.getElementById('toastContainer');
    if (!container) {
        console.error('toastContainer 元素不存在!');
        return null;
    }

    console.log('toastContainer 找到，准备创建 toast');

    const toast = document.createElement('div');
    const toastId = ++toastIdCounter;

    toast.className = `toast ${type}`;
    toast.setAttribute('data-toast-id', toastId);

    const icons = {
        'success': '✓',
        'error': '✗',
        'info': 'ℹ',
        'warning': '⚠'
    };

    const icon = icons[type] || 'ℹ';

    toast.innerHTML = `
        <span class="toast-icon">${icon}</span>
        <span class="toast-message">${message}</span>
    `;

    // 记录到历史（用于错误追踪）
    const historyItem = {
        id: toastId,
        message: message,
        type: type,
        timestamp: new Date(),
        read: false
    };
    toastHistory.unshift(historyItem);

    // 确保toastHistory是数组
    if (!Array.isArray(toastHistory)) {
        toastHistory = [];
    }

    // 限制历史记录数量 - 添加安全检查
    if (toastHistory && toastHistory.length > 50) {
        toastHistory = toastHistory.slice(0, 50);
    }

    // 先添加到容器中，这样可以获取实际尺寸
    container.appendChild(toast);

    // 计算toast的位置（基于现有toast的数量和实际高度）
    const existingToasts = container.children.length - 1; // 减去刚添加的这个
    let totalHeight = 0;

    // 计算前面所有toast的总高度
    for (let i = 0; i < existingToasts; i++) {
        const existingToast = container.children[i];
        if (existingToast !== toast) {
            totalHeight += existingToast.offsetHeight + 12; // 12px gap
        }
    }

    // 设置初始位置
    toast.style.top = `${totalHeight}px`;

    // toast已经在上面添加到容器中了，这里不需要重复添加

    // 触发入场动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // 自动移除
    if (duration > 0) {
        setTimeout(() => {
            closeToast(toastId);
        }, duration);
    }

    // 如果是错误类型，记录到控制台
    if (type === 'error') {
        console.error(`YAugment Error: ${message}`);
    }

    // 特别处理验证码成功消息
    if (type === 'success' && message.includes('验证码')) {
        console.log('验证码成功 toast 已创建并添加到容器');
    }

    console.log('showToast 完成，返回 toastId:', toastId);
    return toastId;
}

function closeToast(toastId) {
    const toast = document.querySelector(`[data-toast-id="${toastId}"]`);
    if (toast) {
        const container = toast.parentNode;

        // 开始退场动画
        toast.classList.remove('show');
        toast.classList.add('hide');

        // 在退场动画完成后重新排列剩余的toast
        setTimeout(() => {
            if (toast.parentNode) {
                // 获取当前要移除的toast的位置信息
                const currentTop = parseInt(toast.style.top) || 0;

                // 移除当前toast
                toast.parentNode.removeChild(toast);

                // 重新计算并更新所有剩余toast的位置
                updateToastPositions(container);
            }
        }, 400); // 与CSS中的toastSlideOut动画时间一致
    }
}

// 新增函数：更新所有toast的位置
function updateToastPositions(container) {
    const toasts = Array.from(container.children);
    let currentTop = 0;

    toasts.forEach((toast) => {
        toast.style.top = `${currentTop}px`;
        // 使用实际的toast高度而不是估算值
        currentTop += toast.offsetHeight + 12; // 12px gap
    });
}

// 对话框控制
function showDialog(title, content, footer = null) {
    const overlay = document.getElementById('dialogOverlay');
    const dialog = document.getElementById('dialog');
    const dialogTitle = document.getElementById('dialogTitle');
    const dialogContent = document.getElementById('dialogContent');
    const dialogHelp = document.getElementById('dialogHelp');

    if (!overlay || !dialog || !dialogTitle || !dialogContent) {
        console.error('对话框元素不存在');
        return;
    }

    dialogTitle.textContent = title;
    dialogContent.innerHTML = content;

    // 只在设置对话框中显示帮助按钮
    if (dialogHelp) {
        if (title === '设置') {
            dialogHelp.style.display = 'flex';
        } else {
            dialogHelp.style.display = 'none';
        }
    }

    // 处理底部按钮区域
    let existingFooter = dialog.querySelector('.dialog-footer');
    if (existingFooter) {
        existingFooter.remove();
    }

    if (footer) {
        dialog.insertAdjacentHTML('beforeend', footer);
    }

    // 禁用背景页面滚动
    document.body.style.overflow = 'hidden';

    // 清除可能存在的hiding类
    overlay.classList.remove('hiding');

    // 显示对话框
    overlay.style.display = 'flex';

    // 强制重绘后添加show类，确保动画正常播放
    requestAnimationFrame(() => {
        overlay.classList.add('show');
    });
}

function closeDialog(event) {
    // 如果没有event参数，或者点击的是overlay背景，则关闭对话框
    if (!event || event.target.id === 'dialogOverlay' || event.target.classList.contains('dialog-close')) {
        const overlay = document.getElementById('dialogOverlay');
        if (!overlay) return;

        // 恢复背景页面滚动
        document.body.style.overflow = '';

        // 添加hiding类开始关闭动画
        overlay.classList.add('hiding');
        overlay.classList.remove('show');

        // 等待动画完成后隐藏对话框
        setTimeout(() => {
            overlay.style.display = 'none';
            overlay.classList.remove('hiding');
        }, 300); // 与CSS动画时间保持一致
    }
}

// ===== 版本检查已在独立的version_check.html中完成，这里不再需要相关函数 =====
                    min-width: 400px;
                    max-width: 90vw;
                    width: auto;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
                    cursor: move;
                ">
                    <!-- 内容区域 -->
                    <div style="padding: 25px; display: flex; flex-direction: column; gap: 20px;">
                        <!-- 标题 -->
                        <div style="
                            color: #FFFFFF;
                            font-weight: bold;
                            font-size: 18px;
                            font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                        ">${title}</div>

                        <!-- 内容文本浏览器 -->
                        <div style="
                            background-color: #121317;
                            color: #9CA2AE;
                            border: none;
                            font-size: 14px;
                            font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                            max-height: 60vh;
                            overflow-y: auto;
                            padding: 0;
                            margin: 0;
                            line-height: 1.6;
                            cursor: default;
                            user-select: text;
                            -webkit-user-select: text;
                            -moz-user-select: text;
                            -ms-user-select: text;
                            white-space: pre-wrap;
                            word-wrap: break-word;
                        " onmousedown="event.stopPropagation()">${content}</div>

                        <!-- 分隔线 -->
                        <div style="
                            height: 1px;
                            background-color: #2A2E36;
                            margin: 0;
                        "></div>

                        <!-- 按钮区域 -->
                        <div style="
                            display: flex;
                            gap: 10px;
                            justify-content: flex-end;
                        " onmousedown="event.stopPropagation()">
                            <!-- 取消按钮 -->
                            <button class="disclaimer-dialog-btn secondary" onclick="closeDisclaimerDialog(false)" style="
                                background-color: #1A1D23;
                                color: #9CA2AE;
                                border: 1px solid #2A2E36;
                                border-radius: 6px;
                                padding: 8px 16px;
                                min-width: 80px;
                                font-size: 14px;
                                font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">${cancelText}</button>

                            <!-- 同意按钮 -->
                            <button class="disclaimer-dialog-btn primary" onclick="closeDisclaimerDialog(true)" style="
                                background-color: #2B9D7C;
                                color: white;
                                border: none;
                                border-radius: 6px;
                                padding: 8px 16px;
                                font-weight: bold;
                                min-width: 80px;
                                font-size: 14px;
                                font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">${agreeText}</button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', dialogHTML);

        // 添加按钮悬停效果
        const secondaryBtn = document.querySelector('.disclaimer-dialog-btn.secondary');
        const primaryBtn = document.querySelector('.disclaimer-dialog-btn.primary');

        // 次要按钮悬停效果 - 按照原版Python Theme.BUTTON_SECONDARY_STYLE
        secondaryBtn.addEventListener('mouseenter', () => {
            secondaryBtn.style.backgroundColor = '#252830'; // Theme.HOVER
            secondaryBtn.style.color = '#FFFFFF';
        });
        secondaryBtn.addEventListener('mouseleave', () => {
            secondaryBtn.style.backgroundColor = '#1A1D23';
            secondaryBtn.style.color = '#9CA2AE';
        });

        // 主要按钮悬停效果 - 按照原版Python Theme.BUTTON_PRIMARY_STYLE
        primaryBtn.addEventListener('mouseenter', () => {
            primaryBtn.style.backgroundColor = '#34B892'; // Theme.ACCENT_HOVER
        });
        primaryBtn.addEventListener('mouseleave', () => {
            primaryBtn.style.backgroundColor = '#2B9D7C'; // Theme.ACCENT
        });

        // 主要按钮按下效果
        primaryBtn.addEventListener('mousedown', () => {
            primaryBtn.style.backgroundColor = '#24856A'; // Theme.ACCENT_PRESSED
        });
        primaryBtn.addEventListener('mouseup', () => {
            primaryBtn.style.backgroundColor = '#34B892'; // 恢复悬停状态
        });

        // 添加拖动功能
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
        const dialog = document.getElementById('disclaimerDialog');

        dialog.addEventListener('mousedown', (e) => {
            isDragging = true;
            const rect = dialog.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
            dialog.style.cursor = 'grabbing';
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const x = e.clientX - dragOffset.x;
                const y = e.clientY - dragOffset.y;
                dialog.style.transform = `translate(${x - window.innerWidth/2 + 250}px, ${y - window.innerHeight/2}px)`;
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
            dialog.style.cursor = 'move';
        });

        // 存储resolve函数供按钮调用
        window.disclaimerDialogResolve = resolve;

        // 显示对话框
        const overlay = document.getElementById('disclaimerDialogOverlay');
        setTimeout(() => {
            overlay.style.opacity = '1';
        }, 10);

        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭免责声明对话框
 * @param {boolean} agreed - 用户是否同意
 */
function closeDisclaimerDialog(agreed) {
    const overlay = document.getElementById('disclaimerDialogOverlay');
    if (!overlay) return;

    // 恢复背景滚动
    document.body.style.overflow = '';

    // 关闭动画
    overlay.style.opacity = '0';

    setTimeout(() => {
        overlay.remove();
        if (window.disclaimerDialogResolve) {
            window.disclaimerDialogResolve(agreed);
            delete window.disclaimerDialogResolve;
        }
    }, 300);
}

/**
 * 显示验证对话框
 * 对应原版Python VerificationDialog类
 * @param {string} mode - 验证模式: "code_verification" 或 "vip_qq_verification"
 * @param {Object} config - 验证配置
 * @returns {Promise<Object>} - 验证结果
 */
function showVerificationDialog(mode = "code_verification", config = {}) {
    return new Promise((resolve) => {
        const isCodeMode = mode === "code_verification";
        const statusText = isCodeMode ? "请输入6位验证码" : "请扫描二维码登录QQ";

        // 创建验证码输入框
        let codeInputHTML = '';
        if (isCodeMode) {
            for (let i = 0; i < 6; i++) {
                codeInputHTML += `<input type="text" class="verification-code-digit" maxlength="1" data-index="${i}" oninput="handleVerificationCodeInput(this)" onkeydown="handleVerificationCodeKeydown(event, this)">`;
            }
        }

        // 创建对话框HTML
        const dialogHTML = `
            <div class="verification-dialog-overlay" id="verificationDialogOverlay">
                <div class="verification-dialog">
                    <div class="verification-dialog-content">
                        <div class="verification-dialog-main">
                            <div class="verification-dialog-left">
                                <div class="verification-dialog-status" id="verificationStatus">${statusText}</div>
                                ${isCodeMode ? `<div class="verification-code-input">${codeInputHTML}</div>` : ''}
                            </div>
                            ${!isCodeMode ? `
                            <div class="verification-dialog-right">
                                <div class="verification-qr-code" id="verificationQRCode">
                                    二维码加载中...
                                </div>
                                <div class="verification-qr-status" id="verificationQRStatus">
                                    请使用手机QQ扫描
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="verification-dialog-actions">
                        <button class="verification-dialog-btn secondary" onclick="closeVerificationDialog(null)">取消</button>
                        ${isCodeMode ?
                            '<button class="verification-dialog-btn secondary" onclick="switchVerificationMode(\'vip_qq_verification\')">我是赞赏用户</button>' :
                            '<button class="verification-dialog-btn secondary" onclick="switchVerificationMode(\'code_verification\')">验证码验证</button>'
                        }
                        <button class="verification-dialog-btn" id="verificationSubmitBtn" onclick="submitVerification()" ${isCodeMode ? 'disabled' : ''}>
                            ${isCodeMode ? '验证' : '确认'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', dialogHTML);

        // 存储resolve函数和配置
        window.verificationDialogResolve = resolve;
        window.verificationDialogMode = mode;
        window.verificationDialogConfig = config;

        // 显示对话框
        const overlay = document.getElementById('verificationDialogOverlay');
        setTimeout(() => {
            overlay.classList.add('show');
        }, 10);

        // 禁用背景滚动
        document.body.style.overflow = 'hidden';

        // 如果是二维码模式，加载二维码
        if (!isCodeMode) {
            loadVerificationQRCode();
        } else {
            // 如果是验证码模式，聚焦第一个输入框
            setTimeout(() => {
                const firstInput = document.querySelector('.verification-code-digit[data-index="0"]');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 100);
        }
    });
}

/**
 * 处理验证码输入
 * @param {HTMLInputElement} input - 输入框元素
 */
function handleVerificationCodeInput(input) {
    const value = input.value.toUpperCase();
    input.value = value;

    // 自动跳转到下一个输入框
    if (value && input.dataset.index < 5) {
        const nextInput = document.querySelector(`[data-index="${parseInt(input.dataset.index) + 1}"]`);
        if (nextInput) {
            nextInput.focus();
        }
    }

    // 检查是否所有输入框都已填写
    updateVerificationSubmitButton();
}

/**
 * 处理验证码输入框键盘事件
 * @param {KeyboardEvent} event - 键盘事件
 * @param {HTMLInputElement} input - 输入框元素
 */
function handleVerificationCodeKeydown(event, input) {
    // 退格键处理
    if (event.key === 'Backspace' && !input.value && input.dataset.index > 0) {
        const prevInput = document.querySelector(`[data-index="${parseInt(input.dataset.index) - 1}"]`);
        if (prevInput) {
            prevInput.focus();
        }
    }
    // 回车键提交
    else if (event.key === 'Enter') {
        submitVerification();
    }
}

/**
 * 更新验证提交按钮状态
 */
function updateVerificationSubmitButton() {
    const inputs = document.querySelectorAll('.verification-code-digit');
    const submitBtn = document.getElementById('verificationSubmitBtn');

    if (!submitBtn) return;

    const allFilled = Array.from(inputs).every(input => input.value.trim() !== '');
    submitBtn.disabled = !allFilled;
}

/**
 * 提交验证
 */
async function submitVerification() {
    const mode = window.verificationDialogMode;
    const submitBtn = document.getElementById('verificationSubmitBtn');

    if (!submitBtn || submitBtn.disabled) return;

    // 禁用按钮防止重复提交
    submitBtn.disabled = true;
    submitBtn.textContent = '验证中...';

    try {
        let result = null;

        if (mode === "code_verification") {
            // 获取验证码
            const inputs = document.querySelectorAll('.verification-code-digit');
            const code = Array.from(inputs).map(input => input.value).join('');

            // 调用后端验证
            result = await invoke('verify_code', { code });
        } else if (mode === "vip_qq_verification") {
            // VIP QQ验证逻辑
            result = await invoke('verify_vip_qq');
        }

        if (result && result.success) {
            closeVerificationDialog(result);
        } else {
            // 验证失败
            const statusEl = document.getElementById('verificationStatus');
            if (statusEl) {
                statusEl.textContent = result?.message || '验证失败，请重试';
                statusEl.style.color = 'var(--theme-error)';
            }

            // 如果是验证码模式，添加错误状态
            if (mode === "code_verification") {
                const inputs = document.querySelectorAll('.verification-code-digit');
                inputs.forEach(input => {
                    input.classList.add('error');
                    // 清空输入框
                    input.value = '';
                });

                // 移除错误状态并聚焦第一个输入框
                setTimeout(() => {
                    inputs.forEach(input => input.classList.remove('error'));
                    if (inputs[0]) inputs[0].focus();
                }, 1000);
            }

            // 重置按钮
            submitBtn.disabled = false;
            submitBtn.textContent = mode === "code_verification" ? '验证' : '确认';
        }
    } catch (error) {
        console.error('验证失败:', error);

        const statusEl = document.getElementById('verificationStatus');
        if (statusEl) {
            statusEl.textContent = '验证失败，请重试';
            statusEl.style.color = 'var(--theme-error)';
        }

        // 如果是验证码模式，添加错误状态
        if (mode === "code_verification") {
            const inputs = document.querySelectorAll('.verification-code-digit');
            inputs.forEach(input => {
                input.classList.add('error');
                // 清空输入框
                input.value = '';
            });

            // 移除错误状态并聚焦第一个输入框
            setTimeout(() => {
                inputs.forEach(input => input.classList.remove('error'));
                if (inputs[0]) inputs[0].focus();
            }, 1000);
        }

        // 重置按钮
        submitBtn.disabled = false;
        submitBtn.textContent = mode === "code_verification" ? '验证' : '确认';
    }
}

/**
 * 加载验证二维码
 */
async function loadVerificationQRCode() {
    const qrCodeEl = document.getElementById('verificationQRCode');
    const qrStatusEl = document.getElementById('verificationQRStatus');

    if (!qrCodeEl || !qrStatusEl) return;

    try {
        qrStatusEl.textContent = '正在生成二维码...';

        // 调用后端生成二维码
        const result = await invoke('generate_verification_qr');

        if (result && result.qr_code) {
            qrCodeEl.innerHTML = `<img src="data:image/png;base64,${result.qr_code}" style="width: 100%; height: 100%; object-fit: contain;">`;
            qrStatusEl.textContent = '请使用手机QQ扫描';

            // 开始检查扫码状态
            checkQRCodeStatus();
        } else {
            qrCodeEl.textContent = '二维码生成失败';
            qrStatusEl.textContent = '请重试';
        }
    } catch (error) {
        console.error('生成二维码失败:', error);
        qrCodeEl.textContent = '二维码生成失败';
        qrStatusEl.textContent = '请重试';
    }
}

/**
 * 检查二维码扫描状态
 */
async function checkQRCodeStatus() {
    const qrStatusEl = document.getElementById('verificationQRStatus');
    if (!qrStatusEl) return;

    try {
        const result = await invoke('check_qr_status');

        if (result && result.status === 'scanned') {
            qrStatusEl.textContent = '扫描成功，请在手机上确认';
        } else if (result && result.status === 'confirmed') {
            // 扫码确认成功
            closeVerificationDialog({
                success: true,
                method: 'vip_qq_verification',
                qq_number: result.qq_number
            });
            return;
        } else if (result && result.status === 'expired') {
            qrStatusEl.textContent = '二维码已过期，请刷新';
            return;
        }

        // 继续检查状态
        setTimeout(checkQRCodeStatus, 2000);
    } catch (error) {
        console.error('检查二维码状态失败:', error);
        setTimeout(checkQRCodeStatus, 5000);
    }
}

/**
 * 切换验证模式
 * @param {string} newMode - 新的验证模式
 */
function switchVerificationMode(newMode) {
    const currentMode = window.verificationDialogMode;
    if (currentMode === newMode) return;

    // 更新模式
    window.verificationDialogMode = newMode;

    // 关闭当前对话框
    const overlay = document.getElementById('verificationDialogOverlay');
    if (overlay) {
        overlay.remove();
    }

    // 恢复背景滚动
    document.body.style.overflow = '';

    // 重新显示新模式的对话框
    setTimeout(() => {
        showVerificationDialog(newMode, window.verificationDialogConfig);
    }, 100);
}

/**
 * 关闭验证对话框
 * @param {Object|null} result - 验证结果
 */
function closeVerificationDialog(result) {
    const overlay = document.getElementById('verificationDialogOverlay');
    if (!overlay) return;

    // 恢复背景滚动
    document.body.style.overflow = '';

    // 关闭动画
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.verificationDialogResolve) {
            window.verificationDialogResolve(result);
            delete window.verificationDialogResolve;
            delete window.verificationDialogMode;
            delete window.verificationDialogConfig;
        }
    }, 300);
}

/**
 * 显示连接错误对话框
 * 对应原版Python的_show_connection_error方法
 * @returns {Promise<boolean>} - 用户是否选择重试
 */
function showConnectionErrorDialog() {
    return new Promise((resolve) => {
        const dialogHTML = `
            <div class="error-dialog-overlay" id="connectionErrorDialogOverlay">
                <div class="error-dialog">
                    <div class="error-dialog-content">
                        <div class="error-dialog-icon">⚠️</div>
                        <h3 class="error-dialog-title">连接错误</h3>
                        <div class="error-dialog-text">
                            无法连接到验证服务器，请检查您的网络连接<br><br>
                            YAugment 需要连接上验证服务器才能正常使用<br><br>
                            请尝试开关代理、更换节点、更换网络后进行重试<br><br>
                            验证服务器在国外，内地网络不一定连的上<br><br>
                            如果依旧无法连接，请查看官方文档是否是版本太低，旧的服务器已经弃用了<br><br>
                            官方文档地址（往下滑）：<br>
                            <a href="https://docs.qq.com/aio/DV2VKUnNaeFRyRGRH?p=DKRZhtXI98ELAa724va8q8" target="_blank" style="color: var(--theme-accent); text-decoration: none;">
                                https://docs.qq.com/aio/DV2VKUnNaeFRyRGRH?p=DKRZhtXI98ELAa724va8q8
                            </a>
                        </div>
                        <div class="error-dialog-actions">
                            <button class="error-dialog-btn secondary" onclick="closeConnectionErrorDialog(false)">退出</button>
                            <button class="error-dialog-btn" onclick="closeConnectionErrorDialog(true)">重试</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.connectionErrorDialogResolve = resolve;

        const overlay = document.getElementById('connectionErrorDialogOverlay');
        setTimeout(() => overlay.classList.add('show'), 10);
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭连接错误对话框
 * @param {boolean} retry - 是否重试
 */
function closeConnectionErrorDialog(retry) {
    const overlay = document.getElementById('connectionErrorDialogOverlay');
    if (!overlay) return;

    document.body.style.overflow = '';
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.connectionErrorDialogResolve) {
            window.connectionErrorDialogResolve(retry);
            delete window.connectionErrorDialogResolve;
        }
    }, 300);
}

/**
 * 显示错误对话框
 * @param {string} title - 错误标题
 * @param {string} message - 错误消息
 * @returns {Promise<void>}
 */
function showErrorDialog(title, message) {
    return new Promise((resolve) => {
        const dialogHTML = `
            <div class="error-dialog-overlay" id="errorDialogOverlay">
                <div class="error-dialog">
                    <div class="error-dialog-content">
                        <div class="error-dialog-icon">❌</div>
                        <h3 class="error-dialog-title">${title}</h3>
                        <div class="error-dialog-text">${message}</div>
                        <div class="error-dialog-actions">
                            <button class="error-dialog-btn" onclick="closeErrorDialog()">确定</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.errorDialogResolve = resolve;

        const overlay = document.getElementById('errorDialogOverlay');
        setTimeout(() => overlay.classList.add('show'), 10);
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭错误对话框
 */
function closeErrorDialog() {
    const overlay = document.getElementById('errorDialogOverlay');
    if (!overlay) return;

    document.body.style.overflow = '';
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.errorDialogResolve) {
            window.errorDialogResolve();
            delete window.errorDialogResolve;
        }
    }, 300);
}

/**
 * 显示强制更新对话框
 * 对应原版Python的_show_force_update_message方法
 * @param {Object} updateInfo - 更新信息
 * @returns {Promise<void>}
 */
function showForceUpdateDialog(updateInfo) {
    return new Promise((resolve) => {
        const {
            message = '发现新版本，需要立即更新',
            updateUrl = '',
            version = ''
        } = updateInfo;

        const dialogHTML = `
            <div class="force-update-dialog-overlay" id="forceUpdateDialogOverlay">
                <div class="force-update-dialog">
                    <div class="force-update-dialog-content">
                        <div class="force-update-dialog-icon">🔄</div>
                        <h3 class="force-update-dialog-title">强制更新</h3>
                        <div class="force-update-dialog-message">${message}</div>
                        ${version ? `<div class="force-update-dialog-version">最新版本: ${version}</div>` : ''}
                        <div class="force-update-dialog-actions">
                            ${updateUrl ?
                                `<button class="force-update-dialog-btn" onclick="openUpdateUrl('${updateUrl}')">立即更新</button>` :
                                `<button class="force-update-dialog-btn" onclick="closeForceUpdateDialog()">确定</button>`
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.forceUpdateDialogResolve = resolve;

        // 显示对话框
        const overlay = document.getElementById('forceUpdateDialogOverlay');
        setTimeout(() => {
            overlay.classList.add('show');
        }, 10);

        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭强制更新对话框
 */
function closeForceUpdateDialog() {
    const overlay = document.getElementById('forceUpdateDialogOverlay');
    if (!overlay) return;

    // 恢复背景滚动
    document.body.style.overflow = '';

    // 关闭动画
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.forceUpdateDialogResolve) {
            window.forceUpdateDialogResolve();
            delete window.forceUpdateDialogResolve;
        }
    }, 300);
}

/**
 * 打开更新链接
 * @param {string} url - 更新链接
 */
async function openUpdateUrl(url) {
    try {
        await invoke('open_url', { url });
        closeForceUpdateDialog();
    } catch (error) {
        console.error('打开更新链接失败:', error);
        closeForceUpdateDialog();
    }
}

/**
 * 显示更新对话框
 * 对应原版Python的版本更新提示
 * @param {Object} updateInfo - 更新信息
 * @returns {Promise<boolean>} - 用户是否选择更新
 */
function showUpdateDialog(updateInfo) {
    return new Promise((resolve) => {
        const {
            title = "发现新版本",
            message = "检测到新版本，建议您及时更新以获得更好的体验。",
            currentVersion = "1.0.0",
            latestVersion = "1.0.1",
            forceUpdate = false,
            updateUrl = ""
        } = updateInfo;

        const dialogHTML = `
            <div class="update-dialog-overlay" id="updateDialogOverlay">
                <div class="update-dialog">
                    <div class="update-dialog-content">
                        <h3 class="update-dialog-title">${title}</h3>
                        <div class="update-dialog-message">${message}</div>
                        <div class="update-dialog-version-info">
                            <div class="update-dialog-version-row">
                                <span class="update-dialog-version-label">当前版本：</span>
                                <span class="update-dialog-version-value">${currentVersion}</span>
                            </div>
                            <div class="update-dialog-version-row">
                                <span class="update-dialog-version-label">最新版本：</span>
                                <span class="update-dialog-version-value">${latestVersion}</span>
                            </div>
                        </div>
                        <div class="update-dialog-separator"></div>
                        <div class="update-dialog-actions">
                            ${!forceUpdate ? '<button class="update-dialog-btn secondary" onclick="closeUpdateDialog(false)">稍后更新</button>' : ''}
                            <button class="update-dialog-btn" onclick="closeUpdateDialog(true)">立即更新</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', dialogHTML);

        // 存储resolve函数和更新URL
        window.updateDialogResolve = resolve;
        window.updateDialogUrl = updateUrl;

        // 显示对话框
        const overlay = document.getElementById('updateDialogOverlay');
        setTimeout(() => {
            overlay.classList.add('show');
        }, 10);

        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭更新对话框
 * @param {boolean} shouldUpdate - 用户是否选择更新
 */
function closeUpdateDialog(shouldUpdate) {
    const overlay = document.getElementById('updateDialogOverlay');
    if (!overlay) return;

    // 恢复背景滚动
    document.body.style.overflow = '';

    // 关闭动画
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.updateDialogResolve) {
            if (shouldUpdate && window.updateDialogUrl) {
                // 打开更新链接
                window.open(window.updateDialogUrl, '_blank');
            }
            window.updateDialogResolve(shouldUpdate);
            delete window.updateDialogResolve;
            delete window.updateDialogUrl;
        }
    }, 300);
}

/**
 * 显示维护模式对话框
 * 对应原版Python的维护模式提示
 * @param {string|Object} maintenanceInfo - 维护信息
 * @returns {Promise<void>}
 */
function showMaintenanceDialog(maintenanceInfo) {
    return new Promise((resolve) => {
        const message = typeof maintenanceInfo === 'string' ? maintenanceInfo :
                       (maintenanceInfo?.message || '系统正在进行维护升级，暂时无法使用。请稍后再试，感谢您的理解。');
        const title = typeof maintenanceInfo === 'object' ? (maintenanceInfo?.title || '系统维护中') : '系统维护中';

        const dialogHTML = `
            <div class="maintenance-dialog-overlay" id="maintenanceDialogOverlay">
                <div class="maintenance-dialog">
                    <div class="maintenance-dialog-content">
                        <h3 class="maintenance-dialog-title">${title}</h3>
                        <div class="maintenance-dialog-message">${message}</div>
                        <div class="maintenance-dialog-actions">
                            <button class="maintenance-dialog-btn" onclick="closeMaintenanceDialog()">确定</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.maintenanceDialogResolve = resolve;

        // 显示对话框
        const overlay = document.getElementById('maintenanceDialogOverlay');
        setTimeout(() => {
            overlay.classList.add('show');
        }, 10);

        // 禁用背景滚动
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭维护模式对话框
 */
function closeMaintenanceDialog() {
    const overlay = document.getElementById('maintenanceDialogOverlay');
    if (!overlay) return;

    // 恢复背景滚动
    document.body.style.overflow = '';

    // 关闭动画
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.maintenanceDialogResolve) {
            window.maintenanceDialogResolve();
            delete window.maintenanceDialogResolve;
        }
    }, 300);
}

// 打开配置说明
async function openConfigHelp() {
    try {
        // 从远程配置获取配置说明链接
        if (typeof bridge !== 'undefined' && bridge.get_config_help_url) {
            let helpUrl;
            try {
                const result = bridge.get_config_help_url();

                // 检查是否是Promise
                if (result && typeof result.then === 'function') {
                    helpUrl = await result;
                } else if (typeof result === 'string') {
                    helpUrl = result;
                } else {
                    helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0';
                }

                // 确保最终结果是字符串
                if (typeof helpUrl !== 'string') {
                    helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0';
                }
            } catch (bridgeError) {
                console.error('调用get_config_help_url失败:', bridgeError);
                helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0';
            }

            // 使用bridge的open_external_url方法在系统默认浏览器中打开链接
            try {
                await bridge.open_external_url(helpUrl);
            } catch (openError) {
                console.error('使用open_external_url打开链接失败:', openError);
                // 备用方案：使用window.open
                window.open(helpUrl, '_blank');
            }
        } else {
            // 备用方案：使用默认链接
            const defaultHelpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0';
            try {
                await bridge.open_external_url(defaultHelpUrl);
            } catch (openError) {
                console.error('使用open_external_url打开默认链接失败:', openError);
                window.open(defaultHelpUrl, '_blank');
            }
        }
    } catch (error) {
        console.error('打开配置说明失败:', error);
        // 最终备用方案：直接使用默认链接
        const defaultHelpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0';
        try {
            await bridge.open_external_url(defaultHelpUrl);
        } catch (openError) {
            console.error('最终备用方案打开链接失败:', openError);
            window.open(defaultHelpUrl, '_blank');
        }
    }
}

// 打开Token获取帮助
async function openTokenHelp() {
    try {
        // 从远程配置获取Token帮助链接
        if (typeof bridge !== 'undefined' && bridge.get_token_help_url) {
            let helpUrl;
            try {
                const result = bridge.get_token_help_url();

                // 检查是否是Promise
                if (result && typeof result.then === 'function') {
                    helpUrl = await result;
                } else if (typeof result === 'string') {
                    helpUrl = result;
                } else {
                    helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf';
                }

                // 确保最终结果是字符串
                if (typeof helpUrl !== 'string') {
                    helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf';
                }
            } catch (bridgeError) {
                console.error('调用get_token_help_url失败:', bridgeError);
                helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf';
            }

            // 使用bridge的open_external_url方法在系统默认浏览器中打开链接
            try {
                await bridge.open_external_url(helpUrl);
            } catch (openError) {
                console.error('使用open_external_url打开链接失败:', openError);
                // 备用方案：使用window.open
                window.open(helpUrl, '_blank');
            }
        } else {
            // 备用方案：使用默认链接
            const defaultHelpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf';
            try {
                await bridge.open_external_url(defaultHelpUrl);
            } catch (openError) {
                console.error('使用open_external_url打开默认链接失败:', openError);
                window.open(defaultHelpUrl, '_blank');
            }
        }
    } catch (error) {
        console.error('打开Token帮助失败:', error);
        // 最终备用方案：直接使用默认链接
        const defaultHelpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf';
        try {
            await bridge.open_external_url(defaultHelpUrl);
        } catch (openError) {
            console.error('最终备用方案打开链接失败:', openError);
            window.open(defaultHelpUrl, '_blank');
        }
    }
}

// 重置Augment - 新的直接执行方式
function executeReset() {
    try {

        if (!bridge || !bridge.reset_augment) {
            throw new Error('Bridge未初始化或reset_augment方法不可用');
        }

        if (!selectedEditor) {
            throw new Error('未选择编辑器');
            return;
        }

        // 更新UI状态
        updateResetUI('running', '正在重置...');

        // 开始重置
        bridge.reset_augment();

        // 使用轮询作为主要机制，信号作为备用

        startResetStatusPolling();

    } catch (error) {
        showToast(`重置失败: ${error.message}`, 'error');
        console.error('Error in executeReset:', error);
        updateResetUI('error', '重置失败');
    }
}

function updateResetUI(state, message) {
    const resetBtn = document.getElementById('resetActionBtn');
    const progressCircle = document.querySelector('.progress-circle');
    const resetIcon = document.querySelector('.reset-icon svg');
    const resetIconContainer = document.querySelector('.reset-icon');
    const resetContainer = document.querySelector('.reset-container');

    // 安全检查 - 如果元素不存在则返回
    if (!resetBtn) {
        console.warn('updateResetUI: resetBtn元素不存在');
        return;
    }

    const btnText = resetBtn.querySelector('.btn-text');
    const btnLoading = resetBtn.querySelector('.btn-loading');

    switch(state) {
        case 'ready':
            resetBtn.disabled = false;
            resetBtn.classList.remove('resetting');
            if (btnText) {
                btnText.style.display = 'block';
                updateTextWithAnimation(btnText, '开始重置');
            }
            if (btnLoading) btnLoading.classList.add('hidden');
            if (progressCircle) {
                progressCircle.style.strokeDashoffset = '314';
            }
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'paused';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.remove('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.remove('resetting');
            }
            break;

        case 'running':
            resetBtn.disabled = true;
            resetBtn.classList.add('resetting');
            if (btnText) btnText.style.display = 'none';
            if (btnLoading) btnLoading.classList.remove('hidden');
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'running';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.add('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.add('resetting');
            }
            break;

        case 'success':
            resetBtn.disabled = false;
            resetBtn.classList.remove('resetting');
            if (btnText) {
                btnText.style.display = 'block';
                updateTextWithAnimation(btnText, '重置完成');
            }
            if (btnLoading) btnLoading.classList.add('hidden');
            if (progressCircle) {
                progressCircle.style.strokeDashoffset = '0';
            }
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'paused';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.remove('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.remove('resetting');
            }
            break;

        case 'error':
            resetBtn.disabled = false;
            resetBtn.classList.remove('resetting');
            if (btnText) {
                btnText.style.display = 'block';
                updateTextWithAnimation(btnText, '重试');
            }
            if (btnLoading) btnLoading.classList.add('hidden');
            if (progressCircle) {
                progressCircle.style.strokeDashoffset = '314';
            }
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'paused';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.remove('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.remove('resetting');
            }
            break;
    }
}

function startResetStatusPolling() {

    // 清除之前的轮询
    if (resetStatusCheckInterval) {
        clearInterval(resetStatusCheckInterval);
    }

    let pollCount = 0;
    const maxPolls = 60; // 最多轮询60次（30秒）

    resetStatusCheckInterval = setInterval(() => {
        pollCount++;

        if (pollCount >= maxPolls) {

            clearInterval(resetStatusCheckInterval);
            resetStatusCheckInterval = null;
            handleResetComplete(false, "重置超时");
            return;
        }

        if (bridge && bridge.get_reset_status) {
            try {

                // 检查方法是否存在

                // 使用回调方式处理异步调用
                bridge.get_reset_status((statusJson) => {

                    // 解析JSON字符串
                    let status;
                    try {
                        status = JSON.parse(statusJson);

                        // 处理状态
                        if (status && typeof status === 'object') {

                            if (status.completed) {
                                // 重置完成，停止轮询
                                clearInterval(resetStatusCheckInterval);
                                resetStatusCheckInterval = null;

                                handleResetComplete(status.success, status.message);
                                return;
                            } else if (status.is_running) {
                                // 更新进度显示
                                const progressText = document.getElementById('resetProgressText');
                                if (progressText) {
                                    updateTextWithAnimation(progressText, status.message || '正在重置...');
                                }

                            } else {

                            }
                        } else {

                        }

                    } catch (parseError) {

                    }
                });

            } catch (error) {

            }
        } else {

        }
    }, 500); // 每500ms检查一次
}

function updateProgress(taskId, progress, message) {
    if (taskId === 'reset') {
        const progressBar = document.getElementById('resetProgressBar');
        const progressText = document.getElementById('resetProgressText');
        const statusText = document.getElementById('resetStatusText');

        if (progressBar && progressText) {
            progressBar.style.width = `${progress * 100}%`;
            updateTextWithAnimation(progressText, message);
        }

        if (statusText) {
            updateTextWithAnimation(statusText, message);
        }
    } else if (taskId === 'verification') {
        updateVerificationStatus('running', message);
    }
}

let resetCompleteHandled = false;

function handleResetComplete(success, message) {

    // 防止重复处理
    if (resetCompleteHandled) {

        return;
    }
    resetCompleteHandled = true;

    // 停止轮询
    if (resetStatusCheckInterval) {
        clearInterval(resetStatusCheckInterval);
        resetStatusCheckInterval = null;
    }

    if (success) {

        updateResetUI('success', '重置成功');
        showToast('重置成功！', 'success');
        setTimeout(() => {
            updateResetUI('ready', '');
            resetCompleteHandled = false; // 重置标志
        }, 3000);
    } else {

        updateResetUI('error', `重置失败: ${message}`);
        showToast(`重置失败: ${message}`, 'error');
        setTimeout(() => {
            updateResetUI('ready', '');
            resetCompleteHandled = false; // 重置标志
        }, 3000);
    }
}

// 验证邮箱配置是否完整
// 异步验证域名配置（用于生成邮箱）
function validateDomainConfigAsync(callback) {
    try {
        if (!bridge || !bridge.get_config) {
            callback({ valid: false, message: 'Bridge未初始化' });
            return;
        }

        // 异步获取当前配置
        bridge.get_config((config) => {
            try {

                if (!config || !config.email) {

                    callback({ valid: false, message: '请先在设置中配置域名' });
                    return;
                }

                const emailConfig = config.email;

                // 验证域名
                if (!emailConfig.domain || emailConfig.domain.trim() === '' || emailConfig.domain === 'xx.com') {

                    callback({ valid: false, message: '请先在设置中配置域名' });
                    return;
                }

                callback({ valid: true, message: '域名配置验证通过' });
            } catch (error) {

                callback({ valid: false, message: `域名配置验证失败: ${error.message}` });
            }
        });
    } catch (error) {

        callback({ valid: false, message: `域名配置验证失败: ${error.message}` });
    }
}

// 异步验证邮箱配置（用于获取验证码）
function validateEmailConfigAsync(callback) {
    try {
        if (!bridge || !bridge.get_config) {
            callback({ valid: false, message: 'Bridge未初始化' });
            return;
        }

        // 异步获取当前配置
        bridge.get_config((config) => {
            try {

                if (!config || !config.email) {
                    callback({ valid: false, message: '请先在设置中配置邮箱' });
                    return;
                }

                const emailConfig = config.email;

                // 获取验证码不需要验证域名，只验证对应的邮箱获取方式配置
                // 根据邮箱获取方式验证不同的字段
                if (emailConfig.use_temp_mail) {
                    // 临时邮箱模式验证
                    if (!emailConfig.temp_mail?.email || emailConfig.temp_mail.email.trim() === '') {

                        callback({ valid: false, message: '请先在设置中配置临时邮箱地址' });
                        return;
                    }
                    if (!emailConfig.temp_mail?.pin || emailConfig.temp_mail.pin.trim() === '') {

                        callback({ valid: false, message: '请先在设置中配置临时邮箱PIN码' });
                        return;
                    }

                } else {
                    // IMAP模式验证
                    const imap = emailConfig.imap || {};
                    if (!imap.server || imap.server.trim() === '') {
                        callback({ valid: false, message: '请先在设置中配置IMAP服务器' });
                        return;
                    }
                    if (!imap.user || imap.user.trim() === '') {
                        callback({ valid: false, message: '请先在设置中配置IMAP邮箱地址' });
                        return;
                    }
                    if (!imap.password || imap.password.trim() === '') {
                        callback({ valid: false, message: '请先在设置中配置IMAP密码/授权码' });
                        return;
                    }

                }

                callback({ valid: true, message: '邮箱配置验证通过' });
            } catch (error) {

                callback({ valid: false, message: `邮箱配置验证失败: ${error.message}` });
            }
        });
    } catch (error) {

        callback({ valid: false, message: `邮箱配置验证失败: ${error.message}` });
    }
}

// 同步验证域名配置（保留用于其他地方）
// 注意：Rust版本不支持同步获取配置，此函数已弃用，请使用 validateDomainConfigAsync
function validateDomainConfig() {
    console.warn('validateDomainConfig 已弃用，请使用 validateDomainConfigAsync');
    return { valid: false, message: '请使用异步配置验证方法' };
}

// 验证完整邮箱配置（用于获取验证码）
// 注意：Rust版本不支持同步获取配置，此函数已弃用，请使用 validateEmailConfigAsync
function validateEmailConfig() {
    console.warn('validateEmailConfig 已弃用，请使用 validateEmailConfigAsync');
    return { valid: false, message: '请使用异步配置验证方法' };
}

// 邮箱工作流 - 现代化邮箱生成
function generateEmailDirect() {
    try {
        if (!bridge || !bridge.generate_email) {
            throw new Error('Bridge未初始化或generate_email方法不可用');
        }

        // 验证域名配置（异步）
        validateDomainConfigAsync((validation) => {
            if (!validation.valid) {
                showToast(validation.message, 'error');
                return;
            }

            // 验证通过，继续生成邮箱
            continueGenerateEmail();
        });
    } catch (error) {
        showToast(`生成邮箱失败: ${error.message}`, 'error');
        console.error('Error in generateEmailDirect:', error);
    }
}

// 继续获取验证码的逻辑
function continueGetVerificationCode() {
    try {

        // 更新工作流状态
        updateWorkflowButtonState('getCodeBtn', 'loading', '获取中...');
        updateWorkspaceStatus('active', '正在获取验证码');
        updateWorkflowProgress(2, false); // 激活第二步

        // 禁用生成邮箱按钮，防止在获取验证码过程中误操作
        const generateEmailBtn = document.getElementById('generateEmailBtn');
        if (generateEmailBtn) {
            generateEmailBtn.disabled = true;
            console.log('获取验证码开始：禁用生成邮箱按钮');
        }

        // 测试信号连接状态

        if (bridge.verification_complete) {

        } else {

        }

        // 重置验证码显示区域
        resetVerificationDisplay();

        // 显示进度条容器
        const progressContainer = document.getElementById('verificationProgressContainer');
        if (progressContainer) {
            progressContainer.style.display = 'block';

        }

        // 初始状态

        updateVerificationStatus('running', '正在获取验证码...');
        updateVerificationDetailedStatus('starting', '正在启动验证码获取...', '');

        bridge.get_verification_code();

        // 启动轮询机制作为信号的备用方案

        startVerificationStatusPolling();

    } catch (error) {
        updateVerificationStatus('error', '获取失败');
        updateVerificationDetailedStatus('error', '获取失败', error.message);

        // 发生异常时也要重新启用生成邮箱按钮
        const generateEmailBtn = document.getElementById('generateEmailBtn');
        if (generateEmailBtn) {
            generateEmailBtn.disabled = false;
            console.log('验证码获取异常：重新启用生成邮箱按钮');
        }
    }
}

// 继续生成邮箱的逻辑
function continueGenerateEmail() {
    try {
        console.log('开始生成邮箱...');

        // 检查是否已经在生成邮箱
        if (isGeneratingEmail) {
            console.warn('邮箱生成已在进行中，忽略重复调用');
            return;
        }

        // 设置生成状态
        isGeneratingEmail = true;

        // 直接调用生成邮箱，不显示loading状态
        bridge.generate_email((email) => {
            try {
                console.log('邮箱生成回调收到结果:', email);
                if (email && email.trim() !== '') {
                    // 使用新的工作流显示方式
                    const displayZone = document.getElementById('emailDisplayZone');
                    if (displayZone) {
                        showEmailResultInWorkflow(email);
                        updateWorkflowProgress(1, true); // 标记第一步完成
                        enableWorkflowButton('getCodeBtn'); // 启用获取验证码按钮
                    } else {
                        // 兼容旧版本
                        const displayArea = document.getElementById('emailDisplayArea');
                        if (displayArea) {
                            showEmailResultInCard(email);
                        } else {
                            showEmailResultDirect(email);
                        }
                    }
                    updateWorkspaceStatus('success', '邮箱生成成功');
                    console.log('邮箱生成成功，状态已更新');
                } else {
                    console.log('邮箱生成失败，email为:', email);
                    updateWorkspaceStatus('error', '邮箱生成失败');
                    showToast('邮箱生成失败', 'error');
                }
            } catch (error) {
                console.error('处理邮箱生成结果时出错:', error);
                updateWorkspaceStatus('error', '处理邮箱结果时出错');
                showToast('处理邮箱结果时出错', 'error');
            } finally {
                // 无论成功失败都重置生成状态
                isGeneratingEmail = false;
            }
        });
    } catch (error) {
        console.error('生成邮箱时发生异常:', error);
        updateWorkspaceStatus('error', '生成邮箱失败');
        showToast('生成邮箱失败: ' + error.message, 'error');
        isGeneratingEmail = false; // 重置生成状态
    }
}

function showEmailResultDirect(email) {
    const emailResult = document.getElementById('emailResult');
    const emailInput = document.getElementById('generatedEmail');

    if (emailResult && emailInput) {
        emailInput.value = email;
        emailResult.classList.remove('hidden');

        // 添加一个淡入动画
        emailResult.style.opacity = '0';
        emailResult.style.transform = 'translateY(10px)';

        setTimeout(() => {
            emailResult.style.transition = 'all 0.3s ease';
            emailResult.style.opacity = '1';
            emailResult.style.transform = 'translateY(0)';
        }, 50);
    } else {
        // 如果旧的UI元素不存在，尝试使用新的UI
        const displayArea = document.getElementById('emailDisplayArea');
        if (displayArea) {
            showEmailResultInCard(email);
        } else {
            console.warn('showEmailResultDirect: 邮箱结果显示元素不存在');
            // 移除toast通知，由信号处理
        }
    }
}

function copyEmailDirect() {
    const emailInput = document.getElementById('generatedEmail');
    if (emailInput && emailInput.value) {
        try {
            // 直接使用可靠的降级方案，避免权限问题
            copyToClipboardFallback(emailInput.value);
            showToast('邮箱地址已复制', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            showToast('复制失败，请手动选择', 'error');
        }
    } else {
        showToast('没有邮箱地址可复制', 'error');
    }
}

function showEmailResult(email) {
    // 重定向到新的直接显示方式
    showEmailResultDirect(email);
}

// 新的工作流邮箱显示函数
function showEmailResultInWorkflow(email) {
    const displayZone = document.getElementById('emailDisplayZone');
    if (!displayZone) {
        console.warn('showEmailResultInWorkflow: 邮箱显示区域不存在');
        return;
    }

    // 创建现代化邮箱结果显示
    displayZone.innerHTML = `
        <div class="email-result-modern">
            <input type="text" class="email-text-modern" value="${email}" readonly>
            <button class="copy-email-btn-modern" onclick="copyEmailFromWorkflow()">
                <svg width="16" height="16" viewBox="0 0 24 24">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z" fill="currentColor"/>
                </svg>
                复制
            </button>
        </div>
    `;

    // 添加淡入动画
    const emailResult = displayZone.querySelector('.email-result-modern');
    if (emailResult) {
        emailResult.style.opacity = '0';
        emailResult.style.transform = 'translateY(20px)';

        setTimeout(() => {
            emailResult.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            emailResult.style.opacity = '1';
            emailResult.style.transform = 'translateY(0)';
        }, 100);
    }

    // 更新信息卡片
    updateEmailInfoCards(email);
}

// 兼容旧版本的邮箱卡片显示函数
function showEmailResultInCard(email) {
    const displayArea = document.getElementById('emailDisplayArea');
    if (!displayArea) {
        console.warn('showEmailResultInCard: 邮箱显示区域不存在');
        return;
    }

    // 创建邮箱结果显示
    displayArea.innerHTML = `
        <div class="email-result">
            <input type="text" class="email-text" value="${email}" readonly>
            <button class="copy-email-btn" onclick="copyEmailFromCard()">
                <svg width="14" height="14" viewBox="0 0 24 24">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z" fill="currentColor"/>
                </svg>
                复制
            </button>
        </div>
    `;

    // 显示浮动卡片
    const floatingCards = document.querySelectorAll('.floating-card');
    floatingCards.forEach(card => {
        card.style.opacity = '1';
        card.style.transform = card.classList.contains('card-1') ? 'rotate(8deg)' : 'rotate(-5deg)';
    });
}

// 辅助函数：不选中文本的复制
function copyToClipboardFallback(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (!successful) {
            throw new Error('execCommand copy failed');
        }
    } finally {
        document.body.removeChild(textArea);
    }
}

// 工作流复制邮箱函数
function copyEmailFromWorkflow() {

    const emailText = document.querySelector('.email-text-modern');

    if (emailText && emailText.value) {
        try {

            // 直接使用可靠的降级方案，避免权限问题
            copyToClipboardFallback(emailText.value);

            showToast('邮箱地址已复制', 'success');
        } catch (error) {

            showToast('复制失败，请手动选择', 'error');
        }
    } else {

        showToast('没有邮箱地址可复制', 'error');
    }
}

function copyEmailFromCard() {
    const emailText = document.querySelector('.email-text');
    if (emailText && emailText.value) {
        try {
            // 直接使用可靠的降级方案，避免权限问题
            copyToClipboardFallback(emailText.value);
            showToast('邮箱地址已复制', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            showToast('复制失败，请手动复制', 'error');
        }
    } else {
        console.warn('copyEmailFromCard: 邮箱输入元素不存在或没有值');
        showToast('没有邮箱地址可复制', 'error');
    }
}

function copyEmail() {
    const emailInput = document.getElementById('generatedEmail');
    if (emailInput && emailInput.value) {
        try {
            // 直接使用可靠的降级方案，避免权限问题
            copyToClipboardFallback(emailInput.value);
            showToast('邮箱地址已复制', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            showToast('复制失败，请手动选择', 'error');
        }
    } else {
        console.warn('copyEmail: 邮箱输入元素不存在或没有值');
        showToast('没有邮箱地址可复制', 'error');
    }
}

function getVerificationCodeDirect() {
    try {

        if (!bridge || !bridge.get_verification_code) {
            throw new Error('Bridge未初始化或get_verification_code方法不可用');
        }

        // 验证邮箱配置（异步）
        validateEmailConfigAsync((validation) => {
            if (!validation.valid) {
                showToast(validation.message, 'error');
                return;
            }

            // 验证通过，继续获取验证码
            continueGetVerificationCode();
        });

    } catch (error) {

        // 移除toast通知，由信号处理
        updateVerificationStatus('error', '获取失败');
        updateVerificationDetailedStatus('error', '获取失败', error.message);
    }
}

function updateVerificationStatus(state, message) {

    // 首先尝试更新工作区状态显示
    const workspaceStatus = document.getElementById('workspaceStatus');
    if (workspaceStatus) {
        const statusDot = workspaceStatus.querySelector('.status-dot');
        const statusText = workspaceStatus.querySelector('.status-text');

        if (statusDot && statusText) {
            // 清除之前的状态类
            statusDot.className = 'status-dot';

            // 根据状态设置样式和使用动画更新文本
            switch (state) {
                case 'running':
                    statusDot.classList.add('running');
                    updateTextWithAnimation(statusText, message || '获取中...');
                    break;
                case 'success':
                    statusDot.classList.add('success');
                    updateTextWithAnimation(statusText, message || '获取成功');
                    break;
                case 'error':
                    statusDot.classList.add('error');
                    updateTextWithAnimation(statusText, message || '获取失败');
                    break;
                default:
                    statusDot.classList.add('idle');
                    updateTextWithAnimation(statusText, message || '就绪');
            }

        }
    }

    // 不在这里更新按钮状态，由工作流函数统一管理
    // updateVerificationButtonState(state);
}

// 更新验证码按钮状态
function updateVerificationButtonState(state) {
    // 尝试找到获取验证码按钮
    const verificationBtn = document.getElementById('getCodeBtn') || document.querySelector('.verification-btn');
    if (!verificationBtn) {

        return;
    }

    switch(state) {
        case 'running':
            verificationBtn.disabled = true;
            verificationBtn.classList.add('loading');
            // 保存原始内容
            if (!verificationBtn.dataset.originalContent) {
                verificationBtn.dataset.originalContent = verificationBtn.innerHTML;
            }
            // 更新按钮文本为加载状态 - 使用动画
            let btnTextRunning = verificationBtn.querySelector('.btn-text');
            if (btnTextRunning) {
                updateTextWithAnimation(btnTextRunning, '获取中...');
            }

            break;
        case 'success':
        case 'error':
        default:
            verificationBtn.disabled = false;
            verificationBtn.classList.remove('loading');
            // 恢复按钮文本 - 使用动画
            let btnTextDefault = verificationBtn.querySelector('.btn-text');
            if (btnTextDefault) {
                updateTextWithAnimation(btnTextDefault, '获取验证码');
            }
            // 清除保存的内容
            if (verificationBtn.dataset.originalContent) {
                delete verificationBtn.dataset.originalContent;
            }

            break;
    }
}

// 存储按钮恢复定时器
const buttonRestoreTimers = {};

// 邮箱生成状态标志
let isGeneratingEmail = false;

// 工作流状态管理函数
function updateWorkflowButtonState(buttonId, state, text) {
    const button = document.getElementById(buttonId);
    if (!button) {

        return;
    }

    const btnContent = button.querySelector('.btn-content');
    const btnLoading = button.querySelector('.btn-loading');
    const btnText = button.querySelector('.btn-text');

    // 清除之前的恢复定时器
    if (buttonRestoreTimers[buttonId]) {
        clearTimeout(buttonRestoreTimers[buttonId]);
        delete buttonRestoreTimers[buttonId];
    }

    // 重置所有状态
    button.classList.remove('loading', 'success', 'error');

    switch (state) {
        case 'loading':
            // 生成邮箱按钮不显示加载状态，其他按钮正常显示
            if (buttonId === 'generateEmailBtn') {
                // 生成邮箱按钮保持正常状态，不显示加载动画
                button.disabled = true;
                if (btnText) {
                    updateTextWithAnimation(btnText, '生成邮箱');
                }
            } else {
                // 其他按钮正常显示加载状态
                button.classList.add('loading');
                button.disabled = true;
                // 使用CSS动画过渡，不直接设置display
                if (btnLoading) {
                    btnLoading.classList.remove('hidden');
                    btnLoading.style.display = 'flex';
                    // 确保加载文字正确显示 - 使用动画
                    const loadingText = btnLoading.querySelector('.loading-text');
                    if (loadingText) {
                        updateTextWithAnimation(loadingText, text || '获取中...');
                    }
                }
                // 同时更新主按钮文本 - 使用动画
                if (btnText) {
                    updateTextWithAnimation(btnText, text || '获取中...');
                }
            }

            break;
        case 'success':
        case 'error':
        case 'ready':
        default:
            // 恢复正常状态
            button.disabled = false;
            button.classList.remove('loading');
            // 使用CSS动画过渡，延迟隐藏加载状态
            if (btnLoading) {
                setTimeout(() => {
                    btnLoading.classList.add('hidden');
                    btnLoading.style.display = 'none';
                }, 300); // 等待动画完成
            }
            if (btnText) {
                // 生成邮箱按钮始终显示"生成邮箱"，不显示状态变化
                if (buttonId === 'generateEmailBtn') {
                    updateTextWithAnimation(btnText, '生成邮箱');
                } else {
                    updateTextWithAnimation(btnText, text || '获取验证码');
                }
            }

            break;
    }
}

// 通用的文字更新动画函数
function updateTextWithAnimation(element, newText, animationDuration = 400) {
    if (!element) return;

    const currentText = element.textContent || '';
    if (currentText === newText) return; // 文字相同，无需更新

    // 添加变化动画类
    element.classList.add('changing');

    // 在动画中间点更新文字内容
    setTimeout(() => {
        element.textContent = newText;
        element.classList.remove('changing');

        // 检查是否需要滚动
        checkAndEnableTextScrolling(element);
    }, animationDuration / 2);
}

// 检查文字是否需要滚动并启用滚动效果
function checkAndEnableTextScrolling(element) {
    if (!element) {
        console.warn('checkAndEnableTextScrolling: element is null or undefined');
        return;
    }

    // 检查元素是否有classList属性
    if (!element.classList) {
        console.warn('checkAndEnableTextScrolling: element does not have classList');
        return;
    }

    // 检查是否是状态文字元素
    if (!element.classList.contains('status-text')) return;

    // 等待DOM更新后再检查
    setTimeout(() => {
        try {
            // 再次检查元素是否仍然存在
            if (!element || !element.parentNode) {
                console.warn('checkAndEnableTextScrolling: element no longer exists in DOM');
                return;
            }

            const containerWidth = 200; // 状态文字容器的最大宽度
            const textWidth = element.scrollWidth || 0;

            // 移除之前的滚动类
            if (element.classList) {
                element.classList.remove('text-scrolling');
            }

            // 如果文字宽度超过容器宽度，启用滚动
            if (textWidth > containerWidth) {
                if (element.classList) {
                    element.classList.add('text-scrolling');
                }

                // 动态计算滚动距离并设置CSS变量
                const scrollDistance = textWidth - containerWidth + 20; // 额外20px边距
                if (element.style) {
                    element.style.setProperty('--scroll-distance', `-${scrollDistance}px`);
                }
            }
        } catch (error) {
            console.error('checkAndEnableTextScrolling error:', error);
        }
    }, 50);
}

function updateWorkspaceStatus(state, message) {
    const statusElement = document.getElementById('workspaceStatus');
    if (!statusElement) return;

    const statusDot = statusElement.querySelector('.status-dot');
    const statusText = statusElement.querySelector('.status-text');

    // 使用通用动画函数更新文字（会自动检查是否需要滚动）
    const newMessage = message || '就绪';
    updateTextWithAnimation(statusText, newMessage);

    // 同步更新状态点和容器的样式，避免宽度闪烁
    if (statusDot) {
        // 移除所有状态类并立即添加新状态
        statusDot.classList.remove('active', 'success', 'error', 'running', 'idle');
        statusDot.classList.add(state);
    }

    // 同步更新整个状态容器的样式
    statusElement.classList.remove('active', 'success', 'error', 'running', 'idle');
    statusElement.classList.add(state);
}

function updateWorkflowProgress(step, completed) {
    const progressSteps = document.querySelectorAll('.progress-step');

    progressSteps.forEach((stepElement, index) => {
        const stepNumber = index + 1;

        if (stepNumber < step || (stepNumber === step && completed)) {
            stepElement.classList.add('completed');
            stepElement.classList.remove('active');
        } else if (stepNumber === step && !completed) {
            stepElement.classList.add('active');
            stepElement.classList.remove('completed');
        } else {
            stepElement.classList.remove('active', 'completed');
        }
    });
}

function enableWorkflowButton(buttonId) {
    const button = document.getElementById(buttonId);
    if (button) {
        button.disabled = false;
        button.classList.remove('disabled');
    }
}

function updateEmailInfoCards(email) {
    // 提取域名
    const domain = email.split('@')[1] || 'unknown';

    // 更新域名信息
    const domainInfo = document.getElementById('emailDomainInfo');
    if (domainInfo) {
        updateTextWithAnimation(domainInfo, domain);
    }

    // 更新模式信息（这里可以根据实际配置来设置）
    const modeInfo = document.getElementById('emailModeInfo');
    if (modeInfo) {
        // 这里可以根据实际的邮箱配置来显示模式
        updateTextWithAnimation(modeInfo, domain.includes('tempmail') ? '临时邮箱' : 'IMAP邮箱');
    }
}

// 保留旧的邮箱功能以兼容性（如果需要）
function generateEmail() {
    generateEmailDirect();
}

function getVerificationCode() {
    getVerificationCodeDirect();
}

function testVerificationSignals() {

    if (!bridge || !bridge.test_verification_signals) {

        showToast('测试功能不可用', 'error');
        return;
    }

    bridge.test_verification_signals();

}

function copyEmail() {
    copyEmailDirect();
}

function getVerificationCodeForEmail() {
    getVerificationCodeDirect();
}

function copyVerificationCode() {
    // 优先查找新工作流中的验证码元素
    let codeElement = document.querySelector('#verificationResultZone .verification-code');

    // 如果没找到，查找旧版本的验证码元素
    if (!codeElement) {
        codeElement = document.getElementById('verificationCode');
    }

    if (codeElement && codeElement.textContent) {
        // 创建临时文本区域来复制文本
        const textArea = document.createElement('textarea');
        textArea.value = codeElement.textContent;
        document.body.appendChild(textArea);
        textArea.select();

        try {
            document.execCommand('copy');
            // 复制成功，不在这里显示toast，由调用方决定

            // 更新复制按钮状态（如果是新工作流）
            const copyBtn = document.querySelector('#verificationResultZone .result-copy-btn');
            if (copyBtn) {
                const originalHTML = copyBtn.innerHTML;
                copyBtn.innerHTML = `
                    <svg width="18" height="18" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.58L19 8l-9 9z" fill="currentColor"/>
                    </svg>
                `;
                copyBtn.style.background = 'linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(22, 163, 74, 0.1))';
                copyBtn.style.borderColor = 'rgba(34, 197, 94, 0.4)';

                setTimeout(() => {
                    copyBtn.innerHTML = originalHTML;
                    copyBtn.style.background = '';
                    copyBtn.style.borderColor = '';
                }, 2000);
            }
        } catch (err) {
            console.error('复制失败:', err);
            showToast('复制失败，请手动复制', 'error');
        }

        document.body.removeChild(textArea);
    } else {
        showToast('没有验证码可复制', 'warning');
    }
}

function showVerificationCode(code) {
    const resultContainer = document.getElementById('verificationResult');
    const codeElement = document.getElementById('verificationCode');

    if (resultContainer && codeElement) {
        codeElement.textContent = code;
        resultContainer.classList.remove('hidden');

        // 更新状态为成功
        updateVerificationStatus('success', '验证码获取成功');

        // 添加一个淡入动画
        resultContainer.style.opacity = '0';
        resultContainer.style.transform = 'translateY(10px)';

        setTimeout(() => {
            resultContainer.style.transition = 'all 0.3s ease';
            resultContainer.style.opacity = '1';
            resultContainer.style.transform = 'translateY(0)';
        }, 50);

        // 移除toast通知，由最终结果处理
    } else {
        console.warn('showVerificationCode: 验证码显示元素不存在');
        // 移除toast通知，由最终结果处理
    }
}

// 新增的详细验证码状态处理函数
function updateVerificationDetailedStatus(status, message, details) {

    // 更新主状态显示
    updateVerificationStatus(getStatusFromDetailedStatus(status), message);

    // 更新详细信息显示
    updateVerificationDetails(status, message, details);
}



function updateVerificationConnectionTest(success, message) {

    if (success) {
        updateVerificationStatus('running', '连接测试成功，开始获取验证码...');
        // 移除toast通知，减少干扰
    } else {
        updateVerificationStatus('error', '连接测试失败');
        // 移除toast通知，由最终结果处理
    }
}

function handleVerificationCompleted(success, code, message) {

    // 停止轮询
    if (verificationStatusCheckInterval) {
        clearInterval(verificationStatusCheckInterval);
        verificationStatusCheckInterval = null;
    }

    // 防止重复处理
    if (verificationCompleteHandled) {

        return;
    }
    verificationCompleteHandled = true;

    // 无论成功还是失败，都直接恢复按钮为原始状态
    setTimeout(() => {
        updateWorkflowButtonState('getCodeBtn', 'ready', '获取验证码');

        // 重新启用生成邮箱按钮
        const generateEmailBtn = document.getElementById('generateEmailBtn');
        if (generateEmailBtn) {
            generateEmailBtn.disabled = false;
            console.log('验证码获取完成：重新启用生成邮箱按钮');
        }
    }, 100);

    if (success && code) {
        // 显示验证码
        showVerificationCodeInWorkflow(code);
        updateVerificationDetailedStatus('success', message, `验证码: ${code}`);
    } else {
        // 失败时只更新状态显示，不更新按钮
        updateWorkspaceStatus('error', message || '验证码获取失败');
        updateVerificationStatus('error', message || '获取验证码失败');
        updateVerificationDetailedStatus('failed', message || '获取验证码失败', '');
    }
}

// 在工作流中显示验证码 - 直接在邮箱显示区域显示，使用和邮箱相同的样式
function showVerificationCodeInWorkflow(code) {
    const emailDisplayZone = document.getElementById('emailDisplayZone');
    if (!emailDisplayZone) {
        console.warn('showVerificationCodeInWorkflow: 邮箱显示区域不存在');
        // 兼容旧版本
        showVerificationCode(code);
        return;
    }

    // 直接在邮箱显示区域显示验证码，使用和邮箱相同的样式
    emailDisplayZone.innerHTML = `
        <div class="email-result-modern">
            <input type="text" class="email-text-modern" value="${code}" readonly>
            <button class="copy-email-btn-modern" onclick="copyVerificationCodeFromWorkflow()">
                <svg width="16" height="16" viewBox="0 0 24 24">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z" fill="currentColor"/>
                </svg>
                复制
            </button>
        </div>
    `;

    // 添加淡入动画
    emailDisplayZone.style.opacity = '0';
    emailDisplayZone.style.transform = 'translateY(10px)';

    setTimeout(() => {
        emailDisplayZone.style.transition = 'all 0.4s ease';
        emailDisplayZone.style.opacity = '1';
        emailDisplayZone.style.transform = 'translateY(0)';
    }, 50);

    // 自动复制验证码到剪贴板
    copyVerificationCodeToClipboard(code);
}

// 从消息中提取验证码的函数
function extractCodeFromMessage(message) {
    if (!message) return '';

    // 尝试从消息中提取6位数字验证码
    const codeMatch = message.match(/\b\d{6}\b/);
    if (codeMatch) {
        return codeMatch[0];
    }

    // 如果没有找到6位数字，尝试查找其他格式的验证码
    const codeMatch2 = message.match(/验证码[：:]\s*(\d+)/);
    if (codeMatch2) {
        return codeMatch2[1];
    }

    // 如果消息本身就是验证码格式
    if (/^\d{4,8}$/.test(message.trim())) {
        return message.trim();
    }

    return '';
}

// 从工作流中复制验证码
function copyVerificationCodeFromWorkflow() {
    const emailInput = document.querySelector('#emailDisplayZone .email-text-modern');
    if (emailInput) {
        const code = emailInput.value;
        copyVerificationCodeToClipboard(code);
        // 显示复制成功提示
        showToast('验证码已复制到剪贴板', 'success', 2000);
    } else {

        showToast('复制失败：找不到验证码', 'error');
    }
}

// 自动复制验证码到剪贴板的函数
function copyVerificationCodeToClipboard(code) {
    try {
        const textArea = document.createElement('textarea');
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // 移除toast通知，由最终结果处理
    } catch (err) {

    }
}

function resetVerificationDisplay() {
    // 重置验证码结果显示
    const resultContainer = document.getElementById('verificationResult');
    if (resultContainer) {
        resultContainer.classList.add('hidden');
    }

    // 重置进度条
    const progressBar = document.getElementById('verificationProgressBar');
    if (progressBar) {
        progressBar.style.width = '0%';
    }

    // 隐藏进度条容器
    const progressContainer = document.getElementById('verificationProgressContainer');
    if (progressContainer) {
        progressContainer.style.display = 'none';
    }

    // 清除详细信息
    updateVerificationDetails('', '', '');
}

function updateVerificationDetails(status, message, details) {
    // 更新详细信息显示区域
    const detailsElement = document.getElementById('verificationDetails');
    if (detailsElement) {
        let statusIcon = getStatusIcon(status);
        let detailsHtml = `
            <div class="verification-detail-item">
                <span class="status-icon">${statusIcon}</span>
                <span class="status-message">${message}</span>
            </div>
        `;

        if (details) {
            detailsHtml += `
                <div class="verification-detail-sub">
                    <span class="detail-text">${details}</span>
                </div>
            `;
        }

        detailsElement.innerHTML = detailsHtml;
    }
}

function getStatusFromDetailedStatus(detailedStatus) {
    const statusMap = {
        'starting': 'running',
        'testing_connection': 'running',
        'connection_success': 'running',
        'connection_failed': 'error',
        'attempting': 'running',
        'fetching_temp_mail': 'running',
        'fetching_imap': 'running',
        'success': 'success',
        'waiting_retry': 'running',
        'attempt_failed': 'running',
        'network_error': 'error',
        'failed': 'error',
        'copied': 'success'
    };

    return statusMap[detailedStatus] || 'running';
}

function getStatusIcon(status) {
    const iconMap = {
        'starting': '🚀',
        'testing_connection': '🔧',
        'connection_success': '✅',
        'connection_failed': '❌',
        'attempting': '⏳',
        'fetching_temp_mail': '📧',
        'fetching_imap': '📬',
        'success': '✅',
        'waiting_retry': '⏰',
        'attempt_failed': '⚠️',
        'network_error': '🌐',
        'failed': '❌',
        'copied': '📋'
    };

    return iconMap[status] || '📄';
}

// 设置功能
function showSettings() {
    try {
        if (!bridge || !bridge.get_config) {
            throw new Error('Bridge未初始化或get_config方法不可用');
        }

        bridge.get_config((config) => {
            try {
                const content = generateSettingsContent(config);
                const footer = `
                    <div class="dialog-footer">
                        <div class="dialog-actions">
                            <button class="action-btn primary" onclick="saveSettings()">
                                <div class="btn-content">
                                    <span class="btn-text">保存设置</span>
                                </div>
                                <div class="btn-loading">
                                    <div class="loading-spinner"></div>
                                    <span class="loading-text">保存中...</span>
                                </div>
                            </button>
                            <button class="action-btn secondary" onclick="closeDialog()">
                                <div class="btn-content">
                                    <span class="btn-text">取消</span>
                                </div>
                            </button>
                        </div>
                    </div>
                `;
                showDialog('设置', content, footer);

                // 初始化单选框状态
                setTimeout(() => {
                    updateRadioItemsState('editor');
                    updateRadioItemsState('emailType');

                    // 添加单选框变化监听器
                    document.querySelectorAll('input[name="editor"]').forEach(radio => {
                        radio.addEventListener('change', () => updateRadioItemsState('editor'));
                    });

                    document.querySelectorAll('input[name="emailType"]').forEach(radio => {
                        radio.addEventListener('change', () => {
                            updateRadioItemsState('emailType');
                            toggleEmailType();
                        });
                    });
                }, 100);
            } catch (error) {
                showToast(`生成设置界面失败: ${error.message}`, 'error');
                console.error('Error in showSettings callback:', error);
            }
        });
    } catch (error) {
        showToast(`打开设置失败: ${error.message}`, 'error');
        console.error('Error in showSettings:', error);
    }
}

function generateSettingsContent(config) {
    const emailConfig = config.email || {};
    return `
        <div class="settings-content">
            <div class="settings-tabs">
                <button class="tab-btn active" onclick="switchTab('editor', this)">编辑器</button>
                <button class="tab-btn" onclick="switchTab('email', this)">邮箱设置</button>
                <button class="tab-btn" onclick="switchTab('advanced', this)">高级设置</button>
            </div>

            <div class="settings-body">
                <div class="tab-content" id="editorTab">
                    <h4>编辑器选择</h4>
                    <div class="radio-setting-group">
                        <div class="radio-setting-item ${selectedEditor === 'vscode' ? 'checked' : ''}" onclick="selectEditorInSettings('vscode')">
                            <input type="radio" name="editor" value="vscode" ${selectedEditor === 'vscode' ? 'checked' : ''} />
                            <div>
                                <div class="setting-label">Visual Studio Code</div>
                                <div class="setting-description">微软开发的现代代码编辑器</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'cursor' ? 'checked' : ''}" onclick="selectEditorInSettings('cursor')">
                            <input type="radio" name="editor" value="cursor" ${selectedEditor === 'cursor' ? 'checked' : ''} />
                            <div>
                                <div class="setting-label">Cursor</div>
                                <div class="setting-description">AI驱动的代码编辑器</div>
                            </div>
                        </div>
                    </div>
                </div>
            
            <div class="tab-content hidden" id="emailTab">
                <h4>邮箱配置</h4>

                <!-- 域名设置 -->
                <div class="setting-item">
                    <div>
                        <div class="setting-label">域名</div>
                        <div class="setting-description">邮箱地址的域名部分</div>
                    </div>
                    <div class="setting-control">
                        <input type="text" id="emailDomain" value="${emailConfig.domain || ''}" placeholder="xx.com" />
                    </div>
                </div>

                <!-- 邮箱获取方式 -->
                <div class="setting-group-title">邮箱获取方式</div>
                <div class="radio-setting-group">
                    <div class="radio-setting-item ${emailConfig.use_temp_mail ? 'checked' : ''}" onclick="selectEmailType('temp')">
                        <input type="radio" name="emailType" value="temp" ${emailConfig.use_temp_mail ? 'checked' : ''} />
                        <div>
                            <div class="setting-label">临时邮箱</div>
                            <div class="setting-description">使用 tempmail.plus 临时邮箱服务</div>
                        </div>
                    </div>
                    <div class="radio-setting-item ${!emailConfig.use_temp_mail ? 'checked' : ''}" onclick="selectEmailType('imap')">
                        <input type="radio" name="emailType" value="imap" ${!emailConfig.use_temp_mail ? 'checked' : ''} />
                        <div>
                            <div class="setting-label">IMAP邮箱</div>
                            <div class="setting-description">使用自己的邮箱通过IMAP协议接收</div>
                        </div>
                    </div>
                </div>
                
                <div id="tempMailConfig" ${!emailConfig.use_temp_mail ? 'style="display:none"' : ''}>
                    <div class="setting-group-title">临时邮箱设置</div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">邮箱地址</div>
                            <div class="setting-description">在 tempmail.plus 创建的邮箱地址</div>
                        </div>
                        <div class="setting-control">
                            <input type="text" id="tempMailEmail" value="${emailConfig.temp_mail?.email || ''}" placeholder="<EMAIL>" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">PIN码</div>
                            <div class="setting-description">访问邮箱时需要的PIN码</div>
                        </div>
                        <div class="setting-control password-input-container">
                            <input type="password" id="tempMailPin" value="${emailConfig.temp_mail?.pin || ''}" placeholder="你设置的PIN" />
                            <button type="button" class="password-toggle-btn" onclick="togglePasswordVisibility('tempMailPin')">
                                <svg class="eye-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="imapConfig" ${emailConfig.use_temp_mail ? 'style="display:none"' : ''}>
                    <div class="setting-group-title">IMAP设置</div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">IMAP服务器</div>
                            <div class="setting-description">邮箱服务商的IMAP服务器地址</div>
                        </div>
                        <div class="setting-control">
                            <input type="text" id="imapServer" value="${emailConfig.imap?.server || 'imap.qq.com'}" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">端口</div>
                            <div class="setting-description">IMAP服务器端口，通常为993</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="imapPort" value="${emailConfig.imap?.port || 993}" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">邮箱地址</div>
                            <div class="setting-description">用于接收验证码的邮箱地址</div>
                        </div>
                        <div class="setting-control">
                            <input type="email" id="imapUser" value="${emailConfig.imap?.user || ''}" placeholder="<EMAIL>" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">密码/授权码</div>
                            <div class="setting-description">邮箱密码或应用专用授权码</div>
                        </div>
                        <div class="setting-control password-input-container">
                            <input type="password" id="imapPassword" value="${emailConfig.imap?.password || ''}" placeholder="password" />
                            <button type="button" class="password-toggle-btn" onclick="togglePasswordVisibility('imapPassword')">
                                <svg class="eye-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">收件箱</div>
                            <div class="setting-description">邮件存放的文件夹名称</div>
                        </div>
                        <div class="setting-control">
                            <input type="text" id="imapFolder" value="${emailConfig.imap?.folder || 'INBOX'}" />
                        </div>
                    </div>

                </div>
            </div>
            
            <div class="tab-content hidden" id="advancedTab">
                <h4>高级设置</h4>

                <!-- 自动复制验证码开关 -->
                <div class="switch-group">
                    <div>
                        <div class="switch-label">自动复制验证码</div>
                        <div class="switch-description">获取验证码后自动复制到剪贴板</div>
                    </div>
                    <label class="toggle-switch" onclick="toggleAutoCopy();">
                        <input type="checkbox" id="autoCopy" ${emailConfig.auto_copy ? 'checked' : ''} />
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <!-- 邮箱生成配置 -->
                <div class="setting-group-title">邮箱生成配置</div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">用户名长度</div>
                        <div class="setting-description">生成邮箱地址的用户名字符数量</div>
                    </div>
                    <div class="setting-control">
                        <input type="number" id="usernameLength" value="${emailConfig.generation?.username_length || 9}" min="5" max="20" />
                    </div>
                </div>

                <!-- 数字配置 -->
                <div class="switch-group">
                    <div>
                        <div class="switch-label">包含数字</div>
                        <div class="switch-description">在生成的用户名中包含数字</div>
                    </div>
                    <label class="toggle-switch" onclick="toggleIncludeNumbers();">
                        <input type="checkbox" id="includeNumbers" ${emailConfig.generation?.include_numbers !== false ? 'checked' : ''} />
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <div id="numberConfig" ${emailConfig.generation?.include_numbers === false ? 'style="display:none"' : ''}>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">数字概率</div>
                            <div class="setting-description">包含数字的概率 (0-100%)</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="numberProbability" value="${Math.round((emailConfig.generation?.number_probability || 0.7) * 100)}" min="0" max="100" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">最少数字个数</div>
                            <div class="setting-description">用户名中最少包含的数字个数</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="minNumbers" value="${emailConfig.generation?.min_numbers || 1}" min="1" max="5" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">最多数字个数</div>
                            <div class="setting-description">用户名中最多包含的数字个数</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="maxNumbers" value="${emailConfig.generation?.max_numbers || 3}" min="1" max="8" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">前缀无数字长度</div>
                            <div class="setting-description">前几位不允许数字，使邮箱更真实</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="noDigitPrefixLength" value="${emailConfig.generation?.no_digit_prefix_length || 3}" min="1" max="5" />
                        </div>
                    </div>
                </div>

                <!-- 大写字母配置 -->
                <div class="switch-group">
                    <div>
                        <div class="switch-label">包含大写字母</div>
                        <div class="switch-description">在生成的用户名中包含大写字母</div>
                    </div>
                    <label class="toggle-switch" onclick="toggleIncludeUppercase();">
                        <input type="checkbox" id="includeUppercase" ${emailConfig.generation?.include_uppercase ? 'checked' : ''} />
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <div id="uppercaseConfig" ${!emailConfig.generation?.include_uppercase ? 'style="display:none"' : ''}>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">大写字母概率</div>
                            <div class="setting-description">包含大写字母的概率 (0-100%)</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="uppercaseProbability" value="${Math.round((emailConfig.generation?.uppercase_probability || 0.3) * 100)}" min="0" max="100" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">最少大写字母个数</div>
                            <div class="setting-description">用户名中最少包含的大写字母个数</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="minUppercase" value="${emailConfig.generation?.min_uppercase || 1}" min="1" max="3" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">最多大写字母个数</div>
                            <div class="setting-description">用户名中最多包含的大写字母个数</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="maxUppercase" value="${emailConfig.generation?.max_uppercase || 2}" min="1" max="5" />
                        </div>
                    </div>
                </div>

                <!-- 重试配置 -->
                <div class="setting-group-title">重试配置</div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">最大重试次数</div>
                        <div class="setting-description">获取验证码失败时的最大重试次数</div>
                    </div>
                    <div class="setting-control">
                        <input type="number" id="maxRetries" value="${emailConfig.retry?.max_retries || 30}" min="1" max="100" />
                    </div>
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">重试间隔</div>
                        <div class="setting-description">每次重试之间的等待时间（秒）</div>
                    </div>
                    <div class="setting-control">
                        <input type="number" id="retryInterval" value="${emailConfig.retry?.retry_interval || 1}" min="1" max="10" />
                    </div>
                </div>
            </div>

        </div>
    `;
}

function switchTab(tabName, clickedElement) {
    // 切换标签按钮状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // 如果没有传入点击的元素，尝试通过事件获取
    const targetElement = clickedElement || event?.target;
    if (targetElement) {
        targetElement.classList.add('active');
    }

    // 切换内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    document.getElementById(`${tabName}Tab`).classList.remove('hidden');
}

function selectEditorInSettings(editorType) {
    // 更新选中的编辑器
    const radioInput = document.querySelector(`input[name="editor"][value="${editorType}"]`);
    if (radioInput) {
        radioInput.checked = true;
        // 更新视觉状态
        updateRadioItemsState('editor');
    }
}

function selectEmailType(emailType) {
    // 更新选中的邮箱类型
    const radioInput = document.querySelector(`input[name="emailType"][value="${emailType}"]`);
    if (radioInput) {
        radioInput.checked = true;
        // 触发切换逻辑
        toggleEmailType();
    }
}

function toggleEmailType() {
    const checkedInput = document.querySelector('input[name="emailType"]:checked');
    if (!checkedInput) return;

    const useTempMail = checkedInput.value === 'temp';
    const tempMailConfig = document.getElementById('tempMailConfig');
    const imapConfig = document.getElementById('imapConfig');

    if (tempMailConfig) {
        tempMailConfig.style.display = useTempMail ? 'block' : 'none';
    }
    if (imapConfig) {
        imapConfig.style.display = useTempMail ? 'none' : 'block';
    }

    // 更新单选框项的视觉状态
    updateRadioItemsState('emailType');
}

// 更新单选框项的视觉状态
function updateRadioItemsState(radioName) {
    const radioItems = document.querySelectorAll('.radio-setting-item');
    radioItems.forEach(item => {
        const radio = item.querySelector(`input[name="${radioName}"]`);
        if (radio) {
            if (radio.checked) {
                item.classList.add('checked');
            } else {
                item.classList.remove('checked');
            }
        }
    });
}

function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.parentElement.querySelector('.password-toggle-btn');
    const eyeIcon = button.querySelector('.eye-icon');

    if (input.type === 'password') {
        input.type = 'text';
        // 切换为闭眼图标（眼睛上有斜线）
        eyeIcon.innerHTML = `
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
            <circle cx="12" cy="12" r="3"/>
            <path d="M2 2l20 20"/>
        `;
    } else {
        input.type = 'password';
        // 切换为睁眼图标
        eyeIcon.innerHTML = `
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
            <circle cx="12" cy="12" r="3"/>
        `;
    }
}

function toggleIncludeNumbers() {
    const checkbox = document.getElementById('includeNumbers');
    if (checkbox) {
        checkbox.checked = !checkbox.checked;

        // 立即更新开关的视觉状态
        const toggleSwitch = checkbox.closest('.toggle-switch');
        if (toggleSwitch) {
            if (checkbox.checked) {
                toggleSwitch.classList.add('checked');
            } else {
                toggleSwitch.classList.remove('checked');
            }
        }

        // 显示/隐藏数字配置区域
        const numberConfig = document.getElementById('numberConfig');
        if (numberConfig) {
            numberConfig.style.display = checkbox.checked ? 'block' : 'none';
        }
    }
}

function toggleIncludeUppercase() {
    const checkbox = document.getElementById('includeUppercase');
    if (checkbox) {
        checkbox.checked = !checkbox.checked;

        // 立即更新开关的视觉状态
        const toggleSwitch = checkbox.closest('.toggle-switch');
        if (toggleSwitch) {
            if (checkbox.checked) {
                toggleSwitch.classList.add('checked');
            } else {
                toggleSwitch.classList.remove('checked');
            }
        }

        // 显示/隐藏大写字母配置区域
        const uppercaseConfig = document.getElementById('uppercaseConfig');
        if (uppercaseConfig) {
            uppercaseConfig.style.display = checkbox.checked ? 'block' : 'none';
        }
    }
}

function toggleAutoCopy() {
    const checkbox = document.getElementById('autoCopy');
    if (checkbox) {
        checkbox.checked = !checkbox.checked;

        // 立即更新开关的视觉状态
        const toggleSwitch = checkbox.closest('.toggle-switch');
        if (toggleSwitch) {
            if (checkbox.checked) {
                toggleSwitch.classList.add('checked');
            } else {
                toggleSwitch.classList.remove('checked');
            }
        }

        // 触发change事件以便其他代码能够监听到变化
        checkbox.dispatchEvent(new Event('change'));
    }
}

function collectSettingsData() {
    try {
        const getElementValue = (id, defaultValue = '') => {
            const element = document.getElementById(id);
            return element ? element.value.trim() : defaultValue;
        };

        // 获取元素值并去除所有空格（用于邮箱、服务器地址等不应包含空格的字段）
        const getElementValueNoSpaces = (id, defaultValue = '') => {
            const element = document.getElementById(id);
            return element ? element.value.replace(/\s+/g, '') : defaultValue;
        };

        const getElementChecked = (id, defaultValue = false) => {
            const element = document.getElementById(id);
            return element ? element.checked : defaultValue;
        };

        const getRadioValue = (name, defaultValue = '') => {
            const element = document.querySelector(`input[name="${name}"]:checked`);
            return element ? element.value : defaultValue;
        };

        return {
            editor_type: getRadioValue('editor', selectedEditor),
            email: {
                domain: getElementValueNoSpaces('emailDomain', 'xx.com'),
                use_temp_mail: getRadioValue('emailType', 'temp') === 'temp',
                temp_mail: {
                    email: getElementValueNoSpaces('tempMailEmail'),
                    pin: getElementValueNoSpaces('tempMailPin')
                },
                imap: {
                    server: getElementValueNoSpaces('imapServer', 'imap.qq.com'),
                    port: parseInt(getElementValueNoSpaces('imapPort', '993')) || 993,
                    user: getElementValueNoSpaces('imapUser'),
                    password: getElementValueNoSpaces('imapPassword'),
                    folder: getElementValue('imapFolder', 'INBOX')
                },
                generation: {
                    username_length: parseInt(getElementValue('usernameLength', '9')) || 9,
                    include_numbers: getElementChecked('includeNumbers', true),
                    number_probability: (parseInt(getElementValue('numberProbability', '70')) || 70) / 100,
                    min_numbers: parseInt(getElementValue('minNumbers', '1')) || 1,
                    max_numbers: parseInt(getElementValue('maxNumbers', '3')) || 3,
                    no_digit_prefix_length: parseInt(getElementValue('noDigitPrefixLength', '3')) || 3,
                    include_uppercase: getElementChecked('includeUppercase', false),
                    uppercase_probability: (parseInt(getElementValue('uppercaseProbability', '30')) || 30) / 100,
                    min_uppercase: parseInt(getElementValue('minUppercase', '1')) || 1,
                    max_uppercase: parseInt(getElementValue('maxUppercase', '2')) || 2
                },
                retry: {
                    max_retries: parseInt(getElementValue('maxRetries', '30')) || 30,
                    retry_interval: parseInt(getElementValue('retryInterval', '1')) || 1
                },
                auto_copy: getElementChecked('autoCopy', false)
            }
        };
    } catch (error) {
        console.error('Error collecting settings data:', error);
        showToast(`收集设置数据失败: ${error.message}`, 'error');
        return null;
    }
}

function saveSettings() {
    try {
        if (!bridge || !bridge.save_config) {
            throw new Error('Bridge未初始化或save_config方法不可用');
        }

        const config = collectSettingsData();
        if (!config) {
            throw new Error('收集设置数据失败');
        }

        // 显示加载状态
        const saveBtn = document.querySelector('.action-btn.primary');
        if (saveBtn) {
            saveBtn.classList.add('loading');
            saveBtn.disabled = true;
        }

        bridge.save_config(config, (success) => {
            try {
                // 恢复按钮状态
                if (saveBtn) {
                    saveBtn.classList.remove('loading');
                    saveBtn.disabled = false;
                }

                if (success) {
                    showToast('设置已保存', 'success');

                    // 如果切换了编辑器，更新选择
                    const newEditor = config.editor_type;
                    if (newEditor !== selectedEditor) {
                        selectedEditor = newEditor;
                        updateEditorDisplay();
                    }

                    // 更新浮动卡片信息
                    updateFloatingCards(config);

                    // 立即关闭对话框
                    closeDialog();
                } else {
                    showToast('保存设置失败', 'error');
                }
            } catch (error) {
                // 恢复按钮状态
                if (saveBtn) {
                    saveBtn.classList.remove('loading');
                    saveBtn.disabled = false;
                }
                showToast(`处理保存结果时发生错误: ${error.message}`, 'error');
                console.error('Error in saveSettings callback:', error);
            }
        });
    } catch (error) {
        // 恢复按钮状态
        const saveBtn = document.querySelector('.action-btn.primary');
        if (saveBtn) {
            saveBtn.classList.remove('loading');
            saveBtn.disabled = false;
        }
        showToast(`保存设置失败: ${error.message}`, 'error');
        console.error('Error in saveSettings:', error);
    }
}

function handleConfigUpdate(config) {
    // 配置更新时的处理 - 与原版Python行为一致
    console.log('配置已更新:', config);

    // 更新浮动卡片信息
    updateFloatingCards(config);

    // 如果编辑器类型发生变化，更新编辑器显示
    if (config.editor_type && config.editor_type !== selectedEditor) {
        selectedEditor = config.editor_type;
        updateEditorDisplay();
    }

    // 可以在这里添加其他需要响应配置变化的逻辑
}

// 添加必要的样式
const style = document.createElement('style');
style.textContent = `
    /* CSS变量定义 */
    :root {
        --bg-primary: #000000;
        --bg-secondary: #0a0a0a;
        --bg-card: #111111;
        --bg-hover: #1a1a1a;

        --purple-primary: #8b5cf6;
        --purple-light: #a78bfa;
        --purple-dark: #6d28d9;
        --purple-glow: rgba(139, 92, 246, 0.5);

        --text-primary: #ffffff;
        --text-secondary: #a0a0a0;
        --text-muted: #666666;

        --border-color: rgba(139, 92, 246, 0.2);
        --shadow-color: rgba(139, 92, 246, 0.3);

        --radius-sm: 16px;
        --radius-md: 20px;
        --radius-lg: 28px;
        --radius-xl: 36px;

        --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .reset-dialog ul {
        margin: 20px 0;
        padding-left: 20px;
        color: var(--text-secondary);
    }
    
    .reset-dialog li {
        margin: 8px 0;
    }
    
    .warning-text {
        color: var(--purple-light);
        margin-top: 20px;
    }
    
    .dialog-actions {
        display: flex;
        gap: 16px;
        margin-top: 30px;
        justify-content: center;
    }
    
    .progress-container {
        margin-top: 30px;
    }
    
    .progress-bar {
        width: 100%;
        height: 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        overflow: hidden;
    }
    
    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--purple-primary), var(--purple-light));
        transition: width 0.3s ease;
        width: 0%;
    }
    
    .progress-text {
        text-align: center;
        margin-top: 12px;
        color: var(--text-secondary);
    }
    
    .email-display {
        display: flex;
        gap: 12px;
        margin: 20px 0;
    }
    
    .email-display input {
        flex: 1;
        padding: 12px 16px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-sm);
        color: var(--text-primary);
        font-size: 16px;
    }
    
    .copy-btn {
        padding: 12px 24px;
        background: rgba(139, 92, 246, 0.1);
        border: 1px solid var(--purple-primary);
        border-radius: var(--radius-sm);
        color: var(--purple-light);
        cursor: pointer;
        transition: var(--transition);
    }
    
    /* 移除copy-btn的悬浮动画 */
    
    .loading-spinner {
        width: 50px;
        height: 50px;
        margin: 20px auto;
        border: 3px solid rgba(255, 255, 255, 0.1);
        border-top-color: var(--purple-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    .verification-code {
        font-size: 48px;
        font-weight: 900;
        color: var(--purple-primary);
        letter-spacing: 8px;
        text-shadow: 0 0 30px var(--purple-glow);
        display: block;
        text-align: center;
        margin: 30px 0;
    }
    
    .info-text {
        text-align: center;
        color: var(--text-secondary);
        margin: 20px 0;
    }
    
    .settings-content {
        min-width: 600px;
        display: flex;
        flex-direction: column;
        height: min(500px, 70vh); /* 固定高度，但不超过屏幕的70%，避免切换标签时高度变化 */
        overflow: hidden;
    }

    .settings-body {
        flex: 1;
        overflow-y: auto;
        padding: 20px 0;
        margin-right: -6px;
        padding-right: 6px;
        min-height: 0; /* 确保flex子项可以收缩 */
    }

    /* 确保标签页内容可以正常滚动 */
    .tab-content {
        min-height: 0;
        overflow: visible;
    }

    /* 为高级设置添加底部间距，确保最后一项可见 */
    #advancedTab {
        padding-bottom: 20px;
    }

    #emailTab {
        padding-bottom: 20px;
    }

    .settings-footer {
        border-top: 1px solid var(--border-color);
        padding: 20px 0 0 0;
        margin-top: auto;
    }
    
    .settings-tabs {
        display: flex;
        gap: 16px;
        margin-bottom: 30px;
        border-bottom: 1px solid var(--border-color);
    }
    
    .tab-btn {
        padding: 12px 24px;
        background: transparent;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        transition: var(--transition);
        position: relative;
    }
    
    .tab-btn.active {
        color: var(--purple-primary);
    }
    
    .tab-btn.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--purple-primary);
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: var(--text-secondary);
        font-size: 14px;
    }
    
    .form-group input[type="text"],
    .form-group input[type="email"],
    .form-group input[type="password"],
    .form-group input[type="number"] {
        width: 100%;
        padding: 12px 16px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-sm);
        color: var(--text-primary);
        font-size: 16px;
        transition: var(--transition);
    }
    
    .form-group input:focus {
        outline: none;
        border-color: var(--purple-primary);
        background: rgba(139, 92, 246, 0.05);
    }
    
    .radio-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .radio-group label {
        display: flex;
        align-items: center;
        cursor: pointer;
    }
    
    .radio-group input[type="radio"] {
        margin-right: 8px;
    }
    
    .email-config-section {
        margin-top: 20px;
        padding: 20px;
        background: rgba(255, 255, 255, 0.03);
        border-radius: var(--radius-md);
    }
    
    .email-config-section h5 {
        margin-bottom: 16px;
        color: var(--purple-light);
    }
    
    .editor-switch {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .editor-switch label {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--radius-sm);
        transition: var(--transition);
    }
    
    .editor-switch label:hover {
        background: rgba(139, 92, 246, 0.1);
    }
    
    .editor-switch input[type="radio"] {
        margin-right: 12px;
    }

    /* 单选框组的现代化样式 */
    .radio-setting-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .radio-setting-item {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid rgba(139, 92, 246, 0.1);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        user-select: none;
    }

    .radio-setting-item:hover {
        background: rgba(139, 92, 246, 0.05);
        border-color: rgba(139, 92, 246, 0.2);
    }

    .radio-setting-item input[type="radio"] {
        margin-right: 12px;
        accent-color: var(--purple-primary);
    }

    .radio-setting-item.checked {
        background: rgba(139, 92, 246, 0.1);
        border-color: var(--purple-primary);
    }

    /* 单选框项中的标签和描述样式 - 与setting-item保持一致 */
    .radio-setting-item .setting-label {
        color: var(--text-primary);
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 0;
    }

    .radio-setting-item .setting-description {
        color: var(--text-muted);
        font-size: 13px;
        margin-top: 4px;
    }

    /* 密码输入框容器样式 */
    .password-input-container {
        position: relative;
        display: flex;
        align-items: center;
    }

    .password-input-container input {
        flex: 1;
        padding-right: 45px; /* 为眼睛按钮留出空间 */
    }

    .password-toggle-btn {
        position: absolute;
        right: 12px;
        background: none;
        border: none;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
    }

    /* 移除password-toggle-btn的悬浮动画 */

    .eye-icon {
        width: 16px;
        height: 16px;
        color: var(--text-secondary);
        transition: color 0.2s ease;
    }

    /* Toggle Switch 样式 */
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 24px;
        flex-shrink: 0;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.1);
        transition: 0.3s ease;
        border-radius: 24px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: 0.3s ease;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    /* 选中状态 - 支持:checked伪类和.checked类 */
    .toggle-switch input:checked + .toggle-slider,
    .toggle-switch.checked .toggle-slider {
        background-color: var(--purple-primary);
        border-color: var(--purple-primary);
    }

    .toggle-switch input:checked + .toggle-slider:before,
    .toggle-switch.checked .toggle-slider:before {
        transform: translateX(20px);
    }

    /* Switch Group 样式 */
    .switch-group {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid rgba(139, 92, 246, 0.1);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        user-select: none;
        margin-bottom: 16px;
    }

    /* 移除switch-group的悬浮动画 */

    .switch-label {
        color: var(--text-primary);
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 0;
    }

    .switch-description {
        color: var(--text-muted);
        font-size: 13px;
        margin-top: 4px;
    }

`;
document.head.appendChild(style);

// 验证码状态轮询机制
let verificationStatusCheckInterval = null;
let verificationCompleteHandled = false;

function startVerificationStatusPolling() {

    // 重置完成标志
    verificationCompleteHandled = false;

    // 清除之前的轮询
    if (verificationStatusCheckInterval) {
        clearInterval(verificationStatusCheckInterval);
        verificationStatusCheckInterval = null;
    }

    let pollCount = 0;
    const maxPolls = 300; // 5分钟超时 (300 * 1秒)

    verificationStatusCheckInterval = setInterval(() => {
        pollCount++;

        if (pollCount >= maxPolls) {

            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;
            handleVerificationCompleted(false, "", "获取验证码超时");
            return;
        }

        if (bridge && bridge.get_verification_status) {
            try {

                // 检查方法是否存在

                // 尝试直接调用同步方法
                try {

                    const statusJson = bridge.get_verification_status();

                    // 检查返回值
                    if (statusJson === undefined) {

                        return;
                    }

                    // 检查是否是Promise对象
                    if (statusJson && typeof statusJson.then === 'function') {

                        statusJson.then(actualJson => {

                            processVerificationStatus(actualJson);
                        }).catch(error => {

                        });
                    } else {

                        processVerificationStatus(statusJson);
                    }
                } catch (directError) {

                    // 回退到回调方式
                    try {
                        bridge.get_verification_status((statusJson) => {

                            processVerificationStatus(statusJson);
                        });
                    } catch (callbackError) {

                    }
                }

            } catch (error) {

            }
        } else {

        }
    }, 1000); // 每秒轮询一次
}

// 新的验证码状态轮询机制
function startVerificationStatusPollingNew() {

    // 重置完成标志
    verificationCompleteHandled = false;

    // 清除可能存在的旧轮询
    if (verificationStatusCheckInterval) {
        clearInterval(verificationStatusCheckInterval);
        verificationStatusCheckInterval = null;
    }

    let pollCount = 0;
    const maxPolls = 300; // 5分钟超时

    verificationStatusCheckInterval = setInterval(() => {
        pollCount++;

        if (pollCount >= maxPolls) {

            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;
            handleVerificationCompleted(false, "", "获取验证码超时");
            return;
        }

        if (bridge && bridge.get_verification_status) {
            try {

                // 直接同步调用
                const statusJson = bridge.get_verification_status();

                if (statusJson) {
                    processVerificationStatusNew(statusJson);
                } else {

                }

            } catch (error) {

            }
        } else {

        }
    }, 1000); // 每秒轮询一次
}

// 处理验证码状态的通用函数
function processVerificationStatus(statusJson) {
    try {
        const status = JSON.parse(statusJson);

        // 检查是否完成
        if (status.completed && !verificationCompleteHandled) {

            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;

            // 移除轮询中的toast显示，由信号处理

            // 处理完成结果
            handleVerificationCompleted(status.success, status.code || "", status.message || "");
        } else if (status.is_running) {

            // 更新进度显示
            if (status.message) {
                updateVerificationStatus('running', status.message);

                // 更新进度条
                if (status.progress !== undefined) {
                    const progressBar = document.getElementById('verificationProgressBar');
                    if (progressBar) {
                        const percentage = status.progress * 100;
                        progressBar.style.width = `${percentage}%`;
                    }
                }
            }
        }
    } catch (parseError) {

    }
}

// 新版处理验证码状态的函数
function processVerificationStatusNew(statusJson) {
    try {
        const status = JSON.parse(statusJson);

        // 检查是否完成
        if (status.completed && !verificationCompleteHandled) {

            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;

            // 处理完成结果
            handleVerificationCompleted(status.success, status.code || "", status.message || "");
        } else if (status.is_running) {

            // 可以在这里更新进度显示
            if (status.message) {
                updateVerificationStatus('running', status.message);
            }
        }
    } catch (parseError) {

    }
}

// 添加按钮加载状态的CSS样式
const buttonLoadingStyle = document.createElement('style');
buttonLoadingStyle.textContent = `
    /* 小型加载图标 */
    .loading-spinner-small {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-top-color: rgba(139, 92, 246, 1);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        flex-shrink: 0;
        margin-top: 2px;     /* 向下微调2px */
        align-self: center;
    }

    /* 加载文字 - 与按钮原始文字样式保持一致 */
    .loading-text {
        color: white;
        font-size: 15px;
        font-weight: 600;
        line-height: 1.2;    /* 稍微增加行高 */
        align-self: center;
        margin-top: 1px;     /* 向下微调1px */
    }

    /* 按钮加载状态布局 - 完美对齐 */
    .btn-loading {
        display: none;
        align-items: center;
        justify-content: center;
        gap: 8px;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
    }

    /* 按钮加载状态显示时 */
    .workflow-btn.loading .btn-loading {
        display: flex !important;
        opacity: 1;
        transform: scale(1);
    }

    /* 隐藏原始内容当加载时 */
    .workflow-btn.loading .btn-content {
        opacity: 0;
        transform: scale(0.95);
        pointer-events: none;
    }

    /* 按钮内容过渡动画 */
    .btn-content {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        opacity: 1;
        transform: scale(1);
    }

    /* 加载状态过渡动画 */
    .btn-loading {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        opacity: 0;
        transform: scale(0.95);
    }

    /* 旋转动画 */
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(buttonLoadingStyle);

// ===== Augment账号管理功能 =====

// 账号管理状态
let augmentAccountState = {
    token: null,
    isTokenValid: false,
    accountData: null,
    isQuerying: false,
    isFortifying: false
};

// Token对话框管理
function showTokenDialog() {
    const overlay = document.getElementById('tokenDialogOverlay');
    const input = document.getElementById('tokenInput');

    if (overlay && input) {
        // 如果已有token，预填充
        if (augmentAccountState.token) {
            input.value = augmentAccountState.token;
        }

        overlay.classList.add('show');

        // 延迟聚焦，确保动画完成
        setTimeout(() => {
            input.focus();
        }, 300);
    }
}

function hideTokenDialog() {
    const overlay = document.getElementById('tokenDialogOverlay');
    if (overlay) {
        overlay.classList.remove('show');
    }
}

function saveToken() {
    const input = document.getElementById('tokenInput');
    if (!input) return;

    let token = input.value.trim();

    // 清理token中的所有空格
    token = token.replace(/\s/g, '');

    if (!token) {
        showToast('请输入有效的Token', 'error');
        return;
    }

    // 保存token到内存
    augmentAccountState.token = token;
    augmentAccountState.isTokenValid = false;

    // 隐藏对话框
    hideTokenDialog();

    // 启用按钮
    enableAccountButtons();

    // 清空之前的数据
    clearAccountData();

    showToast('Token已保存，可以开始使用账号功能', 'success');
}

// 启用账号管理按钮
function enableAccountButtons() {
    const queryBtn = document.getElementById('queryAccountBtn');

    if (queryBtn) {
        queryBtn.disabled = false;
    }
}

// 清空账号数据显示（带动画）
function clearAccountData() {
    const elements = {
        'accountEmail': '未获取',
        'accountPlan': '未获取',
        'accountStatus': '未获取',
        'accountExpiry': '未获取',
        'fortificationStatus': '未获取',
        'creditsUsed': '0',
        'creditsTotal': '0'
    };

    // 添加清空动画的通用函数
    function clearElementWithAnimation(element, newContent) {
        if (!element) return;

        // 淡出
        element.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
        element.style.opacity = '0';
        element.style.transform = 'translateY(-10px)';

        setTimeout(() => {
            // 更新内容
            element.textContent = newContent;

            // 重置颜色为默认（与其他数据项保持一致）
            if (element.id === 'accountStatus' || element.id === 'fortificationStatus') {
                element.style.color = ''; // 清除内联样式，使用CSS默认样式
            }

            // 淡入
            element.style.transform = 'translateY(0)';
            element.style.opacity = '1';
        }, 200);
    }

    // 使用动画清空所有元素
    Object.entries(elements).forEach(([id, defaultValue]) => {
        const element = document.getElementById(id);
        clearElementWithAnimation(element, defaultValue);
    });

    // 重置进度圆环（带动画）
    setTimeout(() => {
        updateCreditsProgress(0);
    }, 100);

    // 重置百分比显示
    const percentageElement = document.getElementById('creditsPercentage');
    clearElementWithAnimation(percentageElement, '0%');

    // 重置查询按钮为初始状态
    const queryBtn = document.getElementById('queryAccountBtn');
    resetQueryButtonToInitial(queryBtn);

    augmentAccountState.accountData = null;
}

// 更新积分进度圆环（带动画）
function updateCreditsProgress(percentage) {
    const progressBar = document.getElementById('circleProgressBar');
    const percentageElement = document.getElementById('creditsPercentage');

    if (progressBar && percentageElement) {
        const circumference = 471; // 2 * π * 75
        const targetOffset = circumference - (percentage / 100) * circumference;

        // 获取当前进度
        const currentOffset = parseFloat(progressBar.style.strokeDashoffset) || circumference;

        // 添加过渡动画
        progressBar.style.transition = 'stroke-dashoffset 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
        progressBar.style.strokeDashoffset = targetOffset;

        // 数字动画
        const currentPercentage = parseFloat(percentageElement.textContent) || 0;
        animateNumber(percentageElement, currentPercentage, percentage, 800, '%');
    }
}

// 数字动画函数
function animateNumber(element, from, to, duration, suffix = '') {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeProgress = 1 - Math.pow(1 - progress, 3);
        const currentValue = from + (to - from) * easeProgress;

        element.textContent = `${Math.round(currentValue)}${suffix}`;

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

// 账号查询功能
async function queryAccount() {
    if (!augmentAccountState.token) {
        showToast('请先输入Token', 'error');
        showTokenDialog();
        return;
    }

    if (augmentAccountState.isQuerying) {
        return;
    }

    const queryBtn = document.getElementById('queryAccountBtn');
    if (!queryBtn) return;

    try {
        augmentAccountState.isQuerying = true;

        // 设置按钮加载状态
        setButtonLoading(queryBtn, true);

        // 显示开始通知
        showToast('正在查询账号信息...', 'info');

        // 调用后端API（异步）
        const result = await window.__TAURI__.core.invoke('query_augment_account', {
            token: augmentAccountState.token
        }).catch(error => {
            console.error('调用query_augment_account失败:', error);
            return {success: false, error: 'network_error'};
        });

        if (result.success) {
            augmentAccountState.isTokenValid = true;
            augmentAccountState.accountData = result.data;

            // 更新UI显示
            updateAccountDisplay(result.data);

            // 切换按钮为刷新模式
            switchToRefreshMode(queryBtn);

            showToast('账号信息获取成功', 'success');
        } else {
            if (result.error === 'invalid_token') {
                showToast('Token无效，请重新输入', 'error');
                augmentAccountState.isTokenValid = false;
                showTokenDialog();
            } else {
                showToast('网络错误', 'error');
            }
        }
    } catch (error) {
        console.error('查询账号失败:', error);
        showToast('网络错误', 'error');
    } finally {
        augmentAccountState.isQuerying = false;
        setButtonLoading(queryBtn, false);
    }
}

// 更新账号信息显示（带动画）
function updateAccountDisplay(data) {
    if (!data) return;

    // 提取用户信息
    const user = data.user || {};
    const subscription = data.subscription || {};
    const credits = data.credits || {};

    // 添加淡入动画的通用函数
    function updateElementWithAnimation(element, newContent, isHtml = false) {
        if (!element) return;

        // 淡出
        element.style.transition = 'opacity 0.3s ease';
        element.style.opacity = '0';

        setTimeout(() => {
            // 更新内容
            if (isHtml) {
                element.innerHTML = newContent;
            } else {
                element.textContent = newContent;
            }

            // 淡入
            element.style.opacity = '1';
        }, 150);
    }

    // 更新邮箱
    const emailElement = document.getElementById('accountEmail');
    updateElementWithAnimation(emailElement, user.email || '未知');

    // 更新计划信息
    const planElement = document.getElementById('accountPlan');
    if (planElement) {
        const planName = subscription.planName || '未知';
        const planType = subscription.augmentPlanType || '';
        const planText = planType ? `${planName} (${planType})` : planName;
        updateElementWithAnimation(planElement, planText);
    }

    // 更新有效性状态
    const statusElement = document.getElementById('accountStatus');
    if (statusElement) {
        const isExpired = subscription.planIsExpired || false;
        const statusText = isExpired ? '❌ 已过期' : '✅ 有效';
        const statusColor = isExpired ? '#ef4444' : '#22c55e';

        // 淡出
        statusElement.style.transition = 'opacity 0.3s ease';
        statusElement.style.opacity = '0';

        setTimeout(() => {
            statusElement.textContent = statusText;
            statusElement.style.color = statusColor;
            statusElement.style.opacity = '1';
        }, 150);
    }

    // 更新到期时间
    const expiryElement = document.getElementById('accountExpiry');
    if (expiryElement) {
        const trialEnd = subscription.trialPeriodEnd || subscription.subscriptionEndDate || '';
        let expiryText = '未知';

        if (trialEnd) {
            try {
                const date = new Date(trialEnd);
                expiryText = date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch {
                expiryText = trialEnd.substring(0, 16);
            }
        }

        updateElementWithAnimation(expiryElement, expiryText);
    }



    // 更新积分信息
    const available = credits.usageUnitsAvailable || 0;
    const used = credits.usageUnitsUsedThisBillingCycle || 0;
    const total = available + used;
    const percentage = total > 0 ? (used / total * 100) : 0;

    const creditsUsedElement = document.getElementById('creditsUsed');
    const creditsTotalElement = document.getElementById('creditsTotal');

    // 使用动画更新积分数字
    updateElementWithAnimation(creditsUsedElement, used.toString());
    updateElementWithAnimation(creditsTotalElement, total.toString());

    // 更新进度圆环
    updateCreditsProgress(percentage);
}

// 切换查询按钮为刷新模式
function switchToRefreshMode(button) {
    if (!button) return;

    button.classList.add('refresh-mode');
    const btnText = button.querySelector('.btn-text');
    if (btnText) {
        btnText.textContent = '刷新';
    }

    // 更新图标为刷新图标
    const btnIcon = button.querySelector('.btn-icon svg path');
    if (btnIcon) {
        btnIcon.setAttribute('d', 'M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z');
    }
}

// 重置查询按钮为初始状态
function resetQueryButtonToInitial(button) {
    if (!button) return;

    button.classList.remove('refresh-mode');
    const btnText = button.querySelector('.btn-text');
    if (btnText) {
        btnText.textContent = '账号查询';
    }

    // 重置图标为查询图标
    const btnIcon = button.querySelector('.btn-icon svg path');
    if (btnIcon) {
        btnIcon.setAttribute('d', 'M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z');
    }
}



// 设置按钮加载状态
function setButtonLoading(button, isLoading) {
    if (!button) return;

    const btnContent = button.querySelector('.btn-content');
    const btnLoading = button.querySelector('.btn-loading');

    if (isLoading) {
        button.classList.add('loading');
        button.disabled = true;

        // 使用CSS类控制显示，而不是直接设置style
        if (btnContent) {
            btnContent.style.opacity = '0';
            btnContent.style.visibility = 'hidden';
        }
        if (btnLoading) {
            btnLoading.classList.remove('hidden');
            btnLoading.style.opacity = '1';
            btnLoading.style.visibility = 'visible';
        }
    } else {
        button.classList.remove('loading');
        button.disabled = false;

        if (btnContent) {
            btnContent.style.opacity = '1';
            btnContent.style.visibility = 'visible';
        }
        if (btnLoading) {
            btnLoading.classList.add('hidden');
            btnLoading.style.opacity = '0';
            btnLoading.style.visibility = 'hidden';
        }
    }
}

// 初始化账号管理组件
function initAugmentAccountManager() {
    // 添加Token对话框点击外部关闭功能
    const tokenOverlay = document.getElementById('tokenDialogOverlay');
    if (tokenOverlay) {
        tokenOverlay.addEventListener('click', (e) => {
            if (e.target === tokenOverlay) {
                hideTokenDialog();
            }
        });
    }

    // 添加ESC键关闭对话框
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const tokenOverlay = document.getElementById('tokenDialogOverlay');
            if (tokenOverlay && tokenOverlay.classList.contains('show')) {
                hideTokenDialog();
            }
        }
    });

    // 初始化进度圆环
    updateCreditsProgress(0);

    // 清空初始数据
    clearAccountData();

    console.log('Augment账号管理组件初始化完成');
}

// 在页面加载完成后初始化账号管理
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保其他组件先加载
    setTimeout(() => {
        initAugmentAccountManager();
    }, 500);
});

/**
 * 显示黑名单消息对话框
 * 对应原版Python的_show_blacklist_message方法
 * @param {string} qqNumber - QQ号码
 * @returns {Promise<void>}
 */
function showBlacklistDialog(qqNumber) {
    return new Promise((resolve) => {
        const dialogHTML = `
            <div class="error-dialog-overlay" id="blacklistDialogOverlay">
                <div class="error-dialog">
                    <div class="error-dialog-content">
                        <div class="error-dialog-icon">🚫</div>
                        <h3 class="error-dialog-title">访问拒绝</h3>
                        <div class="error-dialog-text">
                            您的QQ账号 ${qqNumber} 已被列入黑名单，无法使用YAugment。<br><br>
                            如有疑问，请发送电子邮件给我 <EMAIL>
                        </div>
                        <div class="error-dialog-actions">
                            <button class="error-dialog-btn" onclick="closeBlacklistDialog()">退出</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.blacklistDialogResolve = resolve;

        const overlay = document.getElementById('blacklistDialogOverlay');
        setTimeout(() => overlay.classList.add('show'), 10);
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭黑名单对话框
 */
function closeBlacklistDialog() {
    const overlay = document.getElementById('blacklistDialogOverlay');
    if (!overlay) return;

    document.body.style.overflow = '';
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.blacklistDialogResolve) {
            window.blacklistDialogResolve();
            delete window.blacklistDialogResolve;
        }
    }, 300);
}

/**
 * 显示验证错误对话框
 * 对应原版Python的_show_verification_error方法
 * @param {string} errorMessage - 错误消息
 * @returns {Promise<void>}
 */
function showVerificationErrorDialog(errorMessage) {
    return new Promise((resolve) => {
        const dialogHTML = `
            <div class="error-dialog-overlay" id="verificationErrorDialogOverlay">
                <div class="error-dialog">
                    <div class="error-dialog-content">
                        <div class="error-dialog-icon">❌</div>
                        <h3 class="error-dialog-title">验证错误</h3>
                        <div class="error-dialog-text">
                            ${errorMessage}<br><br>
                            无法完成QQ群验证，程序将退出。
                        </div>
                        <div class="error-dialog-actions">
                            <button class="error-dialog-btn" onclick="closeVerificationErrorDialog()">退出</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.verificationErrorDialogResolve = resolve;

        const overlay = document.getElementById('verificationErrorDialogOverlay');
        setTimeout(() => overlay.classList.add('show'), 10);
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭验证错误对话框
 */
function closeVerificationErrorDialog() {
    const overlay = document.getElementById('verificationErrorDialogOverlay');
    if (!overlay) return;

    document.body.style.overflow = '';
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.verificationErrorDialogResolve) {
            window.verificationErrorDialogResolve();
            delete window.verificationErrorDialogResolve;
        }
    }, 300);
}

/**
 * 显示SSL错误对话框
 * 对应原版Python的_show_ssl_error_message方法
 * @param {string} errorDetails - SSL错误详细信息
 * @returns {Promise<boolean>} - 用户是否选择继续
 */
function showSSLErrorDialog(errorDetails) {
    return new Promise((resolve) => {
        const dialogHTML = `
            <div class="error-dialog-overlay" id="sslErrorDialogOverlay">
                <div class="error-dialog">
                    <div class="error-dialog-content">
                        <div class="error-dialog-icon">⚠️</div>
                        <h3 class="error-dialog-title">SSL证书问题</h3>
                        <div class="error-dialog-text">
                            连接到验证服务器时遇到SSL证书问题：<br><br>
                            ${errorDetails}<br><br>
                            这可能是因为系统时间不正确、系统证书过期或网络环境问题。<br><br>
                            您可以选择忽略此错误并继续使用程序，但这可能存在安全风险。
                        </div>
                        <div class="error-dialog-actions">
                            <button class="error-dialog-btn secondary" onclick="closeSSLErrorDialog(false)">退出</button>
                            <button class="error-dialog-btn" onclick="closeSSLErrorDialog(true)">忽略并继续</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.sslErrorDialogResolve = resolve;

        const overlay = document.getElementById('sslErrorDialogOverlay');
        setTimeout(() => overlay.classList.add('show'), 10);
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭SSL错误对话框
 * @param {boolean} continueAnyway - 是否继续
 */
function closeSSLErrorDialog(continueAnyway) {
    const overlay = document.getElementById('sslErrorDialogOverlay');
    if (!overlay) return;

    document.body.style.overflow = '';
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.sslErrorDialogResolve) {
            window.sslErrorDialogResolve(continueAnyway);
            delete window.sslErrorDialogResolve;
        }
    }, 300);
}

/**
 * 显示严重配置错误对话框
 * 对应原版Python的_show_critical_config_error方法
 * @param {string} detailMessage - 详细错误信息
 * @returns {Promise<void>}
 */
function showCriticalConfigErrorDialog(detailMessage) {
    return new Promise((resolve) => {
        const dialogHTML = `
            <div class="error-dialog-overlay" id="criticalConfigErrorDialogOverlay">
                <div class="error-dialog">
                    <div class="error-dialog-content">
                        <div class="error-dialog-icon">💥</div>
                        <h3 class="error-dialog-title">获取的配置错误</h3>
                        <div class="error-dialog-text">
                            无法加载配置，关键信息缺失或格式不正确<br><br>
                            请更换网络或更新 YAugment 版本后重试<br><br>
                            ${detailMessage ? `详细信息：${detailMessage}` : ''}
                        </div>
                        <div class="error-dialog-actions">
                            <button class="error-dialog-btn" onclick="closeCriticalConfigErrorDialog()">退出</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.criticalConfigErrorDialogResolve = resolve;

        const overlay = document.getElementById('criticalConfigErrorDialogOverlay');
        setTimeout(() => overlay.classList.add('show'), 10);
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭严重配置错误对话框
 */
function closeCriticalConfigErrorDialog() {
    const overlay = document.getElementById('criticalConfigErrorDialogOverlay');
    if (!overlay) return;

    document.body.style.overflow = '';
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.criticalConfigErrorDialogResolve) {
            window.criticalConfigErrorDialogResolve();
            delete window.criticalConfigErrorDialogResolve;
        }
    }, 300);
}

/**
 * 显示通知消息
 * @param {string} message - 通知消息
 * @param {string} level - 通知级别 (info, warning, error)
 */
function showNotification(message, level = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${level}`;
    notification.innerHTML = `
        <div class="notification-content">
            <div class="notification-icon">
                ${level === 'error' ? '❌' : level === 'warning' ? '⚠️' : 'ℹ️'}
            </div>
            <div class="notification-message">${message}</div>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // 自动关闭（除了错误通知）
    if (level !== 'error') {
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }
}

