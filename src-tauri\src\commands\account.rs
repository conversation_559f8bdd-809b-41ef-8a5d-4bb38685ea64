use tauri::{State, AppHandle, Emitter};
use crate::app_state::AppState;
use crate::core::account::AugmentAccountManager;
use serde_json::Value;

/// 查询 Augment 账号信息
/// 对应原版 Python 的 query_augment_account 方法
#[tauri::command]
pub async fn query_augment_account(
    token: String,
    app_handle: AppHandle,
    state: State<'_, AppState>,
) -> Result<Value, String> {
    let _app_state = state.inner();

    // 创建账号管理器实例
    let mut account_manager = AugmentAccountManager::new()
        .map_err(|e| e.to_string())?;

    // 设置进度回调
    let app_handle_clone = app_handle.clone();
    account_manager.set_progress_callback(move |message, progress| {
        let _ = app_handle_clone.emit("augment_progress", serde_json::json!({
            "message": message,
            "progress": progress
        }));
    }).await;

    // 设置token
    if !account_manager.set_token(token) {
        return Ok(serde_json::json!({
            "success": false,
            "error": "invalid_token"
        }));
    }

    // 验证token
    if !account_manager.verify_token().await {
        return Ok(serde_json::json!({
            "success": false,
            "error": "invalid_token"
        }));
    }

    // 获取账号信息
    match account_manager.get_account_info().await {
        Some(account_data) => {
            Ok(serde_json::json!({
                "success": true,
                "data": account_data
            }))
        }
        None => {
            Ok(serde_json::json!({
                "success": false,
                "error": "network_error"
            }))
        }
    }
}



// ==================== 新增命令：基于Portal Token的账号管理 ====================

/// 验证Session Token有效性
/// 在获取Portal Token之前先验证Session Token是否有效
#[tauri::command]
pub async fn verify_session_token(
    token: String,
    app_handle: AppHandle,
    state: State<'_, AppState>,
) -> Result<Value, String> {
    let _app_state = state.inner();

    // 创建账号管理器实例
    let mut account_manager = AugmentAccountManager::new()
        .map_err(|e| e.to_string())?;

    // 设置进度回调
    let app_handle_clone = app_handle.clone();
    account_manager.set_progress_callback(move |message, progress| {
        let _ = app_handle_clone.emit("augment_progress", serde_json::json!({
            "message": message,
            "progress": progress
        }));
    }).await;

    // 设置token
    if !account_manager.set_token(token) {
        return Ok(serde_json::json!({
            "success": false,
            "error": "invalid_token",
            "message": "Session token格式无效"
        }));
    }

    // 验证token有效性
    let verification_result = account_manager.verify_token_and_get_email().await;

    if verification_result.success {
        Ok(serde_json::json!({
            "success": true,
            "email": verification_result.email,
            "message": "Session Token验证成功"
        }))
    } else {
        Ok(serde_json::json!({
            "success": false,
            "error": "invalid_token",
            "message": "Session Token无效"
        }))
    }
}

/// 获取Portal Token
/// 使用session token获取Portal Token并保存账号
#[tauri::command]
pub async fn get_portal_token(
    token: String,
    app_handle: AppHandle,
    state: State<'_, AppState>,
) -> Result<Value, String> {
    let _app_state = state.inner();

    // 创建账号管理器实例
    let mut account_manager = AugmentAccountManager::new()
        .map_err(|e| e.to_string())?;

    // 设置进度回调
    let app_handle_clone = app_handle.clone();
    account_manager.set_progress_callback(move |message, progress| {
        let _ = app_handle_clone.emit("augment_progress", serde_json::json!({
            "message": message,
            "progress": progress
        }));
    }).await;

    // 设置token
    if !account_manager.set_token(token) {
        return Ok(serde_json::json!({
            "success": false,
            "error": "invalid_token",
            "message": "Session token格式无效"
        }));
    }

    // 先验证token有效性
    let verification_result = account_manager.verify_token_and_get_email().await;
    if !verification_result.success {
        return Ok(serde_json::json!({
            "success": false,
            "error": "invalid_token",
            "message": "Session Token无效，请重新输入"
        }));
    }

    // 获取Portal Token并保存账号
    match account_manager.get_portal_token_and_save().await {
        Ok(result) => {
            Ok(serde_json::json!({
                "success": true,
                "message": result
            }))
        }
        Err(e) => {
            Ok(serde_json::json!({
                "success": false,
                "error": "portal_token_failed",
                "message": e.to_string()
            }))
        }
    }
}

/// 使用Portal Token查询账号信息
/// 提供完整的账号数据查询功能
#[tauri::command]
pub async fn query_account_with_portal(
    portal_token: String,
    app_handle: AppHandle,
    state: State<'_, AppState>,
) -> Result<Value, String> {
    let _app_state = state.inner();

    // 创建账号管理器实例
    let account_manager = AugmentAccountManager::new()
        .map_err(|e| e.to_string())?;

    // 设置进度回调
    let app_handle_clone = app_handle.clone();
    account_manager.set_progress_callback(move |message, progress| {
        let _ = app_handle_clone.emit("augment_progress", serde_json::json!({
            "message": message,
            "progress": progress
        }));
    }).await;

    // 使用Portal Token查询账号信息
    match account_manager.query_with_portal_token(&portal_token).await {
        Ok(account_data) => {
            Ok(serde_json::json!({
                "success": true,
                "data": {
                    "email": account_data.email,
                    "plan_name": account_data.plan_name,
                    "status": account_data.status,
                    "end_date": account_data.end_date,
                    "credits_balance": account_data.credits_balance,
                    "total_credits": account_data.total_credits,
                    "validity": account_data.validity,
                    "usage_display": account_data.usage_display
                }
            }))
        }
        Err(e) => {
            Ok(serde_json::json!({
                "success": false,
                "error": "query_failed",
                "message": e.to_string()
            }))
        }
    }
}

/// 使用Portal Token查询账号信息并更新创建时间（如果需要）
/// 专门用于需要更新时间的场景
#[tauri::command]
pub async fn query_account_with_portal_and_update_time(
    portal_token: String,
    app_handle: AppHandle,
    state: State<'_, AppState>,
) -> Result<Value, String> {
    let _app_state = state.inner();

    // 创建账号管理器实例
    let mut account_manager = AugmentAccountManager::new()
        .map_err(|e| e.to_string())?;

    // 设置进度回调
    let app_handle_clone = app_handle.clone();
    account_manager.set_progress_callback(move |message, progress| {
        let _ = app_handle_clone.emit("augment_progress", serde_json::json!({
            "message": message,
            "progress": progress
        }));
    }).await;

    // 查询账号信息并更新时间
    match account_manager.query_with_portal_token_and_update_time(&portal_token).await {
        Ok(account_data) => {
            Ok(serde_json::json!({
                "success": true,
                "data": {
                    "email": account_data.email,
                    "plan_name": account_data.plan_name,
                    "status": account_data.status,
                    "end_date": account_data.end_date,
                    "credits_balance": account_data.credits_balance,
                    "total_credits": account_data.total_credits,
                    "validity": account_data.validity,
                    "usage_display": account_data.usage_display
                }
            }))
        }
        Err(e) => {
            Ok(serde_json::json!({
                "success": false,
                "error": "query_failed",
                "message": e.to_string()
            }))
        }
    }
}

/// 获取保存的账号列表
/// 返回所有保存在本地的账号信息
#[tauri::command]
pub async fn get_saved_accounts(
    state: State<'_, AppState>,
) -> Result<Value, String> {
    let _app_state = state.inner();

    // 创建账号管理器实例
    let account_manager = AugmentAccountManager::new()
        .map_err(|e| e.to_string())?;

    // 获取保存的账号列表
    match account_manager.get_saved_accounts() {
        Ok(accounts) => {
            let accounts_data: Vec<Value> = accounts.iter().map(|account| {
                serde_json::json!({
                    "email": account.email,
                    "account_creation_time": account.account_creation_time,
                    "portal_token_preview": if account.portal_token.len() > 20 {
                        format!("{}...", &account.portal_token[..20])
                    } else {
                        account.portal_token.clone()
                    }
                })
            }).collect();

            Ok(serde_json::json!({
                "success": true,
                "data": accounts_data,
                "count": accounts.len()
            }))
        }
        Err(e) => {
            Ok(serde_json::json!({
                "success": false,
                "error": "get_accounts_failed",
                "message": e.to_string()
            }))
        }
    }
}

/// 根据邮箱获取保存的账号
/// 查询特定邮箱的账号信息
#[tauri::command]
pub async fn get_saved_account_by_email(
    email: String,
    state: State<'_, AppState>,
) -> Result<Value, String> {
    let _app_state = state.inner();

    // 创建账号管理器实例
    let account_manager = AugmentAccountManager::new()
        .map_err(|e| e.to_string())?;

    // 根据邮箱获取账号
    match account_manager.get_saved_account_by_email(&email) {
        Ok(Some(account)) => {
            Ok(serde_json::json!({
                "success": true,
                "data": {
                    "email": account.email,
                    "portal_token": account.portal_token,
                    "account_creation_time": account.account_creation_time
                }
            }))
        }
        Ok(None) => {
            Ok(serde_json::json!({
                "success": false,
                "error": "account_not_found",
                "message": format!("未找到邮箱为 {} 的账号", email)
            }))
        }
        Err(e) => {
            Ok(serde_json::json!({
                "success": false,
                "error": "get_account_failed",
                "message": e.to_string()
            }))
        }
    }
}

/// 删除保存的账号
/// 从本地存储中删除指定邮箱的账号
#[tauri::command]
pub async fn remove_saved_account(
    email: String,
    state: State<'_, AppState>,
) -> Result<Value, String> {
    let _app_state = state.inner();

    // 创建账号管理器实例
    let mut account_manager = AugmentAccountManager::new()
        .map_err(|e| e.to_string())?;

    // 删除账号
    match account_manager.remove_saved_account(&email) {
        Ok(true) => {
            Ok(serde_json::json!({
                "success": true,
                "message": format!("账号 {} 删除成功", email)
            }))
        }
        Ok(false) => {
            Ok(serde_json::json!({
                "success": false,
                "error": "account_not_found",
                "message": format!("未找到邮箱为 {} 的账号", email)
            }))
        }
        Err(e) => {
            Ok(serde_json::json!({
                "success": false,
                "error": "remove_account_failed",
                "message": e.to_string()
            }))
        }
    }
}





/// 获取机器UUID
/// 返回当前机器的唯一标识符
#[tauri::command]
pub async fn get_machine_uuid(
    state: State<'_, AppState>,
) -> Result<Value, String> {
    let _app_state = state.inner();

    // 创建账号管理器实例
    let account_manager = AugmentAccountManager::new()
        .map_err(|e| e.to_string())?;

    // 获取机器UUID
    if let Some(uuid) = account_manager.get_machine_uuid() {
        Ok(serde_json::json!({
            "success": true,
            "uuid": uuid
        }))
    } else {
        Ok(serde_json::json!({
            "success": false,
            "error": "uuid_not_available",
            "message": "机器UUID未获取"
        }))
    }
}

/// 切换到社区计划
/// 对应api_request.py的功能
#[tauri::command]
pub async fn switch_to_community_plan(
    session_token: String,
    state: State<'_, AppState>,
) -> Result<Value, String> {
    let _app_state = state.inner();

    // 创建账号管理器实例
    let account_manager = AugmentAccountManager::new()
        .map_err(|e| e.to_string())?;

    // 先验证session token
    match account_manager.verify_session_token(&session_token).await {
        Ok(verify_result) => {
            if !verify_result.get("success").and_then(|v| v.as_bool()).unwrap_or(false) {
                return Ok(serde_json::json!({
                    "success": false,
                    "error": "invalid_token",
                    "message": "Session Token无效"
                }));
            }
        }
        Err(e) => {
            return Ok(serde_json::json!({
                "success": false,
                "error": "verification_failed",
                "message": format!("Token验证失败: {}", e)
            }));
        }
    }

    // 调用切换社区计划API
    match account_manager.switch_to_community_plan(&session_token).await {
        Ok(result) => {
            Ok(serde_json::json!({
                "success": result.success,
                "message": result.message,
                "type": result.response_type,
                "data": result.data
            }))
        }
        Err(e) => {
            Ok(serde_json::json!({
                "success": false,
                "error": "api_error",
                "message": format!("切换失败: {}", e)
            }))
        }
    }
}
