// 全局变量
let bridge = null;
let currentPage = 'editorSelect';
let selectedEditor = '';
let resetStatusCheckInterval = null;

// 全局错误处理 - 确保所有错误都会显示toast
window.addEventListener('error', function(event) {
    const errorMessage = `JavaScript错误: ${event.error?.message || event.message}`;
    if (typeof showToast === 'function') {
        showToast(errorMessage, 'error', 6000);
    }
    console.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', function(event) {
    const errorMessage = `Promise错误: ${event.reason?.message || event.reason}`;
    if (typeof showToast === 'function') {
        showToast(errorMessage, 'error', 6000);
    }
    console.error('Unhandled promise rejection:', event.reason);
});

// 包装所有异步函数调用，确保错误被捕获
function safeCall(fn, ...args) {
    try {
        const result = fn(...args);
        if (result && typeof result.catch === 'function') {
            result.catch(error => {
                const errorMessage = `操作失败: ${error.message || error}`;
                showToast(errorMessage, 'error', 5000);
                console.error('Async operation failed:', error);
            });
        }
        return result;
    } catch (error) {
        const errorMessage = `操作失败: ${error.message || error}`;
        showToast(errorMessage, 'error', 5000);
        console.error('Sync operation failed:', error);
        throw error;
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    try {
        initWebChannel();
        setupEventListeners();
    } catch (error) {
        console.error('Initialization failed:', error);
        if (typeof showToast === 'function') {
            showToast('应用初始化失败', 'error', 6000);
        }
    }
});

// 初始化Tauri通信桥接
function initWebChannel() {
    // 创建Tauri兼容的bridge对象
    bridge = createTauriBridge();
    window.bridge = bridge;

    // 设置事件监听器
    setupTauriEventListeners();

    // 初始化完成后的操作
    initializeApp();
}

// 创建Tauri兼容的bridge对象
function createTauriBridge() {
    return {
        // 窗口控制
        minimize_window: () => window.__TAURI__.invoke('minimize_window'),
        toggle_maximize: () => window.__TAURI__.invoke('toggle_maximize'),
        close_window: () => window.__TAURI__.invoke('close_window'),
        start_window_drag: (x, y) => window.__TAURI__.invoke('start_window_drag', { x, y }),
        update_window_drag: (x, y) => window.__TAURI__.invoke('update_window_drag', { x, y }),
        end_window_drag: () => window.__TAURI__.invoke('end_window_drag'),

        // 配置管理
        get_config: (callback) => {
            window.__TAURI__.invoke('get_config').then(callback).catch(err => {
                console.error('get_config failed:', err);
                callback(null);
            });
        },
        save_config: (config, callback) => {
            window.__TAURI__.invoke('save_config', { config }).then(result => {
                callback(result);
            }).catch(err => {
                console.error('save_config failed:', err);
                callback(false);
            });
        },

        // 编辑器相关
        get_selected_editor: (callback) => {
            window.__TAURI__.invoke('get_selected_editor').then(callback).catch(err => {
                console.error('get_selected_editor failed:', err);
                callback('');
            });
        },
        select_editor: (editorType, callback) => {
            window.__TAURI__.invoke('select_editor', { editorType }).then(result => {
                callback(result);
            }).catch(err => {
                console.error('select_editor failed:', err);
                callback(false);
            });
        },
        get_editor_status: (callback) => {
            window.__TAURI__.invoke('get_editor_status').then(callback).catch(err => {
                console.error('get_editor_status failed:', err);
                callback({});
            });
        },

        // 应用状态
        is_first_run: (callback) => {
            window.__TAURI__.invoke('is_first_run').then(callback).catch(err => {
                console.error('is_first_run failed:', err);
                callback(false);
            });
        },

        // 重置功能
        reset_augment: () => window.__TAURI__.invoke('reset_augment'),
        get_reset_status: (callback) => {
            window.__TAURI__.invoke('get_reset_status').then(callback).catch(err => {
                console.error('get_reset_status failed:', err);
                callback('{}');
            });
        },

        // 邮箱功能
        generate_email: (callback) => {
            window.__TAURI__.core.invoke('generate_email').then(result => {
                console.log('generate_email result:', result);
                callback(result);
            }).catch(err => {
                console.error('generate_email failed:', err);
                callback(null); // 使用null而不是空字符串，更明确表示失败
            });
        },
        get_verification_code: () => window.__TAURI__.invoke('get_verification_code'),
        test_email_connection: () => window.__TAURI__.invoke('test_email_connection'),

        // 日志
        log_message: (message) => window.__TAURI__.invoke('log_message', { message })
    };
}

// 设置Tauri事件监听器
function setupTauriEventListeners() {
    // 监听来自Rust后端的事件
    window.__TAURI__.listen('show_toast', (event) => {
        const { message, type } = event.payload;
        showToast(message, type);
    });

    window.__TAURI__.listen('update_progress', (event) => {
        const { taskId, progress, message } = event.payload;
        updateProgress(taskId, progress, message);
    });

    window.__TAURI__.listen('update_editor_status', (event) => {
        const { status } = event.payload;
        updateEditorStatus(status);
    });

    window.__TAURI__.listen('show_email_result', (event) => {
        const { email } = event.payload;
        showEmailResultDirect(email);
    });

    window.__TAURI__.listen('show_verification_code', (event) => {
        const { code } = event.payload;
        showVerificationCode(code);
        // 强制显示成功toast - 与原版Python保持一致
        console.log('准备显示验证码获取成功toast:', code);
        const toastResult = showToast(`验证码获取成功: ${code}`, 'success', 6000);
        console.log('showToast 返回结果:', toastResult);

        // 验证码获取成功时重新启用生成邮箱按钮
        const generateEmailBtn = document.getElementById('generateEmailBtn');
        if (generateEmailBtn) {
            generateEmailBtn.disabled = false;
            console.log('验证码获取成功：重新启用生成邮箱按钮');
        }
    });

    window.__TAURI__.listen('verification_complete', (event) => {
        const { success, message } = event.payload;
        if (success) {
            updateVerificationStatus('success', '验证码获取成功');
        } else {
            updateVerificationStatus('error', message || '获取验证码失败');
            // 失败时显示toast - 与原版Python保持一致
            showToast(message || '验证码获取失败', 'error');
        }

        // 无论成功失败都重新启用生成邮箱按钮
        const generateEmailBtn = document.getElementById('generateEmailBtn');
        if (generateEmailBtn) {
            generateEmailBtn.disabled = false;
            console.log('验证码获取完成：重新启用生成邮箱按钮');
        }
    });

    window.__TAURI__.listen('reset_complete', (event) => {
        const { success, message } = event.payload;
        // 停止轮询
        if (resetStatusCheckInterval) {
            clearInterval(resetStatusCheckInterval);
            resetStatusCheckInterval = null;
        }
        // 直接处理重置完成
        handleResetComplete(success, message);
    });

    window.__TAURI__.listen('config_updated', (event) => {
        const { config } = event.payload;
        handleConfigUpdate(config);
    });
}

// 设置事件监听器
function setupEventListeners() {
    // 鼠标跟踪效果（检查元素是否存在）
    document.querySelectorAll('.feature-card').forEach(card => {
        card.addEventListener('mousemove', (e) => {
            const hoverEffect = card.querySelector('.card-hover-effect');
            if (hoverEffect) {
                const rect = card.getBoundingClientRect();
                const x = ((e.clientX - rect.left) / rect.width) * 100;
                const y = ((e.clientY - rect.top) / rect.height) * 100;
                hoverEffect.style.setProperty('--mouse-x', `${x}%`);
                hoverEffect.style.setProperty('--mouse-y', `${y}%`);
            }
        });
    });

    // 设置标题栏拖拽功能
    setupTitlebarDrag();
}

// 标题栏拖拽功能
function setupTitlebarDrag() {
    const titlebar = document.getElementById('titlebar');
    const windowControls = titlebar.querySelector('.window-controls');
    let isDragging = false;
    let startX = 0;
    let startY = 0;

    titlebar.addEventListener('mousedown', (e) => {
        // 如果点击的是窗口控制按钮区域，不启动拖拽
        if (windowControls.contains(e.target) || e.target.closest('.window-controls')) {
            return;
        }

        if (e.button === 0) { // 左键
            isDragging = true;
            startX = e.screenX;
            startY = e.screenY;

            // 阻止默认行为
            e.preventDefault();
            e.stopPropagation();

            // 阻止文本选择，但不改变光标样式
            document.body.style.userSelect = 'none';

            // 通知Qt开始拖拽
            if (bridge && bridge.start_window_drag) {
                bridge.start_window_drag(startX, startY);
            }
        }
    });

    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;

        // 使用屏幕坐标，更稳定
        if (bridge && bridge.update_window_drag) {
            bridge.update_window_drag(e.screenX, e.screenY);
        }
    });

    document.addEventListener('mouseup', (e) => {
        if (isDragging) {
            isDragging = false;
            document.body.style.userSelect = '';

            // 通知Qt结束拖拽
            if (bridge && bridge.end_window_drag) {
                bridge.end_window_drag();
            }
        }
    });

    // 双击标题栏最大化/还原窗口
    titlebar.addEventListener('dblclick', (e) => {
        // 如果双击的是窗口控制按钮区域，不触发最大化
        if (windowControls.contains(e.target) || e.target.closest('.window-controls')) {
            return;
        }
        e.preventDefault();
        e.stopPropagation();
        toggleMaximize();
    });

    // 阻止标题栏上的文本选择
    titlebar.addEventListener('selectstart', (e) => {
        e.preventDefault();
    });

    // 阻止标题栏上的右键菜单
    titlebar.addEventListener('contextmenu', (e) => {
        e.preventDefault();
    });
}

// 初始化应用
function initializeApp() {
    // 确保最小加载时间，避免闪烁
    const minLoadingTime = 1200; // 1.2秒最小加载时间
    const startTime = Date.now();

    bridge.is_first_run((isFirstRun) => {
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

        setTimeout(() => {
            if (!isFirstRun) {
                // 不是首次运行，直接显示主页面
                bridge.get_selected_editor((editor) => {
                    selectedEditor = editor;
                    // 获取配置并更新浮动卡片
                    bridge.get_config((config) => {
                        updateFloatingCards(config);
                        updateEditorDisplay(); // 更新编辑器显示
                        hideLoadingPageAndShow('main');
                    });
                });
            } else {
                // 首次运行，显示编辑器选择页面
                hideLoadingPageAndShow('editorSelect');
            }
        }, remainingTime);
    });
}

// 更新浮动卡片信息
function updateFloatingCards(config) {
    const domainElement = document.getElementById('floatingDomain');
    const modeElement = document.getElementById('floatingMode');

    if (config && config.email) {
        const emailConfig = config.email;

        // 更新域名
        if (domainElement) {
            domainElement.textContent = emailConfig.domain || 'xx.com';
        }

        // 更新模式
        if (modeElement) {
            const mode = emailConfig.use_temp_mail ? '临时邮箱' : 'IMAP';
            modeElement.textContent = mode;
        }
    } else {
        // 如果没有配置，显示默认值
        if (domainElement) {
            domainElement.textContent = 'xx.com';
        }
        if (modeElement) {
            modeElement.textContent = 'IMAP';
        }
    }
}

// 隐藏加载页面并显示目标页面（带平滑过渡）
function hideLoadingPageAndShow(targetPage) {
    const loadingPage = document.getElementById('loadingPage');
    const loadingContainer = loadingPage.querySelector('.loading-container');

    // 开始加载页面淡出动画
    if (loadingContainer) {
        loadingContainer.classList.add('fade-out');
    }

    // 等待淡出动画完成后切换页面
    setTimeout(() => {
        loadingPage.classList.add('hidden');

        if (targetPage === 'main') {
            showMainPageDirectly();
        } else if (targetPage === 'editorSelect') {
            showEditorSelectPage();
        }
    }, 800); // 等待淡出动画完成
}

// 显示编辑器选择页面
function showEditorSelectPage() {
    const editorSelectPage = document.getElementById('editorSelectPage');
    const mainPage = document.getElementById('mainPage');

    // 确保主页面隐藏
    mainPage.classList.add('hidden');

    // 显示编辑器选择页面并添加淡入动画
    editorSelectPage.classList.remove('hidden');
    editorSelectPage.classList.add('fade-in');

    // 更新当前页面状态
    currentPage = 'editorSelect';

    // 动画完成后移除动画类
    setTimeout(() => {
        editorSelectPage.classList.remove('fade-in');
    }, 800);

    // 获取编辑器状态
    bridge.get_editor_status((status) => {
        updateEditorStatus(status);
    });
}

// 直接显示主页面（带淡入动画）
function showMainPageDirectly() {
    const editorSelectPage = document.getElementById('editorSelectPage');
    const mainPage = document.getElementById('mainPage');

    // 确保编辑器选择页面隐藏
    editorSelectPage.classList.add('hidden');

    // 显示主页面并添加淡入动画
    mainPage.classList.remove('hidden');
    mainPage.classList.add('fade-in');

    // 更新当前页面状态
    currentPage = 'main';

    // 更新显示的编辑器
    updateEditorDisplay();

    // 缩短动画时间并立即触发页面动画
    setTimeout(() => {
        mainPage.classList.remove('fade-in');
        // 立即触发动画
        initPageAnimations();
    }, 400);
}

// 更新编辑器状态
function updateEditorStatus(status) {
    if (!status || typeof status !== 'object') {
        console.warn('updateEditorStatus: 无效的状态对象', status);
        return;
    }

    // VS Code状态
    const vscodeStatus = document.getElementById('vscodeStatus');
    if (vscodeStatus) {
        try {
            const vscodeInstalled = status.vscode?.installed || false;
            const indicator = vscodeStatus.querySelector('.status-indicator');
            const text = vscodeStatus.querySelector('.status-text');

            if (indicator && text) {
                indicator.className = `status-indicator ${vscodeInstalled ? 'installed' : 'not-installed'}`;
                text.className = `status-text ${vscodeInstalled ? 'installed' : 'not-installed'}`;
                text.textContent = vscodeInstalled ? '已安装' : '未安装';

                // 检查文字滚动
                checkAndEnableTextScrolling(text);
            }
        } catch (error) {
            console.error('更新VS Code状态时出错:', error);
        }
    }

    // Cursor状态
    const cursorStatus = document.getElementById('cursorStatus');
    if (cursorStatus) {
        try {
            const cursorInstalled = status.cursor?.installed || false;
            const indicator = cursorStatus.querySelector('.status-indicator');
            const text = cursorStatus.querySelector('.status-text');

            if (indicator && text) {
                indicator.className = `status-indicator ${cursorInstalled ? 'installed' : 'not-installed'}`;
                text.className = `status-text ${cursorInstalled ? 'installed' : 'not-installed'}`;
                text.textContent = cursorInstalled ? '已安装' : '未安装';

                // 检查文字滚动
                checkAndEnableTextScrolling(text);
            }
        } catch (error) {
            console.error('更新Cursor状态时出错:', error);
        }
    }
}

// 选择编辑器
function selectEditor(editorType) {
    try {
        // 添加选择动画效果
        const selectedCard = document.getElementById(`${editorType}Card`);
        if (selectedCard) {
            selectedCard.classList.add('selecting');

            setTimeout(() => {
                selectedCard.classList.remove('selecting');
            }, 150);
        }

        if (!bridge || !bridge.select_editor) {
            throw new Error('Bridge未初始化或select_editor方法不可用');
        }

        bridge.select_editor(editorType, (success) => {
            try {
                if (success) {
                    selectedEditor = editorType;
                    showToast(`已选择 ${editorType.toUpperCase()}`, 'success');

                    // 添加成功选择的视觉反馈
                    if (selectedCard) {
                        selectedCard.classList.add('selection-success');
                    }

                    setTimeout(() => {
                        showMainPage();
                    }, 800);
                } else {
                    showToast('选择编辑器失败', 'error');

                    // 添加失败的视觉反馈
                    if (selectedCard) {
                        selectedCard.classList.add('selection-error');

                        setTimeout(() => {
                            selectedCard.classList.remove('selection-error');
                        }, 2000);
                    }
                }
            } catch (error) {
                showToast(`选择编辑器时发生错误: ${error.message}`, 'error');
                console.error('Error in selectEditor callback:', error);
            }
        });
    } catch (error) {
        showToast(`选择编辑器失败: ${error.message}`, 'error');
        console.error('Error in selectEditor:', error);
    }
}

// 显示主页面（带动画，用于编辑器选择后）
function showMainPage() {
    const editorSelectPage = document.getElementById('editorSelectPage');
    const mainPage = document.getElementById('mainPage');
    const editorSelectContainer = editorSelectPage.querySelector('.editor-select-container');

    // 添加出场动画类
    if (editorSelectContainer) {
        editorSelectContainer.classList.add('fade-out');
    }

    // 缩短等待时间，让主页更快出现
    setTimeout(() => {
        editorSelectPage.classList.add('hidden');
        mainPage.classList.remove('hidden');

        // 更新当前页面状态
        currentPage = 'main';

        // 更新显示的编辑器
        updateEditorDisplay();

        // 立即淡入主页面并触发动画
        mainPage.style.opacity = '1';
        initPageAnimations();
    }, 400); // 缩短等待时间
}

// 窗口控制
function minimizeWindow() {
    bridge.minimize_window();
}

function toggleMaximize() {
    bridge.toggle_maximize();
}

function closeWindow() {
    bridge.close_window();
}

// 更新编辑器显示
function updateEditorDisplay() {
    // 更新主页面的编辑器显示
    const selectedEditorEl = document.getElementById('selectedEditor');
    if (selectedEditorEl) {
        selectedEditorEl.textContent = `当前选择的编辑器: ${selectedEditor.toUpperCase()}`;
    }

    // 更新重置区域的编辑器名称
    const currentEditorNameEl = document.getElementById('currentEditorName');
    if (currentEditorNameEl && selectedEditor) {
        currentEditorNameEl.textContent = selectedEditor.toUpperCase();
    }
}

// Toast提示系统 - 符合项目主题风格
let toastHistory = [];
let toastIdCounter = 0;

function showToast(message, type = 'info', duration = 4000) {
    // 确保容器存在
    const container = document.getElementById('toastContainer');
    if (!container) {
        console.error('Toast container not found');
        return;
    }

    const toast = document.createElement('div');
    const toastId = ++toastIdCounter;

    toast.className = `toast ${type}`;
    toast.setAttribute('data-toast-id', toastId);

    const icons = {
        'success': '✓',
        'error': '✗',
        'info': 'ℹ',
        'warning': '⚠'
    };

    const icon = icons[type] || 'ℹ';

    toast.innerHTML = `
        <span class="toast-icon">${icon}</span>
        <span class="toast-message">${message}</span>
    `;

    // 确保toastHistory是数组
    if (!Array.isArray(toastHistory)) {
        toastHistory = [];
    }

    // 记录到历史（用于错误追踪）
    const historyItem = {
        id: toastId,
        message: message,
        type: type,
        timestamp: new Date(),
        read: false
    };
    toastHistory.unshift(historyItem);

    // 限制历史记录数量 - 添加安全检查
    if (toastHistory && toastHistory.length > 50) {
        toastHistory = toastHistory.slice(0, 50);
    }

    // 计算toast的位置（基于现有toast的数量）
    const existingToasts = container.children.length;
    const toastHeight = 40; // 减小估算的toast高度
    const toastGap = 8; // 减小toast之间的间距
    const topPosition = existingToasts * (toastHeight + toastGap);

    // 设置初始位置
    toast.style.top = `${topPosition}px`;

    container.appendChild(toast);

    // 触发入场动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // 自动移除
    if (duration > 0) {
        setTimeout(() => {
            closeToast(toastId);
        }, duration);
    }

    // 如果是错误类型，记录到控制台
    if (type === 'error') {
        console.error(`YAugment Error: ${message}`);
    }

    return toastId;
}

function closeToast(toastId) {
    const toast = document.querySelector(`[data-toast-id="${toastId}"]`);
    if (toast) {
        const container = toast.parentNode;

        // 开始退场动画
        toast.classList.remove('show');
        toast.classList.add('hide');

        // 在退场动画完成后重新排列剩余的toast
        setTimeout(() => {
            if (toast.parentNode) {
                // 获取当前要移除的toast的位置信息
                const currentTop = parseInt(toast.style.top) || 0;

                // 移除当前toast
                toast.parentNode.removeChild(toast);

                // 重新计算并更新所有剩余toast的位置
                updateToastPositions(container);
            }
        }, 400); // 与CSS中的toastSlideOut动画时间一致
    }
}

// 新增函数：更新所有toast的位置
function updateToastPositions(container) {
    const toasts = Array.from(container.children);
    const toastHeight = 60; // 估算的toast高度
    const toastGap = 12; // toast之间的间距

    toasts.forEach((toast, index) => {
        const newTop = index * (toastHeight + toastGap);
        toast.style.top = `${newTop}px`;
    });
}

// 对话框控制
function showDialog(title, content) {
    const overlay = document.getElementById('dialogOverlay');
    const dialog = document.getElementById('dialog');
    const dialogTitle = document.getElementById('dialogTitle');
    const dialogContent = document.getElementById('dialogContent');

    dialogTitle.textContent = title;
    dialogContent.innerHTML = content;

    // 禁用背景页面滚动
    document.body.style.overflow = 'hidden';

    overlay.classList.add('show');
}

function closeDialog(event) {
    // 如果没有event参数，或者点击的是overlay背景，则关闭对话框
    if (!event || event.target.id === 'dialogOverlay' || event.target.classList.contains('dialog-close')) {
        const overlay = document.getElementById('dialogOverlay');

        // 恢复背景页面滚动
        document.body.style.overflow = '';

        overlay.classList.remove('show');
    }
}

// 重置Augment - 新的直接执行方式
function executeReset() {
    try {

        if (!bridge || !bridge.reset_augment) {
            throw new Error('Bridge未初始化或reset_augment方法不可用');
        }

        if (!selectedEditor) {
            throw new Error('未选择编辑器');
            return;
        }

        // 更新UI状态
        updateResetUI('running', '正在重置...');

        // 开始重置
        bridge.reset_augment();

        // 使用轮询作为主要机制，信号作为备用

        startResetStatusPolling();

    } catch (error) {
        showToast(`重置失败: ${error.message}`, 'error');
        console.error('Error in executeReset:', error);
        updateResetUI('error', '重置失败');
    }
}

function updateResetUI(state, message) {
    const resetBtn = document.getElementById('resetActionBtn');
    const progressCircle = document.querySelector('.progress-circle');
    const resetIcon = document.querySelector('.reset-icon svg');
    const resetIconContainer = document.querySelector('.reset-icon');
    const resetContainer = document.querySelector('.reset-container');

    // 安全检查 - 如果元素不存在则返回
    if (!resetBtn) {
        console.warn('updateResetUI: resetBtn元素不存在');
        return;
    }

    const btnText = resetBtn.querySelector('.btn-text');
    const btnLoading = resetBtn.querySelector('.btn-loading');

    switch(state) {
        case 'ready':
            resetBtn.disabled = false;
            resetBtn.classList.remove('resetting');
            if (btnText) {
                btnText.style.display = 'block';
                btnText.textContent = '开始重置';
            }
            if (btnLoading) btnLoading.classList.add('hidden');
            if (progressCircle) {
                progressCircle.style.strokeDashoffset = '314';
            }
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'paused';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.remove('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.remove('resetting');
            }
            break;

        case 'running':
            resetBtn.disabled = true;
            resetBtn.classList.add('resetting');
            if (btnText) btnText.style.display = 'none';
            if (btnLoading) btnLoading.classList.remove('hidden');
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'running';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.add('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.add('resetting');
            }
            break;

        case 'success':
            resetBtn.disabled = false;
            resetBtn.classList.remove('resetting');
            if (btnText) {
                btnText.style.display = 'block';
                btnText.textContent = '重置完成';
            }
            if (btnLoading) btnLoading.classList.add('hidden');
            if (progressCircle) {
                progressCircle.style.strokeDashoffset = '0';
            }
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'paused';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.remove('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.remove('resetting');
            }
            break;

        case 'error':
            resetBtn.disabled = false;
            resetBtn.classList.remove('resetting');
            if (btnText) {
                btnText.style.display = 'block';
                btnText.textContent = '重试';
            }
            if (btnLoading) btnLoading.classList.add('hidden');
            if (progressCircle) {
                progressCircle.style.strokeDashoffset = '314';
            }
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'paused';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.remove('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.remove('resetting');
            }
            break;
    }
}

function startResetStatusPolling() {
    console.log('开始重置状态轮询');

    // 清除之前的轮询
    if (resetStatusCheckInterval) {
        clearInterval(resetStatusCheckInterval);
    }

    let pollCount = 0;
    let lastProgressTime = Date.now(); // 记录最后一次进度更新时间
    let lastProgressMessage = ''; // 记录最后一次进度消息
    const maxIdleTime = 120000; // 最大空闲时间：2分钟无进度更新才认为超时
    const maxTotalTime = 600000; // 最大总时间：10分钟绝对超时

    resetStatusCheckInterval = setInterval(() => {
        pollCount++;
        const currentTime = Date.now();
        const elapsedTime = currentTime - lastProgressTime;

        // 检查是否真正超时（基于进度更新，而不是固定时间）
        if (elapsedTime > maxIdleTime) {
            console.warn(`重置操作空闲超时: ${elapsedTime}ms 无进度更新`);
            clearInterval(resetStatusCheckInterval);
            resetStatusCheckInterval = null;

            // 尝试强制清理重置状态
            if (bridge && bridge.force_clear_reset_status) {
                bridge.force_clear_reset_status(() => {
                    console.log('重置状态已强制清理');
                });
            }

            handleResetComplete(false, "重置操作长时间无响应，状态已清理。如有大量文件，请重试");
            return;
        }

        // 绝对超时保护（10分钟）
        if (pollCount * 500 > maxTotalTime) {
            console.warn('重置操作绝对超时: 超过10分钟');
            clearInterval(resetStatusCheckInterval);
            resetStatusCheckInterval = null;

            // 尝试强制清理重置状态
            if (bridge && bridge.force_clear_reset_status) {
                bridge.force_clear_reset_status(() => {
                    console.log('重置状态已强制清理');
                });
            }

            handleResetComplete(false, "重置操作超时，状态已清理。请检查是否有大量文件需要处理");
            return;
        }

        if (bridge && bridge.get_reset_status) {
            try {

                // 检查方法是否存在

                // 使用回调方式处理异步调用
                bridge.get_reset_status((statusJson) => {

                    // 解析JSON字符串
                    let status;
                    try {
                        status = JSON.parse(statusJson);

                        // 处理状态
                        if (status && typeof status === 'object') {

                            if (status.completed) {
                                // 重置完成，停止轮询
                                clearInterval(resetStatusCheckInterval);
                                resetStatusCheckInterval = null;

                                handleResetComplete(status.success, status.message);
                                return;
                            } else if (status.is_running) {
                                // 更新进度显示
                                const progressText = document.getElementById('resetProgressText');
                                if (progressText) {
                                    progressText.textContent = status.message || '正在重置...';
                                }

                            } else {

                            }
                        } else {

                        }

                    } catch (parseError) {

                    }
                });

            } catch (error) {

            }
        } else {

        }
    }, 500); // 每500ms检查一次
}

// 通用的文字更新动画函数
function updateTextWithAnimation(element, newText, animationDuration = 400) {
    if (!element) return;

    const currentText = element.textContent || '';
    if (currentText === newText) return; // 文字相同，无需更新

    // 添加变化动画类
    element.classList.add('changing');

    // 在动画中间点更新文字内容
    setTimeout(() => {
        element.textContent = newText;
        element.classList.remove('changing');

        // 检查是否需要滚动
        checkAndEnableTextScrolling(element);
    }, animationDuration / 2);
}

// 检查文字是否需要滚动并启用滚动效果
function checkAndEnableTextScrolling(element) {
    if (!element) {
        console.warn('checkAndEnableTextScrolling: element is null or undefined');
        return;
    }

    // 检查元素是否有classList属性
    if (!element.classList) {
        console.warn('checkAndEnableTextScrolling: element does not have classList');
        return;
    }

    // 检查是否是状态文字元素
    if (!element.classList.contains('status-text')) return;

    // 等待DOM更新后再检查
    setTimeout(() => {
        try {
            // 再次检查元素是否仍然存在
            if (!element || !element.parentNode) {
                console.warn('checkAndEnableTextScrolling: element no longer exists in DOM');
                return;
            }

            const containerWidth = 200; // 状态文字容器的最大宽度
            const textWidth = element.scrollWidth || 0;

            // 移除之前的滚动类
            if (element.classList) {
                element.classList.remove('text-scrolling');
            }

            // 如果文字宽度超过容器宽度，启用滚动
            if (textWidth > containerWidth) {
                if (element.classList) {
                    element.classList.add('text-scrolling');
                }

                // 动态计算滚动距离并设置CSS变量
                const scrollDistance = textWidth - containerWidth + 20; // 额外20px边距
                if (element.style) {
                    element.style.setProperty('--scroll-distance', `-${scrollDistance}px`);
                }
            }
        } catch (error) {
            console.error('checkAndEnableTextScrolling error:', error);
        }
    }, 50);
}

function updateProgress(taskId, progress, message) {
    if (taskId === 'reset') {
        const progressBar = document.getElementById('resetProgressBar');
        const progressText = document.getElementById('resetProgressText');
        const statusText = document.getElementById('resetStatusText');

        if (progressBar && progressText) {
            progressBar.style.width = `${progress * 100}%`;
            updateTextWithAnimation(progressText, message);
        }

        if (statusText) {
            updateTextWithAnimation(statusText, message);
        }
    } else if (taskId === 'verification') {
        updateVerificationStatus('running', message);
    }
}

let resetCompleteHandled = false;

function handleResetComplete(success, message) {

    // 防止重复处理
    if (resetCompleteHandled) {

        return;
    }
    resetCompleteHandled = true;

    // 停止轮询
    if (resetStatusCheckInterval) {
        clearInterval(resetStatusCheckInterval);
        resetStatusCheckInterval = null;
    }

    if (success) {

        updateResetUI('success', '重置成功');
        showToast('重置成功！', 'success');
        setTimeout(() => {
            updateResetUI('ready', '');
            resetCompleteHandled = false; // 重置标志
        }, 3000);
    } else {

        updateResetUI('error', `重置失败: ${message}`);
        showToast(`重置失败: ${message}`, 'error');
        setTimeout(() => {
            updateResetUI('ready', '');
            resetCompleteHandled = false; // 重置标志
        }, 3000);
    }
}

// 邮箱功能 - 新的直接操作方式
function generateEmailDirect() {
    try {
        if (!bridge || !bridge.generate_email) {
            throw new Error('Bridge未初始化或generate_email方法不可用');
        }

        bridge.generate_email((email) => {
            try {
                console.log('邮箱生成回调收到结果:', email);
                if (email && email.trim() !== '') {
                    // 优先使用新的卡片显示，如果不存在则使用旧的
                    const displayArea = document.getElementById('emailDisplayArea');
                    if (displayArea) {
                        showEmailResultInCard(email);
                    } else {
                        showEmailResultDirect(email);
                    }
                    showToast('邮箱生成成功', 'success');
                } else {
                    console.log('邮箱生成失败，email为:', email);
                    showToast('邮箱生成失败', 'error');
                }
            } catch (error) {
                console.error('处理邮箱生成结果时出错:', error);
                showToast(`处理邮箱结果时发生错误: ${error.message}`, 'error');
            }
        });
    } catch (error) {
        showToast(`生成邮箱失败: ${error.message}`, 'error');
        console.error('Error in generateEmailDirect:', error);
    }
}

function showEmailResultDirect(email) {
    const emailResult = document.getElementById('emailResult');
    const emailInput = document.getElementById('generatedEmail');

    if (emailResult && emailInput) {
        emailInput.value = email;
        emailResult.classList.remove('hidden');

        // 添加一个淡入动画
        emailResult.style.opacity = '0';
        emailResult.style.transform = 'translateY(10px)';

        setTimeout(() => {
            emailResult.style.transition = 'all 0.3s ease';
            emailResult.style.opacity = '1';
            emailResult.style.transform = 'translateY(0)';
        }, 50);
    } else {
        // 如果旧的UI元素不存在，尝试使用新的UI
        const displayArea = document.getElementById('emailDisplayArea');
        if (displayArea) {
            showEmailResultInCard(email);
        } else {
            console.warn('showEmailResultDirect: 邮箱结果显示元素不存在');
            showToast('邮箱生成成功: ' + email, 'success');
        }
    }
}

function copyEmailDirect() {
    const emailInput = document.getElementById('generatedEmail');
    if (emailInput && emailInput.value) {
        emailInput.select();
        document.execCommand('copy');
        showToast('邮箱地址已复制', 'success');
    } else {
        showToast('没有邮箱地址可复制', 'error');
    }
}

function showEmailResult(email) {
    // 重定向到新的直接显示方式
    showEmailResultDirect(email);
}

// 新的邮箱卡片显示函数
function showEmailResultInCard(email) {
    const displayArea = document.getElementById('emailDisplayArea');
    if (!displayArea) {
        console.warn('showEmailResultInCard: 邮箱显示区域不存在');
        return;
    }

    // 创建邮箱结果显示
    displayArea.innerHTML = `
        <div class="email-result">
            <input type="text" class="email-text" value="${email}" readonly>
            <button class="copy-email-btn" onclick="copyEmailFromCard()">
                <svg width="14" height="14" viewBox="0 0 24 24">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z" fill="currentColor"/>
                </svg>
                复制
            </button>
        </div>
    `;

    // 显示浮动卡片
    const floatingCards = document.querySelectorAll('.floating-card');
    floatingCards.forEach(card => {
        card.style.opacity = '1';
        card.style.transform = card.classList.contains('card-1') ? 'rotate(8deg)' : 'rotate(-5deg)';
    });
}

function copyEmailFromCard() {
    const emailText = document.querySelector('.email-text');
    if (emailText && emailText.value) {
        try {
            emailText.select();
            document.execCommand('copy');
            showToast('邮箱地址已复制', 'success');

            // 更新复制按钮状态
            const copyBtn = document.querySelector('.copy-email-btn');
            if (copyBtn) {
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '<svg width="14" height="14" viewBox="0 0 24 24"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor"/></svg>已复制';
                copyBtn.style.background = 'rgba(34, 197, 94, 0.2)';
                copyBtn.style.borderColor = '#22c55e';
                copyBtn.style.color = '#22c55e';

                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                    copyBtn.style.background = '';
                    copyBtn.style.borderColor = '';
                    copyBtn.style.color = '';
                }, 2000);
            }
        } catch (error) {
            console.error('复制失败:', error);
            showToast('复制失败，请手动复制', 'error');
        }
    } else {
        console.warn('copyEmailFromCard: 邮箱输入元素不存在或没有值');
        showToast('没有邮箱地址可复制', 'error');
    }
}

function copyEmail() {
    const emailInput = document.getElementById('generatedEmail');
    if (emailInput && emailInput.value) {
        emailInput.select();
        document.execCommand('copy');
        showToast('邮箱地址已复制', 'success');
    } else {
        console.warn('copyEmail: 邮箱输入元素不存在或没有值');
        showToast('没有邮箱地址可复制', 'error');
    }
}

function getVerificationCodeDirect() {
    try {
        if (!bridge || !bridge.get_verification_code) {
            throw new Error('Bridge未初始化或get_verification_code方法不可用');
        }

        updateVerificationStatus('running', '正在获取验证码...');

        // 禁用生成邮箱按钮，防止在获取验证码过程中误操作
        const generateEmailBtn = document.getElementById('generateEmailBtn');
        if (generateEmailBtn) {
            generateEmailBtn.disabled = true;
            console.log('获取验证码开始：禁用生成邮箱按钮');
        }

        bridge.get_verification_code();
    } catch (error) {
        showToast(`获取验证码失败: ${error.message}`, 'error');
        console.error('Error in getVerificationCodeDirect:', error);
        updateVerificationStatus('error', '获取失败');

        // 发生异常时也要重新启用生成邮箱按钮
        const generateEmailBtn = document.getElementById('generateEmailBtn');
        if (generateEmailBtn) {
            generateEmailBtn.disabled = false;
            console.log('验证码获取异常：重新启用生成邮箱按钮');
        }
    }
}

function updateVerificationStatus(state, message) {
    // 新的验证状态更新 - 支持新UI设计
    const verificationStatus = document.getElementById('verificationStatus');
    const statusIndicator = verificationStatus?.querySelector('.status-indicator');
    const statusText = verificationStatus?.querySelector('.status-text');

    // 旧的验证状态更新 - 兼容性
    const statusContainer = document.getElementById('verificationStatus');
    const indicator = document.getElementById('verifyIndicator');
    const oldStatusText = document.getElementById('verifyStatusText');
    const resultContainer = document.getElementById('verificationResult');

    // 更新新UI
    if (statusIndicator && statusText) {
        statusIndicator.className = 'status-indicator';

        switch(state) {
            case 'running':
                statusIndicator.classList.add('active');
                statusText.textContent = message || '正在获取...';
                break;
            case 'success':
                statusIndicator.classList.add('active');
                statusText.textContent = message || '获取成功';
                break;
            case 'error':
                statusIndicator.classList.remove('active');
                statusText.textContent = message || '获取失败';
                break;
            default:
                statusIndicator.classList.remove('active');
                statusText.textContent = message || '等待操作';
                break;
        }
    }

    // 更新旧UI（兼容性）
    if (statusContainer && indicator && oldStatusText) {
        statusContainer.classList.remove('hidden');
        indicator.className = 'status-indicator';

        switch(state) {
            case 'running':
                indicator.classList.add('running');
                oldStatusText.textContent = message || '正在获取...';
                if (resultContainer) resultContainer.classList.add('hidden');
                break;
            case 'success':
                indicator.classList.add('success');
                oldStatusText.textContent = message || '获取成功';
                break;
            case 'error':
                indicator.classList.add('error');
                oldStatusText.textContent = message || '获取失败';
                if (resultContainer) resultContainer.classList.add('hidden');
                break;
        }
    }
}

// 保留旧的邮箱功能以兼容性（如果需要）
function generateEmail() {
    generateEmailDirect();
}

function getVerificationCode() {
    getVerificationCodeDirect();
}

function copyEmail() {
    copyEmailDirect();
}

function getVerificationCodeForEmail() {
    getVerificationCodeDirect();
}

function showVerificationCode(code) {
    const resultContainer = document.getElementById('verificationResult');
    const codeElement = document.getElementById('verificationCode');

    if (resultContainer && codeElement) {
        codeElement.textContent = code;
        resultContainer.classList.remove('hidden');

        // 更新状态为成功
        updateVerificationStatus('success', '验证码获取成功');

        // 添加一个淡入动画
        resultContainer.style.opacity = '0';
        resultContainer.style.transform = 'translateY(10px)';

        setTimeout(() => {
            resultContainer.style.transition = 'all 0.3s ease';
            resultContainer.style.opacity = '1';
            resultContainer.style.transform = 'translateY(0)';
        }, 50);

        showToast('验证码已自动复制到剪贴板', 'success');
    } else {
        console.warn('showVerificationCode: 验证码显示元素不存在');
        showToast('验证码: ' + code + ' (已复制到剪贴板)', 'success');
    }
}

// 设置功能
function showSettings() {
    try {
        if (!bridge || !bridge.get_config) {
            throw new Error('Bridge未初始化或get_config方法不可用');
        }

        bridge.get_config((config) => {
            try {
                const content = generateSettingsContent(config);
                showDialog('设置', content);
            } catch (error) {
                showToast(`生成设置界面失败: ${error.message}`, 'error');
                console.error('Error in showSettings callback:', error);
            }
        });
    } catch (error) {
        showToast(`打开设置失败: ${error.message}`, 'error');
        console.error('Error in showSettings:', error);
    }
}

function generateSettingsContent(config) {
    const emailConfig = config.email || {};
    return `
        <div class="settings-content">
            <div class="settings-tabs">
                <button class="tab-btn active" onclick="switchTab('editor', this)">编辑器</button>
                <button class="tab-btn" onclick="switchTab('email', this)">邮箱设置</button>
                <button class="tab-btn" onclick="switchTab('advanced', this)">高级设置</button>
            </div>

            <div class="settings-body">
                <div class="tab-content" id="editorTab">
                    <h4>编辑器选择</h4>
                    <div class="editor-switch">
                        <label>
                            <input type="radio" name="editor" value="vscode" ${selectedEditor === 'vscode' ? 'checked' : ''} />
                            <span>Visual Studio Code</span>
                        </label>
                        <label>
                            <input type="radio" name="editor" value="cursor" ${selectedEditor === 'cursor' ? 'checked' : ''} />
                            <span>Cursor</span>
                        </label>
                    </div>
                </div>
            
            <div class="tab-content hidden" id="emailTab">
                <h4>邮箱配置</h4>
                <div class="form-group">
                    <label>域名</label>
                    <input type="text" id="emailDomain" value="${emailConfig.domain || 'xx.com'}" placeholder="xx.com" />
                </div>
                
                <div class="form-group">
                    <label>邮箱获取方式</label>
                    <div class="radio-group">
                        <label>
                            <input type="radio" name="emailType" value="temp" ${emailConfig.use_temp_mail ? 'checked' : ''} onchange="toggleEmailType()" />
                            <span>临时邮箱 (tempmail.plus)</span>
                        </label>
                        <label>
                            <input type="radio" name="emailType" value="imap" ${!emailConfig.use_temp_mail ? 'checked' : ''} onchange="toggleEmailType()" />
                            <span>IMAP邮箱</span>
                        </label>
                    </div>
                </div>
                
                <div class="email-config-section" id="tempMailConfig" ${!emailConfig.use_temp_mail ? 'style="display:none"' : ''}>
                    <h5>临时邮箱设置</h5>
                    <div class="form-group">
                        <label>邮箱地址</label>
                        <input type="text" id="tempMailEmail" value="${emailConfig.temp_mail?.email || ''}" placeholder="<EMAIL>" />
                    </div>
                    <div class="form-group">
                        <label>PIN码</label>
                        <input type="password" id="tempMailPin" value="${emailConfig.temp_mail?.pin || ''}" placeholder="你设置的PIN" />
                    </div>
                </div>
                
                <div class="email-config-section" id="imapConfig" ${emailConfig.use_temp_mail ? 'style="display:none"' : ''}>
                    <h5>IMAP设置</h5>
                    <div class="form-group">
                        <label>IMAP服务器</label>
                        <input type="text" id="imapServer" value="${emailConfig.imap?.server || 'imap.qq.com'}" />
                    </div>
                    <div class="form-group">
                        <label>端口</label>
                        <input type="number" id="imapPort" value="${emailConfig.imap?.port || 993}" />
                    </div>
                    <div class="form-group">
                        <label>邮箱地址</label>
                        <input type="email" id="imapUser" value="${emailConfig.imap?.user || ''}" placeholder="<EMAIL>" />
                    </div>
                    <div class="form-group">
                        <label>密码/授权码</label>
                        <input type="password" id="imapPassword" value="${emailConfig.imap?.password || ''}" placeholder="password" />
                    </div>
                    <div class="form-group">
                        <label>收件箱</label>
                        <input type="text" id="imapFolder" value="${emailConfig.imap?.folder || 'INBOX'}" />
                    </div>
                </div>
                
                <button class="action-btn secondary" onclick="testEmailConnection()">测试连接</button>
            </div>
            
            <div class="tab-content hidden" id="advancedTab">
                <h4>高级设置</h4>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="autoCopy" ${emailConfig.auto_copy ? 'checked' : ''} />
                        <span>自动复制验证码</span>
                    </label>
                </div>
                
                <h5>邮箱生成配置</h5>
                <div class="form-group">
                    <label>用户名长度</label>
                    <input type="number" id="usernameLength" value="${emailConfig.generation?.username_length || 9}" min="5" max="20" />
                </div>
                
                <h5>重试配置</h5>
                <div class="form-group">
                    <label>最大重试次数</label>
                    <input type="number" id="maxRetries" value="${emailConfig.retry?.max_retries || 30}" min="1" max="100" />
                </div>
                <div class="form-group">
                    <label>重试间隔（秒）</label>
                    <input type="number" id="retryInterval" value="${emailConfig.retry?.retry_interval || 1}" min="1" max="10" />
                </div>
            </div>

            <div class="settings-footer">
                <div class="dialog-actions">
                    <button class="action-btn primary" onclick="saveSettings()">保存设置</button>
                    <button class="action-btn secondary" onclick="closeDialog()">取消</button>
                </div>
            </div>
        </div>
    `;
}

function switchTab(tabName, clickedElement) {
    // 切换标签按钮状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // 如果没有传入点击的元素，尝试通过事件获取
    const targetElement = clickedElement || event?.target;
    if (targetElement) {
        targetElement.classList.add('active');
    }

    // 切换内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    document.getElementById(`${tabName}Tab`).classList.remove('hidden');
}

function toggleEmailType() {
    const checkedInput = document.querySelector('input[name="emailType"]:checked');
    if (!checkedInput) return;

    const useTempMail = checkedInput.value === 'temp';
    const tempMailConfig = document.getElementById('tempMailConfig');
    const imapConfig = document.getElementById('imapConfig');

    if (tempMailConfig) {
        tempMailConfig.style.display = useTempMail ? 'block' : 'none';
    }
    if (imapConfig) {
        imapConfig.style.display = useTempMail ? 'none' : 'block';
    }
}

function testEmailConnection() {
    showToast('正在测试连接...', 'info');
    // 先保存当前配置
    const config = collectSettingsData();
    bridge.save_config(config, (success) => {
        if (success) {
            bridge.test_email_connection();
        } else {
            showToast('保存配置失败', 'error');
        }
    });
}

function collectSettingsData() {
    try {
        const getElementValue = (id, defaultValue = '') => {
            const element = document.getElementById(id);
            return element ? element.value.trim() : defaultValue;
        };

        // 获取元素值并去除所有空格（用于邮箱、服务器地址等不应包含空格的字段）
        const getElementValueNoSpaces = (id, defaultValue = '') => {
            const element = document.getElementById(id);
            return element ? element.value.replace(/\s+/g, '') : defaultValue;
        };

        const getElementChecked = (id, defaultValue = false) => {
            const element = document.getElementById(id);
            return element ? element.checked : defaultValue;
        };

        const getRadioValue = (name, defaultValue = '') => {
            const element = document.querySelector(`input[name="${name}"]:checked`);
            return element ? element.value : defaultValue;
        };

        return {
            editor_type: getRadioValue('editor', selectedEditor),
            email: {
                domain: getElementValueNoSpaces('emailDomain', 'xx.com'),
                use_temp_mail: getRadioValue('emailType', 'temp') === 'temp',
                temp_mail: {
                    email: getElementValueNoSpaces('tempMailEmail'),
                    pin: getElementValueNoSpaces('tempMailPin')
                },
                imap: {
                    server: getElementValueNoSpaces('imapServer', 'imap.qq.com'),
                    port: parseInt(getElementValueNoSpaces('imapPort', '993')) || 993,
                    user: getElementValueNoSpaces('imapUser'),
                    password: getElementValueNoSpaces('imapPassword'),
                    folder: getElementValue('imapFolder', 'INBOX')
                },
                generation: {
                    username_length: parseInt(getElementValue('usernameLength', '9')) || 9
                },
                retry: {
                    max_retries: parseInt(getElementValue('maxRetries', '30')) || 30,
                    retry_interval: parseInt(getElementValue('retryInterval', '1')) || 1
                },
                auto_copy: getElementChecked('autoCopy', false)
            }
        };
    } catch (error) {
        console.error('Error collecting settings data:', error);
        showToast(`收集设置数据失败: ${error.message}`, 'error');
        return null;
    }
}

function saveSettings() {
    try {
        if (!bridge || !bridge.save_config) {
            throw new Error('Bridge未初始化或save_config方法不可用');
        }

        const config = collectSettingsData();
        if (!config) {
            throw new Error('收集设置数据失败');
        }

        bridge.save_config(config, (success) => {
            try {
                if (success) {
                    showToast('设置已保存', 'success');

                    // 如果切换了编辑器，更新选择
                    const newEditor = config.editor_type;
                    if (newEditor !== selectedEditor) {
                        selectedEditor = newEditor;
                        updateEditorDisplay();
                    }

                    // 更新浮动卡片信息
                    updateFloatingCards(config);

                    setTimeout(() => {
                        closeDialog();
                    }, 1000);
                } else {
                    showToast('保存设置失败', 'error');
                }
            } catch (error) {
                showToast(`处理保存结果时发生错误: ${error.message}`, 'error');
                console.error('Error in saveSettings callback:', error);
            }
        });
    } catch (error) {
        showToast(`保存设置失败: ${error.message}`, 'error');
        console.error('Error in saveSettings:', error);
    }
}

function handleConfigUpdate(config) {
    // 配置更新时的处理
    console.log('配置已更新:', config);
}

// 添加必要的样式
const style = document.createElement('style');
style.textContent = `
    .reset-dialog ul {
        margin: 20px 0;
        padding-left: 20px;
        color: var(--text-secondary);
    }
    
    .reset-dialog li {
        margin: 8px 0;
    }
    
    .warning-text {
        color: var(--purple-light);
        margin-top: 20px;
    }
    
    .dialog-actions {
        display: flex;
        gap: 16px;
        margin-top: 30px;
        justify-content: center;
    }
    
    .progress-container {
        margin-top: 30px;
    }
    
    .progress-bar {
        width: 100%;
        height: 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        overflow: hidden;
    }
    
    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--purple-primary), var(--purple-light));
        transition: width 0.3s ease;
        width: 0%;
    }
    
    .progress-text {
        text-align: center;
        margin-top: 12px;
        color: var(--text-secondary);
    }
    
    .email-display {
        display: flex;
        gap: 12px;
        margin: 20px 0;
    }
    
    .email-display input {
        flex: 1;
        padding: 12px 16px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-sm);
        color: var(--text-primary);
        font-size: 16px;
    }
    
    .copy-btn {
        padding: 12px 24px;
        background: rgba(139, 92, 246, 0.1);
        border: 1px solid var(--purple-primary);
        border-radius: var(--radius-sm);
        color: var(--purple-light);
        cursor: pointer;
        transition: var(--transition);
    }
    
    .copy-btn:hover {
        background: rgba(139, 92, 246, 0.2);
    }
    
    .loading-spinner {
        width: 50px;
        height: 50px;
        margin: 20px auto;
        border: 3px solid rgba(255, 255, 255, 0.1);
        border-top-color: var(--purple-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    .verification-code {
        font-size: 48px;
        font-weight: 900;
        color: var(--purple-primary);
        letter-spacing: 8px;
        text-shadow: 0 0 30px var(--purple-glow);
        display: block;
        text-align: center;
        margin: 30px 0;
    }
    
    .info-text {
        text-align: center;
        color: var(--text-secondary);
        margin: 20px 0;
    }
    
    .settings-content {
        min-width: 600px;
        display: flex;
        flex-direction: column;
        height: 500px;
    }

    .settings-body {
        flex: 1;
        overflow-y: auto;
        padding: 20px 0;
    }

    .settings-footer {
        border-top: 1px solid var(--border-color);
        padding: 20px 0 0 0;
        margin-top: auto;
    }
    
    .settings-tabs {
        display: flex;
        gap: 16px;
        margin-bottom: 30px;
        border-bottom: 1px solid var(--border-color);
    }
    
    .tab-btn {
        padding: 12px 24px;
        background: transparent;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        transition: var(--transition);
        position: relative;
    }
    
    .tab-btn.active {
        color: var(--purple-primary);
    }
    
    .tab-btn.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--purple-primary);
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: var(--text-secondary);
        font-size: 14px;
    }
    
    .form-group input[type="text"],
    .form-group input[type="email"],
    .form-group input[type="password"],
    .form-group input[type="number"] {
        width: 100%;
        padding: 12px 16px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-sm);
        color: var(--text-primary);
        font-size: 16px;
        transition: var(--transition);
    }
    
    .form-group input:focus {
        outline: none;
        border-color: var(--purple-primary);
        background: rgba(139, 92, 246, 0.05);
    }
    
    .radio-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .radio-group label {
        display: flex;
        align-items: center;
        cursor: pointer;
    }
    
    .radio-group input[type="radio"] {
        margin-right: 8px;
    }
    
    .email-config-section {
        margin-top: 20px;
        padding: 20px;
        background: rgba(255, 255, 255, 0.03);
        border-radius: var(--radius-md);
    }
    
    .email-config-section h5 {
        margin-bottom: 16px;
        color: var(--purple-light);
    }
    
    .editor-switch {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .editor-switch label {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--radius-sm);
        transition: var(--transition);
    }
    
    .editor-switch label:hover {
        background: rgba(139, 92, 246, 0.1);
    }
    
    .editor-switch input[type="radio"] {
        margin-right: 12px;
    }
`;
document.head.appendChild(style); 