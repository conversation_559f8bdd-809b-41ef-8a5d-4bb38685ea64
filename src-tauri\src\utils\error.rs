use thiserror::Error;

/// 应用错误类型
/// 统一的错误处理，对应原版 Python 的异常处理模式
#[derive(Error, Debug)]
pub enum AppError {
    #[error("配置错误: {0}")]
    Config(String),
    
    #[error("编辑器检测错误: {0}")]
    Editor(String),
    
    #[error("邮箱管理错误: {0}")]
    Email(String),
    
    #[error("账号管理错误: {0}")]
    Account(String),
    
    #[error("重置功能错误: {0}")]
    Reset(String),
    
    #[error("版本检查错误: {0}")]
    Version(String),
    
    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("JSON错误: {0}")]
    Json(#[from] serde_json::Error),
    
    #[error("网络错误: {0}")]
    Network(#[from] reqwest::Error),
    
    #[error("数据库错误: {0}")]
    Database(#[from] rusqlite::Error),
    
    #[error("其他错误: {0}")]
    Other(String),
}
