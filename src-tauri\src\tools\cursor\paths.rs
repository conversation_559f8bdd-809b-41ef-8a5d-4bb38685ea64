/// Cursor 路径工具
/// 
/// 对应原版 Python 的 augment_tools/cursor/utils/paths.py
/// 提供跨平台的 Cursor 相关路径获取功能

use std::path::PathBuf;
use dirs;

/// 获取用户主目录
/// 对应原版 Python 的 get_home_dir 函数
pub fn get_home_dir() -> String {
    dirs::home_dir()
        .unwrap_or_else(|| PathBuf::from("."))
        .to_string_lossy()
        .to_string()
}

/// 获取应用数据目录
/// 对应原版 Python 的 get_app_data_dir 函数
pub fn get_app_data_dir() -> String {
    #[cfg(target_os = "windows")]
    {
        std::env::var("APPDATA").unwrap_or_else(|_| {
            let home = get_home_dir();
            format!("{}\\AppData\\Roaming", home)
        })
    }
    
    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        format!("{}/Library/Application Support", home)
    }
    
    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        format!("{}/.config", home)
    }
}

/// 获取 Cursor storage.json 文件路径
/// 对应原版 Python 的 get_storage_path 函数
/// 
/// 平台特定路径：
/// - Windows: %APPDATA%/Cursor/User/globalStorage/storage.json
/// - macOS: ~/Library/Application Support/Cursor/User/globalStorage/storage.json
/// - Linux: ~/.config/Cursor/User/globalStorage/storage.json
pub fn get_storage_path() -> String {
    #[cfg(target_os = "windows")]
    {
        let base_path = get_app_data_dir();
        format!("{}\\Cursor\\User\\globalStorage\\storage.json", base_path)
    }
    
    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        format!("{}/Library/Application Support/Cursor/User/globalStorage/storage.json", home)
    }
    
    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        format!("{}/.config/Cursor/User/globalStorage/storage.json", home)
    }
}

/// 获取 Cursor 状态数据库路径
/// 对应原版 Python 的 get_db_path 函数
/// 
/// 平台特定路径：
/// - Windows: %APPDATA%/Cursor/User/globalStorage/state.vscdb
/// - macOS: ~/Library/Application Support/Cursor/User/globalStorage/state.vscdb
/// - Linux: ~/.config/Cursor/User/globalStorage/state.vscdb
pub fn get_db_path() -> String {
    #[cfg(target_os = "windows")]
    {
        let base_path = get_app_data_dir();
        format!("{}\\Cursor\\User\\globalStorage\\state.vscdb", base_path)
    }
    
    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        format!("{}/Library/Application Support/Cursor/User/globalStorage/state.vscdb", home)
    }
    
    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        format!("{}/.config/Cursor/User/globalStorage/state.vscdb", home)
    }
}

/// 获取 Cursor 机器 ID 文件路径（主路径）
/// 对应原版 Python 的 get_machine_id_path 函数
///
/// 平台特定路径：
/// - Windows: %APPDATA%/Cursor/User/machineid
/// - macOS: ~/Library/Application Support/Cursor/machineid
/// - Linux: ~/.config/Cursor/User/machineid
pub fn get_machine_id_path() -> String {
    #[cfg(target_os = "windows")]
    {
        let base_path = get_app_data_dir();
        format!("{}\\Cursor\\User\\machineid", base_path)
    }

    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        format!("{}/Library/Application Support/Cursor/machineid", home)
    }

    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        format!("{}/.config/Cursor/User/machineid", home)
    }
}

/// 获取所有可能的 Cursor 机器 ID 文件路径
/// 根据用户反馈，machineid文件可能存在于多个位置
///
/// 返回所有可能的路径，按优先级排序：
/// - Windows:
///   1. %APPDATA%/Cursor/User/machineid (标准路径)
///   2. %APPDATA%/Cursor/machineid (备选路径)
/// - macOS:
///   1. ~/Library/Application Support/Cursor/machineid (标准路径)
///   2. ~/Library/Application Support/Cursor/User/machineid (备选路径)
/// - Linux:
///   1. ~/.config/Cursor/User/machineid (标准路径)
///   2. ~/.config/Cursor/machineid (备选路径)
pub fn get_all_machine_id_paths() -> Vec<String> {
    #[cfg(target_os = "windows")]
    {
        let base_path = get_app_data_dir();
        vec![
            format!("{}\\Cursor\\User\\machineid", base_path),      // 标准路径
            format!("{}\\Cursor\\machineid", base_path),            // 备选路径
        ]
    }

    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        vec![
            format!("{}/Library/Application Support/Cursor/machineid", home),      // 标准路径
            format!("{}/Library/Application Support/Cursor/User/machineid", home), // 备选路径
        ]
    }

    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        vec![
            format!("{}/.config/Cursor/User/machineid", home),     // 标准路径
            format!("{}/.config/Cursor/machineid", home),          // 备选路径
        ]
    }
}

/// 获取 Cursor 工作区存储路径
/// 对应原版 Python 的 get_workspace_storage_path 函数
/// 
/// 平台特定路径：
/// - Windows: %APPDATA%/Cursor/User/workspaceStorage
/// - macOS: ~/Library/Application Support/Cursor/User/workspaceStorage
/// - Linux: ~/.config/Cursor/User/workspaceStorage
pub fn get_workspace_storage_path() -> String {
    #[cfg(target_os = "windows")]
    {
        let base_path = get_app_data_dir();
        format!("{}\\Cursor\\User\\workspaceStorage", base_path)
    }
    
    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        format!("{}/Library/Application Support/Cursor/User/workspaceStorage", home)
    }
    
    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        format!("{}/.config/Cursor/User/workspaceStorage", home)
    }
}
