/// JetBrains 设备代码生成工具
/// 
/// 负责生成JetBrains IDE使用的设备ID和用户ID
/// 对应 augment-vip-rust-master 的UUID生成逻辑

use uuid::Uuid;

/// 生成永久设备ID
/// JetBrains使用标准UUID格式作为设备标识符
/// 
/// Returns:
///     String: 新生成的设备ID (UUID v4格式)
pub fn generate_permanent_device_id() -> String {
    Uuid::new_v4().to_string()
}

/// 生成永久用户ID
/// JetBrains使用标准UUID格式作为用户标识符
/// 
/// Returns:
///     String: 新生成的用户ID (UUID v4格式)
pub fn generate_permanent_user_id() -> String {
    Uuid::new_v4().to_string()
}

/// 验证UUID格式是否有效
/// 
/// Args:
///     uuid_str: 要验证的UUID字符串
/// 
/// Returns:
///     bool: 如果是有效的UUID格式返回true，否则返回false
pub fn is_valid_uuid(uuid_str: &str) -> bool {
    Uuid::parse_str(uuid_str).is_ok()
}

/// 生成所有JetBrains需要的ID
/// 
/// Returns:
///     (String, String): (设备ID, 用户ID)
pub fn generate_all_ids() -> (String, String) {
    (
        generate_permanent_device_id(),
        generate_permanent_user_id(),
    )
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_device_id() {
        let device_id = generate_permanent_device_id();
        println!("Generated device ID: {}", device_id);
        assert!(is_valid_uuid(&device_id));
        assert_eq!(device_id.len(), 36); // UUID标准长度
    }

    #[test]
    fn test_generate_user_id() {
        let user_id = generate_permanent_user_id();
        println!("Generated user ID: {}", user_id);
        assert!(is_valid_uuid(&user_id));
        assert_eq!(user_id.len(), 36); // UUID标准长度
    }

    #[test]
    fn test_uuid_validation() {
        // 有效的UUID
        assert!(is_valid_uuid("550e8400-e29b-41d4-a716-************"));
        
        // 无效的UUID
        assert!(!is_valid_uuid("invalid-uuid"));
        assert!(!is_valid_uuid(""));
        assert!(!is_valid_uuid("123"));
    }

    #[test]
    fn test_generate_all_ids() {
        let (device_id, user_id) = generate_all_ids();
        
        assert!(is_valid_uuid(&device_id));
        assert!(is_valid_uuid(&user_id));
        assert_ne!(device_id, user_id); // 确保生成的ID不同
        
        println!("Device ID: {}", device_id);
        println!("User ID: {}", user_id);
    }

    #[test]
    fn test_id_uniqueness() {
        // 测试多次生成的ID是否唯一
        let mut device_ids = std::collections::HashSet::new();
        let mut user_ids = std::collections::HashSet::new();
        
        for _ in 0..100 {
            let (device_id, user_id) = generate_all_ids();
            device_ids.insert(device_id);
            user_ids.insert(user_id);
        }
        
        // 100次生成应该产生100个不同的ID
        assert_eq!(device_ids.len(), 100);
        assert_eq!(user_ids.len(), 100);
    }
}
