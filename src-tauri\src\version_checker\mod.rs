//! 独立版本检查模块
//! 
//! 这是一个完全独立的版本检查模块，可以直接移植到其他Rust项目中使用。
//! 提供了完整的版本检查、验证流程和用户交互功能。
//! 
//! # 特性
//! 
//! - 完整的版本检查流程
//! - 远程配置获取和解密
//! - 用户验证（验证码和VIP QQ）
//! - 错误处理和日志记录
//! - 可配置的参数
//! - 简洁的API接口
//! 
//! # 使用示例
//! 
//! ```rust
//! use version_checker::{VersionChecker, VersionCheckerConfig};
//! 
//! #[tokio::main]
//! async fn main() -> Result<(), Box<dyn std::error::Error>> {
//!     let config = VersionCheckerConfig::new()
//!         .with_remote_url("https://app.yan.vin/version/YAugment/version.json")
//!         .with_current_version("1.0.0")
//!         .with_app_name("YAugment");
//!     
//!     let checker = VersionChecker::new(config)?;
//!     
//!     if checker.check_and_verify().await? {
//!         println!("版本检查通过，可以继续运行程序");
//!     } else {
//!         println!("版本检查失败，程序退出");
//!     }
//!     
//!     Ok(())
//! }
//! ```

use std::time::SystemTime;
use serde::{Deserialize, Serialize};
use log::{info, warn, error, debug};

/// 版本检查器配置
#[derive(Debug, Clone)]
pub struct VersionCheckerConfig {
    /// 远程配置URL
    pub remote_url: String,
    /// 当前版本
    pub current_version: String,
    /// 应用名称
    pub app_name: String,
    /// 平台信息
    pub platform: String,
    /// 机器UUID
    pub machine_uuid: String,
    /// 缓存持续时间（秒）
    pub cache_duration_seconds: u64,
    /// 是否启用日志
    pub enable_logging: bool,
    /// 自定义用户代理
    pub user_agent: Option<String>,
    /// 连接超时时间（秒）
    pub timeout_seconds: u64,
}

impl Default for VersionCheckerConfig {
    fn default() -> Self {
        Self {
            remote_url: "https://app.yan.vin/version/YAugment/version.json".to_string(),
            current_version: "1.0.0".to_string(),
            app_name: "YAugment".to_string(),
            platform: std::env::consts::OS.to_string(),
            machine_uuid: uuid::Uuid::new_v4().to_string(),
            cache_duration_seconds: 300, // 5分钟
            enable_logging: true,
            user_agent: None,
            timeout_seconds: 30,
        }
    }
}

impl VersionCheckerConfig {
    /// 创建新的配置
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置远程配置URL
    pub fn with_remote_url<S: Into<String>>(mut self, url: S) -> Self {
        self.remote_url = url.into();
        self
    }

    /// 设置当前版本
    pub fn with_current_version<S: Into<String>>(mut self, version: S) -> Self {
        self.current_version = version.into();
        self
    }

    /// 设置应用名称
    pub fn with_app_name<S: Into<String>>(mut self, name: S) -> Self {
        self.app_name = name.into();
        self
    }

    /// 设置平台信息
    pub fn with_platform<S: Into<String>>(mut self, platform: S) -> Self {
        self.platform = platform.into();
        self
    }

    /// 设置机器UUID
    pub fn with_machine_uuid<S: Into<String>>(mut self, uuid: S) -> Self {
        self.machine_uuid = uuid.into();
        self
    }

    /// 设置缓存持续时间
    pub fn with_cache_duration(mut self, seconds: u64) -> Self {
        self.cache_duration_seconds = seconds;
        self
    }

    /// 设置是否启用日志
    pub fn with_logging(mut self, enable: bool) -> Self {
        self.enable_logging = enable;
        self
    }

    /// 设置用户代理
    pub fn with_user_agent<S: Into<String>>(mut self, user_agent: S) -> Self {
        self.user_agent = Some(user_agent.into());
        self
    }

    /// 设置连接超时时间
    pub fn with_timeout(mut self, seconds: u64) -> Self {
        self.timeout_seconds = seconds;
        self
    }
}

/// 版本检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionCheckResult {
    /// 是否可以继续运行
    pub can_continue: bool,
    /// 是否处于维护模式
    pub maintenance_mode: bool,
    /// 维护消息
    pub maintenance_message: Option<String>,
    /// 是否强制更新
    pub force_update: bool,
    /// 是否需要更新
    pub needs_update: bool,
    /// 更新消息
    pub update_message: Option<String>,
    /// 更新URL
    pub update_url: Option<String>,
    /// 最新版本
    pub latest_version: Option<String>,
    /// 是否需要免责声明
    pub disclaimer_needed: bool,
    /// 是否需要验证
    pub verification_needed: bool,
    /// 全局通知
    pub global_notification: Option<GlobalNotification>,
    /// 错误信息
    pub error_message: Option<String>,
}

/// 全局通知
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalNotification {
    /// 是否显示
    pub show: bool,
    /// 通知消息
    pub message: String,
    /// 通知级别
    pub level: String,
}

/// 版本检查错误
#[derive(Debug, thiserror::Error)]
pub enum VersionCheckError {
    #[error("网络连接错误: {0}")]
    NetworkError(String),
    #[error("配置解析错误: {0}")]
    ConfigError(String),
    #[error("解密错误: {0}")]
    DecryptionError(String),
    #[error("验证错误: {0}")]
    VerificationError(String),
    #[error("用户取消操作")]
    UserCancelled,
    #[error("内部错误: {0}")]
    InternalError(String),
}

/// 独立版本检查器
/// 
/// 这是一个完全独立的版本检查器，可以直接移植到其他项目中使用。
/// 它封装了完整的版本检查流程，包括远程配置获取、解密、验证等功能。
pub struct VersionChecker {
    config: VersionCheckerConfig,
    cached_result: Option<(VersionCheckResult, SystemTime)>,
    http_client: reqwest::Client,
}

impl VersionChecker {
    /// 创建新的版本检查器
    pub fn new(config: VersionCheckerConfig) -> Result<Self, VersionCheckError> {
        // 初始化日志
        if config.enable_logging {
            env_logger::try_init().ok(); // 忽略重复初始化错误
        }

        // 创建HTTP客户端
        let mut client_builder = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(config.timeout_seconds));

        if let Some(user_agent) = &config.user_agent {
            client_builder = client_builder.user_agent(user_agent);
        }

        let http_client = client_builder
            .build()
            .map_err(|e| VersionCheckError::InternalError(format!("HTTP客户端创建失败: {}", e)))?;

        Ok(Self {
            config,
            cached_result: None,
            http_client,
        })
    }

    /// 主要的版本检查和验证API
    /// 
    /// 这是模块的核心API，执行完整的版本检查和验证流程。
    /// 
    /// # 返回值
    /// 
    /// - `Ok(true)`: 版本检查通过，可以继续运行程序
    /// - `Ok(false)`: 版本检查失败，程序应该退出
    /// - `Err(error)`: 发生错误
    pub async fn check_and_verify(&mut self) -> Result<bool, VersionCheckError> {
        if self.config.enable_logging {
            info!("开始版本检查流程 - 应用: {}, 版本: {}", 
                  self.config.app_name, self.config.current_version);
        }

        // 1. 检查缓存
        if let Some((cached_result, cached_time)) = &self.cached_result {
            let now = SystemTime::now();
            if let Ok(duration) = now.duration_since(*cached_time) {
                if duration.as_secs() < self.config.cache_duration_seconds {
                    if self.config.enable_logging {
                        debug!("使用缓存的版本检查结果");
                    }
                    return Ok(cached_result.can_continue);
                }
            }
        }

        // 2. 执行版本检查
        let result = self.perform_version_check().await?;

        // 3. 更新缓存
        self.cached_result = Some((result.clone(), SystemTime::now()));

        // 4. 处理结果
        Ok(result.can_continue)
    }

    /// 获取最后的检查结果
    pub fn get_last_result(&self) -> Option<&VersionCheckResult> {
        self.cached_result.as_ref().map(|(result, _)| result)
    }

    /// 清除缓存
    pub fn clear_cache(&mut self) {
        self.cached_result = None;
    }

    /// 获取配置
    pub fn get_config(&self) -> &VersionCheckerConfig {
        &self.config
    }

    /// 执行版本检查
    async fn perform_version_check(&self) -> Result<VersionCheckResult, VersionCheckError> {
        if self.config.enable_logging {
            debug!("开始获取远程配置: {}", self.config.remote_url);
        }

        // 1. 获取远程配置
        let response = self.http_client
            .get(&self.config.remote_url)
            .send()
            .await
            .map_err(|e| {
                if self.config.enable_logging {
                    error!("网络请求失败: {}", e);
                }
                VersionCheckError::NetworkError(format!("无法连接到服务器: {}", e))
            })?;

        if !response.status().is_success() {
            let error_msg = format!("服务器返回错误状态: {}", response.status());
            if self.config.enable_logging {
                error!("{}", error_msg);
            }
            return Err(VersionCheckError::NetworkError(error_msg));
        }

        let response_text = response.text().await
            .map_err(|e| VersionCheckError::NetworkError(format!("读取响应失败: {}", e)))?;

        // 2. 解析和解密配置
        let version_info = self.parse_and_decrypt_config(&response_text)?;

        // 3. 分析版本检查结果
        let result = self.analyze_version_check_result(&version_info)?;

        if self.config.enable_logging {
            info!("版本检查完成 - 可以继续: {}", result.can_continue);
        }

        Ok(result)
    }

    /// 解析和解密配置
    pub fn parse_and_decrypt_config(&self, response_text: &str) -> Result<serde_json::Value, VersionCheckError> {
        // 这里应该集成FernetCompat解密逻辑
        // 为了模块的独立性，我们提供一个简化的实现

        // 尝试直接解析JSON（如果是明文配置）
        if let Ok(json) = serde_json::from_str::<serde_json::Value>(response_text) {
            return Ok(json);
        }

        // 尝试Base64解码后解析（如果是Base64编码的JSON）
        use base64::Engine;
        if let Ok(decoded) = base64::engine::general_purpose::STANDARD.decode(response_text.trim()) {
            if let Ok(decoded_str) = String::from_utf8(decoded) {
                if let Ok(json) = serde_json::from_str::<serde_json::Value>(&decoded_str) {
                    return Ok(json);
                }
            }
        }

        // 如果需要加密解密，这里应该调用FernetCompat
        // 为了保持模块独立性，我们返回一个错误提示
        Err(VersionCheckError::DecryptionError(
            "配置需要解密，请实现FernetCompat解密逻辑".to_string()
        ))
    }

    /// 分析版本检查结果
    pub fn analyze_version_check_result(&self, version_info: &serde_json::Value) -> Result<VersionCheckResult, VersionCheckError> {
        let mut result = VersionCheckResult {
            can_continue: true,
            maintenance_mode: false,
            maintenance_message: None,
            force_update: false,
            needs_update: false,
            update_message: None,
            update_url: None,
            latest_version: None,
            disclaimer_needed: false,
            verification_needed: false,
            global_notification: None,
            error_message: None,
        };

        // 检查维护模式
        if let Some(maintenance) = version_info.get("maintenance_mode") {
            if maintenance.as_bool().unwrap_or(false) {
                result.maintenance_mode = true;
                result.can_continue = false;
                result.maintenance_message = version_info.get("maintenance_message")
                    .and_then(|v| v.as_str())
                    .map(|s| s.to_string());

                if self.config.enable_logging {
                    warn!("系统处于维护模式");
                }
                return Ok(result);
            }
        }

        // 检查版本更新
        if let Some(latest_version) = version_info.get("latest_version").and_then(|v| v.as_str()) {
            result.latest_version = Some(latest_version.to_string());

            if latest_version != self.config.current_version {
                result.needs_update = true;
                result.update_message = version_info.get("update_message")
                    .and_then(|v| v.as_str())
                    .map(|s| s.to_string());
                result.update_url = version_info.get("update_url")
                    .and_then(|v| v.as_str())
                    .map(|s| s.to_string());

                // 检查是否强制更新
                if let Some(force_update) = version_info.get("force_update") {
                    if force_update.as_bool().unwrap_or(false) {
                        result.force_update = true;
                        result.can_continue = false;

                        if self.config.enable_logging {
                            warn!("需要强制更新到版本: {}", latest_version);
                        }
                        return Ok(result);
                    }
                }
            }
        }

        // 检查免责声明
        if let Some(disclaimer) = version_info.get("disclaimer_needed") {
            result.disclaimer_needed = disclaimer.as_bool().unwrap_or(false);
        }

        // 检查验证需求
        if let Some(verification) = version_info.get("verification_needed") {
            result.verification_needed = verification.as_bool().unwrap_or(false);
        }

        // 检查全局通知
        if let Some(notification) = version_info.get("global_notification") {
            if let Some(show) = notification.get("show").and_then(|v| v.as_bool()) {
                if show {
                    result.global_notification = Some(GlobalNotification {
                        show: true,
                        message: notification.get("message")
                            .and_then(|v| v.as_str())
                            .unwrap_or("有新的通知")
                            .to_string(),
                        level: notification.get("level")
                            .and_then(|v| v.as_str())
                            .unwrap_or("info")
                            .to_string(),
                    });
                }
            }
        }

        Ok(result)
    }
}



