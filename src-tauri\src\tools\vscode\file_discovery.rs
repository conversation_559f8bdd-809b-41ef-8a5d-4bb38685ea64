/// VSCode 文件发现工具
/// 
/// 对应原始项目的 get_vscode_files 函数
/// 实现与原始项目完全相同的文件发现逻辑

use std::fs;
use std::path::{Path, PathBuf};

/// VSCode文件发现结果
#[derive(Debug, Clone)]
pub struct VscodeDiscoveryResult {
    pub global_storage_paths: Vec<PathBuf>,
    pub workspace_storage_paths: Vec<PathBuf>,
    pub machine_id_paths: Vec<PathBuf>,
}

/// 获取VSCode文件路径
/// 对应原始项目的 get_vscode_files 函数
/// 
/// 此函数实现与原始项目完全相同的逻辑：
/// 1. 搜索多个基础目录：config_dir, home_dir, data_dir
/// 2. 支持多种路径模式
/// 3. 同时处理 globalStorage 和 workspaceStorage
/// 4. 枚举所有VSCode变体目录
/// 
/// Args:
///     id: 机器ID字符串（通常是"machineId"）
/// 
/// Returns:
///     Option<VscodeDiscoveryResult>: 发现的VSCode文件路径，如果没有找到则返回None
pub fn get_vscode_files(id: &str) -> Option<VscodeDiscoveryResult> {
    let base_dirs = [dirs::config_dir(), dirs::home_dir(), dirs::data_dir()];
    
    // 全局存储模式（对应原始项目的 global_patterns）
    let global_patterns = [
        &["User", "globalStorage"] as &[&str],
        &["data", "User", "globalStorage"],
        &[id],
        &["data", id],
    ];
    
    // 工作区存储模式（对应原始项目的 workspace_patterns）
    let workspace_patterns = [
        &["User", "workspaceStorage"] as &[&str],
        &["data", "User", "workspaceStorage"],
    ];

    let mut global_storage_paths = Vec::new();
    let mut workspace_storage_paths = Vec::new();
    let mut machine_id_paths = Vec::new();

    // 遍历所有基础目录
    for base_dir in base_dirs.into_iter().filter_map(|dir| dir) {
        if let Ok(entries) = fs::read_dir(&base_dir) {
            for entry in entries.filter_map(|e| e.ok()) {
                if entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false) {
                    let entry_path = entry.path();
                    
                    // 处理全局存储路径
                    for pattern in &global_patterns {
                        let global_path = pattern.iter().fold(entry_path.clone(), |path, segment| path.join(segment));
                        if global_path.exists() {
                            global_storage_paths.push(global_path);
                        }
                    }
                    
                    // 处理工作区存储路径 - 枚举所有子目录
                    for pattern in &workspace_patterns {
                        let workspace_base = pattern.iter().fold(entry_path.clone(), |path, segment| path.join(segment));
                        if workspace_base.exists() {
                            if let Ok(workspace_entries) = fs::read_dir(&workspace_base) {
                                for workspace_entry in workspace_entries.filter_map(|e| e.ok()) {
                                    if workspace_entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false) {
                                        workspace_storage_paths.push(workspace_entry.path());
                                    }
                                }
                            }
                        }
                    }
                    
                    // 处理机器ID文件路径
                    // 检查是否是直接的machineId文件
                    let machine_id_file = entry_path.join(id);
                    if machine_id_file.exists() && machine_id_file.is_file() {
                        machine_id_paths.push(machine_id_file);
                    }
                    
                    // 检查data目录下的machineId文件
                    let data_machine_id_file = entry_path.join("data").join(id);
                    if data_machine_id_file.exists() && data_machine_id_file.is_file() {
                        machine_id_paths.push(data_machine_id_file);
                    }
                }
            }
        }
    }

    // 如果没有找到任何路径，返回None
    if global_storage_paths.is_empty() && workspace_storage_paths.is_empty() && machine_id_paths.is_empty() {
        return None;
    }

    Some(VscodeDiscoveryResult {
        global_storage_paths,
        workspace_storage_paths,
        machine_id_paths,
    })
}

/// 获取所有VSCode变体的文件路径
/// 包括VSCode、VSCode Insiders、Cursor等
/// 
/// Returns:
///     Option<VscodeDiscoveryResult>: 所有发现的VSCode变体文件路径
pub fn get_all_vscode_variants() -> Option<VscodeDiscoveryResult> {
    let variants = ["machineId", "Code", "code-insiders", "Cursor", "cursor"];
    
    let mut combined_result = VscodeDiscoveryResult {
        global_storage_paths: Vec::new(),
        workspace_storage_paths: Vec::new(),
        machine_id_paths: Vec::new(),
    };
    
    let mut found_any = false;
    
    for variant in &variants {
        if let Some(result) = get_vscode_files(variant) {
            found_any = true;
            combined_result.global_storage_paths.extend(result.global_storage_paths);
            combined_result.workspace_storage_paths.extend(result.workspace_storage_paths);
            combined_result.machine_id_paths.extend(result.machine_id_paths);
        }
    }
    
    if found_any {
        Some(combined_result)
    } else {
        None
    }
}

/// 检查路径是否包含storage.json文件
/// 
/// Args:
///     path: 要检查的路径
/// 
/// Returns:
///     bool: 如果路径包含storage.json文件则返回true
pub fn has_storage_json(path: &Path) -> bool {
    path.join("storage.json").exists()
}

/// 检查路径是否包含state.vscdb数据库文件
/// 
/// Args:
///     path: 要检查的路径
/// 
/// Returns:
///     bool: 如果路径包含state.vscdb文件则返回true
pub fn has_state_database(path: &Path) -> bool {
    path.join("state.vscdb").exists()
}

/// 获取路径下的storage.json文件路径
/// 
/// Args:
///     path: 基础路径
/// 
/// Returns:
///     PathBuf: storage.json文件的完整路径
pub fn get_storage_json_path(path: &Path) -> PathBuf {
    path.join("storage.json")
}

/// 获取路径下的state.vscdb文件路径
/// 
/// Args:
///     path: 基础路径
/// 
/// Returns:
///     PathBuf: state.vscdb文件的完整路径
pub fn get_state_database_path(path: &Path) -> PathBuf {
    path.join("state.vscdb")
}

/// 打印发现的VSCode文件信息（用于调试）
/// 
/// Args:
///     result: VSCode发现结果
pub fn print_discovery_info(result: &VscodeDiscoveryResult) {
    println!("发现的VSCode文件:");
    println!("  全局存储路径 ({} 个):", result.global_storage_paths.len());
    for path in &result.global_storage_paths {
        println!("    {}", path.display());
    }
    
    println!("  工作区存储路径 ({} 个):", result.workspace_storage_paths.len());
    for path in &result.workspace_storage_paths {
        println!("    {}", path.display());
    }
    
    println!("  机器ID文件路径 ({} 个):", result.machine_id_paths.len());
    for path in &result.machine_id_paths {
        println!("    {}", path.display());
    }
}
