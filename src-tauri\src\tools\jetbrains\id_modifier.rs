/// JetBrains ID 修改工具
/// 
/// 负责修改JetBrains IDE的设备ID和用户ID文件
/// 对应 augment-vip-rust-master 的 update_id_file 函数

use std::fs;
use std::path::Path;
use crate::utils::backup::create_backup;
use super::paths::{get_permanent_device_id_path, get_permanent_user_id_path};
use super::device_codes::{generate_permanent_device_id, generate_permanent_user_id};
use super::file_locker::lock_single_file;

/// ID修改结果结构体
#[derive(Debug, Clone, serde::Serialize)]
pub struct JetBrainsModifyResult {
    pub device_id_backup_path: Option<String>,
    pub user_id_backup_path: Option<String>,
    pub old_device_id: String,
    pub old_user_id: String,
    pub new_device_id: String,
    pub new_user_id: String,
    pub device_id_updated: bool,
    pub user_id_updated: bool,
}

/// 修改单个ID文件
/// 对应 augment-vip-rust-master 的 update_id_file 函数
///
/// Args:
///     file_path: ID文件路径
///     new_id: 新的ID值
///
/// Returns:
///     Result<(String, String), String>: (旧ID, 备份路径) 或错误信息
fn update_id_file(file_path: &Path, new_id: &str) -> Result<(String, String), String> {
    println!("正在更新JetBrains ID文件: {}", file_path.display());

    // 读取旧ID（如果文件存在）
    let old_id = if file_path.exists() {
        fs::read_to_string(file_path)
            .unwrap_or_default()
            .trim()
            .to_string()
    } else {
        String::new()
    };

    if !old_id.is_empty() {
        println!("旧ID: {}", old_id);
    }
    println!("新ID: {}", new_id);

    // 创建备份（如果文件存在）
    let backup_path = if file_path.exists() {
        create_backup(file_path)
            .map_err(|e| format!("创建备份失败: {}", e))?
            .to_string_lossy()
            .to_string()
    } else {
        // 如果文件不存在，创建父目录
        if let Some(parent) = file_path.parent() {
            fs::create_dir_all(parent)
                .map_err(|e| format!("创建目录失败: {}", e))?;
        }
        String::new()
    };

    // 删除文件（如果存在）- 对应原始项目的逻辑
    // 这样可以绕过权限问题，因为删除文件会移除所有权限限制
    if file_path.exists() {
        // 先移除只读属性和权限限制（所有平台）
        if let Ok(metadata) = fs::metadata(file_path) {
            let mut permissions = metadata.permissions();

            // Windows: 移除只读属性
            permissions.set_readonly(false);

            // Unix系统 (Linux/macOS): 设置写权限
            #[cfg(unix)]
            {
                use std::os::unix::fs::PermissionsExt;
                // 设置用户读写权限 (0o600 = rw-------)
                permissions.set_mode(0o600);
            }

            let _ = fs::set_permissions(file_path, permissions);
        }

        // 尝试删除文件
        if let Err(e) = fs::remove_file(file_path) {
            println!("普通删除失败: {}, 尝试强制删除", e);

            // Unix系统: 使用chmod + rm强制删除
            #[cfg(unix)]
            {
                // 先尝试chmod给予完全权限
                let _ = std::process::Command::new("chmod")
                    .arg("777")
                    .arg(&file_path.to_string_lossy().to_string())
                    .status();

                // 再次尝试删除
                let _ = fs::remove_file(file_path);
            }

            // macOS上使用强制删除（需要管理员权限时请手动以管理员身份启动）
            #[cfg(target_os = "macos")]
            {
                let _ = std::process::Command::new("rm")
                    .arg("-rf")
                    .arg(&file_path.to_string_lossy().to_string())
                    .status();
            }
        }
    }

    // 写入新ID到新文件
    fs::write(file_path, new_id)
        .map_err(|e| format!("写入ID文件失败: {}", e))?;

    // 锁定文件防止IDE重新生成（对应原始项目逻辑）
    match lock_single_file(file_path) {
        Ok(_) => {
            // 文件锁定成功，不需要额外打印
        }
        Err(e) => {
            eprintln!("警告: 文件锁定失败: {}", e);
            // 不中断流程，因为文件已经成功写入
        }
    }

    println!("成功更新ID文件: {}", file_path.display());
    Ok((old_id, backup_path))
}



/// 修改所有JetBrains ID文件
/// 包括PermanentDeviceId和PermanentUserId
/// 
/// Returns:
///     Result<JetBrainsModifyResult, String>: 修改结果或错误信息
pub fn modify_jetbrains_ids() -> Result<JetBrainsModifyResult, String> {
    println!("开始修改JetBrains ID文件...");

    // 生成新的ID
    let new_device_id = generate_permanent_device_id();
    let new_user_id = generate_permanent_user_id();

    let mut result = JetBrainsModifyResult {
        device_id_backup_path: None,
        user_id_backup_path: None,
        old_device_id: String::new(),
        old_user_id: String::new(),
        new_device_id: new_device_id.clone(),
        new_user_id: new_user_id.clone(),
        device_id_updated: false,
        user_id_updated: false,
    };

    // 更新设备ID文件
    if let Some(device_id_path) = get_permanent_device_id_path() {
        match update_id_file(&device_id_path, &new_device_id) {
            Ok((old_id, backup_path)) => {
                result.old_device_id = old_id;
                result.device_id_backup_path = if backup_path.is_empty() { None } else { Some(backup_path) };
                result.device_id_updated = true;
                println!("设备ID更新成功");
            }
            Err(e) => {
                eprintln!("设备ID更新失败: {}", e);
                // 继续处理用户ID，不中断整个流程
            }
        }
    } else {
        return Err("未找到JetBrains配置目录，请确保已安装JetBrains IDE".to_string());
    }

    // 更新用户ID文件
    if let Some(user_id_path) = get_permanent_user_id_path() {
        match update_id_file(&user_id_path, &new_user_id) {
            Ok((old_id, backup_path)) => {
                result.old_user_id = old_id;
                result.user_id_backup_path = if backup_path.is_empty() { None } else { Some(backup_path) };
                result.user_id_updated = true;
                println!("用户ID更新成功");
            }
            Err(e) => {
                eprintln!("用户ID更新失败: {}", e);
                // 继续执行，不中断
            }
        }
    }

    // 检查是否至少有一个ID更新成功
    if !result.device_id_updated && !result.user_id_updated {
        return Err("所有ID文件更新失败".to_string());
    }

    println!("JetBrains ID文件修改完成");
    Ok(result)
}

/// 检查JetBrains ID文件状态
/// 
/// Returns:
///     Result<serde_json::Value, String>: ID文件状态信息
pub fn check_jetbrains_id_status() -> Result<serde_json::Value, String> {
    use serde_json::json;

    let device_id_path = get_permanent_device_id_path();
    let user_id_path = get_permanent_user_id_path();

    let device_id_info = if let Some(path) = &device_id_path {
        if path.exists() {
            let content = fs::read_to_string(path).unwrap_or_default();
            json!({
                "exists": true,
                "path": path.to_string_lossy(),
                "content": content.trim(),
                "is_valid_uuid": super::device_codes::is_valid_uuid(content.trim())
            })
        } else {
            json!({
                "exists": false,
                "path": path.to_string_lossy(),
                "content": "",
                "is_valid_uuid": false
            })
        }
    } else {
        json!({
            "exists": false,
            "path": "",
            "content": "",
            "is_valid_uuid": false
        })
    };

    let user_id_info = if let Some(path) = &user_id_path {
        if path.exists() {
            let content = fs::read_to_string(path).unwrap_or_default();
            json!({
                "exists": true,
                "path": path.to_string_lossy(),
                "content": content.trim(),
                "is_valid_uuid": super::device_codes::is_valid_uuid(content.trim())
            })
        } else {
            json!({
                "exists": false,
                "path": path.to_string_lossy(),
                "content": "",
                "is_valid_uuid": false
            })
        }
    } else {
        json!({
            "exists": false,
            "path": "",
            "content": "",
            "is_valid_uuid": false
        })
    };

    Ok(json!({
        "jetbrains_installed": device_id_path.is_some() || user_id_path.is_some(),
        "device_id": device_id_info,
        "user_id": user_id_info
    }))
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use std::env;

    #[test]
    fn test_update_id_file() {
        let temp_dir = env::temp_dir().join("jetbrains_test");
        fs::create_dir_all(&temp_dir).unwrap();
        let test_file = temp_dir.join("test_id");

        // 测试创建新文件
        let new_id = "test-uuid-123";
        let result = update_id_file(&test_file, new_id);
        assert!(result.is_ok());

        let (old_id, _) = result.unwrap();
        assert_eq!(old_id, "");

        // 验证文件内容
        let content = fs::read_to_string(&test_file).unwrap();
        assert_eq!(content, new_id);

        // 测试更新现有文件
        let newer_id = "test-uuid-456";
        let result = update_id_file(&test_file, newer_id);
        assert!(result.is_ok());

        let (old_id, _) = result.unwrap();
        assert_eq!(old_id, new_id);

        // 验证更新后的内容
        let content = fs::read_to_string(&test_file).unwrap();
        assert_eq!(content, newer_id);

        // 清理测试文件
        let _ = fs::remove_file(&test_file);
        let _ = fs::remove_dir(&temp_dir);
    }
}
