use std::path::{Path, PathBuf};
use std::fs;
use std::time::{SystemTime, UNIX_EPOCH};

/// 创建文件备份
/// 对应原版 Python json_modifier.py 的 _create_backup 函数
/// 完全按照原版格式：<filename>.bak.<timestamp>
///
/// Args:
///     file_path: 要备份的文件路径
///
/// Returns:
///     Result<PathBuf, String>: 备份文件路径或错误信息
///
/// Format: <filename>.bak.<timestamp>
pub fn create_backup(file_path: &Path) -> Result<PathBuf, String> {
    // 获取当前时间戳（秒）- 与原版 Python 的 int(time.time()) 一致
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .map_err(|_| "获取时间戳失败".to_string())?
        .as_secs();

    // 构建备份文件路径：<filename>.bak.<timestamp>
    // 完全按照原版 Python 的格式：f"{file_path}.bak.{timestamp}"
    let backup_path = PathBuf::from(format!("{}.bak.{}",
        file_path.to_string_lossy(),
        timestamp
    ));

    // 使用 shutil.copy2 的等效操作 - 复制文件内容和元数据
    fs::copy(file_path, &backup_path)
        .map_err(|_| "创建备份失败".to_string())?;

    Ok(backup_path)
}
