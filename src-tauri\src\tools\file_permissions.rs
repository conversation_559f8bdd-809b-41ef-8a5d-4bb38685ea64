/// 文件权限管理工具
/// 
/// 提供跨平台的文件权限检查和修改功能
/// 确保重置操作前文件具有正确的读写权限

use std::fs;
use std::path::Path;

#[cfg(unix)]
use std::os::unix::fs::PermissionsExt;

/// 权限检查和修改结果
#[derive(Debug, Clone, serde::Serialize)]
pub struct PermissionResult {
    pub path: String,
    pub was_readonly: bool,
    pub permission_changed: bool,
    pub success: bool,
    pub error_message: Option<String>,
}

/// 检查文件是否为只读
/// 
/// Args:
///     file_path: 文件路径
/// 
/// Returns:
///     Result<bool, String>: 是否为只读或错误信息
pub fn is_file_readonly(file_path: &Path) -> Result<bool, String> {
    if !file_path.exists() {
        return Ok(false); // 文件不存在，不是只读问题
    }

    let metadata = fs::metadata(file_path)
        .map_err(|e| format!("Failed to get file metadata: {}", e))?;

    let permissions = metadata.permissions();
    
    #[cfg(windows)]
    {
        // Windows: 检查只读属性
        Ok(permissions.readonly())
    }
    
    #[cfg(unix)]
    {
        // Unix/Linux/macOS: 检查写权限
        let mode = permissions.mode();
        // 检查所有者写权限 (0o200)
        Ok((mode & 0o200) == 0)
    }
}

/// 移除文件的只读属性，使其可写
/// 
/// Args:
///     file_path: 文件路径
/// 
/// Returns:
///     Result<PermissionResult, String>: 权限修改结果或错误信息
pub fn make_file_writable(file_path: &Path) -> Result<PermissionResult, String> {
    let path_str = file_path.to_string_lossy().to_string();
    
    if !file_path.exists() {
        return Ok(PermissionResult {
            path: path_str,
            was_readonly: false,
            permission_changed: false,
            success: true,
            error_message: None,
        });
    }

    // 检查当前是否为只读
    let was_readonly = match is_file_readonly(file_path) {
        Ok(readonly) => readonly,
        Err(e) => {
            return Ok(PermissionResult {
                path: path_str,
                was_readonly: false,
                permission_changed: false,
                success: false,
                error_message: Some(format!("Failed to check readonly status: {}", e)),
            });
        }
    };

    if !was_readonly {
        // 文件已经可写，无需修改
        return Ok(PermissionResult {
            path: path_str,
            was_readonly: false,
            permission_changed: false,
            success: true,
            error_message: None,
        });
    }

    // 尝试修改权限
    let result = modify_file_permissions(file_path);
    
    match result {
        Ok(_) => {
            println!("成功修复文件权限: {}", path_str);
            Ok(PermissionResult {
                path: path_str,
                was_readonly: true,
                permission_changed: true,
                success: true,
                error_message: None,
            })
        }
        Err(e) => {
            eprintln!("警告: 无法修复文件权限 {}: {}", path_str, e);
            Ok(PermissionResult {
                path: path_str,
                was_readonly: true,
                permission_changed: false,
                success: false,
                error_message: Some(e),
            })
        }
    }
}

/// 修改文件权限使其可写
/// 
/// Args:
///     file_path: 文件路径
/// 
/// Returns:
///     Result<(), String>: 成功或错误信息
fn modify_file_permissions(file_path: &Path) -> Result<(), String> {
    let metadata = fs::metadata(file_path)
        .map_err(|e| format!("Failed to get file metadata: {}", e))?;

    let mut permissions = metadata.permissions();

    #[cfg(windows)]
    {
        // Windows: 移除只读属性
        permissions.set_readonly(false);
    }
    
    #[cfg(unix)]
    {
        // Unix/Linux/macOS: 添加所有者写权限
        let mode = permissions.mode();
        permissions.set_mode(mode | 0o200); // 添加所有者写权限
    }

    fs::set_permissions(file_path, permissions)
        .map_err(|e| format!("Failed to set file permissions: {}", e))?;

    Ok(())
}

/// 批量处理多个文件的权限
/// 
/// Args:
///     file_paths: 文件路径列表
/// 
/// Returns:
///     Vec<PermissionResult>: 每个文件的权限处理结果
pub fn make_files_writable(file_paths: &[String]) -> Vec<PermissionResult> {
    let mut results = Vec::new();
    
    for file_path in file_paths {
        let path_obj = Path::new(file_path);
        match make_file_writable(path_obj) {
            Ok(result) => results.push(result),
            Err(e) => {
                results.push(PermissionResult {
                    path: file_path.clone(),
                    was_readonly: false,
                    permission_changed: false,
                    success: false,
                    error_message: Some(e),
                });
            }
        }
    }
    
    results
}

/// 检查并修复单个文件的权限
/// 这是一个便捷函数，结合了检查和修改操作
/// 
/// Args:
///     file_path: 文件路径
/// 
/// Returns:
///     Result<bool, String>: 是否需要修改权限，或错误信息
pub fn ensure_file_writable(file_path: &Path) -> Result<bool, String> {
    if !file_path.exists() {
        return Ok(false); // 文件不存在，无需处理
    }

    let readonly = is_file_readonly(file_path)?;
    
    if readonly {
        let result = make_file_writable(file_path)?;
        if !result.success {
            return Err(result.error_message.unwrap_or_else(|| "Unknown permission error".to_string()));
        }
        Ok(true) // 权限已修改
    } else {
        Ok(false) // 无需修改
    }
}
