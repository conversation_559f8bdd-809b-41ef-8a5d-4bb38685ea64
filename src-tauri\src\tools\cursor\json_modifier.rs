/// Cursor JSON 修改工具
/// 
/// 对应原版 Python 的 augment_tools/cursor/augutils/json_modifier.py
/// 修改 Cursor storage.json 文件和机器 ID 文件中的遥测 ID

use std::fs;
use std::path::Path;
use serde_json::{Value, Map};
use sha2::{Sha256, Digest};
use crate::utils::backup::create_backup;
use crate::tools::file_locker::lock_single_file;
use crate::tools::file_permissions::PermissionResult;
use super::paths::{get_storage_path, get_all_machine_id_paths};
use super::device_codes::{generate_machine_id, generate_device_id};

/// 单个机器ID文件的处理结果
#[derive(Debug, Clone, serde::Serialize)]
pub struct MachineIdFileResult {
    pub path: String,
    pub backup_path: String,
    pub existed: bool,
    pub modified: bool,
}

/// 修改结果结构体
/// 对应原版 Python 函数的返回字典，包含所有4个键的信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct ModifyResult {
    pub storage_backup_path: String,
    pub machine_id_files: Vec<MachineIdFileResult>, // 支持多个machineid文件
    pub permission_results: Vec<PermissionResult>, // 权限修改结果
    pub old_machine_id: String,
    pub old_device_id: String,
    pub old_mac_machine_id: String,
    pub old_service_machine_id: String,
    pub new_machine_id: String,
    pub new_device_id: String,
    pub new_mac_machine_id: String,
    pub new_service_machine_id: String,
}

/// 修改 Cursor storage.json 文件和机器 ID 文件中的遥测 ID
/// 对应原版 Python 的 modify_telemetry_ids 函数
///
/// 此函数：
/// 1. 创建 storage.json 和所有机器 ID 文件的备份
/// 2. 读取 storage.json 文件
/// 3. 生成新的机器和设备 ID
/// 4. 更新 storage.json 中的 telemetry.machineId 和 telemetry.devDeviceId 值
/// 5. 用新的机器 ID 更新所有找到的机器 ID 文件
/// 6. 保存修改后的文件
///
/// Returns:
///     Result<ModifyResult, String>: 包含操作结果的结构体或错误信息
pub fn modify_telemetry_ids() -> Result<ModifyResult, String> {
    let storage_path = get_storage_path();
    let all_machine_id_paths = get_all_machine_id_paths();

    // 检查storage.json文件是否存在
    if !std::path::Path::new(&storage_path).exists() {
        return Err(format!("Storage file not found: {}", storage_path));
    }

    // 检查至少有一个machineid文件存在
    let existing_machine_id_paths: Vec<String> = all_machine_id_paths
        .iter()
        .filter(|path| std::path::Path::new(path).exists())
        .cloned()
        .collect();

    if existing_machine_id_paths.is_empty() {
        return Err(format!("No machine ID files found in any of the expected locations: {:?}", all_machine_id_paths));
    }

    println!("发现 {} 个机器ID文件: {:?}", existing_machine_id_paths.len(), existing_machine_id_paths);

    // 检查并修复文件权限
    println!("正在检查并修复文件权限...");

    // 准备需要检查权限的文件列表
    let mut files_to_check = vec![storage_path.clone()];
    files_to_check.extend(existing_machine_id_paths.clone());

    // 检查并修复所有文件的权限
    let mut permission_results = Vec::new();
    for file_path in &files_to_check {
        let path_obj = Path::new(file_path);
        if path_obj.exists() {
            match crate::tools::file_permissions::make_file_writable(path_obj) {
                Ok(result) => {
                    if result.permission_changed {
                        println!("已修复文件权限: {}", file_path);
                    }
                    permission_results.push(result);
                }
                Err(e) => {
                    eprintln!("警告: 修复文件权限失败 {}: {}", file_path, e);
                    permission_results.push(PermissionResult {
                        path: file_path.clone(),
                        was_readonly: false,
                        permission_changed: false,
                        success: false,
                        error_message: Some(e),
                    });
                }
            }
        }
    }

    // 创建storage.json备份
    let storage_backup_path = create_backup(Path::new(&storage_path))
        .map_err(|e| format!("Failed to create storage backup: {}", e))?;

    // 处理所有machineid文件的备份和修改
    let mut machine_id_files = Vec::new();

    for machine_id_path in &all_machine_id_paths {
        let path_obj = Path::new(machine_id_path);
        let existed = path_obj.exists();

        if existed {
            // 创建备份
            let backup_path = create_backup(path_obj)
                .map_err(|e| format!("Failed to create machine ID backup for {}: {}", machine_id_path, e))?;

            machine_id_files.push(MachineIdFileResult {
                path: machine_id_path.clone(),
                backup_path: backup_path.to_string_lossy().to_string(),
                existed: true,
                modified: false, // 稍后更新
            });
        } else {
            machine_id_files.push(MachineIdFileResult {
                path: machine_id_path.clone(),
                backup_path: String::new(),
                existed: false,
                modified: false,
            });
        }
    }

    // 读取 storage.json 文件
    let storage_content = fs::read_to_string(&storage_path)
        .map_err(|e| format!("Failed to read storage file: {}", e))?;

    let mut data: Map<String, Value> = serde_json::from_str(&storage_content)
        .map_err(|e| format!("Failed to parse storage JSON: {}", e))?;

    // 存储旧值（所有4个键）
    let old_machine_id = data.get("telemetry.machineId")
        .and_then(|v| v.as_str())
        .unwrap_or("")
        .to_string();

    let old_device_id = data.get("telemetry.devDeviceId")
        .and_then(|v| v.as_str())
        .unwrap_or("")
        .to_string();

    let old_mac_machine_id = data.get("telemetry.macMachineId")
        .and_then(|v| v.as_str())
        .unwrap_or("")
        .to_string();

    let old_service_machine_id = data.get("storage.serviceMachineId")
        .and_then(|v| v.as_str())
        .unwrap_or("")
        .to_string();

    // 生成新 ID（对应原始项目的4个键）
    let new_machine_id = generate_machine_id();
    let new_device_id = generate_device_id();

    // 1. telemetry.machineId - 使用SHA-256哈希（对应原始项目逻辑）
    let new_machine_id_hash = format!("{:x}", Sha256::digest(uuid::Uuid::new_v4().as_bytes()));
    data.insert("telemetry.machineId".to_string(), Value::String(new_machine_id_hash.clone()));

    // 2. telemetry.devDeviceId - 使用UUID v4（对应原始项目逻辑）
    data.insert("telemetry.devDeviceId".to_string(), Value::String(new_device_id.clone()));

    // 3. telemetry.macMachineId - 使用SHA-256哈希（对应原始项目逻辑）
    let new_mac_machine_id = format!("{:x}", Sha256::digest(uuid::Uuid::new_v4().as_bytes()));
    data.insert("telemetry.macMachineId".to_string(), Value::String(new_mac_machine_id.clone()));

    // 4. storage.serviceMachineId - 使用SHA-256哈希（对应原始项目逻辑）
    let new_service_machine_id = format!("{:x}", Sha256::digest(uuid::Uuid::new_v4().as_bytes()));
    data.insert("storage.serviceMachineId".to_string(), Value::String(new_service_machine_id.clone()));

    // 将修改后的内容写回 storage.json
    let updated_content = serde_json::to_string_pretty(&data)
        .map_err(|e| format!("Failed to serialize JSON: {}", e))?;

    fs::write(&storage_path, updated_content)
        .map_err(|e| format!("Failed to write storage file: {}", e))?;

    // 将新的机器 ID 写入所有存在的机器 ID 文件
    for machine_id_file in &mut machine_id_files {
        if machine_id_file.existed {
            match fs::write(&machine_id_file.path, &new_machine_id) {
                Ok(_) => {
                    machine_id_file.modified = true;
                    println!("成功更新机器ID文件: {}", machine_id_file.path);
                }
                Err(e) => {
                    eprintln!("警告: 无法写入机器ID文件 {}: {}", machine_id_file.path, e);
                    // 不返回错误，继续处理其他文件
                }
            }
        }
    }

    // 锁定文件防止IDE重新生成（对应原始项目逻辑）
    let storage_path_obj = Path::new(&storage_path);

    // 锁定storage.json文件
    if let Err(e) = lock_single_file(storage_path_obj) {
        eprintln!("警告: storage.json文件锁定失败: {}", e);
    }

    // 锁定所有存在的machineId文件
    for machine_id_file in &machine_id_files {
        if machine_id_file.existed {
            let machine_id_path_obj = Path::new(&machine_id_file.path);
            if let Err(e) = lock_single_file(machine_id_path_obj) {
                eprintln!("警告: machineId文件锁定失败 {}: {}", machine_id_file.path, e);
            }
        }
    }

    // 统计修改结果
    let modified_count = machine_id_files.iter().filter(|f| f.modified).count();
    let total_found = machine_id_files.iter().filter(|f| f.existed).count();

    println!("机器ID文件处理完成: {}/{} 个文件已修改", modified_count, total_found);

    Ok(ModifyResult {
        storage_backup_path: storage_backup_path.to_string_lossy().to_string(),
        machine_id_files,
        permission_results,
        old_machine_id,
        old_device_id,
        old_mac_machine_id,
        old_service_machine_id,
        new_machine_id: new_machine_id_hash,
        new_device_id,
        new_mac_machine_id,
        new_service_machine_id,
    })
}


