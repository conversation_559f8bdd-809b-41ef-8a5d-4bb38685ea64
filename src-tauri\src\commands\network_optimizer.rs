/// 网络优化器命令
/// 
/// 对应原版 Python 的 test/yaugment_optimizer.py 功能
/// 提供 Tauri 命令用于前端调用网络优化功能

use tauri::{State, AppHandle, Emitter};
use crate::app_state::AppState;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::process::Command;
use std::time::{Duration, Instant};
use tokio::time::sleep;
use anyhow::{Result, anyhow};
use std::sync::Arc;
use tokio::sync::Mutex as TokioMutex;

/// 创建隐藏终端窗口的命令
/// 防止在执行系统命令时弹出终端窗口
fn create_hidden_command(program: &str) -> Command {
    #[cfg(windows)]
    let mut cmd = Command::new(program);
    #[cfg(not(windows))]
    let cmd = Command::new(program);

    #[cfg(windows)]
    {
        use std::os::windows::process::CommandExt;
        // Windows: 使用 CREATE_NO_WINDOW 标志隐藏窗口
        cmd.creation_flags(0x08000000); // CREATE_NO_WINDOW
    }

    cmd
}

/// 代理类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ProxyType {
    #[serde(rename = "v2rayN")]
    V2rayN,
    #[serde(rename = "clash-verge")]
    ClashVerge,
    #[serde(rename = "custom")]
    Custom,
    #[serde(rename = "tun")]
    Tun,
}

/// 网络优化配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkOptimizerConfig {
    /// 是否启用网络优化
    pub enabled: bool,
    /// 代理类型
    pub proxy_type: ProxyType,
    /// 自定义代理地址（仅当proxy_type为Custom时使用）
    pub custom_proxy_url: Option<String>,
    /// 是否启用定时优化
    pub auto_optimize_enabled: bool,
    /// 定时优化间隔（分钟）
    pub auto_optimize_interval: u32,
    /// 是否自动跟随设置编辑器代理
    pub auto_set_editor_proxy: bool,
    /// 上次优化时间
    pub last_optimize_time: Option<String>,
    /// 上次优化选择的API
    pub last_optimize_api: Option<String>,
    /// 上次优化平均延迟
    pub last_optimize_latency: Option<f64>,
}

impl Default for NetworkOptimizerConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            proxy_type: ProxyType::V2rayN,
            custom_proxy_url: None,
            auto_optimize_enabled: true,
            auto_optimize_interval: 10,
            auto_set_editor_proxy: true,
            last_optimize_time: None,
            last_optimize_api: None,
            last_optimize_latency: None,
        }
    }
}

/// 测速结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SpeedTestResult {
    pub domain: String,
    pub latency: f64,
    pub real_ip: String,
}

/// 优化结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimizeResult {
    pub success: bool,
    pub message: String,
    pub best_domain: Option<String>,
    pub best_latency: Option<f64>,
    pub best_ip: Option<String>,
    pub optimize_time: String,
    pub editor_proxy_result: Option<EditorProxyResult>,
}

/// 编辑器代理设置结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EditorProxyResult {
    pub editor_type: String,
    pub success: bool,
    pub error_message: Option<String>,
}

/// 网络优化器状态
#[derive(Debug)]
pub struct NetworkOptimizerState {
    /// 定时器任务句柄
    pub timer_task: Arc<TokioMutex<Option<tokio::task::JoinHandle<()>>>>,
    /// 是否正在优化
    pub is_optimizing: Arc<TokioMutex<bool>>,
}

impl Default for NetworkOptimizerState {
    fn default() -> Self {
        Self {
            timer_task: Arc::new(TokioMutex::new(None)),
            is_optimizing: Arc::new(TokioMutex::new(false)),
        }
    }
}

/// 获取网络优化配置
#[tauri::command]
pub async fn get_network_optimizer_config(state: State<'_, AppState>) -> Result<NetworkOptimizerConfig, String> {
    let config_manager = state.config_manager.lock().unwrap();
    let config_value = config_manager.get("network_optimizer", None);
    
    if config_value.is_null() {
        // 返回默认配置
        Ok(NetworkOptimizerConfig::default())
    } else {
        serde_json::from_value(config_value)
            .map_err(|e| format!("解析网络优化配置失败: {}", e))
    }
}

/// 设置网络优化配置
#[tauri::command]
pub async fn set_network_optimizer_config(
    config: NetworkOptimizerConfig,
    state: State<'_, AppState>
) -> Result<bool, String> {
    let mut config_manager = state.config_manager.lock().unwrap();
    let config_value = serde_json::to_value(&config)
        .map_err(|e| format!("序列化网络优化配置失败: {}", e))?;
    
    let _ = config_manager.set("network_optimizer", config_value);
    config_manager.save_config()
        .map_err(|e| format!("保存网络优化配置失败: {}", e))
}

/// 获取代理URL
fn get_proxy_url(proxy_type: &ProxyType, custom_url: &Option<String>) -> Option<String> {
    match proxy_type {
        ProxyType::V2rayN => Some("http://127.0.0.1:10808".to_string()),
        ProxyType::ClashVerge => Some("http://127.0.0.1:7897".to_string()),
        ProxyType::Custom => custom_url.clone(),
        ProxyType::Tun => None, // Tun模式不使用代理
    }
}

/// 验证代理URL格式
fn validate_proxy_url(url: &str) -> bool {
    url.starts_with("http://") || url.starts_with("https://")
}

/// 测试代理连接
#[tauri::command]
pub async fn test_proxy_connection(
    proxy_type: ProxyType,
    custom_proxy_url: Option<String>
) -> Result<bool, String> {
    let proxy_url = get_proxy_url(&proxy_type, &custom_proxy_url);
    
    if let Some(url) = proxy_url {
        if !validate_proxy_url(&url) {
            return Err("代理地址格式不正确".to_string());
        }
        
        // 使用curl测试代理连接
        let output = create_hidden_command("curl")
            .args(&[
                "https://www.google.com",
                "--proxy", &url,
                "--max-time", "10",
                "--connect-timeout", "5",
                "--silent",
                "--show-error",
                "--output", if cfg!(windows) { "NUL" } else { "/dev/null" },
                "--write-out", "%{http_code}"
            ])
            .output();
            
        match output {
            Ok(result) => {
                if result.status.success() {
                    let http_code = String::from_utf8_lossy(&result.stdout);
                    let code = http_code.trim();
                    Ok(matches!(code, "200" | "301" | "302" | "403" | "404"))
                } else {
                    // 检查stderr来区分不同类型的错误
                    let stderr = String::from_utf8_lossy(&result.stderr);
                    if stderr.contains("Connection refused") || stderr.contains("Failed to connect") {
                        Err("代理连接失败".to_string())
                    } else if stderr.contains("timeout") || stderr.contains("Timeout") {
                        Err("代理连接超时".to_string())
                    } else if stderr.contains("proxy") && (stderr.contains("error") || stderr.contains("异常")) {
                        Err("代理异常".to_string())
                    } else {
                        Err("代理连接失败".to_string())
                    }
                }
            }
            Err(e) => {
                // 系统级错误，通常是代理异常
                Err(format!("代理异常: {}", e))
            }
        }
    } else {
        // Tun模式，直接返回成功
        Ok(true)
    }
}

/// 获取域名列表
fn get_domains() -> Vec<String> {
    (1..=20).map(|i| format!("d{}.api.augmentcode.com", i)).collect()
}

/// 验证是否为有效的IPv4地址
fn is_valid_ipv4(ip: &str) -> bool {
    let parts: Vec<&str> = ip.split('.').collect();
    if parts.len() != 4 {
        return false;
    }

    for part in parts {
        if part.parse::<u8>().is_err() {
            return false;
        }
    }

    true
}

/// 直接通过DNS解析获取域名IP（不使用代理）
async fn get_domain_ip_direct(domain: &str) -> Option<String> {
    use std::net::ToSocketAddrs;

    // 使用标准库的DNS解析，不受代理影响
    let addr_str = format!("{}:443", domain);
    if let Ok(addrs) = tokio::task::spawn_blocking(move || {
        addr_str.to_socket_addrs()
    }).await {
        if let Ok(addrs) = addrs {
            if let Some(addr) = addrs.into_iter().next() {
                return Some(addr.ip().to_string());
            }
        }
    }

    None
}

/// 单次测试域名速度
async fn test_domain_speed_single(domain: &str, proxy_url: Option<&String>, timeout: u64) -> Option<(f64, String)> {
    let start_time = Instant::now();
    
    let mut args = vec![
        format!("https://{}/", domain),
        "-H".to_string(),
        "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36".to_string(),
        "--max-time".to_string(),
        timeout.to_string(),
        "--connect-timeout".to_string(),
        "5".to_string(),
        "--insecure".to_string(),
        "--location".to_string(),
        "--silent".to_string(),
        "--show-error".to_string(),
        "--output".to_string(),
        if cfg!(windows) { "NUL".to_string() } else { "/dev/null".to_string() },
        "--write-out".to_string(),
        "%{remote_ip}".to_string(),
    ];
    
    if let Some(proxy) = proxy_url {
        args.extend_from_slice(&["--proxy".to_string(), proxy.clone()]);
    }
    
    let output = create_hidden_command("curl")
        .args(&args)
        .output();
        
    match output {
        Ok(result) => {
            if result.status.success() {
                let elapsed = start_time.elapsed();
                let latency = elapsed.as_millis() as f64;
                let curl_ip = String::from_utf8_lossy(&result.stdout).trim().to_string();

                // 获取真实的服务器IP
                let real_ip = if proxy_url.is_some() {
                    // 使用代理时，通过DNS解析获取真实IP（不使用代理）
                    get_domain_ip_direct(domain).await
                } else {
                    // 直连时，使用curl返回的IP
                    if !curl_ip.is_empty() && is_valid_ipv4(&curl_ip) {
                        Some(curl_ip)
                    } else {
                        // 如果curl没有返回有效IP，使用DNS解析
                        get_domain_ip_direct(domain).await
                    }
                };

                if let Some(ip) = real_ip {
                    Some((latency, ip))
                } else {
                    None
                }
            } else {
                None
            }
        }
        Err(_) => None
    }
}

/// 测试域名速度（多次测试取平均值）
async fn test_domain_speed(domain: &str, proxy_url: Option<&String>, test_count: u32) -> Option<SpeedTestResult> {
    let mut latencies = Vec::new();
    let mut best_ip = None;

    for _ in 0..test_count {
        if let Some((latency, ip)) = test_domain_speed_single(domain, proxy_url, 8).await {
            latencies.push(latency);
            if best_ip.is_none() {
                best_ip = Some(ip);
            }
        }

        // 测试间隔
        sleep(Duration::from_millis(100)).await;
    }

    if !latencies.is_empty() && best_ip.is_some() {
        let avg_latency = latencies.iter().sum::<f64>() / latencies.len() as f64;
        Some(SpeedTestResult {
            domain: domain.to_string(),
            latency: avg_latency,
            real_ip: best_ip.unwrap(),
        })
    } else {
        None
    }
}

/// 并发测试所有域名
#[tauri::command]
pub async fn test_all_domains(
    proxy_type: ProxyType,
    custom_proxy_url: Option<String>,
    app_handle: AppHandle
) -> Result<Vec<SpeedTestResult>, String> {
    let proxy_url = get_proxy_url(&proxy_type, &custom_proxy_url);
    let domains = get_domains();

    // 发送开始测试事件
    let _ = app_handle.emit("network_test_started", serde_json::json!({
        "total_domains": domains.len()
    }));

    let mut tasks = Vec::new();

    // 创建并发任务
    for domain in &domains {
        let proxy_url_clone = proxy_url.clone();
        let app_handle_clone = app_handle.clone();
        let domain_clone = domain.clone();

        let task = tokio::spawn(async move {
            let result = test_domain_speed(&domain_clone, proxy_url_clone.as_ref(), 3).await;

            // 发送单个域名测试完成事件
            let _ = app_handle_clone.emit("network_test_domain_complete", serde_json::json!({
                "domain": domain_clone,
                "success": result.is_some()
            }));

            result
        });

        tasks.push(task);
    }

    // 等待所有任务完成
    let mut results = Vec::new();
    let mut completed = 0;

    for task in tasks {
        if let Ok(result) = task.await {
            if let Some(speed_result) = result {
                results.push(speed_result);
            }
        }

        completed += 1;

        // 发送进度更新
        let _ = app_handle.emit("network_test_progress", serde_json::json!({
            "message": format!("已完成 {}/{} 个域名测试", completed, domains.len()),
            "progress": completed,
            "total": domains.len()
        }));
    }

    // 按延迟排序
    results.sort_by(|a, b| a.latency.partial_cmp(&b.latency).unwrap_or(std::cmp::Ordering::Equal));

    Ok(results)
}

/// 获取hosts文件路径
fn get_hosts_file_path() -> PathBuf {
    if cfg!(windows) {
        PathBuf::from(r"C:\Windows\System32\drivers\etc\hosts")
    } else {
        PathBuf::from("/etc/hosts")
    }
}

/// 检查管理员权限
fn check_admin_privileges() -> bool {
    #[cfg(windows)]
    {
        // Windows: 检查是否以管理员身份运行
        use std::ptr;

        unsafe {
            let mut token_handle = ptr::null_mut();
            let current_process = winapi::um::processthreadsapi::GetCurrentProcess();

            if winapi::um::processthreadsapi::OpenProcessToken(
                current_process,
                winapi::um::winnt::TOKEN_QUERY,
                &mut token_handle,
            ) != 0 {
                let mut elevation = winapi::um::winnt::TOKEN_ELEVATION { TokenIsElevated: 0 };
                let mut return_length = 0;

                if winapi::um::securitybaseapi::GetTokenInformation(
                    token_handle,
                    winapi::um::winnt::TokenElevation,
                    &mut elevation as *mut _ as *mut _,
                    std::mem::size_of::<winapi::um::winnt::TOKEN_ELEVATION>() as u32,
                    &mut return_length,
                ) != 0 {
                    winapi::um::handleapi::CloseHandle(token_handle);
                    return elevation.TokenIsElevated != 0;
                }

                winapi::um::handleapi::CloseHandle(token_handle);
            }

            false
        }
    }
    #[cfg(unix)]
    {
        // Unix系统: 检查是否为root用户
        unsafe {
            libc::geteuid() == 0
        }
    }
    #[cfg(not(any(windows, unix)))]
    {
        // 其他平台默认返回false
        false
    }
}

/// 读取网络配置文件内容
fn read_hosts_file() -> Result<String> {
    let hosts_path = get_hosts_file_path();
    std::fs::read_to_string(&hosts_path)
        .map_err(|e| anyhow!("读取网络配置失败: {}", e))
}

/// 写入网络配置文件内容
fn write_hosts_file(content: &str) -> Result<()> {
    let hosts_path = get_hosts_file_path();
    std::fs::write(&hosts_path, content)
        .map_err(|e| anyhow!("写入网络配置失败: {}", e))
}

/// 清除现有的优化策略
fn remove_existing_optimization() -> Result<()> {
    let content = read_hosts_file()?;
    let lines: Vec<&str> = content.lines().collect();

    let optimization_marker = "# ==== YAugment 优化策略 ====";
    let optimization_end_marker = "# ==== 不需要可以直接删掉 ====";

    let mut start_idx = None;
    let mut end_idx = None;

    for (i, line) in lines.iter().enumerate() {
        if line.contains(optimization_marker) {
            start_idx = Some(i);
        } else if line.contains(optimization_end_marker) && start_idx.is_some() {
            end_idx = Some(i);
            break;
        }
    }

    if let (Some(start), Some(end)) = (start_idx, end_idx) {
        let mut new_lines = Vec::new();

        // 检查优化策略前面是否有空行，如果有的话也删除
        let actual_start = if start > 0 && lines[start - 1].trim().is_empty() {
            start - 1
        } else {
            start
        };

        // 添加优化策略前的内容
        new_lines.extend_from_slice(&lines[..actual_start]);
        // 添加优化策略后的内容
        new_lines.extend_from_slice(&lines[end + 1..]);

        let new_content = new_lines.join("\n");
        write_hosts_file(&new_content)?;
    }

    Ok(())
}

/// 更新hosts文件
fn update_hosts_file(best_result: &SpeedTestResult, proxy_info: &str) -> Result<()> {
    let domains = get_domains();
    let clean_ip = best_result.real_ip.trim();

    let optimization_marker = "# ==== YAugment 优化策略 ====";
    let optimization_end_marker = "# ==== 不需要可以直接删掉 ====";

    let mut optimization_content = Vec::new();
    optimization_content.push(format!("\n{}", optimization_marker));
    optimization_content.push(format!("# 优化环境: {}", proxy_info));
    optimization_content.push(format!("# 最快域名: {} (平均延迟: {:.3}ms)", best_result.domain, best_result.latency));

    // 添加所有域名映射
    for domain in domains {
        optimization_content.push(format!("{}  {}", clean_ip, domain));
    }

    optimization_content.push(optimization_end_marker.to_string());

    // 读取现有内容并追加
    let existing_content = read_hosts_file().unwrap_or_default();
    let new_content = format!("{}\n{}", existing_content, optimization_content.join("\n"));

    write_hosts_file(&new_content)?;
    Ok(())
}

/// 刷新网络配置
fn flush_dns() -> Result<()> {
    if cfg!(windows) {
        create_hidden_command("ipconfig")
            .arg("/flushdns")
            .output()
            .map_err(|e| anyhow!("刷新网络配置失败: {}", e))?;
    } else if cfg!(target_os = "macos") {
        create_hidden_command("sudo")
            .args(&["dscacheutil", "-flushcache"])
            .output()
            .map_err(|e| anyhow!("刷新网络配置失败: {}", e))?;
    } else {
        // Linux
        let _ = create_hidden_command("sudo")
            .args(&["systemctl", "restart", "systemd-resolved"])
            .output();
    }

    Ok(())
}

/// 获取编辑器设置文件路径
fn get_editor_settings_path(editor_type: &str) -> Option<PathBuf> {
    match editor_type.to_lowercase().as_str() {
        "vscode" => {
            if cfg!(windows) {
                if let Ok(appdata) = std::env::var("APPDATA") {
                    Some(PathBuf::from(appdata).join("Code").join("User").join("settings.json"))
                } else {
                    None
                }
            } else if cfg!(target_os = "macos") {
                if let Some(home) = dirs::home_dir() {
                    Some(home.join("Library").join("Application Support").join("Code").join("User").join("settings.json"))
                } else {
                    None
                }
            } else {
                if let Some(home) = dirs::home_dir() {
                    Some(home.join(".config").join("Code").join("User").join("settings.json"))
                } else {
                    None
                }
            }
        }
        "cursor" => {
            if cfg!(windows) {
                if let Ok(appdata) = std::env::var("APPDATA") {
                    Some(PathBuf::from(appdata).join("Cursor").join("User").join("settings.json"))
                } else {
                    None
                }
            } else if cfg!(target_os = "macos") {
                if let Some(home) = dirs::home_dir() {
                    Some(home.join("Library").join("Application Support").join("Cursor").join("User").join("settings.json"))
                } else {
                    None
                }
            } else {
                if let Some(home) = dirs::home_dir() {
                    Some(home.join(".config").join("Cursor").join("User").join("settings.json"))
                } else {
                    None
                }
            }
        }
        // JetBrains IDEs - 使用 proxy.settings 文件
        "intellij" | "pycharm" | "webstorm" | "phpstorm" | "rubymine" |
        "clion" | "goland" | "rider" | "datagrip" | "androidstudio" => {
            get_jetbrains_proxy_settings_path(editor_type)
        }
        _ => None
    }
}

/// 获取 JetBrains IDE 的代理设置文件路径
fn get_jetbrains_proxy_settings_path(editor_type: &str) -> Option<PathBuf> {
    // JetBrains IDE 的代理设置存储在 proxy.settings 文件中
    // 路径格式: ~/.config/JetBrains/{IDE_NAME}{VERSION}/options/proxy.settings

    let ide_name = match editor_type.to_lowercase().as_str() {
        "intellij" => "IntelliJIdea",
        "pycharm" => "PyCharm",
        "webstorm" => "WebStorm",
        "phpstorm" => "PhpStorm",
        "rubymine" => "RubyMine",
        "clion" => "CLion",
        "goland" => "GoLand",
        "rider" => "Rider",
        "datagrip" => "DataGrip",
        "androidstudio" => "AndroidStudio",
        _ => return None,
    };

    if cfg!(windows) {
        // Windows: %APPDATA%/JetBrains/{IDE_NAME}{VERSION}/options/proxy.settings
        if let Ok(appdata) = std::env::var("APPDATA") {
            let jetbrains_dir = PathBuf::from(appdata).join("JetBrains");
            find_latest_jetbrains_version(&jetbrains_dir, ide_name)
                .map(|version_dir| version_dir.join("options").join("proxy.settings"))
        } else {
            None
        }
    } else if cfg!(target_os = "macos") {
        // macOS: ~/Library/Application Support/JetBrains/{IDE_NAME}{VERSION}/options/proxy.settings
        if let Some(home) = dirs::home_dir() {
            let jetbrains_dir = home.join("Library").join("Application Support").join("JetBrains");
            find_latest_jetbrains_version(&jetbrains_dir, ide_name)
                .map(|version_dir| version_dir.join("options").join("proxy.settings"))
        } else {
            None
        }
    } else {
        // Linux: ~/.config/JetBrains/{IDE_NAME}{VERSION}/options/proxy.settings
        if let Some(home) = dirs::home_dir() {
            let jetbrains_dir = home.join(".config").join("JetBrains");
            find_latest_jetbrains_version(&jetbrains_dir, ide_name)
                .map(|version_dir| version_dir.join("options").join("proxy.settings"))
        } else {
            None
        }
    }
}

/// 查找最新版本的 JetBrains IDE 配置目录
fn find_latest_jetbrains_version(jetbrains_dir: &PathBuf, ide_name: &str) -> Option<PathBuf> {
    if !jetbrains_dir.exists() {
        return None;
    }

    let mut matching_dirs = Vec::new();

    if let Ok(entries) = std::fs::read_dir(jetbrains_dir) {
        for entry in entries.flatten() {
            if let Some(dir_name) = entry.file_name().to_str() {
                if dir_name.starts_with(ide_name) {
                    matching_dirs.push(entry.path());
                }
            }
        }
    }

    // 按名称排序，取最新版本（通常是字母序最大的）
    matching_dirs.sort();
    matching_dirs.last().cloned()
}

/// 设置编辑器代理
fn set_editor_proxy(editor_type: &str, proxy_url: &str) -> Result<()> {
    match editor_type.to_lowercase().as_str() {
        "vscode" | "cursor" => set_vscode_like_proxy(editor_type, proxy_url),
        "intellij" | "pycharm" | "webstorm" | "phpstorm" | "rubymine" |
        "clion" | "goland" | "rider" | "datagrip" | "androidstudio" => {
            set_jetbrains_proxy(editor_type, proxy_url)
        }
        _ => Err(anyhow!("不支持的编辑器类型: {}", editor_type))
    }
}

/// 设置 VS Code 类编辑器的代理
fn set_vscode_like_proxy(editor_type: &str, proxy_url: &str) -> Result<()> {
    if let Some(settings_path) = get_editor_settings_path(editor_type) {
        // 确保目录存在
        if let Some(parent) = settings_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        let mut settings: serde_json::Value = if settings_path.exists() {
            let content = std::fs::read_to_string(&settings_path)?;
            serde_json::from_str(&content).unwrap_or(serde_json::json!({}))
        } else {
            serde_json::json!({})
        };

        // 设置代理
        settings["http.proxy"] = serde_json::Value::String(proxy_url.to_string());

        // 写入文件
        let content = serde_json::to_string_pretty(&settings)?;
        std::fs::write(&settings_path, content)?;

        Ok(())
    } else {
        Err(anyhow!("无法获取 {} 设置文件路径", editor_type))
    }
}

/// 设置 JetBrains IDE 的代理
fn set_jetbrains_proxy(editor_type: &str, proxy_url: &str) -> Result<()> {
    if let Some(settings_path) = get_editor_settings_path(editor_type) {
        // 确保目录存在
        if let Some(parent) = settings_path.parent() {
            std::fs::create_dir_all(parent)?;
        }

        // 解析代理URL
        let (host, port) = parse_proxy_url(proxy_url)?;

        // JetBrains IDE 使用 XML 格式的 proxy.settings 文件
        let proxy_settings = format!(
            r#"<application>
  <component name="HttpConfigurable">
    <option name="USE_HTTP_PROXY" value="true" />
    <option name="PROXY_HOST" value="{}" />
    <option name="PROXY_PORT" value="{}" />
    <option name="PROXY_TYPE_IS_SOCKS" value="false" />
    <option name="USE_PROXY_PAC" value="false" />
    <option name="USE_PAC_URL" value="false" />
  </component>
</application>"#, host, port);

        std::fs::write(&settings_path, proxy_settings)?;
        Ok(())
    } else {
        Err(anyhow!("无法获取 {} 代理设置文件路径", editor_type))
    }
}

/// 解析代理URL，提取主机和端口
fn parse_proxy_url(proxy_url: &str) -> Result<(String, u16)> {
    // 移除协议前缀
    let clean_url = proxy_url
        .strip_prefix("http://")
        .or_else(|| proxy_url.strip_prefix("https://"))
        .unwrap_or(proxy_url);

    // 解析 host:port 格式
    if let Some((host, port_str)) = clean_url.split_once(':') {
        if let Ok(port) = port_str.parse::<u16>() {
            Ok((host.to_string(), port))
        } else {
            Err(anyhow!("无效的代理端口: {}", port_str))
        }
    } else {
        // 如果没有端口，使用默认端口
        Ok((clean_url.to_string(), 8080))
    }
}

/// 删除编辑器代理
fn remove_editor_proxy(editor_type: &str) -> Result<()> {
    match editor_type.to_lowercase().as_str() {
        "vscode" | "cursor" => remove_vscode_like_proxy(editor_type),
        "intellij" | "pycharm" | "webstorm" | "phpstorm" | "rubymine" |
        "clion" | "goland" | "rider" | "datagrip" | "androidstudio" => {
            remove_jetbrains_proxy(editor_type)
        }
        _ => Err(anyhow!("不支持的编辑器类型: {}", editor_type))
    }
}

/// 删除 VS Code 类编辑器的代理
fn remove_vscode_like_proxy(editor_type: &str) -> Result<()> {
    if let Some(settings_path) = get_editor_settings_path(editor_type) {
        if settings_path.exists() {
            let content = std::fs::read_to_string(&settings_path)?;
            let mut settings: serde_json::Value = serde_json::from_str(&content)
                .unwrap_or(serde_json::json!({}));

            // 删除代理设置
            if let Some(obj) = settings.as_object_mut() {
                obj.remove("http.proxy");
            }

            // 写入文件
            let content = serde_json::to_string_pretty(&settings)?;
            std::fs::write(&settings_path, content)?;
        }

        Ok(())
    } else {
        Err(anyhow!("无法获取 {} 设置文件路径", editor_type))
    }
}

/// 删除 JetBrains IDE 的代理
fn remove_jetbrains_proxy(editor_type: &str) -> Result<()> {
    if let Some(settings_path) = get_editor_settings_path(editor_type) {
        if settings_path.exists() {
            // JetBrains IDE: 删除整个 proxy.settings 文件或设置为禁用
            let proxy_settings = r#"<application>
  <component name="HttpConfigurable">
    <option name="USE_HTTP_PROXY" value="false" />
  </component>
</application>"#;

            std::fs::write(&settings_path, proxy_settings)?;
        }

        Ok(())
    } else {
        Err(anyhow!("无法获取 {} 代理设置文件路径", editor_type))
    }
}

/// 执行网络优化的内部实现
async fn execute_network_optimization_internal(
    proxy_type: ProxyType,
    custom_proxy_url: Option<String>,
    auto_set_editor_proxy: bool,
    app_handle: AppHandle,
    state: &AppState
) -> Result<OptimizeResult, String> {
    // 检查管理员权限
    if !check_admin_privileges() {
        return Err("需要管理员权限才能应用网络优化策略".to_string());
    }

    // 检查是否正在优化
    let optimizer_state = state.network_optimizer_state.lock().await;
    let mut is_optimizing = optimizer_state.is_optimizing.lock().await;
    if *is_optimizing {
        return Err("网络优化正在进行中，请稍后再试".to_string());
    }
    *is_optimizing = true;
    drop(is_optimizing);
    drop(optimizer_state);

    let result = async {
        // 测试所有域名
        let test_results = test_all_domains(proxy_type.clone(), custom_proxy_url.clone(), app_handle.clone()).await?;

        if test_results.is_empty() {
            return Err("所有域名测试失败".to_string());
        }

        // 获取最快的域名
        let best_result = &test_results[0];

        // 清除现有优化策略
        remove_existing_optimization()
            .map_err(|e| format!("清除现有网络优化配置失败: {}", e))?;

        // 更新网络优化配置
        let proxy_info = if proxy_type == ProxyType::Tun {
            "直连环境".to_string()
        } else {
            format!("代理环境 ({})", get_proxy_url(&proxy_type, &custom_proxy_url).unwrap_or_default())
        };

        update_hosts_file(best_result, &proxy_info)
            .map_err(|e| format!("应用网络优化配置失败: {}", e))?;

        // 刷新网络配置
        let _ = flush_dns();

        // 设置编辑器代理（如果启用且不是Tun模式）
        let mut editor_proxy_result = None;
        if auto_set_editor_proxy && proxy_type != ProxyType::Tun {
            if let Some(proxy_url) = get_proxy_url(&proxy_type, &custom_proxy_url) {
                // 获取当前选择的编辑器
                let config_manager = state.config_manager.lock().unwrap();
                if let Some(editor_type) = config_manager.get("editor_type", None).as_str() {
                    match set_editor_proxy(editor_type, &proxy_url) {
                        Ok(_) => {
                            editor_proxy_result = Some((editor_type.to_string(), true, None));
                        }
                        Err(e) => {
                            editor_proxy_result = Some((editor_type.to_string(), false, Some(e.to_string())));
                        }
                    }
                }
            }
        }

        // 获取当前时间
        let now = chrono::Local::now();
        let optimize_time = now.format("%Y/%m/%d %H:%M:%S").to_string();

        // 更新配置中的优化信息
        let mut config_manager = state.config_manager.lock().unwrap();
        let config_value = config_manager.get("network_optimizer", None);
        let mut config: NetworkOptimizerConfig = if config_value.is_null() {
            NetworkOptimizerConfig::default()
        } else {
            serde_json::from_value(config_value).unwrap_or_default()
        };

        config.last_optimize_time = Some(optimize_time.clone());
        config.last_optimize_api = Some(format!("API{}", best_result.domain.chars().nth(1).unwrap_or('?')));
        config.last_optimize_latency = Some(best_result.latency);

        let config_value = serde_json::to_value(&config).unwrap_or_default();
        let _ = config_manager.set("network_optimizer", config_value);
        let _ = config_manager.save_config();

        // 构建编辑器代理结果
        let editor_proxy_result = editor_proxy_result.map(|(editor_type, success, error_message)| {
            EditorProxyResult {
                editor_type,
                success,
                error_message,
            }
        });

        Ok(OptimizeResult {
            success: true,
            message: "网络优化完成".to_string(),
            best_domain: Some(best_result.domain.clone()),
            best_latency: Some(best_result.latency),
            best_ip: Some(best_result.real_ip.clone()),
            optimize_time,
            editor_proxy_result,
        })
    }.await;

    // 重置优化状态
    let optimizer_state = state.network_optimizer_state.lock().await;
    let mut is_optimizing = optimizer_state.is_optimizing.lock().await;
    *is_optimizing = false;

    result
}

/// 执行网络优化 (Tauri命令)
#[tauri::command]
pub async fn execute_network_optimization(
    proxy_type: ProxyType,
    custom_proxy_url: Option<String>,
    auto_set_editor_proxy: bool,
    app_handle: AppHandle,
    state: State<'_, AppState>
) -> Result<OptimizeResult, String> {
    execute_network_optimization_internal(
        proxy_type,
        custom_proxy_url,
        auto_set_editor_proxy,
        app_handle,
        state.inner()
    ).await
}

/// 清除网络优化策略
#[tauri::command]
pub async fn clear_network_optimization(
    auto_remove_editor_proxy: bool,
    state: State<'_, AppState>
) -> Result<bool, String> {
    // 检查管理员权限
    if !check_admin_privileges() {
        return Err("需要管理员权限才能清除网络优化策略".to_string());
    }

    // 清除网络优化配置
    remove_existing_optimization()
        .map_err(|e| format!("清除网络优化配置失败: {}", e))?;

    // 刷新网络配置
    let _ = flush_dns();

    // 删除编辑器代理设置（如果启用）
    if auto_remove_editor_proxy {
        let config_manager = state.config_manager.lock().unwrap();
        if let Some(editor_type) = config_manager.get("editor_type", None).as_str() {
            let _ = remove_editor_proxy(editor_type);
        }
    }

    Ok(true)
}

/// 启动定时优化
#[tauri::command]
pub async fn start_auto_optimization(
    interval_minutes: u32,
    proxy_type: ProxyType,
    custom_proxy_url: Option<String>,
    auto_set_editor_proxy: bool,
    app_handle: AppHandle,
    state: State<'_, AppState>
) -> Result<bool, String> {
    // 停止现有的定时器
    stop_auto_optimization(state.clone()).await?;

    // 启动定时器，发送事件让前端处理
    let task = tokio::spawn(async move {
        let mut interval = tokio::time::interval(Duration::from_secs(interval_minutes as u64 * 60));

        // 跳过第一次tick，避免立即触发（因为开启网络优化时已经手动执行了一次）
        interval.tick().await;

        loop {
            interval.tick().await;

            // 发送定时优化触发事件
            let _ = app_handle.emit("auto_optimization_trigger", serde_json::json!({
                "proxy_type": proxy_type,
                "custom_proxy_url": custom_proxy_url,
                "auto_set_editor_proxy": auto_set_editor_proxy,
                "timestamp": chrono::Local::now().format("%Y/%m/%d %H:%M:%S").to_string()
            }));

            // 简单的延迟，避免过于频繁的触发
            tokio::time::sleep(Duration::from_secs(1)).await;
        }
    });

    // 保存任务句柄
    let optimizer_state = state.network_optimizer_state.lock().await;
    let mut timer_task = optimizer_state.timer_task.lock().await;
    *timer_task = Some(task);

    Ok(true)
}

/// 停止定时优化
#[tauri::command]
pub async fn stop_auto_optimization(state: State<'_, AppState>) -> Result<bool, String> {
    let optimizer_state = state.network_optimizer_state.lock().await;
    let mut timer_task = optimizer_state.timer_task.lock().await;

    if let Some(task) = timer_task.take() {
        task.abort();
    }

    Ok(true)
}

/// 获取网络优化状态
#[tauri::command]
pub async fn get_network_optimization_status(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    let optimizer_state = state.network_optimizer_state.lock().await;
    let is_optimizing = optimizer_state.is_optimizing.lock().await;
    let has_timer = optimizer_state.timer_task.lock().await.is_some();

    let config = get_network_optimizer_config(state.clone()).await
        .unwrap_or_default();

    Ok(serde_json::json!({
        "is_optimizing": *is_optimizing,
        "has_auto_timer": has_timer,
        "config": config
    }))
}

/// 检查curl命令是否可用
#[tauri::command]
pub async fn check_curl_available() -> Result<bool, String> {
    match create_hidden_command("curl").arg("--version").output() {
        Ok(output) => Ok(output.status.success()),
        Err(_) => Ok(false)
    }
}

/// 检查管理员权限
#[tauri::command]
pub async fn check_admin_privileges_cmd() -> Result<bool, String> {
    Ok(check_admin_privileges())
}
