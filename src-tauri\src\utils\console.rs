//! 跨平台终端/控制台隐藏模块
//! 
//! 提供跨平台的终端窗口隐藏功能，支持 Windows、macOS 和 Linux

use std::env;

/// 跨平台终端隐藏功能
pub fn hide_console_window() {
    #[cfg(windows)]
    hide_console_windows();
    
    #[cfg(target_os = "macos")]
    hide_console_macos();
    
    #[cfg(all(unix, not(target_os = "macos")))]
    hide_console_linux();
}

/// Windows 平台终端隐藏
#[cfg(windows)]
fn hide_console_windows() {
    use winapi::um::wincon::GetConsoleWindow;
    use winapi::um::winuser::{ShowWindow, SW_HIDE};
    
    unsafe {
        let console_window = GetConsoleWindow();
        if !console_window.is_null() {
            ShowWindow(console_window, SW_HIDE);
            log::debug!("Windows 控制台窗口已隐藏");
        } else {
            log::debug!("Windows 控制台窗口句柄为空，可能已经隐藏");
        }
    }
}

/// macOS 平台终端隐藏
#[cfg(target_os = "macos")]
fn hide_console_macos() {
    // 方法1: 检查是否从终端启动
    let from_terminal = env::var("TERM").is_ok() || 
                       env::var("SHELL").is_ok() ||
                       env::var("TERM_PROGRAM").is_ok();
    
    if from_terminal {
        log::debug!("检测到从终端启动，尝试分离进程");
        
        // 方法2: 使用 setsid 创建新会话
        unsafe {
            let result = libc::setsid();
            if result != -1 {
                log::debug!("macOS 进程已分离，会话ID: {}", result);
            } else {
                log::warn!("macOS 进程分离失败");
            }
        }
        
        // 方法3: 清除终端相关环境变量
        env::remove_var("TERM");
        env::remove_var("TERM_PROGRAM");
        
        log::debug!("macOS 终端环境变量已清除");
    } else {
        log::debug!("macOS 应用未从终端启动，无需隐藏");
    }
}

/// Linux/Unix 平台终端隐藏
#[cfg(all(unix, not(target_os = "macos")))]
fn hide_console_linux() {
    // 检查是否在图形环境或终端环境中运行
    let in_terminal = env::var("TERM").is_ok();
    let in_gui = env::var("DISPLAY").is_ok() || env::var("WAYLAND_DISPLAY").is_ok();
    
    if in_terminal {
        log::debug!("检测到 Linux 终端环境，尝试分离进程");
        
        unsafe {
            // 创建新会话，脱离控制终端
            let result = libc::setsid();
            if result != -1 {
                log::debug!("Linux 进程已分离，会话ID: {}", result);
            } else {
                log::warn!("Linux 进程分离失败");
            }
            
            // 可选：重定向标准输入输出到 /dev/null
            // 只有在明确设置环境变量时才执行，避免影响调试
            if env::var("YAUGMENT_DETACH_STDIO").is_ok() {
                let dev_null = std::ffi::CString::new("/dev/null").unwrap();
                let null_fd = libc::open(dev_null.as_ptr(), libc::O_RDWR);
                
                if null_fd != -1 {
                    libc::dup2(null_fd, 0); // stdin
                    libc::dup2(null_fd, 1); // stdout
                    libc::dup2(null_fd, 2); // stderr
                    libc::close(null_fd);
                    log::debug!("Linux 标准输入输出已重定向到 /dev/null");
                }
            }
        }
    } else if in_gui {
        log::debug!("Linux GUI 环境，无需特殊处理");
    } else {
        log::debug!("Linux 未知环境，跳过终端隐藏");
    }
}

/// 检查是否应该隐藏终端
/// 读取配置文件确定隐藏策略
pub fn should_hide_console() -> bool {
    use std::fs;
    
    // 尝试读取版本配置文件
    let config_paths = [
        "version_config.toml",
        "../version_config.toml", 
        "src-tauri/version_config.toml",
        "../src-tauri/version_config.toml",
        "../../src-tauri/version_config.toml",
    ];
    
    for config_path in &config_paths {
        if let Ok(content) = fs::read_to_string(config_path) {
            // 简单解析 hide_console 配置
            for line in content.lines() {
                let line = line.trim();
                if line.starts_with("hide_console") && line.contains("=") {
                    let value = line.split('=').nth(1).unwrap_or("false").trim();
                    let should_hide = value == "true";
                    log::debug!("从配置文件 {} 读取 hide_console = {}", config_path, should_hide);
                    return should_hide;
                }
            }
        }
    }
    
    // 检查环境变量覆盖
    if let Ok(env_value) = env::var("YAUGMENT_HIDE_CONSOLE") {
        let should_hide = env_value.to_lowercase() == "true" || env_value == "1";
        log::debug!("从环境变量读取 YAUGMENT_HIDE_CONSOLE = {}", should_hide);
        return should_hide;
    }
    
    // 默认策略：生产环境隐藏，开发环境显示
    let is_debug = cfg!(debug_assertions);
    let default_hide = !is_debug;
    
    log::debug!("使用默认隐藏策略: {} (debug_assertions = {})", default_hide, is_debug);
    default_hide
}

/// 获取当前平台信息（用于调试）
pub fn get_platform_info() -> String {
    let os = env::consts::OS;
    let arch = env::consts::ARCH;
    let family = env::consts::FAMILY;
    
    format!("OS: {}, Arch: {}, Family: {}", os, arch, family)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_platform_info() {
        let info = get_platform_info();
        assert!(!info.is_empty());
        println!("Platform info: {}", info);
    }

    #[test]
    fn test_should_hide_console() {
        // 测试默认行为
        let result = should_hide_console();
        println!("Should hide console: {}", result);
    }
}
