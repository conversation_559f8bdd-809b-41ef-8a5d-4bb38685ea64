use serde_json::Value;
use std::sync::Arc;
use tokio::sync::Mutex;
use reqwest;

use std::time::Duration;
use crate::utils::ProgressReporter;
use anyhow;

// 新增导入：集成新功能模块
use crate::core::account_storage::{AccountStorageManager, AccountRecord};
use crate::core::portal_query::{OrbAccountInfo, AccountData};
use crate::core::augment_api::AugmentApiClient;
use crate::core::crypto_aes256::CryptoUtils;

/// 账号信息结构体
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct AccountInfo {
    pub credits: Option<Value>,
    pub subscription: Option<Value>,
    pub user: Option<Value>,
}

/// 连接测试结果
#[derive(Debug)]
pub struct TokenVerificationResult {
    pub success: bool,
    pub email: Option<String>,
    pub message: String,
}

/// Augment 账号管理器
/// 对应原版 Python 的 AugmentAccountManager 类
/// 扩展版本：集成Portal Token获取、账号存储、新API调用等功能
#[derive(Debug)]
pub struct AugmentAccountManager {
    // 原有字段
    base_url: String,
    session_token: Option<String>,
    http_client: Option<reqwest::Client>,
    progress_reporter: Arc<Mutex<ProgressReporter>>,

    // 新增字段：集成新功能
    /// 账号存储管理器
    account_storage: Option<AccountStorageManager>,
    /// 新API客户端
    api_client: Option<AugmentApiClient>,
    /// 机器UUID（用于API调用）
    machine_uuid: Option<String>,
}

impl AugmentAccountManager {
    /// 创建新的账号管理器实例
    /// 对应原版 Python 的 __init__ 方法
    /// 扩展版本：初始化新功能组件
    pub fn new() -> Result<Self, anyhow::Error> {
        // 初始化账号存储管理器
        let account_storage = match AccountStorageManager::new() {
            Ok(storage) => Some(storage),
            Err(e) => {
                eprintln!("警告：账号存储管理器初始化失败: {}", e);
                None
            }
        };

        // 初始化新API客户端
        let api_client = match AugmentApiClient::new(None, false) {
            Ok(client) => Some(client),
            Err(e) => {
                eprintln!("警告：新API客户端初始化失败: {}", e);
                None
            }
        };

        // 获取机器UUID
        let machine_uuid = match CryptoUtils::get_machine_uuid() {
            Ok(uuid) => Some(uuid),
            Err(e) => {
                eprintln!("警告：机器UUID获取失败: {}", e);
                None
            }
        };

        Ok(Self {
            // 原有字段
            base_url: "https://app.augmentcode.com".to_string(),
            session_token: None,
            http_client: None,
            progress_reporter: Arc::new(Mutex::new(ProgressReporter::new())),

            // 新增字段
            account_storage,
            api_client,
            machine_uuid,
        })
    }

    /// 设置进度回调函数
    /// 对应原版 Python 的 set_progress_callback 方法
    pub async fn set_progress_callback<F>(&self, callback: F)
    where
        F: Fn(&str, f64) + Send + Sync + 'static,
    {
        let mut reporter = self.progress_reporter.lock().await;
        reporter.set_callback(Box::new(callback));
    }

    /// 发送进度更新
    /// 对应原版 Python 的 _emit_progress 方法
    async fn emit_progress(&self, message: &str, progress: f64) {
        let reporter = self.progress_reporter.lock().await;
        reporter.report(message, progress);
    }

    /// 设置session token并初始化session
    /// 对应原版 Python 的 set_token 方法
    pub fn set_token(&mut self, session_token: String) -> bool {
        // 清理token中的空格
        let cleaned_token = session_token.trim().replace(' ', "");

        if cleaned_token.is_empty() {
            return false;
        }

        self.session_token = Some(cleaned_token.clone());

        // 创建新的session - 完全按照原版Python
        let mut headers = reqwest::header::HeaderMap::new();

        // 设置请求头 - 完全按照原版Python
        headers.insert("accept", "*/*".parse().unwrap());
        headers.insert("accept-language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6".parse().unwrap());
        headers.insert("cache-control", "no-cache".parse().unwrap());
        headers.insert("pragma", "no-cache".parse().unwrap());
        headers.insert("priority", "u=1, i".parse().unwrap());
        headers.insert("sec-ch-ua", r#""Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24""#.parse().unwrap());
        headers.insert("sec-ch-ua-mobile", "?0".parse().unwrap());
        headers.insert("sec-ch-ua-platform", r#""Windows""#.parse().unwrap());
        headers.insert("sec-fetch-dest", "empty".parse().unwrap());
        headers.insert("sec-fetch-mode", "cors".parse().unwrap());
        headers.insert("sec-fetch-site", "same-origin".parse().unwrap());
        headers.insert("referer", "https://app.augmentcode.com/account/subscription".parse().unwrap());
        headers.insert("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********".parse().unwrap());

        // 设置cookies - 完全按照原版Python
        let cookie_value = format!(
            "_session={}; vector_visitor_id=1f84c524-d422-4723-9684-0e636ddb65a5; ajs_anonymous_id=9c59416c-dc4f-4b81-aec4-51147db3318e; _ga_F6GPDJDCJY=GS2.1.s1749036154$o1$g0$t1749036154$j60$l0$h0; _ga=GA1.1.*********.**********; _gcl_au=1.1.**********.**********; ph_phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW_posthog=%7B%22distinct_id%22%3A%229c59416c-dc4f-4b81-aec4-51147db3318e%22%2C%22%24sesid%22%3A%5B1749038708478%2C%2201973ad4-b703-7d41-9b27-7b352e23954e%22%2C1749038708478%5D%2C%22%24session_is_sampled%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Fwww.augmentcode.com%2F%22%7D%7D; ajs_user_id=56eb9c69-1e09-4f4b-814d-3f59edb6085b",
            cleaned_token
        );
        headers.insert("cookie", cookie_value.parse().unwrap());

        // 创建HTTP客户端
        match reqwest::Client::builder()
            .default_headers(headers)
            .timeout(Duration::from_secs(30))
            .build()
        {
            Ok(client) => {
                self.http_client = Some(client);
                true
            }
            Err(_) => false
        }
    }

    /// 验证token是否有效并获取邮箱
    /// 对应原版 Python 的 verify_token_and_get_email 方法
    pub async fn verify_token_and_get_email(&self) -> TokenVerificationResult {
        if self.http_client.is_none() {
            return TokenVerificationResult {
                success: false,
                email: None,
                message: "No session initialized".to_string(),
            };
        }

        let client = self.http_client.as_ref().unwrap();
        let url = format!("{}/api/user", self.base_url);

        match client.get(&url).send().await {
            Ok(response) => {
                if response.status().is_success() {
                    match response.json::<Value>().await {
                        Ok(user_data) => {
                            let email = user_data.get("email")
                                .and_then(|e| e.as_str())
                                .unwrap_or("N/A")
                                .to_string();

                            TokenVerificationResult {
                                success: true,
                                email: Some(email),
                                message: "Token verified successfully".to_string(),
                            }
                        }
                        Err(_) => TokenVerificationResult {
                            success: false,
                            email: None,
                            message: "Failed to parse response".to_string(),
                        },
                    }
                } else {
                    TokenVerificationResult {
                        success: false,
                        email: None,
                        message: format!("HTTP error: {}", response.status()),
                    }
                }
            }
            Err(_) => TokenVerificationResult {
                success: false,
                email: None,
                message: "Network request failed".to_string(),
            },
        }
    }

    /// 验证token是否有效（兼容旧接口）
    /// 对应原版 Python 的 verify_token 方法
    pub async fn verify_token(&self) -> bool {
        if self.session_token.is_none() {
            return false;
        }

        let result = self.verify_token_and_get_email().await;
        result.success
    }

    /// 验证指定的session token是否有效
    /// 用于切换社区计划前的验证
    pub async fn verify_session_token(&self, session_token: &str) -> Result<serde_json::Value, anyhow::Error> {
        if let Some(ref api_client) = self.api_client {
            // 使用API客户端验证token
            match api_client.get_portal_token_from_subscription(session_token).await {
                Ok(_) => {
                    Ok(serde_json::json!({
                        "success": true,
                        "message": "Token验证成功"
                    }))
                }
                Err(e) => {
                    Ok(serde_json::json!({
                        "success": false,
                        "error": "invalid_token",
                        "message": format!("Token验证失败: {}", e)
                    }))
                }
            }
        } else {
            Err(anyhow::anyhow!("API客户端未初始化"))
        }
    }

    /// 获取账号完整信息
    /// 对应原版 Python 的 get_account_info 方法
    /// 修改：确保返回完整数据，带重试机制
    pub async fn get_account_info(&self) -> Option<AccountInfo> {
        if self.session_token.is_none() {
            return None;
        }

        self.emit_progress("正在获取账号信息...", 0.2).await;

        // 带重试机制的获取完整信息
        for attempt in 1..=3 {
            println!("🔄 获取账号信息尝试 {}/3", attempt);

            // 使用并发获取所有信息
            let results = self.get_all_info().await;

            // 检查数据完整性：确保关键数据都存在且有效
            let has_complete_data = results.user.is_some() &&
                                  results.credits.is_some() &&
                                  results.subscription.is_some();

            if has_complete_data {
                // 进一步验证数据质量
                let mut is_valid = false;

                if let (Some(ref user), Some(ref subscription)) = (&results.user, &results.subscription) {
                    // 验证用户数据
                    let has_email = user.get("email").is_some();

                    // 验证订阅数据 - 检查计划名称
                    let has_plan_name = subscription.get("planName")
                        .and_then(|v| v.as_str())
                        .map(|s| !s.is_empty() && s != "未知")
                        .unwrap_or(false);

                    // println!("📊 数据质量检查: email={}, planName={}", has_email, has_plan_name);

                    if has_email && has_plan_name {
                        println!("✅ 获取到完整且有效的账号数据");
                        self.emit_progress("账号信息获取完成", 1.0).await;
                        is_valid = true;
                    } else {
                        println!("⚠️ 数据不完整: email={}, planName={}", has_email, has_plan_name);
                    }
                }

                if is_valid {
                    return Some(results);
                }
            }

            if attempt < 3 {
                println!("⚠️ 数据不完整，1秒后重试...");
                tokio::time::sleep(tokio::time::Duration::from_secs(1)).await;
            } else {
                println!("❌ 重试次数已用完，返回不完整数据");
            }
        }

        self.emit_progress("获取账号信息失败", 1.0).await;
        None
    }

    /// 使用并发同时获取所有信息（积分、订阅、用户）
    /// 对应原版 Python 的 get_all_info 方法
    pub async fn get_all_info(&self) -> AccountInfo {
        // 并发执行三个请求
        let (credits_result, subscription_result, user_result) = tokio::join!(
            self.get_credits(),
            self.get_subscription(),
            self.get_user()
        );

        // 报告进度
        self.emit_progress("正在获取账号信息... (3/3)", 0.8).await;

        AccountInfo {
            credits: credits_result,
            subscription: subscription_result,
            user: user_result,
        }
    }

    /// 获取用户积分信息
    /// 对应原版 Python 的 get_credits 方法
    pub async fn get_credits(&self) -> Option<Value> {
        self.make_request("/api/credits", "积分信息").await
    }

    /// 获取用户订阅信息
    /// 对应原版 Python 的 get_subscription 方法
    pub async fn get_subscription(&self) -> Option<Value> {
        self.make_request("/api/subscription", "订阅信息").await
    }

    /// 获取用户信息
    /// 对应原版 Python 的 get_user 方法
    pub async fn get_user(&self) -> Option<Value> {
        self.make_request("/api/user", "用户信息").await
    }

    /// 通用请求方法
    /// 对应原版 Python 的 _make_request 方法
    async fn make_request(&self, endpoint: &str, _data_type: &str) -> Option<Value> {
        if let Some(client) = &self.http_client {
            let url = format!("{}{}", self.base_url, endpoint);

            match client.get(&url).send().await {
                Ok(response) => {
                    if response.status().is_success() {
                        response.json::<Value>().await.ok()
                    } else {
                        None
                    }
                }
                Err(_) => None,
            }
        } else {
            None
        }
    }





    /// 验证加固结果
    /// 对应原版 Python 的 get_account_info_for_verification 方法
    pub async fn get_account_info_for_verification(&self) -> Option<Value> {
        if let Some(client) = &self.http_client {
            // 并发获取订阅和用户信息
            let subscription_url = format!("{}/api/subscription", self.base_url);
            let user_url = format!("{}/api/user", self.base_url);

            let (subscription_result, user_result) = tokio::join!(
                client.get(&subscription_url).send(),
                client.get(&user_url).send()
            );

            match (subscription_result, user_result) {
                (Ok(sub_resp), Ok(user_resp)) => {
                    if sub_resp.status().is_success() && user_resp.status().is_success() {
                        let (sub_data, user_data) = tokio::join!(
                            sub_resp.json::<Value>(),
                            user_resp.json::<Value>()
                        );

                        match (sub_data, user_data) {
                            (Ok(subscription), Ok(user)) => {
                                Some(serde_json::json!({
                                    "subscription": subscription,
                                    "user": user
                                }))
                            }
                            _ => None,
                        }
                    } else {
                        None
                    }
                }
                _ => None,
            }
        } else {
            None
        }
    }



    // ==================== 新增功能方法 ====================

    /// 获取Portal Token并保存账号
    /// 使用subscription接口获取Portal Token，并保存到账号存储
    pub async fn get_portal_token_and_save(&mut self) -> Result<String, anyhow::Error> {
        // 检查必要组件
        if self.session_token.is_none() {
            return Err(anyhow::anyhow!("Session token未设置"));
        }

        if self.api_client.is_none() {
            return Err(anyhow::anyhow!("API客户端未初始化"));
        }

        let session_token = self.session_token.as_ref().unwrap();
        let api_client = self.api_client.as_ref().unwrap();

        self.emit_progress("正在获取Portal Token...", 0.2).await;

        // 使用subscription接口获取Portal Token
        let portal_token = api_client.get_portal_token_from_subscription(session_token).await?;

        self.emit_progress("正在验证Portal Token...", 0.5).await;

        // 使用Portal Token查询账号信息获取邮箱
        let mut orb_info = OrbAccountInfo::new(portal_token.clone())?;
        let account_data = orb_info.fetch_account_info().await?;

        if let Some(email) = account_data.email {
            self.emit_progress("正在保存账号信息...", 0.8).await;

            // 保存到账号存储
            if let Some(ref mut storage) = self.account_storage {
                let save_result = storage.save_account(email.clone(), portal_token.clone())?;

                match save_result {
                    true => {
                        // 新增账号 - 获取真实创建时间
                        self.emit_progress("正在获取账号创建时间...", 0.9).await;

                        // 获取并保存真实的账号创建时间
                        if let Ok(creation_time) = orb_info.get_customer_creation_time().await {
                            if let Some(creation_time_str) = creation_time {
                                // 重新获取storage的可变引用来更新创建时间
                                if let Some(ref mut storage) = self.account_storage {
                                    if let Some(account) = storage.get_account_by_email_mut(&email) {
                                        account.set_account_creation_time(creation_time_str);
                                        let _ = storage.save_to_file();
                                    }
                                }
                            }
                        }

                        self.emit_progress("账号保存成功", 1.0).await;
                        Ok(format!("新账号添加成功，邮箱: {}", email))
                    }
                    false => {
                        // 更新现有账号 - 如果没有创建时间则获取
                        let needs_creation_time = if let Some(ref storage) = self.account_storage {
                            if let Some(account) = storage.get_account_by_email(&email) {
                                account.account_creation_time.is_none()
                            } else {
                                false
                            }
                        } else {
                            false
                        };

                        if needs_creation_time {
                            self.emit_progress("正在获取账号创建时间...", 0.9).await;

                            if let Ok(creation_time) = orb_info.get_customer_creation_time().await {
                                if let Some(creation_time_str) = creation_time {
                                    if let Some(ref mut storage) = self.account_storage {
                                        if let Some(account) = storage.get_account_by_email_mut(&email) {
                                            account.set_account_creation_time(creation_time_str);
                                            let _ = storage.save_to_file();
                                        }
                                    }
                                }
                            }
                        }

                        self.emit_progress("账号已存在，已更新", 1.0).await;
                        Ok(format!("账号已存在，已更新Token，邮箱: {}", email))
                    }
                }
            } else {
                self.emit_progress("账号保存失败", 1.0).await;
                Err(anyhow::anyhow!("账号存储管理器未初始化"))
            }
        } else {
            Err(anyhow::anyhow!("无法从Token里获取邮箱信息"))
        }
    }

    /// 使用Portal Token查询账号信息
    /// 提供完整的账号数据查询功能
    pub async fn query_with_portal_token(&self, portal_token: &str) -> Result<AccountData, anyhow::Error> {
        self.emit_progress("正在查询账号信息...", 0.3).await;

        let mut orb_info = OrbAccountInfo::new(portal_token.to_string())?;
        let account_data = orb_info.fetch_account_info().await?;

        self.emit_progress("账号信息查询完成", 1.0).await;
        Ok(account_data)
    }

    /// 使用Portal Token查询账号信息并更新创建时间（如果需要）
    /// 专门用于需要更新时间的场景
    pub async fn query_with_portal_token_and_update_time(&mut self, portal_token: &str) -> Result<AccountData, anyhow::Error> {
        self.emit_progress("正在查询账号信息...", 0.3).await;

        let mut orb_info = OrbAccountInfo::new(portal_token.to_string())?;
        let account_data = orb_info.fetch_account_info().await?;

        // 如果获取到了邮箱，检查是否需要更新账号创建时间
        if let Some(ref email) = account_data.email {
            let needs_creation_time = if let Some(ref storage) = self.account_storage {
                if let Some(account) = storage.get_account_by_email(email) {
                    account.account_creation_time.is_none()
                } else {
                    false
                }
            } else {
                false
            };

            if needs_creation_time {
                self.emit_progress("正在获取账号创建时间...", 0.7).await;

                // 获取账号创建时间
                if let Ok(creation_time) = orb_info.get_customer_creation_time().await {
                    if let Some(creation_time_str) = creation_time {
                        // 更新账号创建时间
                        if let Some(ref mut storage) = self.account_storage {
                            if let Some(account) = storage.get_account_by_email_mut(email) {
                                account.set_account_creation_time(creation_time_str);
                                let _ = storage.save_to_file();
                                println!("已更新账号 {} 的创建时间", email);
                            }
                        }
                    }
                }
            }
        }

        self.emit_progress("账号信息查询完成", 1.0).await;
        Ok(account_data)
    }



    /// 获取保存的账号列表
    /// 提供账号存储管理功能
    pub fn get_saved_accounts(&self) -> Result<Vec<AccountRecord>, anyhow::Error> {
        if let Some(ref storage) = self.account_storage {
            Ok(storage.get_accounts().clone())
        } else {
            Err(anyhow::anyhow!("账号存储管理器未初始化"))
        }
    }

    /// 根据邮箱获取保存的账号
    /// 提供特定账号查询功能
    pub fn get_saved_account_by_email(&self, email: &str) -> Result<Option<AccountRecord>, anyhow::Error> {
        if let Some(ref storage) = self.account_storage {
            Ok(storage.get_account_by_email(email).cloned())
        } else {
            Err(anyhow::anyhow!("账号存储管理器未初始化"))
        }
    }

    /// 删除保存的账号
    /// 提供账号删除功能
    pub fn remove_saved_account(&mut self, email: &str) -> Result<bool, anyhow::Error> {
        if let Some(ref mut storage) = self.account_storage {
            storage.remove_account(email)
        } else {
            Err(anyhow::anyhow!("账号存储管理器未初始化"))
        }
    }

    /// 获取机器UUID
    /// 提供机器标识获取功能
    pub fn get_machine_uuid(&self) -> Option<&String> {
        self.machine_uuid.as_ref()
    }

    /// 切换到社区计划
    /// 对应api_request.py的功能
    pub async fn switch_to_community_plan(&self, session_token: &str) -> Result<crate::core::augment_api::SimpleApiResponse, anyhow::Error> {
        if let Some(ref api_client) = self.api_client {
            api_client.switch_to_community_plan(session_token).await
        } else {
            Err(anyhow::anyhow!("API客户端未初始化"))
        }
    }
}
