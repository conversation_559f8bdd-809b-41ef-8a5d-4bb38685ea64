# YAugment 版本检查器配置文件
# 对应原版 Python version_checker.py 中的常量配置
# 这是硬编码配置，不对用户暴露，编译时内嵌到程序中
#
# ⚠️  重要安全说明：
# - 此文件包含敏感信息（密钥、远程地址等）
# - 编译时会内嵌到程序中，用户无法看到或修改
# - 打包后的应用程序不会包含此文件

[version_checker]
# 解密密钥 - 与原版 Python 完全一致
decryption_key = "F8T8d3XQy6j9wL4qA7gC2rX7pV5kM9nH0zK1lC3bE4h="

# 版本检查URL - 与原版 Python 完全一致
version_check_url = "https://app.yan.vin/version/YAugment/version.json"
# version_check_url = "http://localhost:4386/version/YAugment/version.json"
# version_check_url = "http://y/test/YA/versionplus.json"

# 是否启用版本检查 - 与原版 Python 完全一致
enable_version_check = true

# 是否隐藏终端窗口 - 与原版 Python 完全一致
hide_console = true

# 免责声明同意版本文件名 - 与原版 Python 完全一致
agreed_version_filename = "disclaimer_agreed_version"

# 验证时效配置文件名 - 与原版 Python 完全一致
verification_data_filename = "grouptimeliness.json"

[current_versions]
# 当前版本信息（按平台区分）- 与原版 Python 完全一致
win = "2.4.1"
mac = "2.4.1"
linux = "2.4.1"

[verification]
# 验证码验证相关参数 - 与原版 Python 默认值一致
code_verification_enabled = true
code_verification_duration_hours = 24

# VIP QQ验证相关参数 - 与原版 Python 默认值一致
vip_qq_verification_enabled = true
vip_qq_duration_hours = 168
