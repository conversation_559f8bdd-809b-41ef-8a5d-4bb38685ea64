use tauri::{State, AppHandle, Emitter};
use crate::app_state::AppState;
use crate::core::email::EmailManager;

/// 生成邮箱
#[tauri::command]
pub async fn generate_email(state: State<'_, AppState>) -> Result<String, String> {
    println!("generate_email: 开始生成邮箱");

    let email_manager = state.email_manager.lock().map_err(|e| {
        println!("generate_email: 获取EmailManager锁失败: {}", e);
        e.to_string()
    })?;

    println!("generate_email: 成功获取EmailManager锁");
    let result = email_manager.generate_random_email();
    println!("generate_email: 邮箱生成完成: {}", result);

    Ok(result)
}

/// 生成临时邮箱 (保持兼容性)
#[tauri::command]
pub async fn generate_temp_email(state: State<'_, AppState>) -> Result<String, String> {
    // 临时邮箱通常是预配置的，直接从配置中获取
    let app_state = state.inner();
    let config_manager = app_state.config_manager.lock().unwrap();
    let config = config_manager.get_config();

    if let Some(email_config) = config.get("email") {
        if let Some(temp_mail_config) = email_config.get("temp_mail") {
            if let Some(email) = temp_mail_config.get("email").and_then(|v| v.as_str()) {
                return Ok(email.to_string());
            }
        }
    }

    Ok("<EMAIL>".to_string()) // 默认值
}

/// 获取验证码
#[tauri::command]
pub async fn get_verification_code(
    state: State<'_, AppState>,
    app_handle: AppHandle,
) -> Result<Option<String>, String> {
    // 获取最新配置
    let config = {
        let config_manager = state.config_manager.lock().map_err(|e| e.to_string())?;
        config_manager.get_config().clone()
    };

    // 创建EmailManager实例（确保使用最新配置）
    let mut email_manager = EmailManager::new(config)
        .map_err(|e| e.to_string())?;

    // 设置进度回调
    let app_handle_clone = app_handle.clone();
    let progress_callback = Some(Box::new(move |message: &str, progress: f64| {
        let _ = app_handle_clone.emit("verification_progress", serde_json::json!({
            "message": message,
            "progress": progress
        }));
    }) as crate::utils::ProgressCallback);

    email_manager.set_progress_callback(progress_callback).await;
    let result = email_manager.get_verification_code().await;

    // 发送事件到前端（与原版Python行为一致）
    if let Some(ref code) = result {
        // 自动复制到剪贴板
        if let Err(_) = copy_to_clipboard(&app_handle, code).await {
            println!("⚠️ 自动复制失败");
        } else {
            println!("📋 验证码已自动复制到剪贴板");
        }

        // 发送验证码事件
        let _ = app_handle.emit("show_verification_code", serde_json::json!({
            "code": code
        }));

        // 发送完成事件（成功）
        let _ = app_handle.emit("verification_complete", serde_json::json!({
            "success": true,
            "message": "验证码获取成功"
        }));
    } else {
        // 发送完成事件（失败）
        let error_message = email_manager.get_last_error()
            .unwrap_or_else(|| "获取验证码失败".to_string());
        let _ = app_handle.emit("verification_complete", serde_json::json!({
            "success": false,
            "message": error_message
        }));
    }

    Ok(result)
}

/// 测试邮箱连接
#[tauri::command]
pub async fn test_email_connection(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    let app_state = state.inner();
    let config = {
        let config_manager = app_state.config_manager.lock().unwrap();
        config_manager.get_config().clone()
    };

    let email_manager = EmailManager::new(config.clone())
        .map_err(|e| e.to_string())?;
    let result = email_manager.test_connection().await;

    Ok(serde_json::json!({
        "success": result.success,
        "message": result.message
    }))
}

/// 获取验证码状态
/// 对应原版Python的get_verification_status方法
#[tauri::command]
pub async fn get_verification_status(state: State<'_, AppState>) -> Result<String, String> {
    let verification_status = state.get_verification_status().await;
    Ok(serde_json::to_string(&verification_status).unwrap_or_else(|_| "{}".to_string()))
}

/// 停止验证码获取
#[tauri::command]
pub async fn stop_verification_code(_state: State<'_, AppState>) -> Result<(), String> {
    // 这里需要访问当前的 EmailManager 实例
    // 由于 EmailManager 不是全局状态的一部分，我们需要通过事件系统来实现停止功能
    // 暂时返回成功，实际实现需要更复杂的状态管理
    println!("🛑 收到停止验证码获取请求");
    Ok(())
}

/// 复制文本到剪贴板
#[tauri::command]
pub async fn copy_text_to_clipboard(text: String) -> Result<(), String> {
    use arboard::Clipboard;

    match Clipboard::new() {
        Ok(mut clipboard) => {
            match clipboard.set_text(&text) {
                Ok(_) => {
                    println!("📋 成功复制到剪贴板: {}", text);
                    Ok(())
                }
                Err(e) => {
                    let error_msg = format!("复制到剪贴板失败: {}", e);
                    println!("❌ {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        Err(e) => {
            let error_msg = format!("创建剪贴板实例失败: {}", e);
            println!("❌ {}", error_msg);
            Err(error_msg)
        }
    }
}

/// 复制文本到剪贴板的辅助函数
async fn copy_to_clipboard(_app_handle: &AppHandle, text: &str) -> Result<(), String> {
    use arboard::Clipboard;

    match Clipboard::new() {
        Ok(mut clipboard) => {
            match clipboard.set_text(text) {
                Ok(_) => {
                    println!("📋 成功复制到剪贴板: {}", text);
                    Ok(())
                }
                Err(e) => {
                    let error_msg = format!("复制到剪贴板失败: {}", e);
                    println!("❌ {}", error_msg);
                    Err(error_msg)
                }
            }
        }
        Err(e) => {
            let error_msg = format!("创建剪贴板实例失败: {}", e);
            println!("❌ {}", error_msg);
            Err(error_msg)
        }
    }
}
