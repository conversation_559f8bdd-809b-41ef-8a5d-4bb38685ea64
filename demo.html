<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YAugment UI Demo</title>
    <style>
        /* 完全复制YAugment的设计变量 */
        :root {
            --bg-primary: #000000;
            --bg-secondary: #0a0a0a;
            --bg-card: #111111;
            --bg-hover: #1a1a1a;

            --purple-primary: #8b5cf6;
            --purple-light: #a78bfa;
            --purple-dark: #6d28d9;
            --purple-glow: rgba(139, 92, 246, 0.5);

            --green-primary: #22c55e;
            --green-light: #4ade80;
            --green-dark: #16a34a;

            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --text-muted: #666666;

            --border-color: rgba(139, 92, 246, 0.2);
            --shadow-color: rgba(139, 92, 246, 0.3);

            --radius-sm: 16px;
            --radius-md: 20px;
            --radius-lg: 28px;
            --radius-xl: 36px;

            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            border-radius: var(--radius-lg);
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 0;
            height: 0;
        }

        /* 标题栏 - 完全复制YAugment样式 */
        .titlebar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.98) 0%, rgba(0, 0, 0, 0.95) 100%);
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);
            z-index: 1000;
            border-bottom: none;
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
            cursor: default;
            user-select: none;
        }

        .titlebar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 100%;
            padding: 0 20px;
        }

        .app-logo {
            display: flex;
            align-items: center;
        }

        .logo-text {
            font-size: 18px;
            font-weight: 700;
            letter-spacing: 1px;
            background: linear-gradient(135deg, var(--purple-primary), var(--purple-light));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 20px var(--purple-glow);
        }

        /* 页面容器 */
        .page {
            min-height: 100vh;
            padding-top: 60px;
            position: relative;
            opacity: 1;
            transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            overflow-y: auto;
        }

        /* 主容器 */
        .main-container {
            position: relative;
            overflow-y: auto;
        }

        /* Hero区域 - 完全复制YAugment样式 */
        .hero-section {
            position: relative;
            height: calc(100vh - 60px);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            text-align: center;
            z-index: 1;
            transform: translateY(-40px);
        }

        .hero-title {
            font-size: 80px;
            font-weight: 900;
            margin-bottom: 20px;
            position: relative;
            opacity: 1;
        }

        .hero-subtitle {
            font-size: 24px;
            color: var(--text-secondary);
            margin-bottom: 28px;
            opacity: 1;
        }

        /* Glitch效果 - 完全复制YAugment */
        .glitch-text {
            position: relative;
            color: var(--text-primary);
            animation: glitch 3s infinite;
        }

        .glitch-text::before,
        .glitch-text::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            will-change: transform;
        }

        .glitch-text::before {
            animation: glitch-1 0.8s infinite;
            color: var(--purple-primary);
            z-index: -1;
        }

        .glitch-text::after {
            animation: glitch-2 0.9s infinite;
            color: var(--purple-light);
            z-index: -2;
        }

        @keyframes glitch {
            0%, 100% {
                text-shadow: 0 0 8px var(--purple-glow);
                transform: translate(0);
            }
            10% {
                text-shadow: -4px 0 12px var(--purple-glow), 4px 0 12px var(--purple-light);
                transform: translate(-2px, 1px);
            }
            20% {
                text-shadow: 4px 0 12px var(--purple-glow), -4px 0 12px var(--purple-light);
                transform: translate(2px, -1px);
            }
            30% {
                text-shadow: 0 0 8px var(--purple-glow);
                transform: translate(0);
            }
        }

        @keyframes glitch-1 {
            0%, 100% {
                clip-path: inset(0 0 0 0);
                transform: translate(0);
            }
            15% {
                clip-path: inset(15% 0 70% 0);
                transform: translate(-4px, 3px);
            }
        }

        @keyframes glitch-2 {
            0%, 100% {
                clip-path: inset(0 0 0 0);
                transform: translate(0);
            }
            12% {
                clip-path: inset(35% 0 45% 0);
                transform: translate(3px, -2px);
            }
        }

        /* 功能区域 - 完全复制YAugment样式 */
        .features-section {
            display: flex;
            flex-direction: column;
            gap: 60px;
            justify-content: center;
            align-items: center;
            padding: 100px 40px;
            min-height: 100vh;
        }

        .features-row-1 {
            display: flex;
            gap: 60px;
            justify-content: center;
            align-items: flex-start;
        }

        /* 重置容器样式 - 完全复制YAugment */
        .reset-container {
            position: relative;
            width: 380px;
            height: 450px;
            opacity: 1;
            transform: translateY(0);
        }

        .reset-content {
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 30px;
            text-align: center;
            z-index: 5;
        }

        .reset-icon-wrapper {
            position: relative;
            margin-bottom: 35px;
        }

        .reset-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg,
                var(--purple-primary) 0%,
                var(--purple-dark) 50%,
                rgba(139, 92, 246, 0.9) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow:
                0 20px 40px rgba(139, 92, 246, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 6;
        }

        .reset-icon svg {
            filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.2));
            transition: all 0.3s ease;
        }

        .reset-title {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 15px;
            background: linear-gradient(135deg,
                var(--text-primary) 0%,
                var(--purple-light) 50%,
                rgba(255, 255, 255, 0.9) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(139, 92, 246, 0.3);
        }

        .reset-description {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 40px;
            line-height: 1.6;
            opacity: 0.9;
            max-width: 320px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: center;
        }

        .reset-action-btn {
            width: 200px;
            height: 55px;
            background: linear-gradient(135deg,
                var(--purple-primary) 0%,
                var(--purple-dark) 50%,
                rgba(139, 92, 246, 0.9) 100%);
            border: none;
            border-radius: 28px;
            color: white;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 15px 30px rgba(139, 92, 246, 0.5),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .reset-action-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow:
                0 20px 40px rgba(139, 92, 246, 0.6),
                0 0 0 2px rgba(255, 255, 255, 0.2),
                inset 0 2px 0 rgba(255, 255, 255, 0.3);
        }

        /* 邮箱工作流容器 - 完全复制YAugment样式 */
        .email-workflow-container {
            position: relative;
            width: 520px;
            opacity: 1;
            transform: translateY(0);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .email-workflow-card {
            position: relative;
            background: linear-gradient(145deg,
                rgba(15, 15, 15, 0.95) 0%,
                rgba(139, 92, 246, 0.03) 30%,
                rgba(15, 15, 15, 0.95) 100%);
            border: 1px solid rgba(139, 92, 246, 0.15);
            border-radius: 28px;
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            padding: 0;
            overflow: hidden;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(139, 92, 246, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .email-workflow-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg,
                transparent,
                rgba(139, 92, 246, 0.6),
                rgba(167, 139, 250, 0.4),
                transparent);
        }

        .workspace-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 24px;
            padding: 24px 32px 20px;
            border-bottom: 1px solid rgba(139, 92, 246, 0.1);
        }

        .header-icon {
            width: 44px;
            height: 44px;
            background: linear-gradient(135deg,
                rgba(139, 92, 246, 0.15),
                rgba(167, 139, 250, 0.2));
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--purple-light);
        }

        .header-content {
            flex: 1;
        }

        .workspace-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--text-primary);
            margin: 0 0 4px 0;
        }

        .workspace-subtitle {
            font-size: 14px;
            color: var(--text-secondary);
            margin: 0;
            opacity: 0.8;
        }

        .workflow-actions {
            padding: 0 32px 32px;
            display: flex;
            gap: 16px;
            justify-content: center;
        }

        .workflow-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 14px 24px;
            border: none;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-width: 140px;
            height: 48px;
        }

        .workflow-btn.primary {
            background: linear-gradient(135deg, var(--purple-primary), var(--purple-dark));
            color: white;
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }

        .workflow-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
        }

        .workflow-btn.secondary {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid var(--purple-primary);
            color: var(--purple-light);
        }

        .workflow-btn.secondary:hover {
            background: rgba(139, 92, 246, 0.2);
        }

        .workflow-btn.disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-loading {
            display: none;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 通知样式 - YAugment风格 */
        .notification {
            position: fixed;
            top: 80px;
            right: 20px;
            background: linear-gradient(145deg,
                rgba(15, 15, 15, 0.95) 0%,
                rgba(139, 92, 246, 0.03) 30%,
                rgba(15, 15, 15, 0.95) 100%);
            border: 1px solid rgba(139, 92, 246, 0.15);
            border-radius: 16px;
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            padding: 16px 20px;
            min-width: 300px;
            z-index: 1001;
            transform: translateX(400px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                0 0 0 1px rgba(139, 92, 246, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.05);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left: 4px solid var(--green-primary);
        }

        .notification.error {
            border-left: 4px solid #ef4444;
        }

        .notification.info {
            border-left: 4px solid var(--purple-primary);
        }
    </style>
</head>
<body>
    <!-- 标题栏 - 完全复制YAugment -->
    <div class="titlebar">
        <div class="titlebar-content">
            <div class="app-logo">
                <span class="logo-text">YAugment UI Demo</span>
            </div>
        </div>
    </div>

    <!-- 主页面 -->
    <div class="page">
        <div class="main-container">
            <!-- Hero区域 -->
            <div class="hero-section">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <span class="glitch-text" data-text="UI Demo">UI Demo</span>
                    </h1>
                    <p class="hero-subtitle">基于YAugment设计风格的组件展示</p>
                </div>
            </div>

            <!-- 功能区域 -->
            <div class="features-section">
                <!-- 第一行：重置和邮箱工作流 -->
                <div class="features-row-1">
                    <!-- 重置容器 -->
                    <div class="reset-container">
                        <div class="reset-content">
                            <div class="reset-icon-wrapper">
                                <div class="reset-icon">
                                    <svg width="48" height="48" viewBox="0 0 24 24">
                                        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" fill="currentColor"/>
                                    </svg>
                                </div>
                            </div>
                            <h2 class="reset-title">按钮演示</h2>
                            <p class="reset-description">展示各种按钮样式和交互效果</p>
                            <button class="reset-action-btn" onclick="showNotification('按钮点击成功！', 'success')">
                                <span class="btn-content">
                                    <span class="btn-text">点击测试</span>
                                </span>
                            </button>
                        </div>
                    </div>

                    <!-- 邮箱工作流 -->
                    <div class="email-workflow-container">
                        <div class="email-workflow-card">
                            <!-- 邮箱工作区 -->
                            <div class="workspace-header">
                                <div class="header-icon">
                                    <svg width="28" height="28" viewBox="0 0 24 24">
                                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" fill="currentColor"/>
                                    </svg>
                                </div>
                                <div class="header-content">
                                    <h2 class="workspace-title">组件展示</h2>
                                    <p class="workspace-subtitle">各种UI组件的交互演示</p>
                                </div>
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="workflow-actions">
                                <button class="workflow-btn primary" onclick="showNotification('主要操作执行成功！', 'success')">
                                    <div class="btn-content">
                                        <div class="btn-icon">
                                            <svg width="20" height="20" viewBox="0 0 24 24">
                                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="currentColor"/>
                                            </svg>
                                        </div>
                                        <span class="btn-text">主要操作</span>
                                    </div>
                                </button>

                                <button class="workflow-btn secondary" onclick="showNotification('次要操作完成！', 'info')">
                                    <div class="btn-content">
                                        <div class="btn-icon">
                                            <svg width="20" height="20" viewBox="0 0 24 24">
                                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"/>
                                            </svg>
                                        </div>
                                        <span class="btn-text">次要操作</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示通知 - YAugment风格
        function showNotification(message, type = 'info') {
            // 移除现有通知
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px; color: var(--text-primary);">
                    <div style="font-weight: 500; font-size: 14px;">${message}</div>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: var(--text-secondary); cursor: pointer; font-size: 18px; padding: 0; margin-left: auto;">&times;</button>
                </div>
            `;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => notification.classList.add('show'), 100);

            // 自动隐藏
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 400);
            }, 3000);
        }

        // 页面加载完成后显示欢迎消息
        window.addEventListener('load', () => {
            setTimeout(() => {
                showNotification('欢迎使用YAugment UI Demo！', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
