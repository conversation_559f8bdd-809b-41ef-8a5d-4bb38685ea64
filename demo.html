<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YAugment UI Demo</title>
    <style>
        /* 基础变量 - 继承YAugment的设计风格 */
        :root {
            --bg-primary: #000000;
            --bg-secondary: #0a0a0a;
            --bg-card: #111111;
            --bg-hover: #1a1a1a;
            
            --purple-primary: #8b5cf6;
            --purple-light: #a78bfa;
            --purple-dark: #6d28d9;
            --purple-glow: rgba(139, 92, 246, 0.5);
            
            --green-primary: #22c55e;
            --red-primary: #ef4444;
            --blue-primary: #3b82f6;
            
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --text-muted: #666666;
            
            --border-color: rgba(139, 92, 246, 0.2);
            --shadow-color: rgba(139, 92, 246, 0.3);
            
            --radius-sm: 16px;
            --radius-md: 20px;
            --radius-lg: 28px;
            --radius-xl: 36px;
            
            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            min-height: 100vh;
            padding: 20px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 容器 */
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        /* 标题区域 */
        .demo-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .demo-title {
            font-size: 48px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--purple-primary), var(--purple-light));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 16px;
            text-shadow: 0 0 20px var(--purple-glow);
        }

        .demo-subtitle {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .demo-description {
            font-size: 14px;
            color: var(--text-muted);
        }

        /* 网格布局 */
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        /* 卡片基础样式 */
        .demo-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 30px;
            position: relative;
            overflow: hidden;
            transition: var(--transition);
            backdrop-filter: blur(20px);
        }

        .demo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--purple-primary), transparent);
            opacity: 0;
            transition: var(--transition);
        }

        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
            border-color: var(--purple-primary);
        }

        .demo-card:hover::before {
            opacity: 1;
        }

        .card-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-primary);
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--radius-md);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            margin: 8px 8px 8px 0;
        }

        .btn::before {
            content: '';
            position: absolute;
            inset: 0;
            background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            opacity: 0;
            transition: var(--transition);
            transform: scale(0);
        }

        .btn:hover::before {
            opacity: 1;
            transform: scale(1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--purple-primary), var(--purple-dark));
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--purple-glow);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: var(--purple-primary);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--green-primary), #16a34a);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--red-primary), #dc2626);
            color: white;
        }

        /* 输入框样式 */
        .input-group {
            margin-bottom: 20px;
        }

        .input-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .input {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 14px;
            transition: var(--transition);
        }

        .input:focus {
            outline: none;
            border-color: var(--purple-primary);
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
            background: rgba(255, 255, 255, 0.08);
        }

        .input::placeholder {
            color: var(--text-muted);
        }

        /* 开关样式 */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.2);
            transition: var(--transition);
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: var(--transition);
            border-radius: 50%;
        }

        input:checked + .slider {
            background: linear-gradient(135deg, var(--purple-primary), var(--purple-dark));
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* 进度条样式 */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--purple-primary), var(--purple-light));
            border-radius: 4px;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 加载动画 */
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid var(--purple-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
        }

        .modal-content {
            background: var(--bg-card);
            margin: 10% auto;
            padding: 30px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            width: 90%;
            max-width: 500px;
            position: relative;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .close {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            color: var(--text-secondary);
            transition: var(--transition);
        }

        .close:hover {
            color: var(--text-primary);
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: 16px 20px;
            min-width: 300px;
            z-index: 1001;
            transform: translateX(400px);
            transition: var(--transition);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            border-left: 4px solid var(--green-primary);
        }

        .notification.error {
            border-left: 4px solid var(--red-primary);
        }

        .notification.info {
            border-left: 4px solid var(--blue-primary);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 标题区域 -->
        <div class="demo-header">
            <h1 class="demo-title">YAugment UI Demo</h1>
            <p class="demo-subtitle">现代化界面组件展示</p>
            <p class="demo-description">基于YAugment设计风格的UI组件库演示</p>
        </div>

        <!-- 组件展示网格 -->
        <div class="demo-grid">
            <!-- 按钮组件 -->
            <div class="demo-card">
                <h3 class="card-title">按钮组件</h3>
                <button class="btn btn-primary" onclick="showNotification('主要按钮被点击！', 'success')">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    主要按钮
                </button>
                <button class="btn btn-secondary" onclick="showNotification('次要按钮被点击！', 'info')">次要按钮</button>
                <button class="btn btn-success" onclick="showNotification('成功操作！', 'success')">成功按钮</button>
                <button class="btn btn-danger" onclick="showNotification('危险操作！', 'error')">危险按钮</button>
                <button class="btn btn-primary" onclick="simulateLoading(this)">
                    <span class="btn-text">加载按钮</span>
                    <div class="loading-spinner" style="display: none;"></div>
                </button>
            </div>

            <!-- 输入框组件 -->
            <div class="demo-card">
                <h3 class="card-title">输入框组件</h3>
                <div class="input-group">
                    <label class="input-label">用户名</label>
                    <input type="text" class="input" placeholder="请输入用户名" />
                </div>
                <div class="input-group">
                    <label class="input-label">邮箱地址</label>
                    <input type="email" class="input" placeholder="<EMAIL>" />
                </div>
                <div class="input-group">
                    <label class="input-label">密码</label>
                    <input type="password" class="input" placeholder="请输入密码" />
                </div>
                <button class="btn btn-primary" onclick="validateForm()">验证表单</button>
            </div>

            <!-- 开关和进度条 -->
            <div class="demo-card">
                <h3 class="card-title">开关与进度条</h3>
                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                        <span>启用通知</span>
                        <label class="switch">
                            <input type="checkbox" onchange="toggleFeature('notifications', this.checked)">
                            <span class="slider"></span>
                        </label>
                    </label>
                    <label style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                        <span>自动保存</span>
                        <label class="switch">
                            <input type="checkbox" checked onchange="toggleFeature('autosave', this.checked)">
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>
                <div>
                    <p style="margin-bottom: 8px; color: var(--text-secondary);">下载进度: <span id="progress-text">0%</span></p>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                    </div>
                    <button class="btn btn-primary" onclick="startProgress()">开始下载</button>
                </div>
            </div>

            <!-- 弹窗组件 -->
            <div class="demo-card">
                <h3 class="card-title">弹窗组件</h3>
                <button class="btn btn-primary" onclick="openModal()">打开模态框</button>
                <button class="btn btn-secondary" onclick="showNotification('这是一个信息通知', 'info')">显示通知</button>
                <button class="btn btn-success" onclick="showNotification('操作成功完成！', 'success')">成功通知</button>
                <button class="btn btn-danger" onclick="showNotification('发生了错误！', 'error')">错误通知</button>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 style="margin-bottom: 20px; color: var(--text-primary);">示例弹窗</h2>
            <p style="margin-bottom: 20px; color: var(--text-secondary);">这是一个基于YAugment设计风格的模态框示例。</p>
            <div class="input-group">
                <label class="input-label">输入内容</label>
                <input type="text" class="input" placeholder="在这里输入一些内容..." />
            </div>
            <div style="display: flex; gap: 12px; justify-content: flex-end;">
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="closeModal(); showNotification('模态框操作完成！', 'success')">确认</button>
            </div>
        </div>
    </div>

    <script>
        // 显示通知
        function showNotification(message, type = 'info') {
            // 移除现有通知
            const existingNotification = document.querySelector('.notification');
            if (existingNotification) {
                existingNotification.remove();
            }

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px;">
                    <div style="font-weight: 500;">${message}</div>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: var(--text-secondary); cursor: pointer; font-size: 18px;">&times;</button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => notification.classList.add('show'), 100);
            
            // 自动隐藏
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 400);
            }, 3000);
        }

        // 模拟加载
        function simulateLoading(button) {
            const text = button.querySelector('.btn-text');
            const spinner = button.querySelector('.loading-spinner');
            
            text.style.display = 'none';
            spinner.style.display = 'block';
            button.disabled = true;
            
            setTimeout(() => {
                text.style.display = 'block';
                spinner.style.display = 'none';
                button.disabled = false;
                showNotification('加载完成！', 'success');
            }, 2000);
        }

        // 开关切换
        function toggleFeature(feature, enabled) {
            showNotification(`${feature === 'notifications' ? '通知' : '自动保存'}已${enabled ? '启用' : '禁用'}`, enabled ? 'success' : 'info');
        }

        // 进度条动画
        function startProgress() {
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    showNotification('下载完成！', 'success');
                }
                
                progressFill.style.width = progress + '%';
                progressText.textContent = Math.round(progress) + '%';
            }, 200);
        }

        // 模态框控制
        function openModal() {
            document.getElementById('modal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 表单验证
        function validateForm() {
            const inputs = document.querySelectorAll('.demo-card:nth-child(2) .input');
            let isValid = true;
            
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    input.style.borderColor = 'var(--red-primary)';
                } else {
                    input.style.borderColor = 'var(--border-color)';
                }
            });
            
            if (isValid) {
                showNotification('表单验证通过！', 'success');
            } else {
                showNotification('请填写所有必填字段', 'error');
            }
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 页面加载完成后显示欢迎消息
        window.addEventListener('load', () => {
            setTimeout(() => {
                showNotification('欢迎使用YAugment UI Demo！', 'success');
            }, 500);
        });
    </script>
</body>
</html>
