/// 文件权限功能测试
/// 
/// 用于验证权限检查和修改功能是否正常工作

#[cfg(test)]
mod tests {
    use super::super::file_permissions::*;
    use std::fs;
    use std::path::Path;
    use tempfile::NamedTempFile;

    #[test]
    fn test_permission_check_nonexistent_file() {
        let non_existent_path = Path::new("non_existent_file.txt");
        let result = is_file_readonly(non_existent_path);
        assert!(result.is_ok());
        assert!(!result.unwrap()); // 不存在的文件不是只读
    }

    #[test]
    fn test_make_file_writable_nonexistent() {
        let non_existent_path = Path::new("non_existent_file.txt");
        let result = make_file_writable(non_existent_path);
        assert!(result.is_ok());
        let perm_result = result.unwrap();
        assert!(!perm_result.was_readonly);
        assert!(!perm_result.permission_changed);
        assert!(perm_result.success);
    }

    #[test]
    fn test_make_writable_file_writable() {
        // 创建一个临时文件
        let temp_file = NamedTempFile::new().expect("Failed to create temp file");
        let temp_path = temp_file.path();
        
        // 写入一些内容
        fs::write(temp_path, "test content").expect("Failed to write to temp file");
        
        // 测试使文件可写（应该已经是可写的）
        let result = make_file_writable(temp_path);
        assert!(result.is_ok());
        let perm_result = result.unwrap();
        assert!(perm_result.success);
        
        // 验证文件仍然可写
        let readonly = is_file_readonly(temp_path).expect("Failed to check readonly status");
        assert!(!readonly);
    }

    #[test]
    fn test_batch_file_processing() {
        // 创建多个临时文件
        let temp_file1 = NamedTempFile::new().expect("Failed to create temp file 1");
        let temp_file2 = NamedTempFile::new().expect("Failed to create temp file 2");
        
        let paths = vec![
            temp_file1.path().to_string_lossy().to_string(),
            temp_file2.path().to_string_lossy().to_string(),
            "non_existent_file.txt".to_string(), // 不存在的文件
        ];
        
        let results = make_files_writable(&paths);
        assert_eq!(results.len(), 3);
        
        // 前两个文件应该成功处理
        assert!(results[0].success);
        assert!(results[1].success);
        
        // 第三个文件不存在，但应该返回成功（无需处理）
        assert!(results[2].success);
        assert!(!results[2].was_readonly);
        assert!(!results[2].permission_changed);
    }

    #[test]
    fn test_ensure_file_writable() {
        let temp_file = NamedTempFile::new().expect("Failed to create temp file");
        let temp_path = temp_file.path();
        
        // 写入内容
        fs::write(temp_path, "test content").expect("Failed to write to temp file");
        
        // 确保文件可写
        let result = ensure_file_writable(temp_path);
        assert!(result.is_ok());
        
        // 对于已经可写的文件，应该返回false（无需修改）
        let modified = result.unwrap();
        assert!(!modified);
    }

    #[test]
    fn test_ensure_nonexistent_file_writable() {
        let non_existent_path = Path::new("definitely_non_existent_file.txt");
        let result = ensure_file_writable(non_existent_path);
        assert!(result.is_ok());
        assert!(!result.unwrap()); // 不存在的文件返回false
    }
}
