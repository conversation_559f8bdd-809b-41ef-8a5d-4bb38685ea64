/// VSCode 工作区清理工具
/// 
/// 对应原版 Python 的 augment_tools/vscode/augutils/workspace_cleaner.py
/// 清理 VSCode 工作区存储目录

use std::fs;
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};
use std::io::Write;
use zip::write::FileOptions;
use zip::{ZipWriter, CompressionMethod};

/// 失败的操作记录
#[derive(Debug, Clone, serde::Serialize)]
pub struct FailedOperation {
    pub operation_type: String,
    pub path: String,
    pub error: String,
}

/// 失败的压缩记录
#[derive(Debug, Clone, serde::Serialize)]
pub struct FailedCompression {
    pub file: String,
    pub error: String,
}

/// 清理结果结构体
/// 对应原版 Python 函数的返回字典
#[derive(Debug, Clone, serde::Serialize)]
pub struct CleanWorkspaceResult {
    pub backup_path: Option<String>,
    pub deleted_files_count: usize,
    pub failed_operations: Vec<FailedOperation>,
    pub failed_compressions: Vec<FailedCompression>,
}

use super::paths::get_workspace_storage_path;

/// 强制删除目录及其所有内容
/// 返回 true 表示成功，false 表示失败
fn force_delete_directory(path: &Path) -> bool {
    #[cfg(target_os = "windows")]
    {
        // Windows 系统，处理只读文件并使用长路径
        let path_str = format!("\\\\?\\{}", path.display());
        match std::fs::remove_dir_all(&path_str) {
            Ok(_) => true,
            Err(_) => {
                // 如果失败，尝试修改权限后再删除
                if let Ok(entries) = std::fs::read_dir(path) {
                    for entry in entries.flatten() {
                        let entry_path = entry.path();
                        if entry_path.is_file() {
                            #[cfg(target_os = "windows")]
                            {
                                if let Ok(metadata) = std::fs::metadata(&entry_path) {
                                    let mut perms = metadata.permissions();
                                    perms.set_readonly(false);
                                    let _ = std::fs::set_permissions(&entry_path, perms);
                                }
                            }
                            #[cfg(not(target_os = "windows"))]
                            {
                                use std::os::unix::fs::PermissionsExt;
                                let _ = std::fs::set_permissions(&entry_path,
                                    std::fs::Permissions::from_mode(0o644));
                            }
                        }
                    }
                }
                std::fs::remove_dir_all(&path_str).is_ok()
            }
        }
    }
    #[cfg(not(target_os = "windows"))]
    {
        std::fs::remove_dir_all(path).is_ok()
    }
}

/// 清理工作区存储目录
/// 对应原版 Python 的 clean_workspace_storage 函数
/// 
/// 此函数：
/// 1. 获取工作区存储路径
/// 2. 创建目录中所有文件的 zip 备份
/// 3. 删除目录中的所有文件
/// 
/// Returns:
///     Result<CleanWorkspaceResult, String>: 包含操作结果的结构体或错误信息
pub fn clean_workspace_storage() -> Result<CleanWorkspaceResult, String> {
    let workspace_path_str = get_workspace_storage_path();
    let workspace_path = Path::new(&workspace_path_str);

    if !workspace_path.exists() {
        // 如果工作区目录不存在，返回成功结果（没有文件需要清理）
        return Ok(CleanWorkspaceResult {
            backup_path: None,
            deleted_files_count: 0,
            failed_operations: Vec::new(),
            failed_compressions: Vec::new(),
        });
    }

    // 创建带时间戳的备份文件名
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();
    let backup_path = format!("{}_backup_{}.zip", workspace_path_str, timestamp);

    // 创建 zip 备份
    let mut failed_compressions = Vec::new();
    let backup_file = fs::File::create(&backup_path)
        .map_err(|e| format!("Failed to create backup file: {}", e))?;
    
    let mut zip = ZipWriter::new(backup_file);
    let options = FileOptions::default()
        .compression_method(CompressionMethod::Deflated)
        .unix_permissions(0o755);

    // 递归遍历目录并添加到 zip
    fn add_dir_to_zip(
        zip: &mut ZipWriter<fs::File>,
        dir_path: &Path,
        base_path: &Path,
        options: FileOptions,
        failed_compressions: &mut Vec<FailedCompression>,
    ) -> Result<(), String> {
        let entries = fs::read_dir(dir_path)
            .map_err(|e| format!("Failed to read directory {}: {}", dir_path.display(), e))?;

        for entry in entries {
            let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
            let path = entry.path();
            let relative_path = path.strip_prefix(base_path)
                .map_err(|e| format!("Failed to get relative path: {}", e))?;

            if path.is_file() {
                match fs::read(&path) {
                    Ok(content) => {
                        if let Err(e) = zip.start_file(relative_path.to_string_lossy().as_ref(), options) {
                            failed_compressions.push(FailedCompression {
                                file: path.to_string_lossy().to_string(),
                                error: format!("Failed to start zip file: {}", e),
                            });
                            continue;
                        }
                        if let Err(e) = zip.write_all(&content) {
                            failed_compressions.push(FailedCompression {
                                file: path.to_string_lossy().to_string(),
                                error: format!("Failed to write to zip: {}", e),
                            });
                        }
                    }
                    Err(e) => {
                        failed_compressions.push(FailedCompression {
                            file: path.to_string_lossy().to_string(),
                            error: format!("Failed to read file: {}", e),
                        });
                    }
                }
            } else if path.is_dir() {
                add_dir_to_zip(zip, &path, base_path, options, failed_compressions)?;
            }
        }
        Ok(())
    }

    add_dir_to_zip(&mut zip, workspace_path, workspace_path, options, &mut failed_compressions)?;
    zip.finish().map_err(|e| format!("Failed to finish zip: {}", e))?;

    // 计算删除前的文件数量
    let mut total_files = 0;
    fn count_files(dir: &Path, count: &mut usize) -> Result<(), std::io::Error> {
        for entry in fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            if path.is_file() {
                *count += 1;
            } else if path.is_dir() {
                count_files(&path, count)?;
            }
        }
        Ok(())
    }
    let _ = count_files(workspace_path, &mut total_files);

    // 删除目录中的所有文件
    let mut failed_operations = Vec::new();

    // 首先尝试整体删除目录树
    if !force_delete_directory(workspace_path) {
        // 如果整体删除失败，尝试逐个文件删除
        fn delete_files_recursively(
            dir: &Path,
            failed_operations: &mut Vec<FailedOperation>,
        ) -> Result<(), std::io::Error> {
            let entries = fs::read_dir(dir)?;
            
            for entry in entries {
                let entry = entry?;
                let path = entry.path();
                
                if path.is_file() {
                    #[cfg(target_os = "windows")]
                    {
                        // Windows: 清除只读属性
                        if let Ok(metadata) = fs::metadata(&path) {
                            let mut perms = metadata.permissions();
                            perms.set_readonly(false);
                            let _ = fs::set_permissions(&path, perms);
                        }
                    }
                    
                    if let Err(e) = fs::remove_file(&path) {
                        failed_operations.push(FailedOperation {
                            operation_type: "file".to_string(),
                            path: path.to_string_lossy().to_string(),
                            error: e.to_string(),
                        });
                    }
                } else if path.is_dir() {
                    delete_files_recursively(&path, failed_operations)?;
                    if let Err(e) = fs::remove_dir(&path) {
                        failed_operations.push(FailedOperation {
                            operation_type: "directory".to_string(),
                            path: path.to_string_lossy().to_string(),
                            error: e.to_string(),
                        });
                    }
                }
            }
            Ok(())
        }

        let _ = delete_files_recursively(workspace_path, &mut failed_operations);
    }

    Ok(CleanWorkspaceResult {
        backup_path: Some(backup_path),
        deleted_files_count: total_files,
        failed_operations,
        failed_compressions,
    })
}
