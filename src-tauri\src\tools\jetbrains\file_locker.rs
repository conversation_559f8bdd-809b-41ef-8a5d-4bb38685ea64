/// JetBrains 文件锁定工具
/// 
/// 负责锁定JetBrains ID文件，防止IDE重新生成
/// 对应 augment-vip-rust-master 的 lock_file 函数


use std::path::Path;
use std::process::Command;
#[cfg(any(not(target_os = "macos"), test))]
use std::fs;
use super::paths::get_all_id_file_paths;

/// 文件锁定结果结构体
#[derive(Debug, Clone, serde::Serialize)]
pub struct FileLockResult {
    pub locked_files: Vec<String>,
    pub failed_files: Vec<String>,
    pub lock_method: String,
}

/// 锁定单个文件
/// 使用平台特定的命令设置文件为只读
///
/// Args:
///     file_path: 要锁定的文件路径
///
/// Returns:
///     Result<String, String>: 锁定方法描述或错误信息
pub fn lock_single_file(file_path: &Path) -> Result<String, String> {
    println!("正在锁定文件: {}", file_path.display());

    if !file_path.exists() {
        return Err(format!("文件不存在，无法锁定: {}", file_path.display()));
    }

    let mut lock_methods = Vec::new();

    // 平台特定的锁定命令
    if cfg!(target_os = "windows") {
        // Windows: 使用 attrib +R 设置只读属性
        match Command::new("attrib")
            .args(["+R", &file_path.to_string_lossy()])
            .status()
        {
            Ok(status) if status.success() => {
                lock_methods.push("Windows attrib +R".to_string());
            }
            Ok(_) => {
                eprintln!("警告: attrib 命令执行失败");
            }
            Err(e) => {
                eprintln!("警告: 无法执行 attrib 命令: {}", e);
            }
        }
    } else {
        // Unix-like systems: 使用 chmod 444 设置只读权限
        match Command::new("chmod")
            .args(["444", &file_path.to_string_lossy()])
            .status()
        {
            Ok(status) if status.success() => {
                lock_methods.push("Unix chmod 444".to_string());
            }
            Ok(_) => {
                eprintln!("警告: chmod 命令执行失败");
            }
            Err(e) => {
                eprintln!("警告: 无法执行 chmod 命令: {}", e);
            }
        }

        // macOS: 使用 chflags uchg 设置不可变标志（需要管理员权限时请手动以管理员身份启动）
        #[cfg(target_os = "macos")]
        {
            match Command::new("chflags")
                .args(["uchg", &file_path.to_string_lossy()])
                .status()
            {
                Ok(status) if status.success() => {
                    lock_methods.push("macOS chflags uchg".to_string());
                }
                Ok(_) => {
                    eprintln!("警告: chflags 命令执行失败（可能需要管理员权限）");
                }
                Err(e) => {
                    eprintln!("警告: 无法执行 chflags 命令: {}", e);
                }
            }
        }
    }

    // 使用Rust API设置只读权限（跨平台备用方案）
    #[cfg(not(target_os = "macos"))] // macOS上Rust的文件系统API可能不够可靠
    {
        match fs::metadata(file_path) {
            Ok(metadata) => {
                let mut perms = metadata.permissions();
                perms.set_readonly(true);
                match fs::set_permissions(file_path, perms) {
                    Ok(_) => {
                        lock_methods.push("Rust set_readonly".to_string());
                    }
                    Err(e) => {
                        eprintln!("警告: Rust设置只读权限失败: {}", e);
                    }
                }
            }
            Err(e) => {
                eprintln!("警告: 无法获取文件元数据: {}", e);
            }
        }
    }

    if lock_methods.is_empty() {
        return Err("所有锁定方法都失败了".to_string());
    }

    let lock_method = lock_methods.join(", ");
    println!("文件锁定成功: {}", file_path.display());
    Ok(lock_method)
}

/// 锁定所有JetBrains ID文件
/// 防止IDE重新生成这些文件
/// 
/// Returns:
///     Result<FileLockResult, String>: 锁定结果或错误信息
pub fn lock_jetbrains_files() -> Result<FileLockResult, String> {
    println!("开始锁定JetBrains ID文件...");

    let id_file_paths = get_all_id_file_paths();
    
    if id_file_paths.is_empty() {
        return Err("未找到JetBrains ID文件".to_string());
    }

    let mut locked_files = Vec::new();
    let mut failed_files = Vec::new();
    let mut lock_methods = Vec::new();

    for file_path in id_file_paths {
        match lock_single_file(&file_path) {
            Ok(method) => {
                locked_files.push(file_path.to_string_lossy().to_string());
                if !lock_methods.contains(&method) {
                    lock_methods.push(method);
                }
            }
            Err(e) => {
                failed_files.push(format!("{}: {}", file_path.display(), e));
                eprintln!("锁定文件失败: {}", e);
            }
        }
    }

    let result = FileLockResult {
        locked_files,
        failed_files,
        lock_method: lock_methods.join(", "),
    };

    if result.locked_files.is_empty() {
        return Err("所有文件锁定都失败了".to_string());
    }

    println!("JetBrains文件锁定完成: {} 个成功, {} 个失败", 
             result.locked_files.len(), 
             result.failed_files.len());

    Ok(result)
}

/// 解锁单个文件
/// 移除文件的只读属性
/// 
/// Args:
///     file_path: 要解锁的文件路径
/// 
/// Returns:
///     Result<(), String>: 成功或错误信息
fn unlock_single_file(file_path: &Path) -> Result<(), String> {
    println!("正在解锁文件: {}", file_path.display());

    if !file_path.exists() {
        return Err(format!("文件不存在: {}", file_path.display()));
    }

    // 平台特定的解锁命令
    if cfg!(target_os = "windows") {
        // Windows: 使用 attrib -R 移除只读属性
        let _ = Command::new("attrib")
            .args(["-R", &file_path.to_string_lossy()])
            .status();
    } else {
        // Unix-like systems: 使用 chmod 644 设置可写权限
        let _ = Command::new("chmod")
            .args(["644", &file_path.to_string_lossy()])
            .status();

        // macOS: 移除不可变标志
        #[cfg(target_os = "macos")]
        {
            let _ = Command::new("chflags")
                .args(["nouchg", &file_path.to_string_lossy()])
                .status();
        }
    }

    // 使用Rust API移除只读权限
    #[cfg(not(target_os = "macos"))]
    {
        if let Ok(metadata) = fs::metadata(file_path) {
            let mut perms = metadata.permissions();
            perms.set_readonly(false);
            let _ = fs::set_permissions(file_path, perms);
        }
    }

    println!("文件解锁成功: {}", file_path.display());
    Ok(())
}

/// 解锁所有JetBrains ID文件
/// 用于测试或需要重新生成ID的情况
/// 
/// Returns:
///     Result<Vec<String>, String>: 解锁的文件列表或错误信息
pub fn unlock_jetbrains_files() -> Result<Vec<String>, String> {
    println!("开始解锁JetBrains ID文件...");

    let id_file_paths = get_all_id_file_paths();
    let mut unlocked_files = Vec::new();

    for file_path in id_file_paths {
        match unlock_single_file(&file_path) {
            Ok(_) => {
                unlocked_files.push(file_path.to_string_lossy().to_string());
            }
            Err(e) => {
                eprintln!("解锁文件失败: {}", e);
                // 继续处理其他文件
            }
        }
    }

    println!("JetBrains文件解锁完成: {} 个文件", unlocked_files.len());
    Ok(unlocked_files)
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use std::env;

    #[test]
    fn test_lock_and_unlock_file() {
        let temp_dir = env::temp_dir().join("jetbrains_lock_test");
        fs::create_dir_all(&temp_dir).unwrap();
        let test_file = temp_dir.join("test_lock_file");

        // 创建测试文件
        fs::write(&test_file, "test content").unwrap();

        // 测试锁定
        let lock_result = lock_single_file(&test_file);
        assert!(lock_result.is_ok());

        // 验证文件是否只读
        let metadata = fs::metadata(&test_file).unwrap();
        assert!(metadata.permissions().readonly());

        // 测试解锁
        let unlock_result = unlock_single_file(&test_file);
        assert!(unlock_result.is_ok());

        // 验证文件是否可写（在某些平台上可能不准确）
        // let metadata = fs::metadata(&test_file).unwrap();
        // assert!(!metadata.permissions().readonly());

        // 清理测试文件
        let _ = fs::remove_file(&test_file);
        let _ = fs::remove_dir(&temp_dir);
    }
}
