// YAugment Tauri 版本 - 清理版本
// 全局变量
let bridge = null;
let selectedEditor = null;
let currentPage = 'loading';

// 环境检测
function detectEnvironment() {
    const env = {
        isTauri: typeof window.__TAURI__ !== 'undefined',
        userAgent: navigator.userAgent,
        platform: navigator.platform
    };
    
    console.log('运行环境:', env);
    return env;
}

// 初始化 Tauri Bridge
function initWebChannel() {
    console.log('开始初始化 Tauri Bridge...');

    // 检测运行环境
    const env = detectEnvironment();
    console.log('环境检测结果:', env);

    // 检查 Tauri API 是否可用
    if (typeof window.__TAURI__ === 'undefined') {
        console.error('Tauri API 不可用，可能在浏览器中运行');
        // 在浏览器中运行时，直接显示编辑器选择页面
        setTimeout(() => {
            showPage('editorSelectPage');
        }, 1500);
        return;
    }

    // 创建 Tauri bridge 对象
    bridge = {
        // 配置管理
        get_config: async () => await window.__TAURI__.core.invoke('get_config'),
        set_config: async (key, value) => await window.__TAURI__.core.invoke('set_config_value', { key_path: key, value }),

        // 编辑器检测和管理
        detect_editors: async () => await window.__TAURI__.core.invoke('detect_editors'),
        set_selected_editor: async (editor) => await window.__TAURI__.core.invoke('select_editor', { editor_type: editor }),
        get_selected_editor: async () => await window.__TAURI__.core.invoke('get_selected_editor'),

        // 窗口控制
        minimize_window: async () => await window.__TAURI__.core.invoke('minimize_window'),
        toggle_maximize: async () => await window.__TAURI__.core.invoke('toggle_maximize'),
        close_window: async () => await window.__TAURI__.core.invoke('close_window'),

        // 窗口拖拽
        start_window_drag: async (x, y) => await window.__TAURI__.core.invoke('start_window_drag', { x, y }),
        update_window_drag: async (x, y) => await window.__TAURI__.core.invoke('update_window_drag', { x, y }),
        end_window_drag: async () => await window.__TAURI__.core.invoke('end_window_drag'),

        // 日志
        log_message: async (message) => await window.__TAURI__.core.invoke('log_message', { message }),

        // 邮箱管理
        start_verification_code: async () => await window.__TAURI__.core.invoke('start_verification_code'),
        stop_verification_code: async () => await window.__TAURI__.core.invoke('stop_verification_code'),

        // 账户管理
        get_account_info: async () => await window.__TAURI__.core.invoke('get_account_info'),
        set_account_info: async (info) => await window.__TAURI__.core.invoke('set_account_info', { info }),

        // 重置功能
        reset_all: async () => await window.__TAURI__.core.invoke('reset_all'),

        // 版本检查
        check_version: async () => await window.__TAURI__.core.invoke('check_version'),

        // 工具相关
        get_cursor_tools: async () => await window.__TAURI__.core.invoke('get_cursor_tools'),
        get_vscode_tools: async () => await window.__TAURI__.core.invoke('get_vscode_tools'),
        execute_cursor_tool: async (tool, params) => await window.__TAURI__.core.invoke('execute_cursor_tool', { tool, params }),
        execute_vscode_tool: async (tool, params) => await window.__TAURI__.core.invoke('execute_vscode_tool', { tool, params })
    };

    window.bridge = bridge;  // 设置为全局变量

    console.log('Tauri bridge 初始化成功');
    console.log('Bridge 对象:', bridge);
    
    // 初始化应用
    initializeApp();
}

// 页面显示函数
function showPage(pageId) {
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => page.classList.add('hidden'));
    
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        targetPage.classList.remove('hidden');
        currentPage = pageId.replace('Page', '');
        console.log('显示页面:', pageId);
    }
}

// 隐藏加载页面并显示目标页面
function hideLoadingPageAndShow(targetPageId) {
    const loadingPage = document.getElementById('loadingPage');
    const targetPage = document.getElementById(targetPageId);
    
    if (loadingPage) {
        loadingPage.style.opacity = '0';
        setTimeout(() => {
            loadingPage.classList.add('hidden');
            if (targetPage) {
                targetPage.classList.remove('hidden');
                targetPage.style.opacity = '1';
                currentPage = targetPageId.replace('Page', '');
                console.log('切换到页面:', targetPageId);
            }
        }, 300);
    }
}

// 初始化应用
async function initializeApp() {
    try {
        console.log('开始初始化应用...');
        
        // 确保最小加载时间，避免闪烁
        const minLoadingTime = 1200; // 1.2秒最小加载时间
        const startTime = Date.now();
        
        // 显示加载页面
        showPage('loadingPage');
        
        // 检查 bridge 是否可用
        if (!bridge) {
            console.error('Bridge 未初始化');
            setTimeout(() => showPage('editorSelectPage'), minLoadingTime);
            return;
        }
        
        // 获取配置信息
        let config = null;
        try {
            config = await bridge.get_config();
            console.log('获取到配置:', config);
        } catch (error) {
            console.error('获取配置失败:', error);
            config = {};
        }

        // 检测编辑器
        let editors = null;
        try {
            editors = await bridge.detect_editors();
            console.log('检测到编辑器:', editors);
            updateEditorStatus(editors);
        } catch (error) {
            console.error('检测编辑器失败:', error);
        }
        
        // 计算剩余等待时间
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minLoadingTime - elapsedTime);
        
        // 根据配置决定显示哪个页面
        if (config && config.editor_type) {
            // 已配置编辑器，显示主页面
            selectedEditor = config.editor_type;
            console.log('已配置编辑器:', selectedEditor);
            
            // 更新编辑器显示
            updateEditorDisplay();
            
            // 初始化关于页面
            initializeAboutPage();
            
            setTimeout(() => {
                hideLoadingPageAndShow('main');
            }, remainingTime);
        } else {
            // 未配置编辑器，显示编辑器选择页面
            console.log('未配置编辑器，显示选择页面');
            setTimeout(() => {
                hideLoadingPageAndShow('editorSelect');
            }, remainingTime);
        }
        
    } catch (error) {
        console.error('应用初始化失败:', error);
        // 出错时显示编辑器选择页面
        setTimeout(() => {
            showPage('editorSelectPage');
        }, 1200);
    }
}

// 窗口控制函数
async function minimizeWindow() {
    try {
        console.log('最小化窗口...');
        if (!bridge || !bridge.minimize_window) {
            console.error('Bridge 未初始化或 minimize_window 方法不可用');
            return;
        }
        await bridge.minimize_window();
        console.log('窗口最小化成功');
    } catch (error) {
        console.error('最小化窗口失败:', error);
    }
}

async function toggleMaximize() {
    try {
        console.log('切换最大化窗口...');
        if (!bridge || !bridge.toggle_maximize) {
            console.error('Bridge 未初始化或 toggle_maximize 方法不可用');
            return;
        }
        await bridge.toggle_maximize();
        console.log('窗口最大化切换成功');
    } catch (error) {
        console.error('切换最大化窗口失败:', error);
    }
}

async function closeWindow() {
    try {
        console.log('关闭窗口...');
        if (!bridge || !bridge.close_window) {
            console.error('Bridge 未初始化或 close_window 方法不可用');
            return;
        }
        await bridge.close_window();
        console.log('窗口关闭成功');
    } catch (error) {
        console.error('关闭窗口失败:', error);
    }
}

// 设置标题栏事件（双击最大化等）
function setupTitlebarEvents() {
    const titlebar = document.getElementById('titlebar');
    const windowControls = titlebar.querySelector('.window-controls');

    // 双击标题栏最大化/还原窗口
    titlebar.addEventListener('dblclick', (e) => {
        // 如果双击的是窗口控制按钮区域，不触发最大化
        if (windowControls.contains(e.target) || e.target.closest('.window-controls')) {
            return;
        }
        e.preventDefault();
        e.stopPropagation();
        toggleMaximize();
    });

    // 阻止标题栏上的文本选择
    titlebar.addEventListener('selectstart', (e) => {
        e.preventDefault();
    });

    // 阻止标题栏上的右键菜单
    titlebar.addEventListener('contextmenu', (e) => {
        e.preventDefault();
    });
}

// 标题栏拖拽功能
function setupTitlebarDrag() {
    const titlebar = document.getElementById('titlebar');
    const windowControls = titlebar.querySelector('.window-controls');
    let isDragging = false;
    let startX = 0;
    let startY = 0;

    titlebar.addEventListener('mousedown', (e) => {
        // 如果点击的是窗口控制按钮区域，不启动拖拽
        if (windowControls.contains(e.target) || e.target.closest('.window-controls')) {
            return;
        }

        if (e.button === 0) { // 左键
            isDragging = true;
            startX = e.screenX;
            startY = e.screenY;

            // 阻止默认行为
            e.preventDefault();
            e.stopPropagation();

            // 阻止文本选择，但不改变光标样式
            document.body.style.userSelect = 'none';

            // 通知Tauri开始拖拽
            if (bridge && bridge.start_window_drag) {
                bridge.start_window_drag(startX, startY);
            }
        }
    });

    // 优化鼠标移动事件处理，使用节流避免过度调用
    let dragThrottle = false;
    document.addEventListener('mousemove', (e) => {
        if (!isDragging || dragThrottle) return;

        dragThrottle = true;
        requestAnimationFrame(() => {
            // 使用屏幕坐标，更稳定
            if (bridge && bridge.update_window_drag && isDragging) {
                bridge.update_window_drag(e.screenX, e.screenY);
            }
            dragThrottle = false;
        });
    });

    document.addEventListener('mouseup', (e) => {
        if (isDragging) {
            isDragging = false;
            document.body.style.userSelect = '';

            // 通知Tauri结束拖拽
            if (bridge && bridge.end_window_drag) {
                bridge.end_window_drag();
            }
        }
    });

    // 双击标题栏最大化/还原窗口
    titlebar.addEventListener('dblclick', (e) => {
        // 如果双击的是窗口控制按钮区域，不触发最大化
        if (windowControls.contains(e.target) || e.target.closest('.window-controls')) {
            return;
        }
        e.preventDefault();
        e.stopPropagation();
        toggleMaximize();
    });

    // 阻止标题栏上的文本选择
    titlebar.addEventListener('selectstart', (e) => {
        e.preventDefault();
    });

    // 阻止标题栏上的右键菜单
    titlebar.addEventListener('contextmenu', (e) => {
        e.preventDefault();
    });
}

// 更新编辑器状态显示
function updateEditorStatus(status) {
    console.log('更新编辑器状态:', status);

    if (!status || typeof status !== 'object') {
        console.warn('updateEditorStatus: 无效的状态对象', status);
        return;
    }

    // VS Code状态
    const vscodeStatus = document.getElementById('vscodeStatus');
    if (vscodeStatus) {
        try {
            const vscodeInstalled = status.vscode?.installed || false;
            const indicator = vscodeStatus.querySelector('.status-indicator');
            const text = vscodeStatus.querySelector('.status-text');

            if (indicator && text) {
                indicator.className = `status-indicator ${vscodeInstalled ? 'installed' : 'not-installed'}`;
                text.className = `status-text ${vscodeInstalled ? 'installed' : 'not-installed'}`;
                text.textContent = vscodeInstalled ? '已安装' : '未安装';

                // 检查文字滚动
                checkAndEnableTextScrolling(text);
            }
        } catch (error) {
            console.error('更新VS Code状态时出错:', error);
        }
    }

    // Cursor状态
    const cursorStatus = document.getElementById('cursorStatus');
    if (cursorStatus) {
        try {
            const cursorInstalled = status.cursor?.installed || false;
            const indicator = cursorStatus.querySelector('.status-indicator');
            const text = cursorStatus.querySelector('.status-text');

            if (indicator && text) {
                indicator.className = `status-indicator ${cursorInstalled ? 'installed' : 'not-installed'}`;
                text.className = `status-text ${cursorInstalled ? 'installed' : 'not-installed'}`;
                text.textContent = cursorInstalled ? '已安装' : '未安装';

                // 检查文字滚动
                checkAndEnableTextScrolling(text);
            }
        } catch (error) {
            console.error('更新Cursor状态时出错:', error);
        }
    }
}

// 检查文字是否需要滚动并启用滚动效果
function checkAndEnableTextScrolling(element) {
    if (!element) {
        console.warn('checkAndEnableTextScrolling: element is null or undefined');
        return;
    }

    // 检查元素是否有classList属性
    if (!element.classList) {
        console.warn('checkAndEnableTextScrolling: element does not have classList');
        return;
    }

    // 检查是否是状态文字元素
    if (!element.classList.contains('status-text')) return;

    // 等待DOM更新后再检查
    setTimeout(() => {
        try {
            // 再次检查元素是否仍然存在
            if (!element || !element.parentNode) {
                console.warn('checkAndEnableTextScrolling: element no longer exists in DOM');
                return;
            }

            const containerWidth = 200; // 状态文字容器的最大宽度
            const textWidth = element.scrollWidth || 0;

            // 移除之前的滚动类
            if (element.classList) {
                element.classList.remove('text-scrolling');
            }

            // 如果文字宽度超过容器宽度，启用滚动
            if (textWidth > containerWidth) {
                if (element.classList) {
                    element.classList.add('text-scrolling');
                }

                // 动态计算滚动距离并设置CSS变量
                const scrollDistance = textWidth - containerWidth + 20; // 额外20px边距
                if (element.style) {
                    element.style.setProperty('--scroll-distance', `-${scrollDistance}px`);
                }
            }
        } catch (error) {
            console.error('checkAndEnableTextScrolling error:', error);
        }
    }, 50);
}

// 更新编辑器显示
function updateEditorDisplay() {
    console.log('更新编辑器显示:', selectedEditor);
    const currentEditorNameEl = document.getElementById('currentEditorName');
    if (currentEditorNameEl && selectedEditor) {
        currentEditorNameEl.textContent = selectedEditor.toUpperCase();
    }
}

// 初始化关于页面
function initializeAboutPage() {
    console.log('初始化关于页面');
    // 这里可以添加关于页面初始化逻辑
}

// 选择编辑器函数
async function selectEditor(editorType) {
    try {
        console.log('选择编辑器:', editorType);
        selectedEditor = editorType;

        if (bridge && bridge.set_selected_editor) {
            await bridge.set_selected_editor(editorType);
            console.log('编辑器设置成功');
        }

        // 更新显示
        updateEditorDisplay();

        // 跳转到主页面
        setTimeout(() => {
            hideLoadingPageAndShow('main');
        }, 500);

    } catch (error) {
        console.error('选择编辑器失败:', error);
    }
}

// 显示设置页面（占位函数）
function showSettings() {
    console.log('显示设置页面');
    // 这里可以添加设置页面显示逻辑
}

// Toast 提示函数（占位函数）
function showToast(message, type = 'info', duration = 3000) {
    console.log(`Toast [${type}]: ${message}`);
    // 这里可以添加 Toast 显示逻辑
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM 加载完成，开始初始化...');

    // 检查所有必要的页面元素
    const loadingPage = document.getElementById('loadingPage');
    const editorSelectPage = document.getElementById('editorSelectPage');
    const mainPage = document.getElementById('mainPage');

    console.log('页面元素检查:');
    console.log('- loadingPage:', loadingPage ? '存在' : '不存在');
    console.log('- editorSelectPage:', editorSelectPage ? '存在' : '不存在');
    console.log('- mainPage:', mainPage ? '存在' : '不存在');

    // 确保加载页面显示
    if (loadingPage) {
        loadingPage.classList.remove('hidden');
        loadingPage.style.display = 'block';
        console.log('显示加载页面');
    } else {
        console.error('找不到加载页面元素');
    }

    // 设置标题栏事件
    setupTitlebarEvents();

    // 设置标题栏拖拽功能
    setupTitlebarDrag();

    // 初始化 WebChannel
    initWebChannel();
});

// 添加窗口加载事件作为备用
window.addEventListener('load', function() {
    console.log('窗口加载完成');

    // 如果 DOM 事件没有触发，这里作为备用
    if (!bridge) {
        console.log('备用初始化触发');
        initWebChannel();
    }
});

// ===== 缺失的关键页面显示函数 =====

// 隐藏加载页面并显示目标页面（带平滑过渡）
function hideLoadingPageAndShow(targetPageId) {
    const loadingPage = document.getElementById('loadingPage');
    const targetPage = document.getElementById(targetPageId);

    console.log('切换页面:', targetPageId);

    if (loadingPage) {
        loadingPage.style.opacity = '0';
        setTimeout(() => {
            loadingPage.classList.add('hidden');
            if (targetPage) {
                targetPage.classList.remove('hidden');
                targetPage.style.opacity = '1';
                currentPage = targetPageId.replace('Page', '');
                console.log('页面切换完成:', targetPageId);
            } else {
                console.error('目标页面不存在:', targetPageId);
            }
        }, 300);
    }
}

// 基本的页面动画初始化函数
function initPageAnimations() {
    console.log('初始化页面动画');

    // 如果存在animations.js中的函数，优先使用
    if (typeof window.initPageAnimations === 'function' && window.initPageAnimations !== initPageAnimations) {
        window.initPageAnimations();
        return;
    }

    // 基本的动画初始化
    try {
        // 确保主要元素可见
        const heroTitle = document.querySelector('.hero-title');
        const heroSubtitle = document.querySelector('.hero-subtitle');
        const selectedEditor = document.querySelector('.selected-editor');

        if (heroTitle) {
            heroTitle.style.opacity = '1';
            heroTitle.style.transform = 'translateY(0)';
        }

        if (heroSubtitle) {
            heroSubtitle.style.opacity = '1';
            heroSubtitle.style.transform = 'translateY(0)';
        }

        if (selectedEditor) {
            selectedEditor.style.opacity = '1';
            selectedEditor.style.transform = 'scale(1)';
        }

        console.log('基本页面动画初始化完成');
    } catch (error) {
        console.error('页面动画初始化失败:', error);
    }
}
