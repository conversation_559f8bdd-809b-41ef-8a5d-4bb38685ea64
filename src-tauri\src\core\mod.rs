/// 核心业务逻辑模块
/// 
/// 对应原版 Python 的 core 包，包含所有核心功能模块

pub mod config;
pub mod editor;
pub mod email;
pub mod account;
pub mod reset;
pub mod version;
pub mod qq_api;
pub mod expiry_checker;
pub mod crypto_aes256;
pub mod account_storage;
pub mod portal_query;
pub mod augment_api;

// 重新导出核心类型
pub use config::ConfigManager;
pub use editor::EditorDetector;
pub use email::EmailManager;
pub use account::AugmentAccountManager;
pub use crypto_aes256::CryptoUtils;
pub use account_storage::{AccountStorageManager, AccountRecord};
pub use portal_query::{OrbAccountInfo, AccountData};
pub use augment_api::{AugmentApiClient, ApiResponse, PortalTokenData};
// pub use reset::AugmentReset;  // 暂时注释，后续实现
// pub use version::VersionChecker;  // 暂时注释，后续实现
