<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JetBrains SessionID 测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .status-area {
            background: rgba(0,0,0,0.2);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            min-height: 200px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 400px;
        }
        
        .success {
            color: #2ecc71;
        }
        
        .error {
            color: #e74c3c;
        }
        
        .info {
            color: #3498db;
        }
        
        .warning {
            color: #f39c12;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 JetBrains SessionID 测试</h1>
        
        <div class="button-group">
            <button onclick="checkSessionStatus()">检查SessionID状态</button>
            <button onclick="modifySessionIds()">修改SessionID</button>
            <button onclick="modifySessionIdsCustom()">修改SessionID (自定义)</button>
            <button onclick="clearStatus()">清空状态</button>
        </div>
        
        <div class="status-area" id="statusArea">
点击按钮开始测试 JetBrains SessionID 功能...

功能说明：
1. 检查SessionID状态 - 查看当前所有JetBrains产品的SessionID配置
2. 修改SessionID - 自动生成新的SessionID并应用到所有JetBrains产品
3. 修改SessionID (自定义) - 使用自定义的SessionID
4. 清空状态 - 清空显示区域

注意：此功能仅对JetBrains系列IDE有效（IntelliJ IDEA, PyCharm, WebStorm等）
        </div>
    </div>

    <script>
        // 创建Tauri兼容的bridge对象
        const bridge = {
            jetbrains_check_session_status: () => window.__TAURI__.invoke('jetbrains_check_session_status'),
            jetbrains_modify_session_ids: (customSessionId) => window.__TAURI__.invoke('jetbrains_modify_session_ids', { custom_session_id: customSessionId })
        };

        function log(message, type = 'info') {
            const statusArea = document.getElementById('statusArea');
            const timestamp = new Date().toLocaleTimeString();
            const className = type;
            
            statusArea.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            statusArea.scrollTop = statusArea.scrollHeight;
        }

        function clearStatus() {
            document.getElementById('statusArea').innerHTML = '';
        }

        async function checkSessionStatus() {
            log('正在检查JetBrains SessionID状态...', 'info');
            
            try {
                const result = await bridge.jetbrains_check_session_status();
                log('SessionID状态检查完成:', 'success');
                log(JSON.stringify(result, null, 2), 'info');
                
                if (result.products && result.products.length > 0) {
                    log(`\n找到 ${result.total_products} 个JetBrains产品:`, 'info');
                    result.products.forEach(product => {
                        const status = product.has_session_id ? '✅ 已配置' : '❌ 未配置';
                        log(`  ${product.product}: ${status}`, product.has_session_id ? 'success' : 'warning');
                        if (product.session_id) {
                            log(`    SessionID: ${product.session_id}`, 'info');
                        }
                    });
                } else {
                    log('未找到任何JetBrains产品配置', 'warning');
                }
            } catch (error) {
                log(`检查SessionID状态失败: ${error}`, 'error');
            }
        }

        async function modifySessionIds() {
            log('正在修改JetBrains SessionID...', 'info');
            
            try {
                const result = await bridge.jetbrains_modify_session_ids(null);
                log('SessionID修改完成:', 'success');
                log(JSON.stringify(result, null, 2), 'info');
                
                if (result.success) {
                    log(`\n✅ 修改成功!`, 'success');
                    log(`新SessionID: ${result.new_session_id}`, 'success');
                    log(`成功修改: ${result.configs_updated} 个配置`, 'success');
                    log(`失败: ${result.configs_failed} 个配置`, result.configs_failed > 0 ? 'warning' : 'info');
                    
                    if (result.old_session_ids && result.old_session_ids.length > 0) {
                        log(`\n旧SessionID:`, 'info');
                        result.old_session_ids.forEach(oldId => {
                            log(`  ${oldId}`, 'info');
                        });
                    }
                    
                    if (result.session_id_backup_paths && result.session_id_backup_paths.length > 0) {
                        log(`\n备份文件:`, 'info');
                        result.session_id_backup_paths.forEach(path => {
                            log(`  ${path}`, 'info');
                        });
                    }
                } else {
                    log('❌ SessionID修改失败', 'error');
                }
            } catch (error) {
                log(`修改SessionID失败: ${error}`, 'error');
            }
        }

        async function modifySessionIdsCustom() {
            const customId = prompt('请输入自定义SessionID (留空则自动生成):');
            
            log(`正在使用${customId ? '自定义' : '自动生成的'}SessionID修改配置...`, 'info');
            if (customId) {
                log(`自定义SessionID: ${customId}`, 'info');
            }
            
            try {
                const result = await bridge.jetbrains_modify_session_ids(customId || null);
                log('SessionID修改完成:', 'success');
                log(JSON.stringify(result, null, 2), 'info');
                
                if (result.success) {
                    log(`\n✅ 修改成功!`, 'success');
                    log(`使用的SessionID: ${result.new_session_id}`, 'success');
                    log(`成功修改: ${result.configs_updated} 个配置`, 'success');
                    log(`失败: ${result.configs_failed} 个配置`, result.configs_failed > 0 ? 'warning' : 'info');
                } else {
                    log('❌ SessionID修改失败', 'error');
                }
            } catch (error) {
                log(`修改SessionID失败: ${error}`, 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('JetBrains SessionID 测试页面已加载', 'success');
            log('请确保已安装JetBrains系列IDE以测试功能', 'info');
        });
    </script>
</body>
</html>
