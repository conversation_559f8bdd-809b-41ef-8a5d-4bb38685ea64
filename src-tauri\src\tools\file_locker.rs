/// 通用文件锁定工具
/// 
/// 提供跨平台的文件锁定功能，防止IDE重新生成ID文件
/// 对应原始项目的 lock_file 函数

use std::path::Path;
use std::process::Command;

/// 文件锁定结果结构体
#[derive(Debug, Clone, serde::Serialize)]
pub struct FileLockResult {
    pub locked_files: Vec<String>,
    pub failed_files: Vec<String>,
    pub lock_methods: Vec<String>,
}

/// 锁定单个文件
/// 对应原始项目的 lock_file 函数
/// 
/// 此函数使用平台特定的方法锁定文件：
/// - Windows: 使用 attrib +R 设置只读属性
/// - Unix/Linux: 使用 chmod 444 设置只读权限
/// - macOS: 额外使用 chflags uchg 设置不可变标志（需要管理员权限时请手动以管理员身份启动）
/// 
/// Args:
///     file_path: 要锁定的文件路径
/// 
/// Returns:
///     Result<String, String>: 成功时返回使用的锁定方法，失败时返回错误信息
pub fn lock_single_file(file_path: &Path) -> Result<String, String> {
    println!("正在锁定文件: {}", file_path.display());

    if !file_path.exists() {
        return Err(format!("文件不存在，无法锁定: {}", file_path.display()));
    }

    let mut lock_methods = Vec::new();

    // 平台特定的锁定命令
    if cfg!(target_os = "windows") {
        // Windows: 使用 attrib +R 设置只读属性
        match Command::new("attrib")
            .args(["+R", &file_path.to_string_lossy()])
            .status()
        {
            Ok(status) if status.success() => {
                lock_methods.push("Windows attrib +R".to_string());
            }
            Ok(_) => {
                eprintln!("警告: attrib 命令执行失败");
            }
            Err(e) => {
                eprintln!("警告: 无法执行 attrib 命令: {}", e);
            }
        }
    } else {
        // Unix-like systems: 使用 chmod 444 设置只读权限
        match Command::new("chmod")
            .args(["444", &file_path.to_string_lossy()])
            .status()
        {
            Ok(status) if status.success() => {
                lock_methods.push("Unix chmod 444".to_string());
            }
            Ok(_) => {
                eprintln!("警告: chmod 命令执行失败");
            }
            Err(e) => {
                eprintln!("警告: 无法执行 chmod 命令: {}", e);
            }
        }

        // macOS: 使用 chflags uchg 设置不可变标志（需要管理员权限时请手动以管理员身份启动）
        #[cfg(target_os = "macos")]
        {
            match Command::new("chflags")
                .args(["uchg", &file_path.to_string_lossy()])
                .status()
            {
                Ok(status) if status.success() => {
                    lock_methods.push("macOS chflags uchg".to_string());
                }
                Ok(_) => {
                    eprintln!("警告: chflags 命令执行失败（可能需要管理员权限）");
                }
                Err(e) => {
                    eprintln!("警告: 无法执行 chflags 命令: {}", e);
                }
            }
        }
    }

    // 备用方法：使用 Rust API 设置只读属性
    if let Err(e) = std::fs::metadata(file_path)
        .and_then(|metadata| {
            let mut permissions = metadata.permissions();
            permissions.set_readonly(true);
            std::fs::set_permissions(file_path, permissions)
        })
    {
        eprintln!("警告: 无法使用 Rust API 设置只读属性: {}", e);
    } else {
        lock_methods.push("Rust set_readonly".to_string());
    }

    if lock_methods.is_empty() {
        Err("所有锁定方法都失败了".to_string())
    } else {
        let methods_str = lock_methods.join(", ");
        println!("文件锁定成功");
        Ok(methods_str)
    }
}

/// 解锁单个文件
/// 移除文件的只读属性
/// 
/// Args:
///     file_path: 要解锁的文件路径
/// 
/// Returns:
///     Result<(), String>: 成功或错误信息
pub fn unlock_single_file(file_path: &Path) -> Result<(), String> {
    println!("正在解锁文件: {}", file_path.display());

    if !file_path.exists() {
        return Err(format!("文件不存在: {}", file_path.display()));
    }

    // 平台特定的解锁命令
    if cfg!(target_os = "windows") {
        // Windows: 使用 attrib -R 移除只读属性
        let _ = Command::new("attrib")
            .args(["-R", &file_path.to_string_lossy()])
            .status();
    } else {
        // Unix-like systems: 使用 chmod 644 设置可写权限
        let _ = Command::new("chmod")
            .args(["644", &file_path.to_string_lossy()])
            .status();

        // macOS: 移除不可变标志（需要管理员权限时请手动以管理员身份启动）
        #[cfg(target_os = "macos")]
        {
            let _ = Command::new("chflags")
                .args(["nouchg", &file_path.to_string_lossy()])
                .status();
        }
    }

    // 备用方法：使用 Rust API 移除只读属性
    if let Err(e) = std::fs::metadata(file_path)
        .and_then(|metadata| {
            let mut permissions = metadata.permissions();
            permissions.set_readonly(false);
            std::fs::set_permissions(file_path, permissions)
        })
    {
        eprintln!("警告: 无法使用 Rust API 移除只读属性: {}", e);
    }

    println!("文件解锁完成: {}", file_path.display());
    Ok(())
}

/// 锁定多个文件
/// 
/// Args:
///     file_paths: 要锁定的文件路径列表
/// 
/// Returns:
///     Result<FileLockResult, String>: 锁定结果
pub fn lock_multiple_files(file_paths: &[&Path]) -> Result<FileLockResult, String> {
    let mut result = FileLockResult {
        locked_files: Vec::new(),
        failed_files: Vec::new(),
        lock_methods: Vec::new(),
    };

    for file_path in file_paths {
        match lock_single_file(file_path) {
            Ok(method) => {
                result.locked_files.push(file_path.to_string_lossy().to_string());
                result.lock_methods.push(method);
            }
            Err(e) => {
                result.failed_files.push(format!("{}: {}", file_path.display(), e));
            }
        }
    }

    Ok(result)
}

/// 解锁多个文件
/// 
/// Args:
///     file_paths: 要解锁的文件路径列表
/// 
/// Returns:
///     Result<(), String>: 成功或错误信息
pub fn unlock_multiple_files(file_paths: &[&Path]) -> Result<(), String> {
    let mut errors = Vec::new();

    for file_path in file_paths {
        if let Err(e) = unlock_single_file(file_path) {
            errors.push(format!("{}: {}", file_path.display(), e));
        }
    }

    if errors.is_empty() {
        Ok(())
    } else {
        Err(format!("部分文件解锁失败: {}", errors.join("; ")))
    }
}

/// 检查文件是否被锁定（只读）
/// 
/// Args:
///     file_path: 要检查的文件路径
/// 
/// Returns:
///     bool: 如果文件被锁定则返回true
pub fn is_file_locked(file_path: &Path) -> bool {
    if let Ok(metadata) = std::fs::metadata(file_path) {
        metadata.permissions().readonly()
    } else {
        false
    }
}
