<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YAugment</title>
    <link rel="stylesheet" href="styles.css">
    <!-- 引入GSAP动画库 -->
    <script src="gsap.min.js"></script>
    <script src="ScrollTrigger.min.js"></script>
    <!-- 引入Tauri API -->
    <script>
        // Tauri API 将在运行时自动注入
        // 这里只是为了确保在开发时不会出错
        if (typeof window.__TAURI__ === 'undefined') {
            window.__TAURI__ = {
                invoke: function() { console.warn('Tauri not loaded'); return Promise.resolve(); },
                listen: function() { console.warn('Tauri not loaded'); return Promise.resolve(); }
            };
        }
    </script>
</head>
<body>
    <!-- 标题栏 -->
    <div class="titlebar" id="titlebar" data-tauri-drag-region>
        <div class="titlebar-content" data-tauri-drag-region>
            <div class="app-logo">
                <span class="logo-text">YAugment</span>
            </div>
            <div class="window-controls" style="pointer-events: auto;">
                <button class="control-btn settings" onclick="showSettings()" style="pointer-events: auto;">
                    <svg width="16" height="16" viewBox="0 0 24 24">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z" fill="currentColor"/>
                    </svg>
                </button>
                <button class="control-btn minimize" onclick="minimizeWindow()" style="pointer-events: auto;">
                    <svg width="12" height="2" viewBox="0 0 12 2">
                        <rect width="12" height="2" fill="currentColor"/>
                    </svg>
                </button>
                <button class="control-btn maximize" onclick="toggleMaximize()" style="pointer-events: auto;">
                    <svg width="12" height="12" viewBox="0 0 12 12">
                        <rect x="1" y="1" width="10" height="10" stroke="currentColor" stroke-width="1.5" fill="none"/>
                    </svg>
                </button>
                <button class="control-btn close" onclick="closeWindow()" style="pointer-events: auto;">
                    <svg width="12" height="12" viewBox="0 0 12 12">
                        <path d="M1 1L11 11M11 1L1 11" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 加载页面 -->
    <div class="page" id="loadingPage">
        <div class="loading-container">
            <div class="loading-spinner-large"></div>
            <h2 class="loading-title">YAugment</h2>
            <p class="loading-subtitle">正在初始化...</p>
        </div>
    </div>

    <!-- 编辑器选择页面 -->
    <div class="page hidden" id="editorSelectPage">
        <div class="editor-select-container">
            <div class="select-header">
                <h1 class="select-title">
                    <span class="title-gradient">选择编辑器</span>
                </h1>
                <p class="select-subtitle">请选择您使用Augment插件的编辑器以继续</p>
            </div>

            <div class="editor-grid">
                <div class="editor-option" id="vscodeCard" onclick="selectEditor('vscode')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/vscode.png" alt="VS Code" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon vscode-fallback">
                                <svg width="48" height="48" viewBox="0 0 100 100">
                                    <path d="M74.5 0L35 31.5L14 17L0 23.5V76.5L14 83L35 68.5L74.5 100L100 87.5V12.5L74.5 0Z" fill="#007ACC"/>
                                </svg>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">Visual Studio Code</h3>
                            <p class="editor-description">微软开发的免费代码编辑器</p>
                            <div class="install-status" id="vscodeStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>

                <div class="editor-option" id="cursorCard" onclick="selectEditor('cursor')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/Cursor.ico" alt="Cursor" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon cursor-fallback">
                                <div class="cursor-logo">C</div>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">Cursor</h3>
                            <p class="editor-description">AI驱动的代码编辑器</p>
                            <div class="install-status" id="cursorStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>

                <div class="editor-option" id="intellijCard" onclick="selectEditor('intellij')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/intellij.svg" alt="IntelliJ IDEA" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon intellij-fallback">
                                <div class="intellij-logo">IJ</div>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">IntelliJ IDEA</h3>
                            <p class="editor-description">Java/Kotlin 开发IDE</p>
                            <div class="install-status" id="intellijStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>

                <div class="editor-option" id="pycharmCard" onclick="selectEditor('pycharm')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/pycharm.svg" alt="PyCharm" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon pycharm-fallback">
                                <div class="pycharm-logo">PC</div>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">PyCharm</h3>
                            <p class="editor-description">Python 开发IDE</p>
                            <div class="install-status" id="pycharmStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>

                <div class="editor-option" id="webstormCard" onclick="selectEditor('webstorm')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/webstorm.svg" alt="WebStorm" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon webstorm-fallback">
                                <div class="webstorm-logo">WS</div>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">WebStorm</h3>
                            <p class="editor-description">Web 开发IDE</p>
                            <div class="install-status" id="webstormStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>

                <div class="editor-option" id="phpstormCard" onclick="selectEditor('phpstorm')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/phpstorm.svg" alt="PhpStorm" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon phpstorm-fallback">
                                <div class="phpstorm-logo">PS</div>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">PhpStorm</h3>
                            <p class="editor-description">PHP 开发IDE</p>
                            <div class="install-status" id="phpstormStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>

                <div class="editor-option" id="rubymineCard" onclick="selectEditor('rubymine')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/rubymine.svg" alt="RubyMine" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon rubymine-fallback">
                                <div class="rubymine-logo">RM</div>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">RubyMine</h3>
                            <p class="editor-description">Ruby 开发IDE</p>
                            <div class="install-status" id="rubymineStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>

                <div class="editor-option" id="clionCard" onclick="selectEditor('clion')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/clion.svg" alt="CLion" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon clion-fallback">
                                <div class="clion-logo">CL</div>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">CLion</h3>
                            <p class="editor-description">C/C++ 开发IDE</p>
                            <div class="install-status" id="clionStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>

                <div class="editor-option" id="golandCard" onclick="selectEditor('goland')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/goland.svg" alt="GoLand" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon goland-fallback">
                                <div class="goland-logo">GL</div>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">GoLand</h3>
                            <p class="editor-description">Go 开发IDE</p>
                            <div class="install-status" id="golandStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>

                <div class="editor-option" id="riderCard" onclick="selectEditor('rider')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/rider.svg" alt="Rider" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon rider-fallback">
                                <div class="rider-logo">RD</div>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">Rider</h3>
                            <p class="editor-description">.NET 开发IDE</p>
                            <div class="install-status" id="riderStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>

                <div class="editor-option" id="datagripCard" onclick="selectEditor('datagrip')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/datagrip.svg" alt="DataGrip" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon datagrip-fallback">
                                <div class="datagrip-logo">DG</div>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">DataGrip</h3>
                            <p class="editor-description">数据库管理IDE</p>
                            <div class="install-status" id="datagripStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>

                <div class="editor-option" id="androidstudioCard" onclick="selectEditor('androidstudio')">
                    <div class="option-background"></div>
                    <div class="option-content">
                        <div class="editor-icon-container">
                            <img src="editor-icons/androidstudio.svg" alt="Android Studio" class="editor-icon" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="fallback-icon androidstudio-fallback">
                                <div class="androidstudio-logo">AS</div>
                            </div>
                        </div>
                        <div class="editor-info">
                            <h3 class="editor-name">Android Studio</h3>
                            <p class="editor-description">Android 开发IDE</p>
                            <div class="install-status" id="androidstudioStatus">
                                <div class="status-indicator checking"></div>
                                <span class="status-text">检测中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="option-overlay"></div>
                </div>
            </div>

            <div class="select-footer">
                <p class="footer-note">选择后将自动进入主界面</p>
            </div>
        </div>
    </div>

    <!-- 主页面 -->
    <div class="page hidden" id="mainPage">
        <div class="main-container">
            <!-- Hero区域 -->
            <div class="hero-section">
                <div class="hero-background">
                    <div class="particle-field" id="particleField"></div>
                </div>
                <div class="hero-content">
                    <h1 class="hero-title">
                        <span class="glitch-text" data-text="YAugment">YAugment</span>
                    </h1>
                    <p class="hero-subtitle">   「 先生，我把自己活成了透明的雨伞，下雨时才被想起，天晴后又被忘在墙角 」</p>
                    <div class="selected-editor" id="selectedEditor"></div>
                </div>
            </div>

            <!-- 功能区域 -->
            <div class="features-section">
                <!-- 第一行：重置和邮箱工作流 -->
                <div class="features-row-1">
                    <!-- 重置Augment - 极简设计 -->
                    <div class="reset-container">
                    <div class="reset-background">
                        <div class="reset-particles"></div>
                    </div>
                    <div class="reset-content">
                        <div class="reset-icon-wrapper">
                            <div class="reset-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24">
                                    <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z" fill="currentColor"/>
                                </svg>
                            </div>
                            <div class="reset-status-ring">
                                <svg class="ring-progress" width="120" height="120">
                                    <circle cx="60" cy="60" r="50" stroke="rgba(139, 92, 246, 0.2)" stroke-width="4" fill="none"/>
                                    <circle cx="60" cy="60" r="50" stroke="var(--purple-primary)" stroke-width="4" fill="none"
                                            stroke-dasharray="314" stroke-dashoffset="314" class="progress-circle"/>
                                </svg>
                            </div>
                        </div>
                        <h2 class="reset-title">重置 Augment</h2>
                        <p class="reset-description">请先选择编辑器</p>
                        <button class="reset-action-btn" onclick="executeReset()" id="resetActionBtn">
                            <span class="btn-content">
                                <span class="btn-text">开始重置</span>
                                <span class="btn-loading hidden">
                                    <div class="loading-dots">
                                        <span></span><span></span><span></span>
                                    </div>
                                </span>
                            </span>
                        </button>

                    </div>
                </div>

                <!-- 邮箱工作流 - 现代流畅设计 -->
                <div class="email-workflow-container">
                    <!-- 主工作流卡片 -->
                    <div class="email-workflow-card">
                        <!-- 流程指示器 -->
                        <div class="workflow-progress">
                            <div class="progress-step active" data-step="1">
                                <div class="step-icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24">
                                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.58L19 8l-9 9z" fill="currentColor"/>
                                    </svg>
                                </div>
                                <span class="step-label">生成邮箱</span>
                            </div>
                            <div class="progress-line"></div>
                            <div class="progress-step" data-step="2">
                                <div class="step-icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24">
                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"/>
                                    </svg>
                                </div>
                                <span class="step-label">获取验证码</span>
                            </div>
                        </div>

                        <!-- 邮箱工作区 -->
                        <div class="email-workspace">
                            <div class="workspace-header">
                                <div class="header-icon">
                                    <svg width="28" height="28" viewBox="0 0 24 24">
                                        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" fill="currentColor"/>
                                    </svg>
                                </div>
                                <div class="header-content">
                                    <h2 class="workspace-title">邮箱工作流</h2>
                                    <p class="workspace-subtitle">一站式邮箱生成与验证码获取</p>
                                </div>
                                <div class="workspace-status" id="workspaceStatus">
                                    <div class="status-dot"></div>
                                    <span class="status-text">就绪</span>
                                </div>
                            </div>

                            <!-- 邮箱显示区域 -->
                            <div class="email-display-zone" id="emailDisplayZone">
                                <div class="email-placeholder-modern">
                                    <div class="placeholder-icon">
                                        <svg width="48" height="48" viewBox="0 0 24 24" opacity="0.3">
                                            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" fill="currentColor"/>
                                        </svg>
                                    </div>
                                    <div class="placeholder-text">
                                        <h3>开始您的邮箱工作流</h3>
                                        <p>点击下方按钮生成随机邮箱地址</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="workflow-actions">
                                <button class="workflow-btn primary" id="generateEmailBtn" onclick="generateEmailDirect()">
                                    <div class="btn-content">
                                        <div class="btn-icon">
                                            <svg width="20" height="20" viewBox="0 0 24 24">
                                                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="currentColor"/>
                                            </svg>
                                        </div>
                                        <span class="btn-text">生成邮箱</span>
                                    </div>
                                    <div class="btn-loading hidden">
                                        <div class="loading-spinner"></div>
                                    </div>
                                </button>

                                <button class="workflow-btn secondary disabled" id="getCodeBtn" onclick="getVerificationCode()" disabled>
                                    <div class="btn-content">
                                        <div class="btn-icon">
                                            <svg width="20" height="20" viewBox="0 0 24 24">
                                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"/>
                                            </svg>
                                        </div>
                                        <span class="btn-text">获取验证码</span>
                                    </div>
                                    <div class="btn-loading hidden">
                                        <div class="loading-spinner-small"></div>
                                        <span class="loading-text">获取中...</span>
                                    </div>
                                </button>
                            </div>

                            <!-- 验证码结果区域 -->
                            <div class="verification-result-zone hidden" id="verificationResultZone">
                                <div class="result-card">
                                    <div class="result-header">
                                        <div class="result-icon success">
                                            <svg width="24" height="24" viewBox="0 0 24 24">
                                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.58L19 8l-9 9z" fill="currentColor"/>
                                            </svg>
                                        </div>
                                        <div class="result-title">验证码获取成功</div>
                                        <button class="result-copy-btn" onclick="copyVerificationCode()">
                                            <svg width="18" height="18" viewBox="0 0 24 24">
                                                <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z" fill="currentColor"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="verification-code-display">
                                        <span class="verification-code" id="verificationCode">000000</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 浮动信息卡片 -->
                    <div class="email-floating-cards">
                        <div class="floating-card card-1">
                            <div class="card-content">
                                <span class="card-label">域名</span>
                                <span class="card-value" id="floatingDomain">加载中...</span>
                            </div>
                        </div>
                        <div class="floating-card card-2">
                            <div class="card-content">
                                <span class="card-label">模式</span>
                                <span class="card-value" id="floatingMode">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
                </div>

                <!-- Augment账号加固 - 紧凑设计 -->
                <div class="augment-account-container">
                    <div class="augment-account-card">
                        <!-- 标题区域 -->
                        <div class="account-header">
                            <div class="header-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24">
                                    <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H16V16H8V11H9.2V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.4,8.7 10.4,10V11H13.6V10C13.6,8.7 12.8,8.2 12,8.2Z" fill="currentColor"/>
                                </svg>
                            </div>
                            <div class="header-content">
                                <h2 class="account-title">Augment账号管理</h2>
                                <p class="account-subtitle">账号查询功能</p>
                            </div>
                            <div class="header-controls">
                                <!-- 账号选择下拉框 -->
                                <div class="account-select-container">
                                    <div class="custom-select" id="customAccountSelect">
                                        <div class="select-trigger" onclick="toggleAccountDropdown()">
                                            <span class="select-text">选择已保存账号</span>
                                            <svg class="select-arrow" width="16" height="16" viewBox="0 0 24 24">
                                                <path d="M7 10l5 5 5-5z" fill="currentColor"/>
                                            </svg>
                                        </div>
                                        <div class="select-dropdown" id="accountDropdown">
                                            <!-- 选项将通过JavaScript动态生成 -->
                                        </div>
                                    </div>
                                </div>
                                <!-- 添加账号按钮 -->
                                <button class="add-account-btn" id="addAccountBtn" onclick="addAccountToSaved()" title="添加当前Session Token账号到保存列表">
                                    <div class="btn-content">
                                        <svg width="16" height="16" viewBox="0 0 24 24">
                                            <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" fill="currentColor"/>
                                        </svg>
                                    </div>
                                    <div class="btn-loading hidden">
                                        <div class="loading-spinner-small"></div>
                                    </div>
                                </button>
                                <!-- Token输入按钮 -->
                                <button class="token-input-btn" id="tokenInputBtn" onclick="showTokenDialog()">
                                    <svg width="20" height="20" viewBox="0 0 24 24">
                                        <path d="M12.65 10C11.83 7.67 9.61 6 7 6c-3.31 0-6 2.69-6 6s2.69 6 6 6c2.61 0 4.83-1.67 5.65-4H17v4h4v-4h2v-4H12.65zM7 14c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z" fill="currentColor"/>
                                    </svg>
                                    <span>Token</span>
                                </button>
                            </div>
                        </div>

                        <!-- 主要内容区域 -->
                        <div class="account-main-content">
                            <!-- 左侧：积分可视化 (60%) -->
                            <div class="credits-visualization">
                                <div class="credits-circle-container">
                                    <div class="credits-circle" id="creditsCircle">
                                        <svg class="circle-progress" width="180" height="180" viewBox="0 0 180 180">
                                            <circle class="circle-bg" cx="90" cy="90" r="75" fill="none" stroke="rgba(139, 92, 246, 0.1)" stroke-width="8"/>
                                            <circle class="circle-progress-bar" id="circleProgressBar" cx="90" cy="90" r="75" fill="none" stroke="url(#creditsGradient)" stroke-width="8" stroke-linecap="round" transform="rotate(-90 90 90)"/>
                                            <defs>
                                                <linearGradient id="creditsGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                                    <stop offset="0%" style="stop-color:#8b5cf6"/>
                                                    <stop offset="100%" style="stop-color:#a78bfa"/>
                                                </linearGradient>
                                            </defs>
                                        </svg>
                                        <div class="circle-content">
                                            <div class="credits-percentage" id="creditsPercentage">0%</div>
                                            <div class="credits-label">额度使用率</div>
                                        </div>
                                    </div>
                                    <div class="credits-details">
                                        <div class="credits-info">
                                            <span class="credits-used" id="creditsUsed">0</span>
                                            <span class="credits-separator">/</span>
                                            <span class="credits-total" id="creditsTotal">0</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 右侧：账号信息与操作 (40%) -->
                            <div class="account-info-section">
                                <!-- 账号信息展示 -->
                                <div class="account-data-grid">
                                    <div class="data-item">
                                        <div class="data-label">邮箱</div>
                                        <div class="data-value" id="accountEmail">未获取</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-label">计划</div>
                                        <div class="data-value" id="accountPlan">未获取</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-label">有效性</div>
                                        <div class="data-value" id="accountStatus">未获取</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-label">到期时间</div>
                                        <div class="data-value" id="accountExpiry">未获取</div>
                                    </div>
                                    <div class="data-item">
                                        <div class="data-label">查询时间</div>
                                        <div class="data-value" id="queryTime">未查询</div>
                                    </div>
                                </div>

                                <!-- 操作按钮区域 -->
                                <div class="account-actions">
                                    <!-- 账号查询按钮 -->
                                    <div class="account-actions-row">
                                        <button class="account-btn query-btn" id="queryAccountBtn" onclick="queryAccount()" disabled>
                                            <div class="btn-content">
                                                <div class="btn-icon">
                                                    <svg width="18" height="18" viewBox="0 0 24 24">
                                                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" fill="currentColor"/>
                                                    </svg>
                                                </div>
                                                <span class="btn-text">账号查询</span>
                                            </div>
                                            <div class="btn-loading hidden">
                                                <div class="loading-spinner-small"></div>
                                            </div>
                                        </button>

                                        <!-- 切换社区计划按钮 -->
                                        <button class="account-btn switch-plan-btn" id="switchPlanBtn" onclick="switchToCommunityPlan()" disabled>
                                            <div class="btn-content">
                                                <div class="btn-icon">
                                                    <svg width="18" height="18" viewBox="0 0 24 24">
                                                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"/>
                                                    </svg>
                                                </div>
                                                <span class="btn-text">切换社区计划</span>
                                            </div>
                                            <div class="btn-loading hidden">
                                                <div class="loading-spinner-small"></div>
                                            </div>
                                        </button>


                                    </div>


                            </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Augment网络优化策略 -->
                <div class="network-optimizer-container">
                    <div class="network-optimizer-card">
                        <!-- 标题区域 -->
                        <div class="optimizer-header">
                            <div class="header-icon">
                                <svg width="32" height="32" viewBox="0 0 24 24">
                                    <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z" fill="currentColor"/>
                                </svg>
                            </div>
                            <div class="header-content">
                                <h2 class="optimizer-title">Augment 网络优化策略</h2>
                                <p class="optimizer-subtitle"></p>
                            </div>
                            <div class="header-controls">
                                <!-- 主开关 -->
                                <div class="main-switch-container">
                                    <label class="switch">
                                        <input type="checkbox" id="networkOptimizerEnabled">
                                        <span class="slider"></span>
                                    </label>
                                    <span class="switch-label">优化策略</span>
                                </div>
                            </div>
                        </div>

                        <!-- 内容区域 -->
                        <div class="optimizer-content">
                            <!-- 第一行：代理设置和数据展示 -->
                            <div class="optimizer-row">
                                <!-- 左侧：代理配置 (50%) -->
                                <div class="optimizer-config-section">
                                    <div class="config-group">
                                        <div class="config-label">代理设置</div>
                                        <div class="proxy-options">
                                            <label class="radio-option">
                                                <input type="radio" name="proxyType" value="v2rayN" checked>
                                                <span class="radio-custom"></span>
                                                <span class="radio-text">v2rayN 默认配置</span>
                                            </label>
                                            <label class="radio-option">
                                                <input type="radio" name="proxyType" value="clash-verge">
                                                <span class="radio-custom"></span>
                                                <span class="radio-text">Clash Verge 默认配置</span>
                                            </label>
                                            <label class="radio-option">
                                                <input type="radio" name="proxyType" value="tun">
                                                <span class="radio-custom"></span>
                                                <span class="radio-text">Tun 模式</span>
                                            </label>
                                            <label class="radio-option">
                                                <input type="radio" name="proxyType" value="custom">
                                                <span class="radio-custom"></span>
                                                <span class="radio-text">自定义代理地址</span>
                                            </label>
                                        </div>

                                        <!-- 自定义代理输入框 -->
                                        <div class="custom-proxy-input hidden" id="customProxyInput">
                                            <input type="text" id="customProxyUrl" placeholder="http://127.0.0.1:10808" class="proxy-input">
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧：数据展示 (50%) -->
                                <div class="optimizer-status-section">
                                    <div class="config-group">
                                        <div class="status-display">
                                            <div class="status-item">
                                                <div class="status-label">上次优化时间</div>
                                                <div class="status-value" id="lastOptimizeTime">未优化</div>
                                            </div>
                                            <div class="status-item">
                                                <div class="status-label">选择的API</div>
                                                <div class="status-value" id="selectedApi">-</div>
                                            </div>
                                            <div class="status-item">
                                                <div class="status-label">平均延迟</div>
                                                <div class="status-value" id="averageLatency">-</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 第二行：优化设置 -->
                            <div class="optimizer-row">
                                <div class="optimizer-settings-section">
                                    <div class="config-group">
                                        <div class="config-label">优化设置</div>
                                        <div class="optimizer-settings-row">
                                            <div class="setting-item">
                                                <label class="setting-checkbox">
                                                    <input type="checkbox" id="autoOptimizeEnabled" checked>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">定时优化</span>
                                                </label>
                                                <div class="interval-setting">
                                                    <span class="interval-prefix">每</span>
                                                    <input type="number" id="optimizeInterval" value="10" min="1" max="1440" class="interval-input">
                                                    <span class="interval-unit">分钟</span>
                                                </div>
                                            </div>
                                            <div class="setting-item">
                                                <label class="setting-checkbox">
                                                    <input type="checkbox" id="autoSetEditorProxy" checked>
                                                    <span class="checkbox-custom"></span>
                                                    <span class="checkbox-text">自动跟随设置编辑器的代理配置</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 关于区域 - 分散式现代设计 -->
            <div class="about-section">
                <!-- 标题区域 -->
                <div class="about-header">
                    <div class="about-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h2 class="about-title">关于</h2>
                </div>

                <!-- 主要信息 -->
                <div class="app-main-info">
                    <div class="app-name">YAugment v1.0.0</div>
                    <div class="app-developer">Developed by Yan</div>
                </div>

                <!-- 编程工作环境区域 -->
                <div class="coding-workspace">
                    <div class="workspace-container">
                        <!-- 主显示器 -->
                        <div class="main-monitor">
                            <svg width="120" height="80" viewBox="0 0 120 80" class="monitor-svg">
                                <rect x="5" y="5" width="110" height="65" rx="4" fill="rgba(0, 0, 0, 0.9)" stroke="rgba(139, 92, 246, 0.7)" stroke-width="2"/>
                                <rect x="8" y="8" width="104" height="59" fill="rgba(0, 0, 0, 0.95)"/>
                                <!-- 代码行 -->
                                <rect x="12" y="12" width="30" height="3" fill="rgba(139, 92, 246, 0.8)" class="code-line-1"/>
                                <rect x="12" y="18" width="25" height="3" fill="rgba(168, 139, 246, 0.6)" class="code-line-2"/>
                                <rect x="12" y="24" width="40" height="3" fill="rgba(139, 92, 246, 0.7)" class="code-line-3"/>
                                <rect x="12" y="30" width="35" height="3" fill="rgba(168, 139, 246, 0.5)" class="code-line-4"/>
                                <rect x="12" y="36" width="28" height="3" fill="rgba(139, 92, 246, 0.6)" class="code-line-5"/>
                                <rect x="12" y="42" width="45" height="3" fill="rgba(168, 139, 246, 0.7)" class="code-line-6"/>
                                <rect x="12" y="48" width="32" height="3" fill="rgba(139, 92, 246, 0.5)" class="code-line-7"/>
                                <rect x="12" y="54" width="38" height="3" fill="rgba(168, 139, 246, 0.6)" class="code-line-8"/>
                                <!-- 光标 -->
                                <rect x="50" y="54" width="2" height="3" fill="rgba(139, 92, 246, 1)" class="cursor-blink"/>
                                <!-- 支架 -->
                                <rect x="55" y="70" width="10" height="8" fill="rgba(139, 92, 246, 0.4)"/>
                                <rect x="45" y="78" width="30" height="2" rx="1" fill="rgba(139, 92, 246, 0.3)"/>
                            </svg>
                        </div>

                        <!-- 副显示器 -->
                        <div class="secondary-monitor">
                            <svg width="80" height="60" viewBox="0 0 80 60" class="monitor-svg">
                                <rect x="3" y="3" width="74" height="50" rx="3" fill="rgba(0, 0, 0, 0.8)" stroke="rgba(139, 92, 246, 0.6)" stroke-width="1.5"/>
                                <rect x="6" y="6" width="68" height="44" fill="rgba(0, 0, 0, 0.9)"/>
                                <!-- 终端界面 -->
                                <rect x="9" y="9" width="20" height="2" fill="rgba(34, 197, 94, 0.8)" class="terminal-line-1"/>
                                <rect x="9" y="13" width="15" height="2" fill="rgba(34, 197, 94, 0.6)" class="terminal-line-2"/>
                                <rect x="9" y="17" width="25" height="2" fill="rgba(34, 197, 94, 0.7)" class="terminal-line-3"/>
                                <rect x="9" y="21" width="18" height="2" fill="rgba(34, 197, 94, 0.5)" class="terminal-line-4"/>
                                <!-- 支架 -->
                                <rect x="37" y="53" width="6" height="5" fill="rgba(139, 92, 246, 0.4)"/>
                                <rect x="30" y="58" width="20" height="2" rx="1" fill="rgba(139, 92, 246, 0.3)"/>
                            </svg>
                        </div>

                        <!-- 键盘 -->
                        <div class="keyboard">
                            <svg width="100" height="30" viewBox="0 0 100 30" class="keyboard-svg">
                                <rect x="2" y="15" width="96" height="12" rx="2" fill="rgba(139, 92, 246, 0.2)" stroke="rgba(139, 92, 246, 0.4)" stroke-width="1"/>
                                <!-- 按键 -->
                                <rect x="6" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-1"/>
                                <rect x="12" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-2"/>
                                <rect x="18" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-3"/>
                                <rect x="24" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-4"/>
                                <rect x="30" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-5"/>
                                <rect x="36" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-6"/>
                                <rect x="42" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-7"/>
                                <rect x="48" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-8"/>
                                <rect x="54" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-9"/>
                                <rect x="60" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-10"/>
                                <rect x="66" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-11"/>
                                <rect x="72" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-12"/>
                                <rect x="78" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-13"/>
                                <rect x="84" y="18" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-14"/>
                                <rect x="90" y="18" width="6" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-15"/>
                                <!-- 第二行按键 -->
                                <rect x="8" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-16"/>
                                <rect x="14" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-17"/>
                                <rect x="20" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-18"/>
                                <rect x="26" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-19"/>
                                <rect x="32" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-20"/>
                                <rect x="38" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-21"/>
                                <rect x="44" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-22"/>
                                <rect x="50" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-23"/>
                                <rect x="56" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-24"/>
                                <rect x="62" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-25"/>
                                <rect x="68" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-26"/>
                                <rect x="74" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-27"/>
                                <rect x="80" y="22" width="4" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-28"/>
                                <rect x="86" y="22" width="10" height="3" rx="0.5" fill="rgba(139, 92, 246, 0.3)" class="key-29"/>
                                <!-- 空格键 -->
                                <rect x="25" y="26" width="50" height="2" rx="1" fill="rgba(139, 92, 246, 0.4)" class="spacebar"/>
                            </svg>
                        </div>

                        <!-- 鼠标 -->
                        <div class="mouse">
                            <svg width="25" height="35" viewBox="0 0 25 35" class="mouse-svg">
                                <ellipse cx="12.5" cy="20" rx="10" ry="12" fill="rgba(139, 92, 246, 0.3)" stroke="rgba(139, 92, 246, 0.5)" stroke-width="1"/>
                                <rect x="11" y="10" width="3" height="8" rx="1.5" fill="rgba(139, 92, 246, 0.4)"/>
                                <circle cx="12.5" cy="14" r="1" fill="rgba(139, 92, 246, 0.6)" class="mouse-light"/>
                            </svg>
                        </div>

                        <!-- 咖啡杯（放大版） -->
                        <div class="coffee-cup-large">
                            <svg width="40" height="50" viewBox="0 0 40 50" class="coffee-svg">
                                <rect x="6" y="16" width="24" height="30" rx="4" fill="rgba(139, 92, 246, 0.3)" stroke="rgba(139, 92, 246, 0.6)" stroke-width="2"/>
                                <rect x="8" y="18" width="20" height="16" fill="rgba(139, 92, 246, 0.6)"/>
                                <ellipse cx="18" cy="18" rx="8" ry="2" fill="rgba(168, 139, 246, 0.4)"/>
                                <rect x="30" y="24" width="6" height="12" rx="3" fill="none" stroke="rgba(139, 92, 246, 0.5)" stroke-width="2"/>
                                <!-- 蒸汽 -->
                                <path d="M 14 10 Q 14 6 16 8 Q 16 4 18 6" stroke="rgba(139, 92, 246, 0.4)" stroke-width="2" fill="none" class="steam-1"/>
                                <path d="M 22 12 Q 22 8 24 10 Q 24 6 26 8" stroke="rgba(139, 92, 246, 0.4)" stroke-width="2" fill="none" class="steam-2"/>
                                <path d="M 18 8 Q 18 4 20 6 Q 20 2 22 4" stroke="rgba(139, 92, 246, 0.4)" stroke-width="2" fill="none" class="steam-3"/>
                            </svg>
                        </div>

                        <!-- 书籍堆（放大版） -->
                        <div class="books-stack">
                            <svg width="60" height="45" viewBox="0 0 60 45" class="books-svg">
                                <rect x="5" y="30" width="20" height="4" fill="rgba(139, 92, 246, 0.4)"/>
                                <rect x="6" y="26" width="20" height="4" fill="rgba(168, 139, 246, 0.5)"/>
                                <rect x="4" y="34" width="20" height="4" fill="rgba(139, 92, 246, 0.3)"/>
                                <rect x="7" y="22" width="20" height="4" fill="rgba(168, 139, 246, 0.6)"/>
                                <rect x="3" y="38" width="20" height="4" fill="rgba(139, 92, 246, 0.5)"/>

                                <rect x="35" y="28" width="15" height="6" fill="rgba(139, 92, 246, 0.4)"/>
                                <rect x="36" y="22" width="15" height="6" fill="rgba(168, 139, 246, 0.5)"/>
                                <rect x="34" y="34" width="15" height="6" fill="rgba(139, 92, 246, 0.3)"/>
                                <rect x="37" y="16" width="15" height="6" fill="rgba(168, 139, 246, 0.6)"/>

                                <!-- 书签 -->
                                <rect x="12" y="18" width="2" height="8" fill="rgba(139, 92, 246, 0.7)"/>
                                <rect x="43" y="12" width="2" height="8" fill="rgba(168, 139, 246, 0.7)"/>
                            </svg>
                        </div>

                        <!-- 植物 -->
                        <div class="plant">
                            <svg width="35" height="50" viewBox="0 0 35 50" class="plant-svg">
                                <!-- 花盆 -->
                                <path d="M 10 35 L 25 35 L 23 45 L 12 45 Z" fill="rgba(139, 92, 246, 0.3)" stroke="rgba(139, 92, 246, 0.5)" stroke-width="1"/>
                                <!-- 土壤 -->
                                <ellipse cx="17.5" cy="35" rx="7.5" ry="2" fill="rgba(139, 92, 246, 0.4)"/>
                                <!-- 茎 -->
                                <rect x="16.5" y="20" width="2" height="15" fill="rgba(34, 197, 94, 0.6)"/>
                                <!-- 叶子 -->
                                <ellipse cx="12" cy="25" rx="4" ry="6" fill="rgba(34, 197, 94, 0.7)" class="leaf-1"/>
                                <ellipse cx="23" cy="22" rx="3" ry="5" fill="rgba(34, 197, 94, 0.6)" class="leaf-2"/>
                                <ellipse cx="14" cy="15" rx="3" ry="4" fill="rgba(34, 197, 94, 0.8)" class="leaf-3"/>
                                <ellipse cx="21" cy="18" rx="2.5" ry="3.5" fill="rgba(34, 197, 94, 0.7)" class="leaf-4"/>
                            </svg>
                        </div>

                        <!-- 增强的飘动代码 -->
                        <div class="floating-code-enhanced">
                            <div class="code-particle large" data-delay="0">function</div>
                            <div class="code-particle medium" data-delay="0.8">const</div>
                            <div class="code-particle small" data-delay="1.6">{}</div>
                            <div class="code-particle large" data-delay="2.4">async</div>
                            <div class="code-particle medium" data-delay="3.2">return</div>
                            <div class="code-particle small" data-delay="4">;</div>
                            <div class="code-particle large" data-delay="4.8">import</div>
                            <div class="code-particle medium" data-delay="5.6">class</div>
                            <div class="code-particle small" data-delay="6.4">=></div>
                            <div class="code-particle large" data-delay="7.2">export</div>
                            <div class="code-particle medium" data-delay="8">if</div>
                            <div class="code-particle small" data-delay="8.8">[]</div>
                        </div>
                    </div>
                </div>

                <!-- 详细信息网格 -->
                <div class="info-grid">
                    <div class="info-item copyright-item">
                        <div class="info-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div class="info-content">
                            <div class="info-label">版权信息</div>
                            <div class="info-value">2025 Yan - All rights reserved</div>
                        </div>
                    </div>

                    <div class="info-item email-item">
                        <div class="info-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                            </svg>
                        </div>
                        <div class="info-content">
                            <div class="info-label">联系邮箱</div>
                            <div class="info-value"><EMAIL></div>
                        </div>
                    </div>

                    <div class="info-item qq-item">
                        <div class="info-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-4h2v4h2v2H4c-1.1 0-2-.9-2-2zm16-5v3c0 1.1-.9 2-2 2h-4v-2h4v-3h2zM12 13c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4z"/>
                            </svg>
                        </div>
                        <div class="info-content">
                            <div class="info-label">QQ群</div>
                            <div class="info-value">1051756343 <span class="group-note">(老年不交流 Five)</span></div>
                        </div>
                    </div>
                </div>

                <!-- 感谢信息 -->
                <div class="thanks-message">
                    <div class="thanks-text">感谢您的使用和支持</div>
                    <div class="thanks-heart">❤️</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast提示 -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Token输入对话框 -->
    <div class="augment-token-dialog-overlay" id="tokenDialogOverlay">
        <div class="augment-token-dialog">
            <div class="augment-token-dialog-header">
                <div class="augment-token-dialog-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <path d="M12.65 10C11.83 7.67 9.61 6 7 6c-3.31 0-6 2.69-6 6s2.69 6 6 6c2.61 0 4.83-1.67 5.65-4H17v4h4v-4h2v-4H12.65zM7 14c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z" fill="currentColor"/>
                    </svg>
                </div>
                <h3 class="augment-token-dialog-title">设置Session Token</h3>
                <button class="augment-token-dialog-help" onclick="openTokenHelp()">
                    <svg width="16" height="16" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" fill="currentColor"/>
                    </svg>
                    <span>如何获取token</span>
                </button>
            </div>
            <div class="augment-token-dialog-content">
                <div class="augment-token-input-group">
                    <textarea class="augment-token-input" id="tokenInput" placeholder="请输入您的Augment session token..." rows="4"></textarea>
                    <div class="augment-token-hint">
                        <svg width="16" height="16" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" fill="currentColor"/>
                        </svg>
                        <span>Session Token将仅保存在内存中，应用关闭后自动清理</span>
                    </div>
                </div>
                <div class="augment-token-dialog-actions">
                    <button class="augment-token-btn-cancel" onclick="hideTokenDialog()">取消</button>
                    <button class="augment-token-btn-save" onclick="saveToken()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 网络优化提醒对话框 -->
    <div class="augment-token-dialog-overlay" id="networkOptimizerWarningOverlay">
        <div class="augment-token-dialog">
            <div class="augment-token-dialog-header">
                <div class="augment-token-dialog-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <path d="M12,2L1,21H23M12,6L19.53,19H4.47M11,10V14H13V10M11,16V18H13V16" fill="currentColor"/>
                    </svg>
                </div>
                <h3 class="augment-token-dialog-title">网络优化策略提醒</h3>
            </div>
            <div class="augment-token-dialog-content">
                <div class="network-warning-content">
                    <div class="network-warning-main">
                        <p><strong>开启 Augment 网络优化策略时，必须要开启或设置您选择的代理，否则会出现 JSON 报错</strong></p>
                        <p><strong>注意：Augment 只支持 HTTP/HTTPS 代理</strong></p>
                    </div>
                    <div class="network-warning-details">
                        <ul>
                            <li>请确保您的代理软件已正确配置并运行</li>
                            <li>开启了 自动跟随设置编辑器的代理配置 功能时，启动时会自动配置对应编辑器的代理设置（Tun模式除外）</li>
                        </ul>
                    </div>
                </div>
                <div class="augment-token-dialog-actions">
                    <button class="augment-token-btn-cancel" onclick="hideNetworkOptimizerWarning()">取消</button>
                    <button class="augment-token-btn-save" onclick="confirmNetworkOptimization()">我已了解，继续开启</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 切换社区计划确认对话框 -->
    <div class="augment-token-dialog-overlay" id="switchPlanDialogOverlay">
        <div class="augment-token-dialog">
            <div class="augment-token-dialog-header">
                <div class="augment-token-dialog-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <path d="M12,2L1,21H23M12,6L19.53,19H4.47M11,10V14H13V10M11,16V18H13V16" fill="currentColor"/>
                    </svg>
                </div>
                <h3 class="augment-token-dialog-title">切换社区计划确认</h3>
            </div>
            <div class="augment-token-dialog-content">
                <div class="switch-plan-warning-content">
                    <div class="switch-plan-warning-main">
                        <p><strong>⚠️ 重要提醒</strong></p>
                        <p>切换为社区计划虽然说很稳，但是会<strong>上传项目代码进行内部训练</strong></p>
                        <p><strong>介意请勿切换</strong>，除非您完全了解并接受此风险</p>
                    </div>
                    <div class="switch-plan-warning-details">
                        <ul>
                            <li>社区计划会将您的代码用于模型训练</li>
                            <li>这可能涉及您的代码隐私和知识产权</li>
                            <li>切换后无法恢复到原计划</li>
                        </ul>
                    </div>
                </div>
                <div class="augment-token-dialog-actions">
                    <button class="augment-token-btn-cancel" onclick="hideSwitchPlanDialog()">取消</button>
                    <button class="augment-token-btn-save" onclick="confirmSwitchToCommunityPlan()">我已了解风险，确认切换</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 对话框容器 -->
    <div class="dialog-overlay" id="dialogOverlay" onclick="closeDialog(event)">
        <div class="dialog" id="dialog" onclick="event.stopPropagation()">
            <div class="dialog-header">
                <h3 class="dialog-title" id="dialogTitle">对话框标题</h3>
                <div class="dialog-header-actions">
                    <button class="dialog-help" id="dialogHelp" onclick="openConfigHelp()" style="display: none;">配置说明</button>
                </div>
            </div>
            <div class="dialog-content" id="dialogContent">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <script src="app_fixed.js?v=20241202_fixed"></script>
    <script src="animations.js"></script>
</body>
</html> 