/// VSCode SQLite 修改工具
/// 
/// 对应原版 Python 的 augment_tools/vscode/augutils/sqlite_modifier.py
/// 清理 VSCode SQLite 数据库中的 augment 相关数据

use std::path::Path;
use rusqlite::Connection;
use crate::utils::backup::create_backup;
use super::paths::get_db_path;

/// 清理结果结构体
/// 对应原版 Python 函数的返回字典
#[derive(Debug, Clone, serde::Serialize)]
pub struct CleanResult {
    pub db_backup_path: String,
    pub deleted_rows: usize,
}

/// 清理指定数据库文件中的 augment 相关数据
/// 对应原版项目的 clean_vscode_database 函数
///
/// 此函数：
/// 1. 检查数据库文件是否存在
/// 2. 创建数据库文件的备份
/// 3. 打开数据库连接
/// 4. 统计要删除的记录数
/// 5. 删除 key 包含 'augment' 的记录
/// 6. 如果不是备份文件，递归处理备份文件
///
/// Args:
///     db_path: 数据库文件路径
///     file_name: 文件名（用于判断是否为备份文件）
///
/// Returns:
///     Result<CleanResult, String>: 包含操作结果的结构体或错误信息
pub fn clean_augment_data_from_file(db_path: &Path, file_name: &str) -> Result<CleanResult, String> {
    // 检查数据库文件是否存在
    if !db_path.exists() {
        return Ok(CleanResult {
            db_backup_path: String::new(),
            deleted_rows: 0,
        });
    }

    // 在修改前创建备份
    let db_backup_path = create_backup(db_path)
        .map_err(|e| format!("Failed to create database backup: {}", e))?;

    // 连接到数据库
    let conn = Connection::open(db_path)
        .map_err(|e| format!("Failed to open database: {}", e))?;

    // 统计要删除的记录数
    let count_query = "SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'";
    let rows_to_delete: i64 = conn.prepare(count_query)
        .map_err(|e| format!("Failed to prepare count query: {}", e))?
        .query_row([], |row| row.get(0))
        .map_err(|e| format!("Failed to execute count query: {}", e))?;

    let mut total_deleted = 0;

    if rows_to_delete > 0 {
        println!("在 '{}' 中发现 {} 个待删除的条目", file_name, rows_to_delete);

        // 执行删除查询
        let delete_query = "DELETE FROM ItemTable WHERE key LIKE '%augment%'";
        let deleted_rows = conn.execute(delete_query, [])
            .map_err(|e| format!("Failed to execute delete query: {}", e))?;

        total_deleted = deleted_rows;
        println!("成功从 '{}' 中删除了 {} 个条目", file_name, deleted_rows);
    }

    // 如果不是备份文件，递归处理备份文件（对应原始项目逻辑）
    if !file_name.ends_with(".backup") {
        let backup_file_name = format!("{}.backup", file_name);
        let backup_db_path = db_path.parent()
            .unwrap_or_else(|| Path::new("."))
            .join(&backup_file_name);

        if let Ok(backup_result) = clean_augment_data_from_file(&backup_db_path, &backup_file_name) {
            total_deleted += backup_result.deleted_rows;
        }
    }

    Ok(CleanResult {
        db_backup_path: db_backup_path.to_string_lossy().to_string(),
        deleted_rows: total_deleted,
    })
}

/// 清理 VSCode SQLite 数据库中的 augment 相关数据
/// 对应原版 Python 的 clean_augment_data 函数
///
/// 此函数使用默认的数据库路径进行清理
///
/// Returns:
///     Result<CleanResult, String>: 包含操作结果的结构体或错误信息
pub fn clean_augment_data() -> Result<CleanResult, String> {
    let db_path = get_db_path();
    let db_path_obj = Path::new(&db_path);

    // 使用新的函数处理数据库清理（包括备份文件）
    clean_augment_data_from_file(db_path_obj, "state.vscdb")
}

/// 计算数据库中包含 'augment' 的记录数量
/// 用于验证清理操作的效果
/// 
/// Returns:
///     Result<usize, String>: 记录数量或错误信息
pub fn count_augment_records() -> Result<usize, String> {
    let db_path = get_db_path();

    // 检查数据库文件是否存在
    if !std::path::Path::new(&db_path).exists() {
        return Ok(0); // 如果数据库不存在，返回 0
    }

    // 连接到数据库
    let conn = Connection::open(&db_path)
        .map_err(|e| format!("Failed to open database: {}", e))?;

    // 查询记录数量
    let mut stmt = conn.prepare("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        .map_err(|e| format!("Failed to prepare query: {}", e))?;

    let count: usize = stmt.query_row([], |row| {
        Ok(row.get::<_, i64>(0)? as usize)
    }).map_err(|e| format!("Failed to execute count query: {}", e))?;

    Ok(count)
}
