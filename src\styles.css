/* 全局样式 */
:root {
    --bg-primary: #000000;
    --bg-secondary: #0a0a0a;
    --bg-card: #111111;
    --bg-hover: #1a1a1a;

    --purple-primary: #8b5cf6;
    --purple-light: #a78bfa;
    --purple-dark: #6d28d9;
    --purple-glow: rgba(139, 92, 246, 0.5);

    --green-primary: #22c55e;
    --green-light: #4ade80;
    --green-dark: #16a34a;

    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --text-muted: #666666;

    --border-color: rgba(139, 92, 246, 0.2);
    --shadow-color: rgba(139, 92, 246, 0.3);

    --radius-sm: 16px;
    --radius-md: 20px;
    --radius-lg: 28px;
    --radius-xl: 36px;

    --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    /* 原版Python Theme类颜色定义 - 用于版本检查对话框 */
    --theme-primary: #121317;
    --theme-secondary: #1A1D23;
    --theme-accent: #2B9D7C;
    --theme-accent-hover: #34B892;
    --theme-accent-pressed: #24856A;
    --theme-text-primary: #FFFFFF;
    --theme-text-secondary: #9CA2AE;
    --theme-success: #2B9D7C;
    --theme-warning: #CBAF67;
    --theme-error: #BC4A59;
    --theme-card-bg: #1E2128;
    --theme-border: #2A2E36;
    --theme-hover: #252830;
    --theme-card-level-1: #1A1D23;
    --theme-card-level-2: #21252D;
    --theme-glass-bg: rgba(30, 33, 40, 0.75);
    --theme-glass-border: rgba(60, 63, 70, 0.35);
    --theme-font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
    --theme-font-size-small: 12px;
    --theme-font-size-normal: 14px;
    --theme-font-size-title: 18px;
    --theme-border-radius: 14px;
    --theme-border-radius-small: 10px;
    --theme-spacing-small: 8px;
    --theme-spacing-medium: 16px;
    --theme-spacing-large: 24px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    border-radius: var(--radius-lg);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-dark));
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--purple-light), var(--purple-primary));
}

/* 隐藏滚动条 */
::-webkit-scrollbar {
    width: 0;
    height: 0;
}

/* 标题栏 - 融入应用界面 */
.titlebar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.98) 0%, rgba(0, 0, 0, 0.95) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    z-index: 1000;
    border-bottom: none;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    cursor: default;
    user-select: none;
}

.titlebar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 20px;
}

.app-logo {
    display: flex;
    align-items: center;
}

.logo-text {
    font-size: 18px;
    font-weight: 700;
    letter-spacing: 1px;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 20px var(--purple-glow);
}

.window-controls {
    display: flex;
    gap: 8px;
    cursor: default;
    pointer-events: auto;
}

.control-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(255, 255, 255, 0.08);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: background 0.4s cubic-bezier(0.4, 0, 0.2, 1), transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.control-btn::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(0);
}

.control-btn:hover::before {
    opacity: 1;
    transform: scale(1);
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    transform: scale(1.01) translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.control-btn:active {
    transform: scale(0.95) translateY(0px);
    transition: all 0.1s ease;
}

.control-btn svg {
    transition: filter 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 0 0px transparent);
}

.control-btn:hover svg {
    filter: drop-shadow(0 0 8px currentColor);
}

/* 设置按钮特殊动画 */
.control-btn.settings:hover {
    background: rgba(139, 92, 246, 0.8);
    transform: scale(1.01) translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.control-btn.settings:hover svg {
    transform: rotate(180deg);
    filter: drop-shadow(0 0 12px rgba(139, 92, 246, 0.8));
}

/* 最小化按钮特殊动画 */
.control-btn.minimize:hover {
    background: rgba(255, 204, 0, 0.8);
    transform: scale(1.01) translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 204, 0, 0.4);
}

.control-btn.minimize:hover svg {
    transform: scaleX(0.7);
    filter: drop-shadow(0 0 12px rgba(255, 204, 0, 0.8));
}

/* 全屏按钮特殊动画 */
.control-btn.maximize:hover {
    background: rgba(40, 205, 65, 0.8);
    transform: scale(1.01) translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 205, 65, 0.4);
}

.control-btn.maximize:hover svg {
    transform: scale(1.2);
    filter: drop-shadow(0 0 12px rgba(40, 205, 65, 0.8));
}

/* 关闭按钮特殊动画 */
.control-btn.close:hover {
    background: rgba(239, 68, 68, 0.9);
    transform: scale(1.01) translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.control-btn.close:hover svg {
    transform: rotate(90deg) scale(1.1);
    filter: drop-shadow(0 0 12px rgba(239, 68, 68, 0.8));
}

/* 页面容器 */
.page {
    min-height: 100vh;
    padding-top: 60px;
    position: relative;
    opacity: 1;
    transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1), transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
}

/* 页面淡入动画 */
.page.fade-in {
    animation: pageFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes pageFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px) scale(0.98);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.page.hidden,
.hidden {
    display: none !important;
}

/* 加载页面 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 60px);
    text-align: center;
    opacity: 1;
    transition: opacity 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 加载页面淡出动画 */
.loading-container.fade-out {
    opacity: 0;
}

.loading-spinner-large {
    width: 60px;
    height: 60px;
    border: 3px solid rgba(139, 92, 246, 0.1);
    border-top: 3px solid var(--purple-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 32px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-title {
    font-size: 48px;
    font-weight: 800;
    margin-bottom: 16px;
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 30%, #c7d2fe 60%, #a78bfa 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -1px;
}

.loading-subtitle {
    font-size: 16px;
    color: var(--text-secondary);
    opacity: 0.8;
    margin: 0;
}

/* 编辑器选择页面 */
.editor-select-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 60px);
    padding: 40px;
    background:
        radial-gradient(ellipse 800px 600px at 50% 20%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
        radial-gradient(ellipse 600px 400px at 80% 80%, rgba(168, 85, 247, 0.06) 0%, transparent 50%);
    opacity: 0;
    animation: fadeInSlide 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 入场动画 */
@keyframes fadeInSlide {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
        filter: blur(10px);
    }
    60% {
        opacity: 0.8;
        transform: translateY(5px) scale(0.98);
        filter: blur(2px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

/* 出场动画 */
.editor-select-container.fade-out {
    animation: fadeOutSlide 0.4s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
}

@keyframes fadeOutSlide {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px) scale(0.98);
        filter: blur(5px);
    }
}

.select-header {
    text-align: center;
    margin-bottom: 60px;
    opacity: 0;
    animation: headerFadeIn 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s forwards;
}

@keyframes headerFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.select-title {
    font-size: 48px;
    font-weight: 800;
    margin-bottom: 16px;
    letter-spacing: -1px;
    position: relative;
}

.title-gradient {
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 30%, #c7d2fe 60%, #a78bfa 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 40px rgba(139, 92, 246, 0.3);
    animation: titleGlow 4s ease-in-out infinite, gradientShift 6s ease-in-out infinite;
}

/* 标题循环动画 */
@keyframes titleGlow {
    0%, 100% {
        text-shadow:
            0 0 20px rgba(139, 92, 246, 0.3),
            0 0 40px rgba(139, 92, 246, 0.2),
            0 0 60px rgba(139, 92, 246, 0.1);
        transform: scale(1);
    }
    50% {
        text-shadow:
            0 0 30px rgba(139, 92, 246, 0.5),
            0 0 60px rgba(139, 92, 246, 0.3),
            0 0 90px rgba(139, 92, 246, 0.2);
        transform: scale(1.02);
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.select-subtitle {
    font-size: 18px;
    color: var(--text-secondary);
    font-weight: 400;
    letter-spacing: 0.3px;
    opacity: 0.9;
}

.editor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 32px;
    max-width: 800px;
    width: 100%;
    opacity: 0;
    animation: gridFadeIn 1.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.6s forwards;
}

@keyframes gridFadeIn {
    0% {
        opacity: 0;
        transform: translateY(40px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.editor-option {
    position: relative;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.02), rgba(139, 92, 246, 0.01));
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 24px;
    padding: 32px;
    cursor: pointer;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    overflow: visible; /* 改为 visible 防止图标被裁剪 */
    min-height: 200px;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    animation: optionSlideIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 编辑器选项入场动画 */
.editor-option:nth-child(1) {
    animation-delay: 0.9s;
}

.editor-option:nth-child(2) {
    animation-delay: 1.1s;
}

@keyframes optionSlideIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.option-background {
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.03) 0%, rgba(168, 85, 247, 0.02) 100%);
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 24px;
}

.editor-option:hover .option-background {
    opacity: 0; /* 完全隐藏背景，保持卡片内部干净 */
}

.option-overlay {
    position: absolute;
    inset: -2px;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light));
    border-radius: 26px;
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    filter: blur(20px);
    z-index: -1;
}

.editor-option:hover .option-overlay {
    opacity: 0.02; /* 进一步降低紫色覆盖层透明度 */
    filter: blur(15px); /* 保持模糊度 */
}

.editor-option:hover {
    transform: translateY(-8px); /* 恢复原版上移距离 */
    border-color: rgba(139, 92, 246, 0.4); /* 调整为更淡的边框颜色，接近原版 */
    box-shadow:
        0 0 0 1px rgba(139, 92, 246, 0.3), /* 更淡的边框光晕 */
        0 20px 60px rgba(139, 92, 246, 0.15), /* 减淡外部阴影 */
        0 10px 30px rgba(139, 92, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1); /* 减淡内阴影 */
    /* 移除背景渐变，保持卡片内部干净 */
}

/* 点击动画 */
.editor-option:active {
    transform: translateY(-4px); /* 去掉点击时的放大效果 */
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 选择动画效果 */
.editor-option.selecting {
    transform: scale(0.95) !important;
    opacity: 0.7 !important;
    transition: all 0.15s ease !important;
}

/* 成功选择反馈 */
.editor-option.selection-success {
    border-color: rgba(34, 197, 94, 0.5) !important;
    box-shadow: 0 0 30px rgba(34, 197, 94, 0.3) !important;
}

/* 失败选择反馈 */
.editor-option.selection-error {
    border-color: rgba(239, 68, 68, 0.5) !important;
    box-shadow: 0 0 30px rgba(239, 68, 68, 0.3) !important;
}

.option-content {
    position: relative;
    display: flex;
    align-items: center;
    gap: 24px;
    height: 100%;
    z-index: 1;
}

.editor-icon-container {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(139, 92, 246, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: visible; /* 改为 visible 防止图标被裁剪 */
}

.editor-icon-container::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(168, 85, 247, 0.05)); /* 降低紫色强度 */
    opacity: 0;
    transition: opacity 0.5s ease;
    border-radius: 20px;
}

.editor-option:hover .editor-icon-container {
    transform: rotate(2deg) scale(1.02); /* 减少旋转角度，添加轻微放大 */
    border-color: rgba(139, 92, 246, 0.6); /* 增强边框颜色 */
    /* 移除图标光晕效果 - 保持图标清晰 */
}

.editor-option:hover .editor-icon-container::before {
    opacity: 0.8; /* 降低透明度 */
}

.editor-icon {
    width: 42px !important;
    height: 42px !important;
    object-fit: contain;
    display: block;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    /* 移除默认阴影效果 */
}

.editor-option:hover .editor-icon {
    transform: rotate(-2deg) scale(1.15); /* 增加放大效果，从1.08提升到1.15 */
    /* 移除发光效果，保持图标清晰 */
}

/* Cursor图标的样式  */
.editor-icon[src="editor-icons/Cursor.ico"] {
    width: 56px !important;
    height: 56px !important;
    margin-top: 0px; /* 移除向上偏移，保持与其他图标对齐 */
}

.editor-option:hover .editor-icon[src="editor-icons/Cursor.ico"] {
    transform: rotate(-2deg) scale(1.15); /* 与其他图标保持一致的悬浮效果，移除额外的偏移 */
}

/* 设置对话框中的编辑器图标样式 */
.radio-setting-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.setting-editor-icon {
    width: 28px;
    height: 28px;
    object-fit: contain;
    border-radius: 4px;
}

/* 特殊处理Cursor图标，让它更大一些 */
.setting-editor-icon[src="editor-icons/Cursor.ico"] {
    width: 36px;
    height: 36px;
}

/* 调整radio-setting-item的布局以支持图标 */
.radio-setting-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: rgba(255, 255, 255, 0.02);
    /* 入场动画初始状态 */
    opacity: 0;
    transform: translateY(20px);
    animation: slideInUp 0.4s ease-out forwards;
}

.radio-setting-item:hover {
    background: rgba(139, 92, 246, 0.1);
    border-color: rgba(139, 92, 246, 0.3);
}

.radio-setting-item.checked {
    background: rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.5);
}

.radio-setting-item input[type="radio"] {
    margin-left: auto; /* 将单选按钮推到最右边 */
    flex-shrink: 0;
    order: 3; /* 确保单选按钮在最后 */
}

/* 图标在最左边 */
.radio-setting-icon {
    order: 1;
}

/* 文字内容在中间 */
.radio-setting-item > div:not(.radio-setting-icon) {
    order: 2;
    flex: 1; /* 占据剩余空间 */
}

/* 编辑器选择项入场动画 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 为每个编辑器项目设置不同的延迟 */
.radio-setting-item:nth-child(1) {
    animation-delay: 0.1s;
}

.radio-setting-item:nth-child(2) {
    animation-delay: 0.2s;
}

.radio-setting-item:nth-child(3) {
    animation-delay: 0.3s;
}

.radio-setting-item:nth-child(4) {
    animation-delay: 0.4s;
}

.radio-setting-item:nth-child(5) {
    animation-delay: 0.5s;
}

.radio-setting-item:nth-child(6) {
    animation-delay: 0.6s;
}

.fallback-icon {
    display: none;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
}

.vscode-fallback svg {
    width: 42px;
    height: 42px;
}

.cursor-fallback .cursor-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* IntelliJ IDEA 样式 */
.intellij-fallback .intellij-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* PyCharm 样式 */
.pycharm-fallback .pycharm-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #21d789, #07c3f2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* WebStorm 样式 */
.webstorm-fallback .webstorm-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #07c3f2, #0077ff);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* PhpStorm 样式 */
.phpstorm-fallback .phpstorm-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #b345f1, #9333ea);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* RubyMine 样式 */
.rubymine-fallback .rubymine-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* CLion 样式 */
.clion-fallback .clion-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #22d3ee, #06b6d4);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* GoLand 样式 */
.goland-fallback .goland-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Rider 样式 */
.rider-fallback .rider-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* DataGrip 样式 */
.datagrip-fallback .datagrip-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Android Studio 样式 */
.androidstudio-fallback .androidstudio-logo {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #a3e635, #65a30d);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 900;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.editor-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.editor-option:hover .editor-info {
    transform: translateX(4px);
}

.editor-name {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.3px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.editor-option:hover .editor-name {
    color: var(--purple-light);
    text-shadow: 0 0 12px rgba(139, 92, 246, 0.2); /* 减淡文字发光效果 */
}

.editor-description {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
    opacity: 0.8;
    line-height: 1.4;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.editor-option:hover .editor-description {
    opacity: 1;
    color: var(--text-primary);
}

.install-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 4px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.editor-option:hover .install-status {
    transform: translateX(2px);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--purple-primary);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 0 8px currentColor;
}

.editor-option:hover .status-indicator {
    width: 10px;
    height: 10px;
    box-shadow: 0 0 16px currentColor;
}

.status-indicator.checking {
    animation: pulseGlow 2s infinite;
}

.status-indicator.installed {
    background: #22c55e;
}

.status-indicator.not-installed {
    background: #ef4444;
}

@keyframes pulseGlow {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 0 8px currentColor;
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
        box-shadow: 0 0 16px currentColor;
    }
}

.status-text {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.editor-option:hover .status-text {
    font-weight: 600;
    text-shadow: 0 0 4px currentColor; /* 减淡状态文字发光效果 */
}

.status-text.installed {
    color: #22c55e;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); /* 添加颜色过渡动画 */
}

.status-text.not-installed {
    color: #ef4444;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); /* 添加颜色过渡动画 */
}

/* 修复悬浮时已安装状态文字颜色同步问题 */
.editor-option:hover .status-text.installed {
    color: #16a34a !important; /* 悬浮时使用稍深的绿色，创建颜色变化效果 */
    text-shadow: 0 0 4px #22c55e; /* 使用具体颜色而不是currentColor */
}

.editor-option:hover .status-text.not-installed {
    color: #dc2626 !important; /* 悬浮时使用稍深的红色，创建颜色变化效果 */
    text-shadow: 0 0 4px #ef4444; /* 使用具体颜色而不是currentColor */
}

.select-footer {
    margin-top: 40px;
    text-align: center;
    opacity: 0;
    animation: footerFadeIn 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) 1.5s forwards;
}

@keyframes footerFadeIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.footer-note {
    font-size: 14px;
    color: var(--text-secondary);
    opacity: 0.7;
    margin: 0;
    transition: all 0.3s ease;
}

.footer-note:hover {
    opacity: 1;
    color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .editor-select-container {
        padding: 20px;
    }

    .select-title {
        font-size: 36px;
    }

    .select-subtitle {
        font-size: 16px;
    }

    .editor-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        max-width: 400px;
    }

    .editor-option {
        padding: 24px;
        min-height: 160px;
    }

    .option-content {
        gap: 16px;
    }

    .editor-icon-container {
        width: 60px;
        height: 60px;
    }

    .editor-icon {
        width: 32px;
        height: 32px;
    }

    .fallback-icon {
        width: 36px;
        height: 36px;
    }

    .cursor-fallback .cursor-logo {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }

    .intellij-fallback .intellij-logo,
    .pycharm-fallback .pycharm-logo,
    .webstorm-fallback .webstorm-logo,
    .phpstorm-fallback .phpstorm-logo,
    .rubymine-fallback .rubymine-logo,
    .clion-fallback .clion-logo,
    .goland-fallback .goland-logo,
    .rider-fallback .rider-logo,
    .datagrip-fallback .datagrip-logo,
    .androidstudio-fallback .androidstudio-logo {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }

    .vscode-fallback svg {
        width: 32px;
        height: 32px;
    }

    .editor-name {
        font-size: 18px;
    }

    .editor-description {
        font-size: 13px;
    }
}

/* 主页面 */
.main-container {
    position: relative;
    overflow-y: auto;
}

/* Hero区域 - 独占一页 */
.hero-section {
    position: relative;
    height: calc(100vh - 60px);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    inset: 0;
    overflow: hidden;
}

.particle-field {
    position: absolute;
    inset: 0;
}

.hero-content {
    position: relative;
    text-align: center;
    z-index: 1;
    transform: translateY(-40px); /* 向上移动整个内容区域 */
}

.hero-title {
    font-size: 80px;
    font-weight: 900;
    margin-bottom: 20px; /* 稍微增加间距 */
    position: relative;
    opacity: 0;
    transform: translateY(50px);
}

.hero-subtitle {
    font-size: 24px;
    color: var(--text-secondary);
    margin-bottom: 28px; /* 稍微增加间距 */
    opacity: 0;
    transform: translateY(30px);
}

.selected-editor {
    display: inline-block;
    padding: 12px 24px;
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid var(--purple-primary);
    border-radius: var(--radius-md);
    font-size: 16px;
    color: var(--purple-light);
    opacity: 0;
    transform: scale(0.8);
}

/* Glitch效果 - 增强版 */
.glitch-text {
    position: relative;
    color: var(--text-primary);
    animation: glitch 3s infinite; /* 稍微加快动画速度 */
}

.glitch-text::before,
.glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    will-change: transform; /* 优化GPU加速 */
}

.glitch-text::before {
    animation: glitch-1 0.8s infinite; /* 加快动画速度，增强效果 */
    color: var(--purple-primary);
    z-index: -1;
}

.glitch-text::after {
    animation: glitch-2 0.9s infinite; /* 加快动画速度，增强效果 */
    color: var(--purple-light);
    z-index: -2;
}

@keyframes glitch {
    0%, 100% {
        text-shadow: 0 0 8px var(--purple-glow);
        transform: translate(0);
    }
    10% {
        text-shadow: -4px 0 12px var(--purple-glow), 4px 0 12px var(--purple-light);
        transform: translate(-2px, 1px);
    }
    20% {
        text-shadow: 4px 0 12px var(--purple-glow), -4px 0 12px var(--purple-light);
        transform: translate(2px, -1px);
    }
    30% {
        text-shadow: 0 0 8px var(--purple-glow);
        transform: translate(0);
    }
    40% {
        text-shadow: -3px 0 10px var(--purple-glow), 3px 0 10px var(--purple-light);
        transform: translate(-1px, 2px);
    }
    50% {
        text-shadow: 3px 0 10px var(--purple-glow), -3px 0 10px var(--purple-light);
        transform: translate(1px, -2px);
    }
    60% {
        text-shadow: 0 0 8px var(--purple-glow);
        transform: translate(0);
    }
}

@keyframes glitch-1 {
    0%, 100% {
        clip-path: inset(0 0 0 0);
        transform: translate(0);
    }
    15% {
        clip-path: inset(15% 0 70% 0);
        transform: translate(-4px, 3px);
    }
    25% {
        clip-path: inset(25% 0 50% 0);
        transform: translate(3px, -4px);
    }
    35% {
        clip-path: inset(60% 0 15% 0);
        transform: translate(-3px, 2px);
    }
    50% {
        clip-path: inset(40% 0 35% 0);
        transform: translate(4px, -3px);
    }
    65% {
        clip-path: inset(10% 0 80% 0);
        transform: translate(-2px, 4px);
    }
    80% {
        clip-path: inset(75% 0 10% 0);
        transform: translate(2px, -2px);
    }
}

@keyframes glitch-2 {
    0%, 100% {
        clip-path: inset(0 0 0 0);
        transform: translate(0);
    }
    12% {
        clip-path: inset(35% 0 45% 0);
        transform: translate(3px, -2px);
    }
    28% {
        clip-path: inset(55% 0 25% 0);
        transform: translate(-4px, 3px);
    }
    42% {
        clip-path: inset(20% 0 65% 0);
        transform: translate(2px, 4px);
    }
    58% {
        clip-path: inset(70% 0 15% 0);
        transform: translate(-3px, -2px);
    }
    72% {
        clip-path: inset(5% 0 85% 0);
        transform: translate(4px, 2px);
    }
    88% {
        clip-path: inset(45% 0 40% 0);
        transform: translate(-2px, -4px);
    }
}

/* 功能区域 */
.features-section {
    display: flex;
    flex-direction: column;
    gap: 60px;
    justify-content: center;
    align-items: center;
    padding: 100px 40px;
    min-height: 100vh;
}

/* 第一行容器：重置和邮箱工作流 */
.features-row-1 {
    display: flex;
    gap: 60px;
    justify-content: center;
    align-items: flex-start;
}

/* 第二行：Augment账号管理（单独一行，居中） */
.features-section > .augment-account-container {
    margin-top: 0;
}

/* 重置 Augment - 全新动态设计 */
.reset-container {
    position: relative;
    width: 380px;
    height: 450px;
    opacity: 0;
    transform: translateY(100px);
}

/* 全新的动态背景 */
.reset-background {
    display: none;
}

/* 重置时的容器动画 */
.reset-container.resetting {
    animation: containerEnergy 3s ease-in-out infinite;
}

.reset-container.resetting::before {
    content: '';
    position: absolute;
    inset: -20px;
    background: conic-gradient(
        from 0deg,
        transparent 0deg,
        rgba(139, 92, 246, 0.1) 90deg,
        transparent 180deg,
        rgba(139, 92, 246, 0.1) 270deg,
        transparent 360deg
    );
    border-radius: 50%;
    animation: containerSpin 4s linear infinite;
    z-index: -1;
}

.reset-container.resetting::after {
    content: '';
    position: absolute;
    inset: -40px;
    background: radial-gradient(
        circle,
        transparent 70%,
        rgba(139, 92, 246, 0.05) 80%,
        rgba(139, 92, 246, 0.1) 90%,
        transparent 100%
    );
    border-radius: 50%;
    animation: containerPulse 2.5s ease-in-out infinite;
    z-index: -2;
}

@keyframes containerEnergy {
    0%, 100% {
        transform: translateY(0) scale(1);
    }
    50% {
        transform: translateY(-5px) scale(1.01);
    }
}

@keyframes containerSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes containerPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.6;
    }
}

/* 简化粒子效果 */
.reset-particles {
    position: absolute;
    inset: 0;
    background:
        radial-gradient(circle at 25% 25%, rgba(139, 92, 246, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(167, 139, 250, 0.02) 0%, transparent 40%);
    animation: particleFloat 15s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-10px) rotate(1deg);
        opacity: 1;
    }
}

/* 内容区域 - 居中对齐 */
.reset-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 30px;
    text-align: center;
    z-index: 5;
}

/* 图标区域 - 居中设计 */
.reset-icon-wrapper {
    position: relative;
    margin-bottom: 35px;
}

.reset-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg,
        var(--purple-primary) 0%,
        var(--purple-dark) 50%,
        rgba(139, 92, 246, 0.9) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 20px 40px rgba(139, 92, 246, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 6;
}

.reset-icon svg {
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.2));
    animation: resetIconSpin 2s linear infinite paused;
    transition: all 0.3s ease;
}

/* 全新的重置动画 - 能量充电效果 */
.reset-icon.resetting {
    position: relative;
    animation: energyCharge 1.8s ease-in-out infinite;
    background: linear-gradient(45deg,
        var(--purple-primary) 0%,
        #ff6b6b 25%,
        #4ecdc4 50%,
        #45b7d1 75%,
        var(--purple-primary) 100%);
    background-size: 400% 400%;
    animation: energyCharge 1.8s ease-in-out infinite,
               colorShift 3s ease-in-out infinite;
}

.reset-icon.resetting::before {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: conic-gradient(
        from 0deg,
        transparent 0deg,
        var(--purple-primary) 90deg,
        transparent 180deg,
        var(--purple-primary) 270deg,
        transparent 360deg
    );
    animation: energySpin 1.2s linear infinite;
    z-index: -1;
}

.reset-icon.resetting::after {
    content: '';
    position: absolute;
    inset: -12px;
    border-radius: 50%;
    background: radial-gradient(
        circle,
        transparent 60%,
        rgba(139, 92, 246, 0.1) 70%,
        rgba(139, 92, 246, 0.3) 80%,
        transparent 90%
    );
    animation: energyPulse 2s ease-in-out infinite;
    z-index: -2;
}

.reset-icon.resetting svg {
    animation: iconMorph 2s ease-in-out infinite;
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8));
}

@keyframes energyCharge {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        box-shadow:
            0 0 20px rgba(139, 92, 246, 0.6),
            0 0 40px rgba(139, 92, 246, 0.4),
            0 0 60px rgba(139, 92, 246, 0.2);
    }
    50% {
        transform: scale(1.15) rotate(180deg);
        box-shadow:
            0 0 30px rgba(139, 92, 246, 0.8),
            0 0 60px rgba(139, 92, 246, 0.6),
            0 0 90px rgba(139, 92, 246, 0.4);
    }
}

@keyframes colorShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes energySpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes energyPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.3);
        opacity: 0.7;
    }
}

@keyframes iconMorph {
    0%, 100% {
        transform: scale(1) rotateY(0deg);
    }
    25% {
        transform: scale(0.8) rotateY(90deg);
    }
    50% {
        transform: scale(1.1) rotateY(180deg);
    }
    75% {
        transform: scale(0.9) rotateY(270deg);
    }
}

.reset-status-ring {
    position: absolute;
    top: -25px;
    left: -25px;
    z-index: 2;
    opacity: 0.8;
}

.ring-progress .progress-circle {
    transition: stroke-dashoffset 0.5s ease;
    transform-origin: center;
    transform: rotate(-90deg);
}

/* 标题 - 居中对齐 */
.reset-title {
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 15px;
    background: linear-gradient(135deg,
        var(--text-primary) 0%,
        var(--purple-light) 50%,
        rgba(255, 255, 255, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(139, 92, 246, 0.3);
}

/* 描述文字 - 居中对齐 */
.reset-description {
    font-size: 16px;
    color: var(--text-secondary);
    margin-bottom: 40px;
    line-height: 1.6;
    opacity: 0.9;
    max-width: 320px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

/* 按钮 - 居中设计 */
.reset-action-btn {
    width: 200px;
    height: 55px;
    background: linear-gradient(135deg,
        var(--purple-primary) 0%,
        var(--purple-dark) 50%,
        rgba(139, 92, 246, 0.9) 100%);
    border: none;
    border-radius: 28px;
    color: white;
    font-size: 18px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 15px 30px rgba(139, 92, 246, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.reset-action-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 20px 40px rgba(139, 92, 246, 0.6),
        0 0 0 2px rgba(255, 255, 255, 0.2),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

.reset-action-btn:active {
    transform: translateY(-1px) scale(1.02);
}

/* 全新按钮动画 - 数据流效果 */
.reset-action-btn:disabled {
    cursor: not-allowed;
    position: relative;
    overflow: hidden;
}

.reset-action-btn.resetting {
    background: linear-gradient(45deg,
        #667eea 0%,
        #764ba2 25%,
        #f093fb 50%,
        #f5576c 75%,
        #4facfe 100%);
    background-size: 400% 400%;
    animation: dataFlow 2.5s ease-in-out infinite,
               buttonMorph 3s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.reset-action-btn.resetting::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.4) 50%, transparent 100%),
        linear-gradient(0deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
    background-size: 50% 100%, 100% 50%;
    animation: dataStream 1.5s linear infinite;
}

.reset-action-btn.resetting::after {
    content: '';
    position: absolute;
    inset: 2px;
    border-radius: 26px;
    background: linear-gradient(45deg,
        rgba(255,255,255,0.1) 0%,
        transparent 50%,
        rgba(255,255,255,0.1) 100%);
    animation: innerGlow 2s ease-in-out infinite;
}

@keyframes dataFlow {
    0%, 100% {
        background-position: 0% 50%;
        transform: scale(1);
    }
    50% {
        background-position: 100% 50%;
        transform: scale(1.02);
    }
}

@keyframes buttonMorph {
    0%, 100% {
        border-radius: 28px;
        box-shadow:
            0 15px 35px rgba(102, 126, 234, 0.4),
            0 5px 15px rgba(118, 75, 162, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    33% {
        border-radius: 35px;
        box-shadow:
            0 20px 45px rgba(240, 147, 251, 0.5),
            0 8px 20px rgba(245, 87, 108, 0.4),
            inset 0 2px 0 rgba(255, 255, 255, 0.3);
    }
    66% {
        border-radius: 20px;
        box-shadow:
            0 18px 40px rgba(79, 172, 254, 0.5),
            0 6px 18px rgba(102, 126, 234, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.25);
    }
}

@keyframes dataStream {
    0% {
        background-position: -100% 0%, 0% -100%;
    }
    100% {
        background-position: 100% 0%, 0% 100%;
    }
}

@keyframes innerGlow {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(0.98);
    }
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.loading-dots {
    display: flex;
    gap: 4px;
}

.loading-dots span {
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
    animation: loadingDots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

@keyframes resetIconSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}



/* 邮箱工作流 - 现代流畅设计 */
.email-workflow-container {
    position: relative;
    width: 520px;
    opacity: 0;
    transform: translateY(100px);
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.email-workflow-card {
    position: relative;
    background: linear-gradient(145deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(139, 92, 246, 0.03) 30%,
        rgba(15, 15, 15, 0.95) 100%);
    border: 1px solid rgba(139, 92, 246, 0.15);
    border-radius: 28px;
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    padding: 0;
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(139, 92, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.email-workflow-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(139, 92, 246, 0.6),
        rgba(167, 139, 250, 0.4),
        transparent);
}

/* 工作流进度指示器 */
.workflow-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px 32px 16px;
    gap: 20px;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    opacity: 0.4;
    transition: all 0.3s ease;
}

.progress-step.active {
    opacity: 1;
}

.progress-step.completed {
    opacity: 1;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(139, 92, 246, 0.1);
    border: 2px solid rgba(139, 92, 246, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(139, 92, 246, 0.6);
    transition: all 0.3s ease;
}

.progress-step.active .step-icon {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light));
    border-color: var(--purple-primary);
    color: white;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.progress-step.completed .step-icon {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light));
    border-color: var(--purple-primary);
    color: white;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4), 0 0 20px rgba(139, 92, 246, 0.3);
}

.step-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--text-secondary);
    text-align: center;
    transition: color 0.3s ease;
}

.progress-step.active .step-label {
    color: var(--purple-light);
}

.progress-step.completed .step-label {
    color: var(--purple-light);
}

.progress-line {
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg,
        rgba(139, 92, 246, 0.2),
        rgba(139, 92, 246, 0.1));
    border-radius: 1px;
    position: relative;
    overflow: hidden;
}

.progress-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, var(--purple-primary), var(--purple-light));
    border-radius: 1px;
    transition: left 0.6s ease;
}

.progress-step.completed ~ .progress-line::after {
    left: 0;
}

/* 邮箱工作区 */
.email-workspace {
    padding: 0 32px 32px;
}

.workspace-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.1);
}

.header-icon {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.15),
        rgba(167, 139, 250, 0.2));
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--purple-light);
}

.header-content {
    flex: 1;
}

.workspace-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    line-height: 1.2;
}

.workspace-subtitle {
    font-size: 13px;
    color: var(--text-secondary);
    margin: 0;
    opacity: 0.8;
}

.workspace-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(139, 92, 246, 0.08);
    border: 1px solid rgba(139, 92, 246, 0.15);
    border-radius: 20px;
    /* 添加丝滑的过渡动画 */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 0; /* 允许收缩 */
    max-width: 200px; /* 限制最大宽度，防止布局被撑坏 */
    overflow: hidden; /* 防止内容溢出 */
    position: relative;
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--text-muted);
    /* 增强过渡动画，包含变形和阴影 */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0; /* 防止点收缩 */
}

.status-dot.active {
    background: var(--purple-primary);
    box-shadow: 0 0 8px rgba(139, 92, 246, 0.5);
}

.status-dot.success {
    background: #22c55e;
    box-shadow: 0 0 8px rgba(34, 197, 94, 0.5);
}

.status-dot.error {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.5);
}

.status-dot.running {
    background: #8b5cf6;
    box-shadow: 0 0 8px rgba(139, 92, 246, 0.5);
    animation: statusPulse 2s infinite;
}

.status-dot.idle {
    background: var(--text-muted);
    box-shadow: none;
}

.workspace-status .status-text {
    font-size: 11px;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    /* 添加文字过渡动画 */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏溢出文字 */
    position: relative;
    display: inline-block;
    max-width: 140px; /* 限制文字区域最大宽度 */
    flex: 1; /* 占用剩余空间 */
    min-width: 0; /* 允许收缩 */
}

/* 文字滚动效果 */
.workspace-status .status-text.text-scrolling {
    animation: textScroll 8s linear infinite;
    animation-delay: 1s; /* 延迟1秒开始滚动 */
}

/* 滚动动画关键帧 */
@keyframes textScroll {
    0%, 20% {
        transform: translateX(0);
    }
    80%, 100% {
        transform: translateX(var(--scroll-distance, -100px));
    }
}

/* 通用文字变化动画 - 适用于所有元素 */
.changing {
    opacity: 0;
    transform: translateY(-2px);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 状态文字变化动画 */
.workspace-status .status-text.changing {
    opacity: 0;
    transform: translateY(-2px);
}

/* 不同状态下的样式增强 */
.workspace-status.active {
    background: rgba(139, 92, 246, 0.12);
    border-color: rgba(139, 92, 246, 0.25);
    transform: scale(1.02);
}

.workspace-status.success {
    background: rgba(34, 197, 94, 0.12);
    border-color: rgba(34, 197, 94, 0.25);
    transform: scale(1.02);
}

.workspace-status.error {
    background: rgba(239, 68, 68, 0.12);
    border-color: rgba(239, 68, 68, 0.25);
    transform: scale(1.02);
}

.workspace-status.running {
    background: rgba(139, 92, 246, 0.12);
    border-color: rgba(139, 92, 246, 0.25);
    transform: scale(1.02);
}

.workspace-status.idle {
    background: rgba(139, 92, 246, 0.08);
    border-color: rgba(139, 92, 246, 0.15);
    transform: scale(1.02);
}

/* 状态文字颜色过渡 */
.workspace-status.active .status-text {
    color: rgba(139, 92, 246, 0.9);
}

.workspace-status.success .status-text {
    color: rgba(34, 197, 94, 0.9);
}

.workspace-status.error .status-text {
    color: rgba(239, 68, 68, 0.9);
}

.workspace-status.running .status-text {
    color: rgba(139, 92, 246, 0.9);
}

/* 邮箱显示区域 */
.email-display-zone {
    height: 120px;
    background: linear-gradient(145deg,
        rgba(0, 0, 0, 0.4) 0%,
        rgba(139, 92, 246, 0.02) 50%,
        rgba(0, 0, 0, 0.4) 100%);
    border: 1px solid rgba(139, 92, 246, 0.12);
    border-radius: 20px;
    padding: 24px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.email-display-zone::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(139, 92, 246, 0.3),
        transparent);
}

.email-placeholder-modern {
    text-align: center;
    opacity: 0.6;
    transform: scale(0.85);
    padding: 8px;
}

.placeholder-icon {
    margin-bottom: 8px;
    opacity: 0.4;
}

.placeholder-text h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
}

.placeholder-text p {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
    opacity: 0.8;
}

/* 邮箱结果显示 */
.email-result-modern {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 20px;
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.08) 0%,
        rgba(167, 139, 250, 0.05) 100%);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.email-text-modern {
    flex: 1;
    font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
    font-size: 15px;
    font-weight: 500;
    color: var(--purple-light);
    background: transparent;
    border: none;
    outline: none;
    padding: 0;
    letter-spacing: 0.5px;
}

.copy-email-btn-modern {
    padding: 8px 16px;
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.15),
        rgba(167, 139, 250, 0.2));
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 12px;
    color: var(--purple-light);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.copy-email-btn-modern:hover {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.25),
        rgba(167, 139, 250, 0.3));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

/* 工作流操作按钮 */
.workflow-actions {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
}

.workflow-btn {
    flex: 1;
    height: 52px;
    border: none;
    border-radius: 16px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.workflow-btn.primary {
    background: linear-gradient(135deg,
        var(--purple-primary) 0%,
        var(--purple-light) 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: white;
    box-shadow:
        0 4px 16px rgba(139, 92, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.workflow-btn.primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow:
        0 8px 24px rgba(139, 92, 246, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.workflow-btn.secondary {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.08) 0%,
        rgba(167, 139, 250, 0.05) 100%);
    border: 1px solid rgba(139, 92, 246, 0.2);
    color: var(--purple-light);
}

.workflow-btn.secondary:hover:not(:disabled) {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.15) 0%,
        rgba(167, 139, 250, 0.1) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.2);
}

.workflow-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.workflow-btn.disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.btn-content {
    display: flex;
    align-items: center;
    gap: 8px;
    transition: opacity 0.3s ease;
}

.btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.workflow-btn.loading .btn-content {
    opacity: 0;
}

.workflow-btn.loading .btn-loading {
    opacity: 1;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 验证码结果区域 */
.verification-result-zone {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.4s ease;
}

.verification-result-zone:not(.hidden) {
    opacity: 1;
    transform: translateY(0);
}

.result-card {
    background: linear-gradient(145deg,
        rgba(34, 197, 94, 0.05) 0%,
        rgba(22, 163, 74, 0.03) 100%);
    border: 1px solid rgba(34, 197, 94, 0.2);
    border-radius: 20px;
    padding: 24px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.result-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.result-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.result-icon.success {
    background: linear-gradient(135deg, #22c55e, #16a34a);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.result-title {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    color: #22c55e;
    margin: 0;
}

.result-copy-btn {
    padding: 8px 12px;
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.1) 0%,
        rgba(22, 163, 74, 0.05) 100%);
    border: 1px solid rgba(34, 197, 94, 0.3);
    border-radius: 12px;
    color: #22c55e;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.result-copy-btn:hover {
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.2) 0%,
        rgba(22, 163, 74, 0.1) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
}

.verification-code-display {
    text-align: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(34, 197, 94, 0.2);
    border-radius: 16px;
}

.verification-code {
    font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
    font-size: 32px;
    font-weight: 700;
    color: #22c55e;
    letter-spacing: 4px;
    text-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

/* 浮动卡片样式 */
.email-floating-cards {
    position: absolute;
    inset: 0;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.12) 0%,
        rgba(167, 139, 250, 0.08) 100%);
    border: 1px solid rgba(139, 92, 246, 0.25);
    border-radius: 14px;
    padding: 14px 18px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    opacity: 0;
    transform: scale(1);
    pointer-events: auto;
    min-width: 90px;
}

.floating-card.card-1 {
    top: -28px;
    right: -38px;
    z-index: 2;
    transform: rotate(8deg) scale(1.0);
    animation: none; /* 禁用CSS动画，让GSAP控制 */
}

.floating-card.card-2 {
    bottom: -23px;
    left: -33px;
    z-index: 1;
    transform: rotate(-5deg) scale(1.0);
    animation: none; /* 禁用CSS动画，让GSAP控制 */
}

.floating-card:hover {
    transform: scale(1.05) translateY(-2px) rotate(var(--rotation, 0deg));
    border-color: rgba(139, 92, 246, 0.4);
    box-shadow: 0 8px 32px rgba(139, 92, 246, 0.15);
}

.floating-card.card-1 {
    --rotation: 8deg;
}

.floating-card.card-2 {
    --rotation: -5deg;
}

.floating-card .card-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.floating-card .card-label {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.floating-card .card-value {
    font-size: 12px;
    color: var(--purple-light);
    font-weight: 400;
}

@keyframes float1 {
    0%, 100% {
        transform: rotate(8deg) scale(1.0) translateY(0px);
    }
    50% {
        transform: rotate(9deg) scale(1.0) translateY(-8px);
    }
}

@keyframes float2 {
    0%, 100% {
        transform: rotate(-5deg) scale(1.0) translateY(0px);
    }
    50% {
        transform: rotate(-6deg) scale(1.0) translateY(-6px);
    }
}

/* 工作流容器悬停效果 */
.email-workflow-container:hover .email-workflow-card {
    border-color: rgba(139, 92, 246, 0.25);
    transform: translateY(-3px);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(139, 92, 246, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.email-workflow-container:hover .info-card {
    transform: translateY(-1px);
}

/* 工作流进度动画 */
.progress-step.active .step-icon {
    animation: stepPulse 2s ease-in-out infinite;
}

@keyframes stepPulse {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 6px 20px rgba(139, 92, 246, 0.5);
        transform: scale(1.05);
    }
}

.generate-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-dark));
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
}

.email-display-area {
    min-height: 80px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(139, 92, 246, 0.1);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.email-placeholder {
    color: var(--text-muted);
    font-style: italic;
    text-align: center;
}

.email-result {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.email-text {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 16px;
    color: var(--purple-light);
    background: transparent;
    border: none;
    outline: none;
    padding: 8px;
    border-radius: 8px;
    background: rgba(139, 92, 246, 0.05);
}

.copy-email-btn {
    padding: 8px 12px;
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid var(--purple-primary);
    border-radius: 8px;
    color: var(--purple-light);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
}

.copy-email-btn:hover {
    background: rgba(139, 92, 246, 0.2);
}

.email-verification-section {
    background: rgba(167, 139, 250, 0.03);
    border: 1px solid rgba(167, 139, 250, 0.15);
    border-radius: 16px;
    padding: 20px;
}

.verification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.verification-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.verification-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-muted);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.status-indicator.active {
    background: var(--purple-primary);
    box-shadow: 0 0 8px rgba(139, 92, 246, 0.5);
}

.status-indicator.success {
    background: #22c55e;
    box-shadow: 0 0 8px rgba(34, 197, 94, 0.5);
}

.status-indicator.error {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.5);
}

.verification-status .status-text {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1;
    display: flex;
    align-items: center;
}

.verification-btn {
    width: 100%;
    height: 48px;
    background: linear-gradient(135deg, rgba(167, 139, 250, 0.8), rgba(139, 92, 246, 0.9));
    border: 1px solid var(--purple-light);
    border-radius: 12px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.verification-btn .btn-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.verification-btn .btn-icon svg {
    display: block;
}

.verification-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(167, 139, 250, 0.3);
}

.verification-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.verification-btn.loading {
    position: relative;
    color: transparent;
}

.verification-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}





/* 添加一些额外的动画效果 */

/* 重置容器悬停效果 */
.reset-container:hover .reset-particles {
    opacity: 1;
    transform: translateY(-5px) rotate(2deg);
}

.reset-container:hover .reset-particles {
    animation-duration: 6s;
}

/* 旧版邮箱容器悬停效果（兼容性保留） */
.email-container:hover .email-main-card {
    border-color: rgba(139, 92, 246, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.15);
}

.email-container:hover .floating-card {
    transform: scale(1.05) rotate(var(--rotation, 0deg));
}

.floating-card.card-1 {
    --rotation: 8deg;
}

.floating-card.card-2 {
    --rotation: -5deg;
}

/* 进度环动画 */
.reset-status-ring {
    animation: ringPulse 2s ease-in-out infinite;
}

@keyframes ringPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* 按钮点击效果 */
.reset-action-btn:active {
    transform: translateY(-1px) scale(0.98);
}

.generate-btn:active,
.verification-btn:active,
.copy-email-btn:active {
    transform: translateY(1px) scale(0.98);
}

/* 邮箱文本选中效果 */
.email-text:focus {
    outline: none;
    border: 1px solid var(--purple-primary);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

/* 状态指示器脉冲动画 */
.status-indicator.active {
    animation: statusPulse 1.5s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% {
        box-shadow: 0 0 8px rgba(139, 92, 246, 0.5);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 12px rgba(139, 92, 246, 0.8);
        transform: scale(1.1);
    }
}

/* 状态文字变化动画 */
@keyframes statusTextChange {
    0% {
        opacity: 1;
        transform: translateY(0);
    }
    50% {
        opacity: 0;
        transform: translateY(-2px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 状态容器宽度变化动画 */
@keyframes statusWidthChange {
    0% {
        transform: scaleX(1);
    }
    50% {
        transform: scaleX(1.05);
    }
    100% {
        transform: scaleX(1);
    }
}

/* 关于区域 - 完全打破传统的创意布局 */
.about-section {
    padding: 40px 20px 60px;
    position: relative;
    overflow: hidden;
    min-height: 400px;
    height: 400px;
    width: 100%;
}

/* 关于区域分割线 */
.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.4), transparent);
}

/* 关于标题 - 左边放大 */
.about-header {
    position: absolute;
    top: 80px;
    left: 60px;
    display: flex;
    align-items: center;
    gap: 16px;
    opacity: 0;
    transform: translateX(-80px) scale(0.8);
}

.about-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.about-title {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
}

/* 主应用名 - 右上角大字，倾斜 */
.app-main-info {
    position: absolute;
    top: 50px;
    right: 80px;
    text-align: right;
    opacity: 0;
    transform: translateX(100px) translateY(-50px) rotate(10deg);
}

.app-name {
    font-size: 36px;
    font-weight: 800;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.02em;
    line-height: 0.9;
    transform: rotate(-3deg);
    text-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

.app-developer {
    font-size: 11px;
    color: var(--text-secondary);
    opacity: 0.6;
    margin-top: 6px;
    font-style: italic;
    transform: rotate(-1deg);
}

/* 编程工作环境区域 */
.coding-workspace {
    position: absolute;
    top: 120px;
    left: 320px;
    transform: translateX(-60px) rotate(-3deg) scale(1.2);
    opacity: 0;
    z-index: 3;
}

.workspace-container {
    position: relative;
    width: 320px;
    height: 220px;
}

/* 主显示器 */
.main-monitor {
    position: absolute;
    top: 20px;
    left: 50px;
    z-index: 2;
}

.monitor-svg {
    filter: drop-shadow(0 4px 12px rgba(139, 92, 246, 0.4)) drop-shadow(0 0 15px rgba(139, 92, 246, 0.2));
    animation: monitor-glow 4s ease-in-out infinite;
}

@keyframes monitor-glow {
    0%, 100% {
        filter: drop-shadow(0 4px 12px rgba(139, 92, 246, 0.4)) drop-shadow(0 0 15px rgba(139, 92, 246, 0.2));
    }
    50% {
        filter: drop-shadow(0 4px 12px rgba(139, 92, 246, 0.6)) drop-shadow(0 0 20px rgba(139, 92, 246, 0.4));
    }
}

/* 副显示器 */
.secondary-monitor {
    position: absolute;
    top: 30px;
    left: 180px;
    z-index: 1;
}

/* 键盘 */
.keyboard {
    position: absolute;
    bottom: 40px;
    left: 60px;
    z-index: 1;
}

.keyboard-svg {
    filter: drop-shadow(0 2px 6px rgba(139, 92, 246, 0.3));
}

/* 键盘按键动画 */
.key-1, .key-5, .key-9, .key-13, .key-17, .key-21, .key-25 {
    animation: key-press 6s ease-in-out infinite;
}

.key-2, .key-6, .key-10, .key-14, .key-18, .key-22, .key-26 {
    animation: key-press 6s ease-in-out infinite 0.2s;
}

.key-3, .key-7, .key-11, .key-15, .key-19, .key-23, .key-27 {
    animation: key-press 6s ease-in-out infinite 0.4s;
}

.key-4, .key-8, .key-12, .key-16, .key-20, .key-24, .key-28 {
    animation: key-press 6s ease-in-out infinite 0.6s;
}

@keyframes key-press {
    0%, 95% { transform: translateY(0); fill: rgba(139, 92, 246, 0.3); }
    97% { transform: translateY(1px); fill: rgba(139, 92, 246, 0.6); }
    100% { transform: translateY(0); fill: rgba(139, 92, 246, 0.3); }
}

/* 鼠标 */
.mouse {
    position: absolute;
    bottom: 50px;
    left: 180px;
    z-index: 1;
}

.mouse-svg {
    filter: drop-shadow(0 2px 4px rgba(139, 92, 246, 0.3));
}

.mouse-light {
    animation: mouse-blink 3s ease-in-out infinite;
}

@keyframes mouse-blink {
    0%, 90% { opacity: 0.6; }
    95% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* 大咖啡杯 */
.coffee-cup-large {
    position: absolute;
    bottom: 80px;
    left: 10px;
    z-index: 1;
}

.coffee-svg {
    filter: drop-shadow(0 4px 8px rgba(139, 92, 246, 0.4));
}

/* 蒸汽动画 */
.steam-1 {
    animation: steam-rise 4s ease-in-out infinite;
}

.steam-2 {
    animation: steam-rise 4s ease-in-out infinite 0.8s;
}

.steam-3 {
    animation: steam-rise 4s ease-in-out infinite 1.6s;
}

@keyframes steam-rise {
    0% { opacity: 0; transform: translateY(0); }
    30% { opacity: 0.8; transform: translateY(-4px); }
    60% { opacity: 0.6; transform: translateY(-8px); }
    100% { opacity: 0; transform: translateY(-12px); }
}

/* 书籍堆 */
.books-stack {
    position: absolute;
    bottom: 120px;
    left: 220px;
    z-index: 1;
}

.books-svg {
    filter: drop-shadow(0 3px 6px rgba(139, 92, 246, 0.3));
}

/* 植物 */
.plant {
    position: absolute;
    bottom: 100px;
    left: 280px;
    z-index: 1;
}

.plant-svg {
    filter: drop-shadow(0 2px 4px rgba(139, 92, 246, 0.2));
}

.leaf-1 {
    animation: leaf-sway 6s ease-in-out infinite;
    transform-origin: 16px 31px;
}

.leaf-2 {
    animation: leaf-sway 6s ease-in-out infinite 1s;
    transform-origin: 20px 27px;
}

.leaf-3 {
    animation: leaf-sway 6s ease-in-out infinite 2s;
    transform-origin: 17px 19px;
}

.leaf-4 {
    animation: leaf-sway 6s ease-in-out infinite 3s;
    transform-origin: 19px 21px;
}

@keyframes leaf-sway {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(2deg); }
    75% { transform: rotate(-2deg); }
}

/* 打字动画 */
.left-arm {
    animation: typing-left 2s ease-in-out infinite;
    transform-origin: 23px 29.5px;
}

.right-arm {
    animation: typing-right 2s ease-in-out infinite 0.3s;
    transform-origin: 37px 29.5px;
}

@keyframes typing-left {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-15deg); }
    75% { transform: rotate(5deg); }
}

@keyframes typing-right {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(15deg); }
    75% { transform: rotate(-5deg); }
}

/* 代码行动画 */
.code-line-1 {
    animation: code-typing 8s ease-in-out infinite;
}

.code-line-2 {
    animation: code-typing 8s ease-in-out infinite 0.5s;
}

.code-line-3 {
    animation: code-typing 8s ease-in-out infinite 1s;
}

.code-line-4 {
    animation: code-typing 8s ease-in-out infinite 1.5s;
}

.code-line-5 {
    animation: code-typing 8s ease-in-out infinite 2s;
}

.code-line-6 {
    animation: code-typing 8s ease-in-out infinite 2.5s;
}

.code-line-7 {
    animation: code-typing 8s ease-in-out infinite 3s;
}

.code-line-8 {
    animation: code-typing 8s ease-in-out infinite 3.5s;
}

@keyframes code-typing {
    0%, 90% { width: 0; opacity: 0; }
    95% { width: var(--target-width, 30px); opacity: 1; }
    100% { width: var(--target-width, 30px); opacity: 1; }
}

/* 光标闪烁 */
.cursor-blink {
    animation: cursor-flash 1.2s ease-in-out infinite;
}

@keyframes cursor-flash {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 终端行动画 */
.terminal-line-1 {
    animation: terminal-scroll 6s ease-in-out infinite;
}

.terminal-line-2 {
    animation: terminal-scroll 6s ease-in-out infinite 0.3s;
}

.terminal-line-3 {
    animation: terminal-scroll 6s ease-in-out infinite 0.6s;
}

.terminal-line-4 {
    animation: terminal-scroll 6s ease-in-out infinite 0.9s;
}

@keyframes terminal-scroll {
    0%, 85% { opacity: 0.5; }
    90% { opacity: 1; }
    100% { opacity: 0.5; }
}

/* 屏幕代码闪烁动画 */
.code-line-1 { animation: code-blink 3s ease-in-out infinite; }
.code-line-2 { animation: code-blink 3s ease-in-out infinite 0.5s; }
.code-line-3 { animation: code-blink 3s ease-in-out infinite 1s; }
.code-line-4 { animation: code-blink 3s ease-in-out infinite 1.5s; }

@keyframes code-blink {
    0%, 90% { opacity: 1; }
    95% { opacity: 0.3; }
    100% { opacity: 1; }
}

/* 增强的飘动代码片段 */
.floating-code-enhanced {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 320px;
    height: 220px;
    pointer-events: none;
    z-index: 5;
}

.code-particle {
    position: absolute;
    font-family: 'Courier New', monospace;
    color: rgba(139, 92, 246, 0.8);
    font-weight: 600;
    opacity: 0;
    animation: float-code-enhanced 15s ease-in-out infinite;
    text-shadow: 0 0 6px rgba(139, 92, 246, 0.4);
}

.code-particle.large {
    font-size: 16px;
    font-weight: bold;
    color: rgba(139, 92, 246, 0.9);
    text-shadow: 0 0 8px rgba(139, 92, 246, 0.6);
}

.code-particle.medium {
    font-size: 13px;
    color: rgba(168, 139, 246, 0.8);
    text-shadow: 0 0 6px rgba(168, 139, 246, 0.4);
}

.code-particle.small {
    font-size: 11px;
    color: rgba(139, 92, 246, 0.7);
    text-shadow: 0 0 4px rgba(139, 92, 246, 0.3);
}

.code-particle:nth-child(1) {
    left: 50px;
    top: 30px;
    animation-delay: 0s;
}

.code-particle:nth-child(2) {
    left: 150px;
    top: 60px;
    animation-delay: 1.2s;
}

.code-particle:nth-child(3) {
    left: 80px;
    top: 100px;
    animation-delay: 2.4s;
}

.code-particle:nth-child(4) {
    left: 200px;
    top: 40px;
    animation-delay: 3.6s;
}

.code-particle:nth-child(5) {
    left: 120px;
    top: 120px;
    animation-delay: 4.8s;
}

.code-particle:nth-child(6) {
    left: 30px;
    top: 80px;
    animation-delay: 6s;
}

.code-particle:nth-child(7) {
    left: 180px;
    top: 100px;
    animation-delay: 7.2s;
}

.code-particle:nth-child(8) {
    left: 100px;
    top: 50px;
    animation-delay: 8.4s;
}

.code-particle:nth-child(9) {
    left: 250px;
    top: 80px;
    animation-delay: 9.6s;
}

.code-particle:nth-child(10) {
    left: 60px;
    top: 140px;
    animation-delay: 10.8s;
}

.code-particle:nth-child(11) {
    left: 160px;
    top: 20px;
    animation-delay: 12s;
}

.code-particle:nth-child(12) {
    left: 220px;
    top: 120px;
    animation-delay: 13.2s;
}

@keyframes float-code-enhanced {
    0% {
        opacity: 0;
        transform: translateY(0) scale(0.6) rotate(0deg);
    }
    15% {
        opacity: 0.8;
        transform: translateY(-20px) scale(1.1) rotate(5deg);
    }
    30% {
        opacity: 1;
        transform: translateY(-40px) scale(1) rotate(-3deg);
    }
    60% {
        opacity: 1;
        transform: translateY(-80px) scale(0.9) rotate(2deg);
    }
    85% {
        opacity: 0.6;
        transform: translateY(-120px) scale(0.8) rotate(-1deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-160px) scale(0.5) rotate(0deg);
    }
}

/* 分散式信息布局 - 完全打破网格 */
.info-grid {
    position: relative;
    width: 100%;
    height: 300px;
}

/* 版权信息 - 右下角 */
.copyright-item {
    position: absolute;
    bottom: 130px;
    right: 120px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 16px 20px;
    backdrop-filter: blur(10px);
    transform: rotate(3deg);
    z-index: 10;
    opacity: 0;
    /* 移除transition避免与GSAP动画冲突 */
}

.copyright-item:hover {
    transform: rotate(0deg) scale(1.05);
    background: rgba(139, 92, 246, 0.08);
    border-color: rgba(139, 92, 246, 0.3);
}

/* 邮箱信息 - 中心偏右 */
.email-item {
    position: absolute;
    top: 190px;
    right: 20px;
    background: rgba(139, 92, 246, 0.08);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 12px;
    padding: 16px 20px;
    backdrop-filter: blur(10px);
    transform: rotate(0deg) scale(1.05);
    z-index: 5;
    opacity: 0;
    /* 移除transition避免与GSAP动画冲突 */
}

.email-item:hover {
    transform: rotate(0deg) scale(1.1);
    background: rgba(139, 92, 246, 0.12);
    border-color: rgba(139, 92, 246, 0.4);
    box-shadow: 0 8px 32px rgba(139, 92, 246, 0.2);
}

/* QQ群信息 - 右中 */
.qq-item {
    position: absolute;
    top: 230px;
    right: 250px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 16px 20px;
    backdrop-filter: blur(10px);
    transform: rotate(-10deg);
    opacity: 0;
    /* 移除transition避免与GSAP动画冲突 */
}

.qq-item:hover {
    transform: rotate(0deg) scale(1.05);
    background: rgba(139, 92, 246, 0.08);
    border-color: rgba(139, 92, 246, 0.3);
}

.info-item {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 200px;
}

.info-icon {
    width: 24px;
    height: 24px;
    background: rgba(139, 92, 246, 0.15);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--purple-light);
    flex-shrink: 0;
    font-size: 12px;
    font-weight: 600;
}

.info-content {
    flex: 1;
    min-width: 0;
}

.info-label {
    font-size: 10px;
    color: var(--text-secondary);
    opacity: 0.7;
    margin-bottom: 2px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 13px;
    color: var(--text-primary);
    font-weight: 600;
    line-height: 1.3;
}

.info-note {
    font-size: 10px;
    color: var(--text-secondary);
    opacity: 0.6;
    font-style: italic;
    margin-top: 2px;
}

.group-note {
    font-size: 10px;
    color: var(--text-secondary);
    opacity: 0.7;
    font-weight: 400;
    font-style: italic;
}

/* 感谢信息 - 右下角 */
.thanks-message {
    position: absolute;
    bottom: 30px;
    right: 60px;
    display: flex;
    align-items: center;
    gap: 6px;
    transform: rotate(2deg);
    opacity: 0;
    /* 移除transition避免与GSAP动画冲突 */
}

.thanks-message:hover {
    transform: rotate(0deg) scale(1.1);
}

.thanks-text {
    font-size: 14px;
    color: var(--purple-light);
    font-weight: 500;
    opacity: 0.8;
}

.thanks-heart {
    font-size: 16px;
    animation: heartBeat 2s ease-in-out infinite;
}

/* 动画 */
@keyframes iconFloat {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-8px);
    }
}

@keyframes heartBeat {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}

/* Toast提示系统 - 符合项目主题风格 */
.toast-container {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 15000;
    pointer-events: none;
    max-width: 600px;
    width: auto;
    height: auto;
    /* 移除flexbox布局，改用绝对定位来控制toast位置 */
}

.toast {
    background: linear-gradient(145deg, rgba(0, 0, 0, 0.95), rgba(15, 15, 15, 0.98));
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-radius: var(--radius-lg);
    padding: 16px 24px;
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.6),
        0 4px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 14px;
    pointer-events: all;
    /* 改为绝对定位，这样我们可以完全控制位置 */
    position: absolute;
    left: 50%;
    transform: translateX(-50%) translateY(-100px) scale(0.9);
    overflow: hidden;
    width: fit-content;
    max-width: none; /* 移除最大宽度限制 */
    min-width: auto;
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    font-size: 15px;
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap; /* 不换行，让宽度完全根据内容调整 */
    /* 添加平滑的位置过渡动画，包括top属性 */
    transition: top 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    /* 点击交互样式 */
    cursor: pointer;
    user-select: none;
}

/* Toast悬停效果 */
.toast:hover {
    transform: translateX(-50%) translateY(0) scale(1.02);
    box-shadow:
        0 16px 50px rgba(0, 0, 0, 0.7),
        0 6px 20px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.15),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

/* Toast点击效果 */
.toast:active {
    transform: translateX(-50%) translateY(0) scale(0.98);
}

/* Toast类型样式 - 使用项目紫色主题 */
.toast.success {
    border: 1px solid rgba(34, 197, 94, 0.4);
    background: linear-gradient(145deg,
        rgba(34, 197, 94, 0.08),
        rgba(0, 0, 0, 0.95)
    );
    box-shadow:
        0 12px 40px rgba(34, 197, 94, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(34, 197, 94, 0.2);
}

.toast.error {
    border: 1px solid rgba(239, 68, 68, 0.4);
    background: linear-gradient(145deg,
        rgba(239, 68, 68, 0.08),
        rgba(0, 0, 0, 0.95)
    );
    box-shadow:
        0 12px 40px rgba(239, 68, 68, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(239, 68, 68, 0.2);
}

.toast.warning {
    border: 1px solid rgba(245, 158, 11, 0.4);
    background: linear-gradient(145deg,
        rgba(245, 158, 11, 0.08),
        rgba(0, 0, 0, 0.95)
    );
    box-shadow:
        0 12px 40px rgba(245, 158, 11, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(245, 158, 11, 0.2);
}

.toast.info {
    border: 1px solid rgba(139, 92, 246, 0.4);
    background: linear-gradient(145deg,
        rgba(139, 92, 246, 0.08),
        rgba(0, 0, 0, 0.95)
    );
    box-shadow:
        0 12px 40px rgba(139, 92, 246, 0.15),
        0 4px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(139, 92, 246, 0.2);
}

/* Toast图标 */
.toast-icon {
    font-size: 18px;
    font-weight: 700;
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-shadow: 0 0 8px currentColor;
}

.toast.success .toast-icon {
    color: #22c55e;
    background: rgba(34, 197, 94, 0.15);
}

.toast.error .toast-icon {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.15);
}

.toast.warning .toast-icon {
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.15);
}

.toast.info .toast-icon {
    color: var(--purple-primary);
    background: rgba(139, 92, 246, 0.15);
}

/* Toast消息文本 */
.toast-message {
    line-height: 1.4;
    white-space: nowrap;
}

/* Toast入场动画 */
.toast.show {
    animation: toastSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Toast出场动画 */
.toast.hide {
    animation: toastSlideOut 0.4s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
}

@keyframes toastSlideIn {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(-100px) scale(0.9);
        filter: blur(8px);
    }
    50% {
        opacity: 0.8;
        transform: translateX(-50%) translateY(10px) scale(1.05);
        filter: blur(2px);
    }
    100% {
        opacity: 1;
        transform: translateX(-50%) translateY(0) scale(1);
        filter: blur(0);
    }
}

@keyframes toastSlideOut {
    0% {
        opacity: 1;
        transform: translateX(-50%) translateY(0) scale(1);
        filter: blur(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-80px) scale(0.95);
        filter: blur(4px);
    }
}

/* 对话框 */
.dialog-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 3000;
    opacity: 0;
    transition: opacity 0.3s ease, backdrop-filter 0.3s ease;
    will-change: opacity;
    contain: layout style paint;
}

.dialog-overlay.show {
    display: flex;
    opacity: 1;
}

.dialog-overlay.hiding {
    opacity: 0;
}

.dialog {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    min-width: 500px;
    max-width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
    contain: layout style paint;
    display: flex;
    flex-direction: column;
}

.dialog-overlay.show .dialog {
    transform: scale(1);
    opacity: 1;
}

.dialog-overlay.hiding .dialog {
    transform: scale(0.9);
    opacity: 0;
}

@keyframes dialogIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes dialogOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.9);
    }
}

.dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    border-bottom: 1px solid var(--border-color);
}

.dialog-title {
    font-size: 24px;
    font-weight: 700;
}

.dialog-header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.dialog-help {
    height: 36px;
    padding: 0 12px;
    background: rgba(139, 92, 246, 0.15);
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: var(--purple-primary);
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    white-space: nowrap;
    font-weight: 500;
}

.dialog-help:hover {
    background: rgba(139, 92, 246, 0.25);
    border-color: rgba(139, 92, 246, 0.5);
    color: var(--purple-primary);
}

.dialog-close {
    width: 40px;
    height: 40px;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 24px;
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    transform-origin: center center;
}

.dialog-close:hover {
    color: #ef4444;
    transform: rotate(180deg);
}

.dialog-content {
    padding: 24px 24px 0 24px;
    overflow: hidden;
    max-height: calc(90vh - 180px);
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 自定义滚动条（对话框内） */
.dialog-content::-webkit-scrollbar {
    width: 6px;
}

.dialog-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.dialog-content::-webkit-scrollbar-thumb {
    background: var(--purple-primary);
    border-radius: 3px;
}

/* 对话框底部按钮区域 */
.dialog-footer {
    border-top: 1px solid var(--border-color);
    padding: 20px 24px;
    background: var(--bg-secondary);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    flex-shrink: 0;
}

/* 对话框按钮样式 */
.dialog-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
}

.action-btn {
    height: 48px;
    padding: 0 24px;
    border: none;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 120px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.action-btn.primary {
    background: linear-gradient(135deg,
        var(--purple-primary) 0%,
        var(--purple-light) 100%);
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: white;
    box-shadow:
        0 4px 16px rgba(139, 92, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 移除action-btn.primary的悬浮动画 */

.action-btn.secondary {
    background: linear-gradient(135deg,
        rgba(139, 92, 246, 0.08) 0%,
        rgba(167, 139, 250, 0.05) 100%);
    border: 1px solid rgba(139, 92, 246, 0.2);
    color: var(--purple-light);
}

/* 移除action-btn.secondary的悬浮动画 */

.action-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* 按钮加载状态 */
.action-btn .btn-content {
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
    transform: scale(1);
}

.action-btn .btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-btn.loading .btn-loading {
    display: flex !important;
    opacity: 1;
}

.action-btn.loading .btn-content {
    opacity: 0;
    transform: scale(0.95);
    pointer-events: none;
}

.action-btn .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-top-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.action-btn .loading-text {
    color: inherit;
    font-size: 15px;
    font-weight: 600;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 设置对话框专用样式 */
.settings-content {
    min-width: 600px;
    display: flex;
    flex-direction: column;
    height: min(500px, 70vh); /* 固定高度，但不超过屏幕的70%，避免切换标签时高度变化 */
    overflow: hidden;
}

.settings-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
    margin-right: -6px;
    padding-right: 6px;
    min-height: 0; /* 确保flex子项可以收缩 */
}

/* 确保标签页内容可以正常滚动 */
.tab-content {
    min-height: 0;
    overflow: visible;
}

/* 为高级设置添加底部间距，确保最后一项可见 */
#advancedTab {
    padding-bottom: 20px;
}

#emailTab {
    padding-bottom: 20px;
}

.settings-tabs {
    display: flex;
    gap: 16px;
    margin-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    padding: 12px 24px;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    font-size: 14px;
    font-weight: 500;
}

.tab-btn.active {
    color: var(--purple-primary);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--purple-primary);
}

/* 移除tab-btn的悬浮动画 */

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="number"] {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: var(--purple-primary);
    background: rgba(139, 92, 246, 0.08);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.form-group input::placeholder {
    color: var(--text-muted);
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.radio-group label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 12px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid transparent;
    border-radius: 12px;
    transition: all 0.3s ease;
    margin-bottom: 0;
}

.radio-group label:hover {
    background: rgba(139, 92, 246, 0.05);
    border-color: rgba(139, 92, 246, 0.2);
}

.radio-group input[type="radio"] {
    margin-right: 12px;
    accent-color: var(--purple-primary);
}

.radio-group input[type="radio"]:checked + span {
    color: var(--purple-light);
}

.email-config-section {
    margin-top: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(139, 92, 246, 0.1);
    border-radius: 16px;
}

.email-config-section h5 {
    margin-bottom: 16px;
    color: var(--purple-light);
    font-size: 16px;
    font-weight: 600;
}

.editor-switch {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.editor-switch label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 16px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid transparent;
    border-radius: 12px;
    transition: all 0.3s ease;
    margin-bottom: 0;
}

.editor-switch label:hover {
    background: rgba(139, 92, 246, 0.05);
    border-color: rgba(139, 92, 246, 0.2);
    transform: translateY(-1px);
}

.editor-switch input[type="radio"] {
    margin-right: 12px;
    accent-color: var(--purple-primary);
}

.editor-switch input[type="radio"]:checked + span {
    color: var(--purple-light);
    font-weight: 600;
}

/* 现代化开关样式 */
.switch-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(139, 92, 246, 0.1);
    border-radius: 12px;
    margin-top: 16px;
    transition: all 0.2s ease;
}

/* 移除switch-group的悬浮动画 */

.switch-group .switch-label {
    color: var(--text-primary);
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    margin-bottom: 0;
    flex: 1;
    pointer-events: none;
}

.switch-group .switch-description {
    color: var(--text-muted);
    font-size: 13px;
    margin-top: 4px;
    pointer-events: none;
}

/* 简化的开关按钮样式 */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    flex-shrink: 0;
    cursor: pointer;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #333;
    transition: 0.2s;
    border-radius: 10px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background: white;
    transition: 0.2s;
    border-radius: 50%;
}

/* 修复：确保checked状态优先级高于hover */
input:checked + .toggle-slider {
    background: var(--purple-primary) !important;
}

input:checked + .toggle-slider:before {
    transform: translateX(20px) !important;
}

/* hover状态 - 分别处理选中和未选中状态 */
input:not(:checked) + .toggle-slider:hover {
    background: #444 !important;
}

input:checked + .toggle-slider:hover {
    background: var(--purple-light) !important;
}

/* 统一的设置项样式 */
.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(139, 92, 246, 0.1);
    border-radius: 12px;
    margin-bottom: 16px;
    transition: all 0.3s ease;
}

/* 移除setting-item的悬浮动画 */

.setting-item .setting-label {
    color: var(--text-primary);
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 0;
    flex: 1;
}

.setting-item .setting-description {
    color: var(--text-muted);
    font-size: 13px;
    margin-top: 4px;
}

.setting-item .setting-control {
    flex-shrink: 0;
    margin-left: 16px;
}

/* 输入框在设置项中的样式 */
.setting-item input[type="text"],
.setting-item input[type="email"],
.setting-item input[type="password"],
.setting-item input[type="number"] {
    width: 200px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.3s ease;
}

.setting-item input:focus {
    outline: none;
    border-color: var(--purple-primary);
    background: rgba(139, 92, 246, 0.1);
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
}

/* 单选框组的现代化样式 */
.radio-setting-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.radio-setting-item {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(139, 92, 246, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}

.radio-setting-item:hover {
    background: rgba(139, 92, 246, 0.05);
    border-color: rgba(139, 92, 246, 0.2);
}

.radio-setting-item input[type="radio"] {
    margin-right: 12px;
    accent-color: var(--purple-primary);
}

.radio-setting-item.checked {
    background: rgba(139, 92, 246, 0.1);
    border-color: var(--purple-primary);
}

/* 单选框项中的标签和描述样式 - 与setting-item保持一致 */
.radio-setting-item .setting-label {
    color: var(--text-primary);
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 0;
}

.radio-setting-item .setting-description {
    color: var(--text-muted);
    font-size: 13px;
    margin-top: 4px;
}

/* 分组标题样式 */
.setting-group-title {
    color: var(--purple-light);
    font-size: 16px;
    font-weight: 600;
    margin: 24px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.2);
}

/* 传统复选框样式（备用） */
.checkbox-group {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 16px;
}

.checkbox-group input[type="checkbox"] {
    accent-color: var(--purple-primary);
    transform: scale(1.2);
}

.checkbox-group label {
    color: var(--text-secondary);
    font-size: 14px;
    cursor: pointer;
    margin-bottom: 0;
}

.form-row {
    display: flex;
    gap: 16px;
}

.form-row .form-group {
    flex: 1;
}

.test-connection-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(167, 139, 250, 0.05));
    border: 1px solid var(--purple-primary);
    border-radius: 8px;
    color: var(--purple-light);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 8px;
}

.test-connection-btn:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(167, 139, 250, 0.1));
    transform: translateY(-1px);
}

/* 隐藏元素样式 */
.hidden {
    display: none !important;
}

/* 对话框内容区域滚动条优化 */
.settings-body::-webkit-scrollbar {
    width: 6px;
}

.settings-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.settings-body::-webkit-scrollbar-thumb {
    background: var(--purple-primary);
    border-radius: 3px;
}

.settings-body::-webkit-scrollbar-thumb:hover {
    background: var(--purple-light);
}

/* 高级设置标题样式 */
.tab-content h4 {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.2);
}

.tab-content h5 {
    color: var(--purple-light);
    font-size: 16px;
    font-weight: 600;
    margin: 24px 0 16px 0;
}

/* 表单分组样式 */
.form-section {
    margin-bottom: 32px;
}

.form-section:last-child {
    margin-bottom: 0;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .settings-content {
        min-width: 90vw;
        height: 80vh;
    }

    .form-row {
        flex-direction: column;
    }

    .dialog-actions {
        flex-direction: column;
        gap: 12px;
    }

    .action-btn {
        width: 100%;
    }

    .switch-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .switch {
        align-self: flex-end;
    }
}

/* 隐藏元素样式 */
.hidden {
    display: none !important;
}

/* 对话框内容区域滚动条优化 */
.settings-body::-webkit-scrollbar {
    width: 6px;
}

.settings-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.settings-body::-webkit-scrollbar-thumb {
    background: var(--purple-primary);
    border-radius: 3px;
}

.settings-body::-webkit-scrollbar-thumb:hover {
    background: var(--purple-light);
}

/* 成功和错误状态的按钮样式 */
.action-btn.success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
}

.action-btn.error {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
}

/* 按钮禁用状态优化 */
.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
    pointer-events: none;
}

/* 表单验证样式 */
.form-group.error input {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.05);
}

.form-group.error label {
    color: #ef4444;
}

.form-group.success input {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.05);
}

/* 加载状态的额外样式 */
.action-btn.loading {
    pointer-events: none;
}

.action-btn.loading .loading-spinner {
    animation: spin 1s linear infinite;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .settings-content {
        min-width: 90vw;
        height: 80vh;
    }

    .form-row {
        flex-direction: column;
    }

    .dialog-actions {
        flex-direction: column;
        gap: 12px;
    }

    .action-btn {
        width: 100%;
    }
}

/* 关于页面动画 - 移除可能导致位置变化的动画 */
@keyframes aboutIconGlow {
    0%, 100% {
        box-shadow:
            0 8px 24px rgba(139, 92, 246, 0.3),
            0 4px 12px rgba(0, 0, 0, 0.2);
    }
    50% {
        box-shadow:
            0 12px 32px rgba(139, 92, 246, 0.5),
            0 6px 16px rgba(0, 0, 0, 0.3);
    }
}

/* 移除可能导致位置变化的动画 */
/* .about-icon 动画已被移除，避免影响布局 */

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-title {
        font-size: 48px;
    }

    .features-section {
        padding: 40px 20px;
        gap: 40px;
    }

    .features-row-1 {
        flex-direction: column;
        gap: 40px;
    }

    .feature-card {
        width: 100%;
        max-width: 400px;
    }

    .reset-container,
    .email-workflow-container {
        width: 100%;
        max-width: 400px;
    }

    .augment-account-container {
        width: 100%;
        max-width: 400px;
    }

    .dialog {
        min-width: 90%;
    }

    /* 关于页面响应式 - 移动端调整 */
    .about-section {
        padding: 30px 15px 50px;
    }

    .about-header {
        top: 60px;
        left: 20px;
    }

    .about-title {
        font-size: 20px;
    }

    .about-icon {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }

    .app-main-info {
        top: 60px;
        right: 30px;
    }

    .app-name {
        font-size: 28px;
    }

    .app-developer {
        font-size: 10px;
    }

    /* 移动端工作环境调整 */
    .coding-workspace {
        top: 100px;
        left: 140px;
        transform: translateX(-30px) rotate(-2deg) scale(0.8);
    }

    .workspace-container {
        width: 250px;
        height: 160px;
    }

    .main-monitor {
        top: 15px;
        left: 30px;
        transform: scale(0.7);
    }

    .secondary-monitor {
        top: 20px;
        left: 120px;
        transform: scale(0.6);
    }

    .keyboard {
        bottom: 30px;
        left: 40px;
        transform: scale(0.7);
    }

    .mouse {
        bottom: 35px;
        left: 130px;
        transform: scale(0.7);
    }

    .coffee-cup-large {
        bottom: 60px;
        left: 5px;
        transform: scale(0.6);
    }

    .books-stack {
        bottom: 80px;
        left: 160px;
        transform: scale(0.6);
    }

    .plant {
        bottom: 70px;
        left: 200px;
        transform: scale(0.6);
    }

    .floating-code-enhanced {
        width: 250px;
        height: 160px;
    }

    .code-particle {
        font-size: 8px;
    }

    .code-particle.large {
        font-size: 12px;
    }

    .code-particle.medium {
        font-size: 10px;
    }

    .code-particle.small {
        font-size: 8px;
    }

    /* 移动端信息项重新定位 */
    .copyright-item {
        bottom: 120px;
        right: 20px;
        transform: rotate(0deg);
    }

    .email-item {
        top: 120px;
        right: 20px;
        transform: rotate(0deg);
    }

    .qq-item {
        top: 160px;
        right: 20px;
        transform: rotate(0deg);
    }

    .thanks-message {
        bottom: 15px;
        right: 20px;
        transform: rotate(0deg);
    }

    .info-item {
        min-width: 160px;
    }

    .info-value {
        font-size: 12px;
    }
}

/* 密码输入框容器样式 */
.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-container input {
    flex: 1;
    padding-right: 45px; /* 为眼睛按钮留出空间 */
}

.password-toggle-btn {
    position: absolute;
    right: 12px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
}

/* 移除password-toggle-btn的悬浮动画 */

.eye-icon {
    width: 16px;
    height: 16px;
    color: var(--text-secondary);
    transition: color 0.2s ease;
}

/* ===== Augment账号管理组件样式 ===== */

/* 账号管理容器 */
.augment-account-container {
    position: relative;
    width: 960px; /* 重置卡片(380px) + 邮箱工作流(520px) + gap(60px) = 960px */
    opacity: 0;
    transform: translateY(100px);
    margin: 0; /* 移除边距，让flex布局控制 */
}

.augment-account-card {
    position: relative;
    background: linear-gradient(145deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(139, 92, 246, 0.03) 30%,
        rgba(15, 15, 15, 0.95) 100%);
    border: 1px solid rgba(139, 92, 246, 0.15);
    border-radius: 28px;
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    padding: 0;
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(139, 92, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 450px; /* 调整卡片高度 - 适中的高度提供良好的视觉平衡 */
}

.augment-account-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(139, 92, 246, 0.6),
        rgba(167, 139, 250, 0.4),
        transparent);
}

/* 标题区域 */
.account-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 32px 20px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.1);
}

.account-header .header-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(167, 139, 250, 0.1));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--purple-light);
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.account-header .header-content {
    flex: 1;
    margin-left: 20px;
}

/* 头部控制区域 */
.header-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 账号选择下拉框 */
.account-select-container {
    position: relative;
    width: 280px; /* 增加选择框宽度 */
    flex-shrink: 0; /* 防止被压缩 */
}

.account-select {
    width: 100%;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(167, 139, 250, 0.05));
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 10px;
    padding: 8px 32px 8px 12px;
    color: var(--text-primary);
    font-size: 11px; /* 减小字体大小 */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    height: 42px; /* 明确设置高度与按钮一致 */
    box-sizing: border-box; /* 确保padding包含在高度内 */
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏溢出内容 */
    text-overflow: ellipsis; /* 显示省略号 */
}

.account-select:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(167, 139, 250, 0.1));
    border-color: rgba(139, 92, 246, 0.4);
}

.account-select:focus {
    outline: none;
    border-color: var(--purple-light);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.account-select option {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 6px 12px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace; /* 使用等宽字体 */
    font-size: 11px; /* 与选择框保持一致的字体大小 */
    white-space: pre; /* 保持空格格式 */
}

.select-arrow {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: var(--text-secondary);
    transition: transform 0.3s ease;
}

.account-select:focus + .select-arrow {
    transform: translateY(-50%) rotate(180deg);
}

/* 自定义下拉框样式 */
.custom-select {
    position: relative;
    width: 100%;
}

.select-trigger {
    width: 100%;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(167, 139, 250, 0.05));
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 10px;
    padding: 8px 32px 8px 12px;
    color: var(--text-primary);
    font-size: 11px; /* 减小字体大小 */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 42px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.select-trigger:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(167, 139, 250, 0.1));
    border-color: rgba(139, 92, 246, 0.4);
}

.select-text {
    flex: 1;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif; /* 使用默认字体 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 220px; /* 增加选择框文本最大宽度 */
}

.custom-select .select-arrow {
    color: var(--text-secondary);
    transition: transform 0.3s ease;
    flex-shrink: 0;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    font-size: 20px; /* 增大下拉图标 */
}

.custom-select.open .select-arrow {
    transform: translateY(-50%) rotate(180deg);
}

.select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--bg-secondary);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 10px;
    margin-top: 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    /* 让下拉框独立计算宽度，不受选择框影响 */
    width: max-content;
    min-width: 300px; /* 设置最小宽度确保内容显示完整 */
    max-width: 500px; /* 设置最大宽度避免过宽 */
}

.custom-select.open .select-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.select-option {
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px; /* 减小字体大小 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(139, 92, 246, 0.1);
}

.select-option:last-child {
    border-bottom: none;
}

.select-option:hover {
    background: rgba(139, 92, 246, 0.1);
}

.select-option.selected {
    color: var(--purple-light);
}

.option-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    min-width: 0; /* 允许flex子元素收缩 */
}

.account-info {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
}

.selected-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--purple-light);
    margin-right: 8px;
    flex-shrink: 0;
    box-shadow:
        0 0 6px var(--purple-light),
        0 0 12px rgba(139, 92, 246, 0.6),
        0 0 18px rgba(139, 92, 246, 0.4);
    animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
    from {
        box-shadow:
            0 0 6px var(--purple-light),
            0 0 12px rgba(139, 92, 246, 0.6),
            0 0 18px rgba(139, 92, 246, 0.4);
    }
    to {
        box-shadow:
            0 0 8px var(--purple-light),
            0 0 16px rgba(139, 92, 246, 0.8),
            0 0 24px rgba(139, 92, 246, 0.6);
    }
}

.account-email {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
    max-width: 240px; /* 限制邮箱最大宽度，约30个字符 */
}

.account-time {
    flex-shrink: 0;
    margin-left: 12px;
    color: var(--text-secondary);
    font-size: 10px;
}

.delete-btn {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 6px;
    color: #ef4444;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 8px;
    flex-shrink: 0;
}

.delete-btn:hover {
    background: rgba(239, 68, 68, 0.3);
    border-color: rgba(239, 68, 68, 0.5);
    transform: scale(1.1);
}

.delete-btn svg {
    width: 12px;
    height: 12px;
}

/* ===== 删除账号确认对话框样式 ===== */
.delete-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.delete-dialog-overlay.show {
    opacity: 1;
    visibility: visible;
}

.delete-dialog {
    background: var(--bg-primary);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 20px;
    width: 480px;
    max-width: 90vw;
    overflow: hidden;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.6),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.delete-dialog-overlay.show .delete-dialog {
    transform: scale(1) translateY(0);
}

.delete-dialog-header {
    display: flex;
    align-items: center;
    padding: 24px 24px 20px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.1);
    gap: 16px;
}

.delete-dialog-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(167, 139, 250, 0.1));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--purple-light);
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.delete-dialog-title {
    flex: 1;
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.delete-dialog-content {
    padding: 24px;
}

.delete-dialog-info {
    margin-bottom: 20px;
    text-align: center;
}

.delete-dialog-email-label {
    font-size: 16px;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-weight: 500;
}

.delete-dialog-email {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    background: rgba(107, 114, 128, 0.1);
    border: 1px solid rgba(107, 114, 128, 0.2);
    border-radius: 8px;
    padding: 12px 16px;
    margin: 8px 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    word-break: break-all;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.delete-dialog-notice {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: 8px;
    padding: 12px;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 24px;
}

.delete-dialog-notice svg {
    color: #f59e0b;
    flex-shrink: 0;
}

.delete-dialog-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.delete-dialog-btn {
    height: 40px;
    padding: 0 20px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    min-width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.delete-dialog-btn.cancel {
    background: rgba(107, 114, 128, 0.15);
    color: var(--text-secondary);
    border-color: rgba(107, 114, 128, 0.3);
}

.delete-dialog-btn.cancel:hover {
    background: rgba(107, 114, 128, 0.25);
    border-color: rgba(107, 114, 128, 0.4);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.delete-dialog-btn.confirm {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.15), rgba(239, 68, 68, 0.1));
    color: #dc2626;
    border-color: rgba(220, 38, 38, 0.25);
}

.delete-dialog-btn.confirm:hover {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.25), rgba(239, 68, 68, 0.15));
    border-color: rgba(220, 38, 38, 0.4);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.25);
}

.account-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    background: linear-gradient(135deg, var(--text-primary), var(--purple-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.account-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
    opacity: 0.8;
}

.token-input-btn {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(167, 139, 250, 0.1));
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 12px;
    padding: 10px 20px;
    color: var(--purple-light);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    height: 42px; /* 与选择框高度一致 */
}

.token-input-btn:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.25), rgba(167, 139, 250, 0.15));
    border-color: rgba(139, 92, 246, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

.add-account-btn {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(167, 139, 250, 0.1));
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 12px;
    padding: 10px 12px;
    color: var(--purple-light);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 42px; /* 与选择框高度一致 */
    position: relative; /* 为绝对定位的加载动画提供参考 */
}

.add-account-btn:hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.25), rgba(167, 139, 250, 0.15));
    border-color: rgba(139, 92, 246, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

.add-account-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 添加账号按钮加载状态 */
.add-account-btn.loading {
    cursor: not-allowed;
    transform: none;
}

.add-account-btn .btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.add-account-btn .btn-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s ease, visibility 0.2s ease;
}

.add-account-btn .btn-loading.hidden {
    opacity: 0;
    visibility: hidden;
}

.add-account-btn .loading-spinner-small {
    border: 2px solid rgba(139, 92, 246, 0.2);
    border-top: 2px solid var(--purple-light);
}

/* 主要内容区域 */
.account-main-content {
    display: flex;
    height: calc(100% - 92px); /* 减去header高度 */
    padding: 0;
    min-height: 280px; /* 调整最小高度适应新的卡片高度 */
}

/* 左侧积分可视化区域 (60%) */
.credits-visualization {
    flex: 0 0 60%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    border-right: 1px solid rgba(139, 92, 246, 0.1);
}

.credits-circle-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}

.credits-circle {
    position: relative;
    width: 180px;
    height: 180px;
}

.circle-progress {
    transform: rotate(-90deg);
}

.circle-progress-bar {
    stroke-dasharray: 471; /* 2 * π * 75 */
    stroke-dashoffset: 471;
    transition: stroke-dashoffset 1.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.circle-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.credits-percentage {
    font-size: 32px;
    font-weight: 800;
    color: var(--purple-light);
    line-height: 1;
    margin-bottom: 4px;
}

.credits-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.credits-details {
    text-align: center;
}

.credits-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-size: 18px;
    font-weight: 600;
}

.credits-used {
    color: var(--purple-light);
}

.credits-separator {
    color: var(--text-secondary);
}

.credits-total {
    color: var(--text-secondary);
}

/* 右侧账号信息区域 (40%) */
.account-info-section {
    flex: 0 0 40%;
    display: flex;
    flex-direction: column;
    padding: 16px 24px 24px 24px; /* 底部增加更多内边距为按钮留空间 */
    gap: 12px; /* 减少间距为按钮留空间 */
}

/* 账号数据网格 */
.account-data-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    flex: 0 1 auto; /* 不占用额外空间，让按钮区域有足够空间 */
}

.data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 14px;
    background: rgba(139, 92, 246, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.1);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.data-item:hover {
    background: rgba(139, 92, 246, 0.08);
    border-color: rgba(139, 92, 246, 0.2);
}

.data-label {
    font-size: 13px;
    color: var(--text-secondary);
    font-weight: 500;
    min-width: 60px;
}

.data-value {
    font-size: 13px;
    color: var(--text-primary);
    font-weight: 600;
    text-align: right;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}



/* 操作按钮区域 */
.account-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 16px; /* 增加顶部间距，因为现在只有一个按钮行 */
    flex-shrink: 0; /* 防止按钮区域被挤压 */
}

.account-actions-row {
    display: flex;
    gap: 10px;
}

.account-btn {
    flex: 1;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.15), rgba(167, 139, 250, 0.1));
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: 10px;
    padding: 10px 14px;
    color: var(--purple-light);
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 40px; /* 确保按钮有固定的最小高度 */
}

.account-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: rgba(139, 92, 246, 0.05);
    border-color: rgba(139, 92, 246, 0.1);
    color: var(--text-secondary);
}

.account-btn:not(:disabled):hover {
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.25), rgba(167, 139, 250, 0.15));
    border-color: rgba(139, 92, 246, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

.account-btn .btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.account-btn .btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.account-btn .btn-loading.hidden {
    opacity: 0;
    visibility: hidden;
}

.account-btn.loading .btn-content {
    opacity: 0;
    visibility: hidden;
}

.account-btn.loading .btn-loading {
    opacity: 1;
    visibility: visible;
}

/* 查询按钮特殊样式 */
.query-btn.refresh-mode {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(34, 197, 94, 0.1));
    border-color: rgba(34, 197, 94, 0.3);
    color: #22c55e;
}

.query-btn.refresh-mode:not(:disabled):hover {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.25), rgba(34, 197, 94, 0.15));
    border-color: rgba(34, 197, 94, 0.5);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
}

/* 刷新模式的加载动画 - 绿色主题 */
.query-btn.refresh-mode .loading-spinner-small {
    border: 2px solid rgba(34, 197, 94, 0.2);
    border-top: 2px solid #22c55e;
}

/* 切换社区计划按钮样式 - 蓝色主题 */
.switch-plan-btn {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.1)) !important;
    border-color: rgba(59, 130, 246, 0.3) !important;
    color: #3b82f6 !important;
}

.switch-plan-btn:not(:disabled):hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.25), rgba(59, 130, 246, 0.15)) !important;
    border-color: rgba(59, 130, 246, 0.5) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2) !important;
}

.switch-plan-btn .loading-spinner-small {
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-top: 2px solid #3b82f6;
    animation: spin 1s linear infinite;
}



/* 容器悬停效果 */
.augment-account-container:hover .augment-account-card {
    border-color: rgba(139, 92, 246, 0.25);
    transform: translateY(-3px);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(139, 92, 246, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

/* 加载动画 */
.loading-spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(139, 92, 246, 0.2);
    border-top: 2px solid var(--purple-light);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* ===== Token输入对话框样式 ===== */
.augment-token-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.augment-token-dialog-overlay.show {
    opacity: 1;
    visibility: visible;
}

.augment-token-dialog {
    background: var(--bg-primary);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 20px;
    width: 500px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(139, 92, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.augment-token-dialog-overlay.show .augment-token-dialog {
    transform: scale(1) translateY(0);
}

.augment-token-dialog-header {
    display: flex;
    align-items: center;
    padding: 24px 24px 20px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.1);
    gap: 16px;
}

.augment-token-dialog-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(167, 139, 250, 0.1));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--purple-light);
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.augment-token-dialog-title {
    flex: 1;
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.augment-token-dialog-help {
    height: 36px;
    padding: 0 12px;
    background: rgba(139, 92, 246, 0.15);
    border: 1px solid rgba(139, 92, 246, 0.3);
    color: var(--purple-primary);
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 14px;
    white-space: nowrap;
    font-weight: 500;
}

.augment-token-dialog-help:hover {
    background: rgba(139, 92, 246, 0.25);
    border-color: rgba(139, 92, 246, 0.4);
    color: var(--purple-light);
}

.augment-token-dialog-content {
    padding: 24px;
}

.augment-token-input-group {
    margin-bottom: 24px;
}

.augment-token-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.augment-token-input {
    width: 100%;
    background: rgba(139, 92, 246, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 12px;
    padding: 12px 16px;
    color: var(--text-primary);
    font-size: 14px;
    font-family: 'Consolas', 'Monaco', monospace;
    resize: vertical;
    min-height: 100px;
    transition: all 0.3s ease;
}

.augment-token-input:focus {
    outline: none;
    border-color: var(--purple-primary);
    background: rgba(139, 92, 246, 0.08);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.augment-token-input::placeholder {
    color: var(--text-muted);
}

.augment-token-hint {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(139, 92, 246, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.1);
    border-radius: 8px;
    font-size: 12px;
    color: var(--text-secondary);
}

.augment-token-dialog-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.augment-token-btn-cancel,
.augment-token-btn-save {
    padding: 12px 24px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid;
}

.augment-token-btn-cancel {
    background: transparent;
    border-color: rgba(139, 92, 246, 0.2);
    color: var(--text-secondary);
}

.augment-token-btn-cancel:hover {
    background: rgba(139, 92, 246, 0.1);
    border-color: rgba(139, 92, 246, 0.3);
    color: var(--text-primary);
}

.augment-token-btn-save {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light));
    border-color: var(--purple-primary);
    color: white;
}

.augment-token-btn-save:hover {
    background: linear-gradient(135deg, var(--purple-dark), var(--purple-primary));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

/* ===== 版本检查对话框样式 - 完全复制原版Qt Theme ===== */

/* 免责声明对话框 */
.disclaimer-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.disclaimer-dialog-overlay.show {
    display: flex;
    opacity: 1;
}

.disclaimer-dialog {
    background: var(--theme-primary);
    border: 1px solid var(--theme-glass-border);
    border-radius: var(--theme-border-radius);
    width: 500px;
    max-width: 90vw;
    max-height: 80vh;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
}

.disclaimer-dialog-overlay.show .disclaimer-dialog {
    transform: scale(1);
}

.disclaimer-dialog-content {
    padding: 25px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
    overflow: hidden;
}

.disclaimer-dialog-title {
    color: var(--theme-text-primary);
    font-weight: bold;
    font-size: var(--theme-font-size-title);
    margin: 0;
    font-family: var(--theme-font-family);
}

.disclaimer-dialog-text {
    background: var(--theme-primary);
    color: var(--theme-text-secondary);
    border: none;
    font-size: var(--theme-font-size-normal);
    font-family: var(--theme-font-family);
    min-height: 200px;
    overflow-y: auto;
    padding: 0;
    margin: 0;
    flex: 1;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
}

.disclaimer-dialog-text::-webkit-scrollbar {
    width: 12px;
}

.disclaimer-dialog-text::-webkit-scrollbar-track {
    background: var(--theme-card-level-1);
}

.disclaimer-dialog-text::-webkit-scrollbar-thumb {
    background: var(--theme-border);
    border-radius: 6px;
    min-height: 20px;
}

.disclaimer-dialog-separator {
    height: 1px;
    background: var(--theme-border);
    margin: 0;
}

.disclaimer-dialog-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    align-items: center;
}

.disclaimer-dialog-btn {
    background: var(--theme-accent);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    min-width: 80px;
    cursor: pointer;
    font-family: var(--theme-font-family);
    font-size: var(--theme-font-size-normal);
    transition: background-color 0.2s ease;
}

.disclaimer-dialog-btn:hover {
    background: var(--theme-accent-hover);
}

.disclaimer-dialog-btn:active {
    background: var(--theme-accent-pressed);
}

.disclaimer-dialog-btn.secondary {
    background: var(--theme-card-level-1);
    color: var(--theme-text-secondary);
    border: 1px solid var(--theme-border);
}

.disclaimer-dialog-btn.secondary:hover {
    background: var(--theme-hover);
    color: var(--theme-text-primary);
}

.disclaimer-dialog-btn.secondary:active {
    background: var(--theme-card-level-2);
}

/* 验证对话框 */
.verification-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.verification-dialog-overlay.show {
    display: flex;
    opacity: 1;
}

.verification-dialog {
    background: var(--theme-primary);
    border: 1px solid var(--theme-glass-border);
    border-radius: var(--theme-border-radius);
    width: 450px;
    min-height: 380px;
    max-width: 90vw;
    max-height: 80vh;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
}

.verification-dialog-overlay.show .verification-dialog {
    transform: scale(1);
}

.verification-dialog-content {
    padding: 20px 20px 15px 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
}

.verification-dialog-main {
    display: flex;
    gap: 20px;
    flex: 1;
}

.verification-dialog-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: center;
}

.verification-dialog-status {
    font-size: 16px;
    font-weight: bold;
    color: var(--theme-text-primary);
    text-align: center;
    padding: 5px 0;
    margin-bottom: 10px;
    font-family: var(--theme-font-family);
}

.verification-code-input {
    display: flex;
    gap: 5px;
    justify-content: center;
    align-items: center;
    margin: 10px 0;
}

.verification-code-digit {
    width: 40px;
    height: 40px;
    background: var(--theme-card-level-1);
    border: 1px solid var(--theme-border);
    border-radius: 6px;
    color: var(--theme-text-primary);
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    font-family: var(--theme-font-family);
    outline: none;
    transition: border-color 0.3s ease, background-color 0.2s ease, border-width 0.3s ease, box-shadow 0.3s ease, transform 0.2s ease;
    caret-color: transparent; /* 默认隐藏光标 */
    user-select: none; /* 禁止文本选择 */
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE/Edge */
}

/* 当输入框为空且获得焦点时显示光标 */
.verification-code-digit:focus:placeholder-shown {
    caret-color: var(--theme-text-primary);
}

/* 当输入框有内容时隐藏光标 */
.verification-code-digit:not(:placeholder-shown) {
    caret-color: transparent;
}

.verification-code-digit:focus {
    border: 3px solid #FFFFFF !important; /* 白色边框，更明显 */
    background: rgba(255, 255, 255, 0.05) !important; /* 淡白色背景 */
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.4) !important; /* 白色发光效果 */
    transform: scale(1.05) !important; /* 轻微放大效果 */
}

/* 隐藏文本选中效果 */
.verification-code-digit::selection {
    background: transparent;
}

.verification-code-digit::-moz-selection {
    background: transparent;
}

.verification-code-digit.error {
    border-color: var(--theme-error);
    background: rgba(255, 99, 71, 0.1);
    animation: shake 0.3s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

.verification-dialog-right {
    width: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.verification-qr-code {
    width: 120px;
    height: 120px;
    background: var(--theme-card-level-1);
    border: 1px solid var(--theme-border);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--theme-text-secondary);
    font-size: 12px;
    text-align: center;
}

.verification-qr-status {
    font-size: 12px;
    color: var(--theme-text-secondary);
    text-align: center;
    font-family: var(--theme-font-family);
}

.verification-dialog-actions {
    border-top: 1px solid var(--theme-border);
    padding: 15px 20px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    align-items: center;
}

/* 错误对话框 */
.error-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.error-dialog-overlay.show {
    display: flex;
    opacity: 1;
}

.error-dialog {
    background: var(--theme-primary);
    border: 1px solid var(--theme-glass-border);
    border-radius: var(--theme-border-radius);
    width: 400px;
    max-width: 90vw;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.error-dialog-overlay.show .error-dialog {
    transform: scale(1);
}

.error-dialog-content {
    padding: 20px;
    text-align: center;
}

.error-dialog-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.error-dialog-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--theme-text-primary);
    margin-bottom: 10px;
    font-family: var(--theme-font-family);
}

.error-dialog-text {
    font-size: 14px;
    color: var(--theme-text-secondary);
    margin-bottom: 20px;
    line-height: 1.5;
    font-family: var(--theme-font-family);
}

.error-dialog-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.error-dialog-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: var(--theme-font-family);
    background: var(--theme-accent);
    color: white;
}

.error-dialog-btn:hover {
    background: var(--theme-accent-hover);
}

.error-dialog-btn.secondary {
    background: var(--theme-card-level-1);
    color: var(--theme-text-primary);
    border: 1px solid var(--theme-border);
}

.error-dialog-btn.secondary:hover {
    background: var(--theme-hover);
}

/* 维护对话框 */
.maintenance-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.maintenance-dialog-overlay.show {
    display: flex;
    opacity: 1;
}

.maintenance-dialog {
    background: var(--theme-primary);
    border: 1px solid var(--theme-glass-border);
    border-radius: var(--theme-border-radius);
    width: 400px;
    max-width: 90vw;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.maintenance-dialog-overlay.show .maintenance-dialog {
    transform: scale(1);
}

.maintenance-dialog-content {
    padding: 20px;
    text-align: center;
}

.maintenance-dialog-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--theme-text-primary);
    margin-bottom: 15px;
    font-family: var(--theme-font-family);
}

.maintenance-dialog-message {
    font-size: 14px;
    color: var(--theme-text-secondary);
    margin-bottom: 20px;
    line-height: 1.5;
    font-family: var(--theme-font-family);
}

.maintenance-dialog-actions {
    display: flex;
    justify-content: center;
}

.maintenance-dialog-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: var(--theme-font-family);
    background: var(--theme-accent);
    color: white;
}

.maintenance-dialog-btn:hover {
    background: var(--theme-accent-hover);
}

/* 强制更新对话框 */
.force-update-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.force-update-dialog-overlay.show {
    display: flex;
    opacity: 1;
}

.force-update-dialog {
    background: var(--theme-primary);
    border: 1px solid var(--theme-glass-border);
    border-radius: var(--theme-border-radius);
    width: 400px;
    max-width: 90vw;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.force-update-dialog-overlay.show .force-update-dialog {
    transform: scale(1);
}

.force-update-dialog-content {
    padding: 20px;
    text-align: center;
}

.force-update-dialog-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.force-update-dialog-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--theme-text-primary);
    margin-bottom: 10px;
    font-family: var(--theme-font-family);
}

.force-update-dialog-message {
    font-size: 14px;
    color: var(--theme-text-secondary);
    margin-bottom: 10px;
    line-height: 1.5;
    font-family: var(--theme-font-family);
}

.force-update-dialog-version {
    font-size: 12px;
    color: var(--theme-accent);
    margin-bottom: 20px;
    font-family: var(--theme-font-family);
}

.force-update-dialog-actions {
    display: flex;
    justify-content: center;
}

.force-update-dialog-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-family: var(--theme-font-family);
    background: var(--theme-accent);
    color: white;
}

.force-update-dialog-btn:hover {
    background: var(--theme-accent-hover);
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--theme-primary);
    border: 1px solid var(--theme-glass-border);
    border-radius: var(--theme-border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 10001;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    max-width: 400px;
    min-width: 300px;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification-content {
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.notification-icon {
    font-size: 20px;
    flex-shrink: 0;
}

.notification-message {
    flex: 1;
    font-size: 14px;
    color: var(--theme-text-primary);
    line-height: 1.4;
    font-family: var(--theme-font-family);
}

.notification-close {
    background: none;
    border: none;
    color: var(--theme-text-secondary);
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: var(--theme-hover);
    color: var(--theme-text-primary);
}

.notification-info {
    border-left: 4px solid var(--theme-accent);
}

.notification-warning {
    border-left: 4px solid #ffa500;
}

.notification-error {
    border-left: 4px solid var(--theme-error);
}

/* 对话框链接样式 */
.error-dialog-text a,
.maintenance-dialog-message a,
.force-update-dialog-message a {
    color: var(--theme-accent);
    text-decoration: none;
    transition: color 0.2s ease;
}

.error-dialog-text a:hover,
.maintenance-dialog-message a:hover,
.force-update-dialog-message a:hover {
    color: var(--theme-accent-hover);
    text-decoration: underline;
}

/* 对话框按钮禁用状态 */
.error-dialog-btn:disabled,
.maintenance-dialog-btn:disabled,
.force-update-dialog-btn:disabled,
.update-dialog-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 对话框响应式设计 */
@media (max-width: 480px) {
    .error-dialog,
    .maintenance-dialog,
    .force-update-dialog,
    .update-dialog {
        width: 95vw;
        margin: 10px;
    }

    .error-dialog-content,
    .maintenance-dialog-content,
    .force-update-dialog-content,
    .update-dialog-content {
        padding: 15px;
    }

    .error-dialog-actions,
    .maintenance-dialog-actions,
    .force-update-dialog-actions,
    .update-dialog-actions {
        flex-direction: column;
        gap: 8px;
    }

    .error-dialog-btn,
    .maintenance-dialog-btn,
    .force-update-dialog-btn,
    .update-dialog-btn {
        width: 100%;
    }
}

.verification-dialog-btn {
    background: var(--theme-accent);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    min-width: 80px;
    cursor: pointer;
    font-family: var(--theme-font-family);
    font-size: var(--theme-font-size-normal);
    transition: background-color 0.2s ease;
}

.verification-dialog-btn:hover {
    background: var(--theme-accent-hover);
}

.verification-dialog-btn:active {
    background: var(--theme-accent-pressed);
}

.verification-dialog-btn.secondary {
    background: var(--theme-card-level-1);
    color: var(--theme-text-secondary);
    border: 1px solid var(--theme-border);
}

.verification-dialog-btn.secondary:hover {
    background: var(--theme-hover);
    color: var(--theme-text-primary);
}

.verification-dialog-btn.secondary:active {
    background: var(--theme-card-level-2);
}

.verification-dialog-btn:disabled {
    background: #4D5057;
    color: #8A8D93;
    cursor: not-allowed;
}

/* 更新对话框 */
.update-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.update-dialog-overlay.show {
    display: flex;
    opacity: 1;
}

.update-dialog {
    background: var(--theme-primary);
    border: 1px solid var(--theme-glass-border);
    border-radius: var(--theme-border-radius);
    width: 500px;
    max-width: 90vw;
    max-height: 80vh;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
    transform: scale(0.9);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
}

.update-dialog-overlay.show .update-dialog {
    transform: scale(1);
}

.update-dialog-content {
    padding: 25px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
}

.update-dialog-title {
    color: var(--theme-text-primary);
    font-weight: bold;
    font-size: var(--theme-font-size-title);
    margin: 0;
    font-family: var(--theme-font-family);
    text-align: center;
}

.update-dialog-message {
    color: var(--theme-text-secondary);
    font-size: var(--theme-font-size-normal);
    font-family: var(--theme-font-family);
    line-height: 1.5;
    text-align: center;
}

.update-dialog-version-info {
    background: var(--theme-card-level-1);
    border: 1px solid var(--theme-border);
    border-radius: 6px;
    padding: 15px;
    margin: 10px 0;
}

.update-dialog-version-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px 0;
    font-family: var(--theme-font-family);
    font-size: var(--theme-font-size-normal);
}

.update-dialog-version-label {
    color: var(--theme-text-secondary);
}

.update-dialog-version-value {
    color: var(--theme-text-primary);
    font-weight: bold;
}

.update-dialog-separator {
    height: 1px;
    background: var(--theme-border);
    margin: 0;
}

.update-dialog-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
}

.update-dialog-btn {
    background: var(--theme-accent);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    min-width: 80px;
    cursor: pointer;
    font-family: var(--theme-font-family);
    font-size: var(--theme-font-size-normal);
    transition: background-color 0.2s ease;
}

.update-dialog-btn:hover {
    background: var(--theme-accent-hover);
}

.update-dialog-btn:active {
    background: var(--theme-accent-pressed);
}

.update-dialog-btn.secondary {
    background: var(--theme-card-level-1);
    color: var(--theme-text-secondary);
    border: 1px solid var(--theme-border);
}

.update-dialog-btn.secondary:hover {
    background: var(--theme-hover);
    color: var(--theme-text-primary);
}

.update-dialog-btn.secondary:active {
    background: var(--theme-card-level-2);
}

/* 维护模式对话框 */
.maintenance-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.maintenance-dialog-overlay.show {
    display: flex;
    opacity: 1;
}

.maintenance-dialog {
    background: var(--theme-primary);
    border: 1px solid var(--theme-glass-border);
    border-radius: var(--theme-border-radius);
    width: 450px;
    max-width: 90vw;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.7);
    transform: scale(0.9);
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
}

.maintenance-dialog-overlay.show .maintenance-dialog {
    transform: scale(1);
}

.maintenance-dialog-content {
    padding: 30px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    text-align: center;
}

.maintenance-dialog-title {
    color: var(--theme-warning);
    font-weight: bold;
    font-size: 20px;
    margin: 0;
    font-family: var(--theme-font-family);
}

.maintenance-dialog-message {
    color: var(--theme-text-secondary);
    font-size: var(--theme-font-size-normal);
    font-family: var(--theme-font-family);
    line-height: 1.5;
}

.maintenance-dialog-actions {
    display: flex;
    justify-content: center;
}

.maintenance-dialog-btn {
    background: var(--theme-warning);
    color: var(--theme-primary);
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: bold;
    cursor: pointer;
    font-family: var(--theme-font-family);
    font-size: var(--theme-font-size-normal);
    transition: background-color 0.2s ease;
}

.maintenance-dialog-btn:hover {
    background: #D4C374;
}

.maintenance-dialog-btn:active {
    background: #B8A05C;
}

/* 隐藏数字输入框的上下箭头 */
input[type="number"].no-spinner::-webkit-outer-spin-button,
input[type="number"].no-spinner::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"].no-spinner {
    -moz-appearance: textfield;
    appearance: textfield;
}

/* 禁用浏览器自动填充和建议 */
input[autocomplete="off"],
input[autocomplete="new-password"],
input[data-form-type="other"],
input[data-lpignore="true"],
input[data-1p-ignore="true"] {
    -webkit-autofill: none !important;
    -webkit-box-shadow: none !important;
    box-shadow: none !important;
    background-color: transparent !important;
}

/* 强制禁用自动填充 */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px var(--bg-secondary) inset !important;
    box-shadow: 0 0 0 30px var(--bg-secondary) inset !important;
    -webkit-text-fill-color: var(--text-primary) !important;
    color: var(--text-primary) !important;
    background-color: transparent !important;
    transition: background-color 5000s ease-in-out 0s;
}

input[autocomplete="off"]::-webkit-contacts-auto-fill-button,
input[autocomplete="new-password"]::-webkit-contacts-auto-fill-button,
input[data-form-type="other"]::-webkit-contacts-auto-fill-button {
    visibility: hidden;
    display: none !important;
    pointer-events: none;
    position: absolute;
    right: 0;
}

/* 禁用浏览器密码保存提示 */
input[type="password"][autocomplete="new-password"] {
    -webkit-autofill: none !important;
}

/* 隐藏浏览器自动填充下拉箭头 */
input::-webkit-calendar-picker-indicator {
    display: none !important;
}

input::-webkit-list-button {
    display: none !important;
}

/* 禁用拼写检查和自动更正的视觉提示 */
input[spellcheck="false"] {
    -webkit-text-decorations-in-effect: none !important;
    text-decoration: none !important;
}

input[autocorrect="off"] {
    -webkit-text-decorations-in-effect: none !important;
}

/* ===== 网络优化器组件样式 ===== */

/* 网络优化器容器 */
.network-optimizer-container {
    position: relative;
    width: 960px; /* 与账号管理卡片保持一致 */
    opacity: 0;
    transform: translateY(100px);
    margin: 0;
}

.network-optimizer-card {
    position: relative;
    background: linear-gradient(145deg,
        rgba(15, 15, 15, 0.95) 0%,
        rgba(139, 92, 246, 0.03) 30%,
        rgba(15, 15, 15, 0.95) 100%);
    border: 1px solid rgba(139, 92, 246, 0.15);
    border-radius: 28px;
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    padding: 0;
    overflow: hidden;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(139, 92, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 480px;
    height: auto;
}

.network-optimizer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(139, 92, 246, 0.6),
        rgba(167, 139, 250, 0.4),
        transparent);
}

/* 标题区域 */
.optimizer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 32px 20px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.1);
}

.optimizer-header .header-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(167, 139, 250, 0.1));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--purple-light);
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.optimizer-header .header-content {
    flex: 1;
    margin-left: 20px;
}

.optimizer-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    line-height: 1.2;
}

.optimizer-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
    opacity: 0.8;
}

/* 头部控制区域 */
.header-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.main-switch-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    transition: 0.3s;
    border-radius: 24px;
    border: 1px solid rgba(139, 92, 246, 0.2);
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background-color: rgba(255, 255, 255, 0.8);
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-light));
    border-color: var(--purple-primary);
}

input:checked + .slider:before {
    transform: translateX(26px);
    background-color: white;
}

.switch-label {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 500;
}

/* 内容区域 */
.optimizer-content {
    display: flex;
    flex-direction: column;
    padding: 24px 32px 32px;
    gap: 24px;
    min-height: 380px;
}

/* 行布局 */
.optimizer-row {
    display: flex;
    gap: 32px;
    width: 100%;
}

/* 第一行：代理设置和数据展示 */
.optimizer-row:first-child .optimizer-config-section,
.optimizer-row:first-child .optimizer-status-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* 确保两侧的config-group高度一致 */
.optimizer-row:first-child .config-group {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 第二行：优化设置 */
.optimizer-settings-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* 配置组 */
.config-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.config-label {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

/* 代理选项 */
.proxy-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.radio-option:hover {
    background: rgba(139, 92, 246, 0.05);
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 50%;
    position: relative;
    transition: all 0.2s ease;
}

.radio-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--purple-primary);
    background: var(--purple-primary);
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
}

.radio-text {
    font-size: 14px;
    color: var(--text-primary);
}

/* 自定义代理输入 */
.custom-proxy-input {
    margin-top: 8px;
}

.proxy-input {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.2s ease;
}

.proxy-input:focus {
    outline: none;
    border-color: var(--purple-primary);
    background: rgba(255, 255, 255, 0.08);
}

.proxy-input::placeholder {
    color: var(--text-secondary);
    opacity: 0.6;
}

/* 优化设置 */
.optimizer-settings {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.optimizer-settings-row {
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: stretch;
    width: 100%;
}

/* 优化设置中的每个设置项 */
.optimizer-settings-row .setting-item {
    flex: 1 1 50%; /* 强制每个项目占50%宽度 */
    max-width: calc(50% - 10px); /* 减去gap的一半 */
    min-width: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(139, 92, 246, 0.1);
    border-radius: 12px;
    transition: all 0.3s ease;
    height: 60px; /* 固定高度确保一致性 */
    box-sizing: border-box;
}

/* 确保设置项内的标签和控件样式一致 */
.optimizer-settings-row .setting-item .setting-label {
    color: var(--text-primary);
    font-size: 15px;
    font-weight: 500;
    margin: 0;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.optimizer-settings-row .setting-item .setting-control {
    flex-shrink: 0;
    display: flex;
    align-items: center;
}

/* 处理定时优化设置项的特殊布局 */
.optimizer-settings-row .setting-item .interval-setting {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 16px;
}

.optimizer-settings-row .setting-item .interval-input {
    width: 60px;
    padding: 6px 8px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 14px;
    text-align: center;
    -moz-appearance: textfield; /* Firefox隐藏spinner */
    appearance: textfield; /* 标准属性，实现兼容性 */
}

/* 隐藏Chrome/Safari的spinner */
.optimizer-settings-row .setting-item .interval-input::-webkit-outer-spin-button,
.optimizer-settings-row .setting-item .interval-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.optimizer-settings-row .setting-item .interval-prefix {
    color: var(--text-muted);
    font-size: 14px;
    white-space: nowrap;
}

.optimizer-settings-row .setting-item .interval-unit {
    color: var(--text-muted);
    font-size: 14px;
    white-space: nowrap;
}

/* 确保第二个设置项有足够的右侧空间来平衡布局 */
.optimizer-settings-row .setting-item:last-child {
    padding-right: 120px; /* 与第一个设置项的输入区域宽度匹配 */
}

.setting-checkbox {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    flex: 1;
}

.setting-checkbox input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 36px;
    height: 20px;
    background: rgba(139, 92, 246, 0.2);
    border-radius: 10px;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid rgba(139, 92, 246, 0.3);
}

.checkbox-custom::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 14px;
    height: 14px;
    background: rgba(139, 92, 246, 0.6);
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.setting-checkbox input[type="checkbox"]:checked + .checkbox-custom {
    background: var(--purple-primary);
    border-color: var(--purple-primary);
}

.setting-checkbox input[type="checkbox"]:checked + .checkbox-custom::after {
    left: 18px;
    background: white;
}

.checkbox-text {
    font-size: 14px;
    color: var(--text-primary);
}

.interval-setting {
    display: flex;
    align-items: center;
    gap: 8px;
}

.interval-input {
    width: 60px;
    padding: 6px 8px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 14px;
    text-align: center;
    transition: all 0.2s ease;
}

.interval-input:focus {
    outline: none;
    border-color: var(--purple-primary);
    background: rgba(255, 255, 255, 0.08);
}

.interval-unit {
    font-size: 14px;
    color: var(--text-secondary);
}

/* 状态显示 */
.status-display {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(139, 92, 246, 0.1);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex: 1; /* 填充整个可用空间 */
    justify-content: space-evenly; /* 均匀分布状态项 */
}

.status-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1; /* 让每个状态项占用相等的空间 */
    justify-content: center; /* 垂直居中内容 */
    text-align: left; /* 保持左对齐 */
}

.status-label {
    font-size: 12px;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.status-value {
    font-size: 16px;
    color: var(--text-primary);
    font-weight: 600;
    word-break: break-word; /* 防止长文本溢出 */
}



/* 网络优化提醒对话框样式 */
.network-warning-content {
    margin-bottom: 24px;
}

.network-warning-main {
    margin-bottom: 20px;
}

.network-warning-main p {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
}

.network-warning-main p:last-child {
    margin-bottom: 0;
}

.network-warning-details {
    background: rgba(139, 92, 246, 0.05);
    border: 1px solid rgba(139, 92, 246, 0.1);
    border-radius: 12px;
    padding: 16px;
}

.network-warning-details ul {
    margin: 0;
    padding-left: 20px;
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.5;
}

.network-warning-details li {
    margin-bottom: 8px;
}

.network-warning-details li:last-child {
    margin-bottom: 0;
}

/* 切换社区计划对话框样式 */
.switch-plan-warning-content {
    margin-bottom: 24px;
}

.switch-plan-warning-main {
    margin-bottom: 20px;
}

.switch-plan-warning-main p {
    margin: 0 0 12px 0;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
}

.switch-plan-warning-main p:last-child {
    margin-bottom: 0;
}

.switch-plan-warning-main strong {
    color: #f59e0b;
    font-weight: 600;
}

.switch-plan-warning-details {
    background: rgba(239, 68, 68, 0.05);
    border: 1px solid rgba(239, 68, 68, 0.1);
    border-radius: 12px;
    padding: 16px;
}

.switch-plan-warning-details ul {
    margin: 0;
    padding-left: 20px;
    color: var(--text-secondary);
    font-size: 13px;
    line-height: 1.5;
}

.switch-plan-warning-details li {
    margin-bottom: 8px;
}

.switch-plan-warning-details li:last-child {
    margin-bottom: 0;
}



/* 隐藏类 */
.hidden {
    display: none !important;
}

