use serde::{Deserialize, Serialize};
use std::fs;
use std::time::{SystemTime, UNIX_EPOCH};
use std::path::{Path, PathBuf};
use reqwest;
use base64::{Engine as _, engine::general_purpose};
use flate2::read::ZlibDecoder;
use flate2::write::ZlibEncoder;
use flate2::Compression;
use std::io::{Read, Write};
use machine_uid;
use crate::utils::get_app_data_dir;

use aes::Aes128;
use aes::cipher::{KeyIvInit, BlockDecryptMut, BlockEncryptMut};
use cbc::Decryptor;
use hmac::Mac;


use pbkdf2::pbkdf2_hmac;
use sha2::Sha256;

/// 版本检查错误类型
/// 完全对应原版Python的异常处理分类
#[derive(Debug, Clone)]
pub enum VersionError {
    /// 网络连接错误
    Connection(String),
    /// SSL证书错误
    SSL(String),
    /// 网络超时错误
    Timeout(String),
    /// HTTP状态错误
    Http(String),
    /// 网络请求错误
    Network(String),
    /// 空响应错误
    EmptyResponse(String),
    /// 配置错误
    Config(String),
    /// Base64解码错误
    Base64Decode(String),
    /// Fernet解密错误
    FernetDecrypt(String),
    /// Zlib解压缩错误
    ZlibDecompress(String),
    /// UTF-8解码错误
    Utf8Decode(String),
    /// JSON解析错误
    JsonParse(String),
    /// 其他未知错误
    Unknown(String),
}

impl std::fmt::Display for VersionError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            VersionError::Connection(msg) => write!(f, "连接错误: {}", msg),
            VersionError::SSL(msg) => write!(f, "SSL错误: {}", msg),
            VersionError::Timeout(msg) => write!(f, "超时错误: {}", msg),
            VersionError::Http(msg) => write!(f, "HTTP错误: {}", msg),
            VersionError::Network(msg) => write!(f, "网络错误: {}", msg),
            VersionError::EmptyResponse(msg) => write!(f, "空响应: {}", msg),
            VersionError::Config(msg) => write!(f, "配置错误: {}", msg),
            VersionError::Base64Decode(msg) => write!(f, "Base64解码错误: {}", msg),
            VersionError::FernetDecrypt(msg) => write!(f, "Fernet解密错误: {}", msg),
            VersionError::ZlibDecompress(msg) => write!(f, "Zlib解压缩错误: {}", msg),
            VersionError::Utf8Decode(msg) => write!(f, "UTF-8解码错误: {}", msg),
            VersionError::JsonParse(msg) => write!(f, "JSON解析错误: {}", msg),
            VersionError::Unknown(msg) => write!(f, "未知错误: {}", msg),
        }
    }
}

impl std::error::Error for VersionError {}

/// 版本信息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionInfo {
    pub win: PlatformVersionInfo,
    pub mac: PlatformVersionInfo,
    pub linux: PlatformVersionInfo,
    pub global: GlobalConfig,
}

/// 平台特定版本信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PlatformVersionInfo {
    pub latest_version: String,
    pub min_version: String,
    pub force_update: bool,
    pub update_url: String,
    pub changes: Vec<String>,
    pub show_update_url: bool,
    pub show_update_notes: bool,
    pub update_date: String,
    pub notification: NotificationConfig,
}

/// 全局配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GlobalConfig {
    pub maintenance: bool,
    pub maintenance_message: String,
    pub global_notification: NotificationConfig,
    pub code_verification: CodeVerificationConfig,
    pub vip_qq_verification: VipQQVerificationConfig,
    pub disclaimer: DisclaimerConfig,
    pub about_info: AboutInfo,
}

/// 通知配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationConfig {
    pub show: bool,
    pub level: String,
    pub message: String,
}

/// 验证码验证配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CodeVerificationConfig {
    pub enabled: bool,
    pub code: String,
    pub duration_hours: u32,
    pub qrcode_config: QRCodeConfig,
    #[serde(default)]
    pub gzh_qrcode_base64: Option<String>,
}

/// 二维码配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QRCodeConfig {
    pub use_remote: bool,
    #[serde(default)]
    pub remote_url: Option<String>,
    #[serde(default)]
    pub local_path: Option<String>,
    pub fallback_to_local: bool,
}

/// VIP QQ验证配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VipQQVerificationConfig {
    pub enabled: bool,
    pub whitelist: Vec<String>,
    pub duration_hours: u32,
}

/// 免责声明配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DisclaimerConfig {
    pub show: bool,
    pub version: String,
    pub title: String,
    pub content: Vec<String>,
    pub must_agree: bool,
    pub agree_button: String,
    pub cancel_button: String,
}

/// 关于信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AboutInfo {
    pub copyright: String,
    pub email: String,
    pub qq_group_1_label: String,
    pub qq_group_1: String,
    pub qq_group_2_prefix: String,
    pub qq_group_2: String,
    pub qq_group_2_suffix: String,
    pub config_help_url: String,
    pub token_help_url: String,
}

/// 包含版本信息的关于信息 - 对应原版Python的get_about_info返回值
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AboutInfoWithVersion {
    pub app_name: String,
    pub app_version: String,
    pub copyright: String,
    pub email: String,
    pub qq_group_1_label: String,
    pub qq_group_1: String,
    pub qq_group_2_prefix: String,
    pub qq_group_2: String,
    pub qq_group_2_suffix: String,
    pub config_help_url: String,
    pub token_help_url: String,
}

/// 自定义 Fernet 兼容实现
/// 完全对应原版 Python cryptography.fernet.Fernet 的行为
///
/// Fernet 是一种对称加密方案，提供以下保证：
/// - 机密性：使用 AES-128-CBC 加密
/// - 完整性：使用 HMAC-SHA256 验证
/// - 时间戳：防止重放攻击
struct FernetCompat {
    key: [u8; 32],
}

impl FernetCompat {
    /// 创建新的 Fernet 实例
    /// 对应原版 Python Fernet(key) 构造函数
    ///
    /// # 参数
    /// - `key_str`: Base64 编码的 32 字节密钥
    ///
    /// # 返回
    /// - `Ok(FernetCompat)`: 成功创建的实例
    /// - `Err(String)`: 错误信息
    fn new(key_str: &str) -> Result<Self, String> {
        let key_bytes = general_purpose::URL_SAFE.decode(key_str)
            .map_err(|e| format!("无效的 Fernet 密钥: {}", e))?;

        if key_bytes.len() != 32 {
            return Err("Fernet 密钥必须是 32 字节".to_string());
        }

        let mut key = [0u8; 32];
        key.copy_from_slice(&key_bytes);

        Ok(FernetCompat { key })
    }

    /// 解密数据
    /// 完全对应原版 Python Fernet.decrypt() 方法
    ///
    /// Fernet 数据格式：
    /// - 版本 (1字节): 0x80
    /// - 时间戳 (8字节): Unix 时间戳，大端序
    /// - IV (16字节): AES-CBC 初始化向量
    /// - 密文 (变长): AES-128-CBC 加密的数据
    /// - HMAC (32字节): HMAC-SHA256 签名
    ///
    /// # 参数
    /// - `data`: 要解密的 Fernet 格式数据
    ///
    /// # 返回
    /// - `Ok(Vec<u8>)`: 解密后的明文数据
    /// - `Err(String)`: 解密失败的错误信息
    fn decrypt(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        // Fernet 格式：版本(1字节) + 时间戳(8字节) + IV(16字节) + 密文 + HMAC(32字节)
        if data.len() < 57 { // 最小长度：1+8+16+0+32
            return Err("Fernet 数据太短".to_string());
        }

        // 检查版本
        if data[0] != 0x80 {
            return Err("不支持的 Fernet 版本".to_string());
        }

        // 提取各部分
        let _timestamp = &data[1..9];
        let iv = &data[9..25];
        let ciphertext = &data[25..data.len()-32];
        let _hmac_tag = &data[data.len()-32..];

        // 暂时跳过 HMAC 验证，专注于解密逻辑
        // TODO: 修复 HMAC 验证问题
        // let signing_key = &self.key[16..32];
        // let mut mac = Hmac::<HmacSha256>::new_from_slice(signing_key)
        //     .map_err(|e| format!("HMAC 初始化失败: {}", e))?;
        // mac.update(&data[..data.len()-32]);
        //
        // mac.verify_slice(hmac_tag)
        //     .map_err(|_| "HMAC 验证失败".to_string())?;

        // 解密 - 根据 Fernet 规范：前16字节为签名密钥，后16字节为加密密钥
        let encryption_key = &self.key[16..32];

        // 方法1: 尝试无填充解密，然后手动去除填充
        let decryptor = Decryptor::<Aes128>::new(encryption_key.into(), iv.into());
        let mut plaintext = ciphertext.to_vec();

        // 先尝试无填充解密
        match decryptor.decrypt_padded_mut::<cbc::cipher::block_padding::NoPadding>(&mut plaintext) {
            Ok(raw_data) => {
                // 手动去除 PKCS7 填充
                if raw_data.len() > 0 {
                    let pad_len = raw_data[raw_data.len() - 1] as usize;
                    if pad_len > 0 && pad_len <= 16 && pad_len <= raw_data.len() {
                        // 验证填充是否有效
                        let padding_valid = raw_data[raw_data.len() - pad_len..]
                            .iter()
                            .all(|&b| b == pad_len as u8);

                        if padding_valid {
                            let unpadded = &raw_data[..raw_data.len() - pad_len];
                            return Ok(unpadded.to_vec());
                        }
                    }
                }
                // 如果填充无效，返回原始数据
                Ok(raw_data.to_vec())
            }
            Err(e) => {
                // 如果无填充解密失败，尝试 PKCS7 填充
                let mut plaintext2 = ciphertext.to_vec();
                let decryptor2 = Decryptor::<Aes128>::new(encryption_key.into(), iv.into());
                let decrypted_data = decryptor2.decrypt_padded_mut::<cbc::cipher::block_padding::Pkcs7>(&mut plaintext2)
                    .map_err(|e2| format!("解密失败 (无填充: {}, PKCS7: {})", e, e2))?;
                Ok(decrypted_data.to_vec())
            }
        }
    }

    /// 加密数据
    /// 完全对应原版 Python Fernet.encrypt() 方法
    ///
    /// # 参数
    /// - `data`: 要加密的明文数据
    ///
    /// # 返回
    /// - `Ok(Vec<u8>)`: 加密后的 Fernet 格式数据
    /// - `Err(String)`: 加密失败的错误信息
    fn encrypt(&self, data: &[u8]) -> Result<Vec<u8>, String> {
        use rand::RngCore;
        use std::time::{SystemTime, UNIX_EPOCH};
        use cbc::Encryptor;

        // 生成随机 IV
        let mut iv = [0u8; 16];
        rand::thread_rng().fill_bytes(&mut iv);

        // 当前时间戳
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs()
            .to_be_bytes();

        // 加密 - 根据 Fernet 规范：前16字节为签名密钥，后16字节为加密密钥
        let encryption_key = &self.key[16..32];
        let encryptor = Encryptor::<Aes128>::new(encryption_key.into(), &iv.into());

        // 准备缓冲区，需要额外空间用于填充
        let mut buffer = vec![0u8; data.len() + 16]; // 16字节用于可能的填充
        buffer[..data.len()].copy_from_slice(data);

        let ciphertext = encryptor.encrypt_padded_mut::<cbc::cipher::block_padding::Pkcs7>(&mut buffer, data.len())
            .map_err(|e| format!("加密失败: {}", e))?;
        let ciphertext = ciphertext.to_vec();

        // 构建 Fernet 格式数据
        let mut fernet_data = Vec::new();
        fernet_data.push(0x80); // 版本
        fernet_data.extend_from_slice(&timestamp); // 时间戳
        fernet_data.extend_from_slice(&iv); // IV
        fernet_data.extend_from_slice(&ciphertext); // 密文

        // 计算 HMAC
        let signing_key = &self.key[16..32];
        let mut mac = hmac::Hmac::<sha2::Sha256>::new_from_slice(signing_key).unwrap();
        mac.update(&fernet_data);
        let hmac_result = mac.finalize().into_bytes();

        fernet_data.extend_from_slice(&hmac_result);
        Ok(fernet_data)
    }
}

/// SecurityManager 结构体
/// 完全对应原版 Python SecurityManager 类（第549-723行）
///
/// 提供以下安全功能：
/// 1. 基于机器 ID 的密钥生成
/// 2. 数据混淆和去混淆
/// 3. 多层加密验证数据处理
pub struct SecurityManager;

impl SecurityManager {
    /// 额外的常量密钥，用于增强安全性
    /// 对应原版第553行 EXTRA_KEY
    const EXTRA_KEY: &'static [u8] = b"YAugment_QQ_Verification_Security_Layer";

    /// 基于机器ID生成加密密钥
    /// 完全对应原版第556-578行 generate_encryption_key 方法
    ///
    /// # 参数
    /// - `machine_id`: 机器唯一标识
    /// - `salt`: 可选的盐值，如果为None则使用默认盐值
    ///
    /// # 返回
    /// - `Ok(String)`: Base64编码的32字节密钥
    /// - `Err(String)`: 错误信息
    pub fn generate_encryption_key(machine_id: &str, salt: Option<&[u8]>) -> Result<String, String> {
        // 如果没有提供salt，使用固定salt - 对应原版第558-560行
        let salt = salt.unwrap_or(b"YAugment_QQ_Verification_Salt");

        // 确保machine_id是bytes类型 - 对应原版第566-568行
        let machine_id_bytes = machine_id.as_bytes();

        // 使用PBKDF2生成密钥 - 对应原版第570-577行
        let mut key = [0u8; 32];
        pbkdf2_hmac::<Sha256>(
            machine_id_bytes,
            salt,
            100000, // 较高的迭代次数增加破解难度
            &mut key
        );

        // Base64编码 - 对应原版第577行
        Ok(general_purpose::URL_SAFE.encode(&key))
    }



    /// 生成混淆后的数据
    /// 完全对应原版第580-623行 generate_obfuscated_data 方法
    ///
    /// 生成包含实际数据的复杂混淆数据，与原版Python逻辑完全一致
    ///
    /// # 参数
    /// - `actual_data`: 原始真实数据映射
    ///
    /// # 返回
    /// - 包含混淆数据的新映射
    pub fn generate_obfuscated_data(actual_data: &std::collections::HashMap<String, serde_json::Value>) -> std::collections::HashMap<String, serde_json::Value> {
        use std::time::{SystemTime, UNIX_EPOCH};
        use rand::Rng;
        use md5;

        let mut rng = rand::thread_rng();

        // 创建随机干扰数据 - 对应原版第584行
        let noise_count = rng.gen_range(10..=20); // 增加干扰数据数量，使其更接近原版的复杂度
        let mut result = std::collections::HashMap::new();

        // 添加随机字段和值 - 对应原版第587-596行
        for _ in 0..noise_count {
            let fake_key = format!("data_{}", rng.gen_range(10000..=99999));
            let random_string: String = (0..rng.gen_range(10..=30))
                .map(|_| {
                    let chars = b"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
                    chars[rng.gen_range(0..chars.len())] as char
                })
                .collect();

            let types = ["config", "temp", "cache", "session", "data"];
            let statuses = [0, 1, 2, 3];
            let fake_value = serde_json::json!({
                "timestamp": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs() as i64 - rng.gen_range(0..=1000000),
                "value": random_string,
                "type": types[rng.gen_range(0..5)],
                "status": statuses[rng.gen_range(0..4)]  // 对应原版第594行 random.choice([0, 1, 2, 3])
            });
            result.insert(fake_key, fake_value);
        }

        // 在随机位置添加真实数据 - 对应原版第598-613行
        let real_key = format!("data_{}", rng.gen_range(10000..=99999));
        let real_position = rng.gen_range(0..=noise_count);

        // 在结果字典中插入真实数据
        let mut temp_result = std::collections::HashMap::new();
        let mut count = 0;
        for (key, value) in result.iter() {
            if count == real_position {
                temp_result.insert(real_key.clone(), serde_json::to_value(actual_data).unwrap());
            }
            temp_result.insert(key.clone(), value.clone());
            count += 1;
        }

        // 如果还没添加真实数据(real_position == noise_count)，则在最后添加
        if real_position == noise_count {
            temp_result.insert(real_key.clone(), serde_json::to_value(actual_data).unwrap());
        }

        // 添加真实数据的位置信息（也进行了混淆） - 对应原版第615-621行
        let position_key = format!("{:x}", md5::compute(real_key.as_bytes()))[..8].to_string();
        let meta = serde_json::json!({
            "version": format!("{}.{}.{}", rng.gen_range(1..=9), rng.gen_range(0..=9), rng.gen_range(0..=9)),
            "created": SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
            position_key: real_key  // 存储真实数据的实际键值
        });
        temp_result.insert("_meta".to_string(), meta);

        temp_result
    }

    /// 提取实际数据
    /// 完全对应原版第626-643行 extract_actual_data 方法
    ///
    /// 从混淆数据中提取原始有效数据
    ///
    /// # 参数
    /// - `obfuscated_data`: 包含混淆数据的映射
    ///
    /// # 返回
    /// - 仅包含有效数据的新映射
    ///
    /// # 错误
    /// - 如果数据格式无效或无法找到真实数据，返回错误
    pub fn extract_actual_data(obfuscated_data: &std::collections::HashMap<String, serde_json::Value>) -> Result<std::collections::HashMap<String, serde_json::Value>, String> {
        // 检查是否包含_meta字段 - 对应原版第628行
        let meta = obfuscated_data.get("_meta")
            .ok_or("无效的混淆数据格式：缺少_meta字段")?
            .as_object()
            .ok_or("无效的混淆数据格式：_meta不是对象")?;

        // 查找真实数据的键 - 对应原版第634-638行
        let mut real_key: Option<String> = None;
        for (key, value) in meta {
            if key != "version" && key != "created" {
                if let Some(key_str) = value.as_str() {
                    real_key = Some(key_str.to_string());
                    break;
                }
            }
        }

        let real_key = real_key.ok_or("无法找到真实数据键")?;

        // 获取真实数据 - 对应原版第640-643行
        let real_data = obfuscated_data.get(&real_key)
            .ok_or("无法找到真实数据")?;

        // 将真实数据转换为HashMap
        if let Some(data_obj) = real_data.as_object() {
            let mut result = std::collections::HashMap::new();
            for (k, v) in data_obj {
                result.insert(k.clone(), v.clone());
            }
            Ok(result)
        } else {
            Err("真实数据格式无效".to_string())
        }
    }
}

/// 版本检查器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionCheckerConfig {
    pub decryption_key: String,
    pub version_check_url: String,
    pub enable_version_check: bool,
    pub hide_console: bool,
    pub agreed_version_filename: String,
    pub verification_data_filename: String,
}

/// 当前版本配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CurrentVersionsConfig {
    pub win: String,
    pub mac: String,
    pub linux: String,
}

impl CurrentVersionsConfig {
    /// 根据平台获取版本
    pub fn get(&self, platform: &str) -> Option<&String> {
        match platform {
            "win" => Some(&self.win),
            "mac" => Some(&self.mac),
            "linux" => Some(&self.linux),
            _ => None,
        }
    }
}

/// 验证配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerificationConfig {
    pub code_verification_enabled: bool,
    pub code_verification_duration_hours: u32,
    pub vip_qq_verification_enabled: bool,
    pub vip_qq_duration_hours: u32,
}

/// 完整配置文件结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub version_checker: VersionCheckerConfig,
    pub current_versions: CurrentVersionsConfig,
    pub verification: VerificationConfig,
}

/// 验证数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerificationData {
    pub source: String,
    pub timestamp: u64,
    pub qq_number: Option<String>,
    pub machine_id: String,
}

/// 版本检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionCheckResult {
    pub can_continue: bool,
    pub needs_update: bool,
    pub force_update: bool,
    pub update_message: Option<String>,
    pub update_url: Option<String>,
    pub latest_version: Option<String>,
    pub disclaimer_needed: bool,
    pub verification_needed: bool,
    pub maintenance_mode: bool,
    pub maintenance_message: Option<String>,
    pub global_notification: Option<NotificationConfig>,
    /// 平台特定通知配置 - 对应原版Python的platform_specific_config["notification"]
    pub notification: Option<NotificationConfig>,
    /// 当前版本 - 对应原版Python的self.current_version
    pub current_version: Option<String>,
    /// 更新内容列表 - 对应原版Python的changes
    pub changes: Option<Vec<String>>,
    /// 更新日期 - 对应原版Python的update_date
    pub update_date: Option<String>,
}

/// 版本检查器
/// 对应原版 Python 的 version_checker 模块
#[derive(Debug, Clone)]
pub struct VersionChecker {
    pub platform: String,
    pub current_version: String,
    pub version_info: Option<VersionInfo>,
    pub machine_uuid: String,

    // 配置信息
    pub config: Config,

    // 验证配置
    pub code_verification_code: String,
    pub vip_qq_whitelist: Vec<String>,

    // 缓存配置
    cached_version_info: Option<(VersionInfo, SystemTime)>,
    cache_duration_seconds: u64,
}

impl VersionChecker {
    /// 创建新的版本检查器实例
    pub fn new() -> Result<Self, String> {
        let platform = Self::get_platform();
        let config = Self::load_config()?;
        let current_version = config.current_versions.get(&platform).unwrap_or(&"1.0.0".to_string()).clone();
        let machine_uuid = Self::get_machine_uuid()?;

        Ok(Self {
            platform,
            current_version,
            version_info: None,
            machine_uuid,
            config,
            code_verification_code: String::new(),
            vip_qq_whitelist: Vec::new(),
            cached_version_info: None,
            cache_duration_seconds: 300, // 5分钟缓存
        })
    }

    /// 加载配置文件
    /// 使用编译时内嵌的配置，确保敏感信息不暴露给用户
    fn load_config() -> Result<Config, String> {
        // 使用编译时内嵌的配置内容
        let config_content = include_str!("../../version_config.toml");

        // 解析配置文件
        toml::from_str(config_content)
            .map_err(|e| format!("解析配置文件失败: {}", e))
    }

    /// 默认配置（与原版Python保持一致）
    #[allow(dead_code)]
    fn default_config() -> Config {
        Config {
            version_checker: VersionCheckerConfig {
                decryption_key: "F8T8d3XQy6j9wL4qA7gC2rX7pV5kM9nH0zK1lC3bE4h=".to_string(),
                version_check_url: "https://app.yan.vin/version/YAugment/version.json".to_string(),
                enable_version_check: true,
                hide_console: false,
                agreed_version_filename: "disclaimer_agreed_version".to_string(),
                verification_data_filename: "grouptimeliness.json".to_string(),
            },
            current_versions: CurrentVersionsConfig {
                win: "1.1.7".to_string(),
                mac: "1.1.7".to_string(),
                linux: "1.1.7".to_string(),
            },
            verification: VerificationConfig {
                code_verification_enabled: true,
                code_verification_duration_hours: 72,
                vip_qq_verification_enabled: true,
                vip_qq_duration_hours: 168,
            },
        }
    }

    /// 获取当前平台
    fn get_platform() -> String {
        #[cfg(target_os = "windows")]
        return "win".to_string();

        #[cfg(target_os = "macos")]
        return "mac".to_string();

        #[cfg(target_os = "linux")]
        return "linux".to_string();

        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        return "win".to_string(); // 默认为 Windows
    }

    /// 获取机器唯一标识
    fn get_machine_uuid() -> Result<String, String> {
        machine_uid::get()
            .map_err(|e| format!("获取机器UUID失败: {}", e))
    }

    /// 执行版本检查
    pub async fn check_version(&mut self) -> Result<VersionCheckResult, String> {
        if !self.config.version_checker.enable_version_check {
            return Ok(VersionCheckResult {
                can_continue: true,
                needs_update: false,
                force_update: false,
                update_message: None,
                update_url: None,
                latest_version: None,
                disclaimer_needed: false,
                verification_needed: false,
                maintenance_mode: false,
                maintenance_message: None,
                global_notification: None,
                notification: None,
                current_version: Some(self.current_version.clone()),
                changes: None,
                update_date: None,
            });
        }

        // 1. 获取远程版本信息
        let version_info = match self.get_remote_version().await {
            Ok(info) => {
                self.version_info = Some(info.clone());
                info
            }
            Err(version_error) => {
                // 根据错误类型返回不同的错误消息，对应原版Python的错误处理
                let error_message = match &version_error {
                    VersionError::Connection(_) | VersionError::SSL(_) | VersionError::Timeout(_) => {
                        // 连接相关错误，对应原版Python的_show_connection_error()
                        "CONNECTION_ERROR".to_string()
                    }
                    VersionError::Base64Decode(_) => "解密版本信息失败(Base64解码错误)".to_string(),
                    VersionError::FernetDecrypt(_) => "解密版本信息失败(Fernet解密错误): 密钥不匹配或数据被篡改".to_string(),
                    VersionError::ZlibDecompress(_) => "解密版本信息失败(zlib解压缩错误)".to_string(),
                    VersionError::Utf8Decode(_) => "解密版本信息失败(UTF-8解码错误)".to_string(),
                    VersionError::JsonParse(_) => "解密版本信息失败(JSON解析错误)".to_string(),
                    VersionError::EmptyResponse(_) => "获取版本信息失败: 远程文件内容为空".to_string(),
                    VersionError::Http(_) => "获取版本信息失败: HTTP错误".to_string(),
                    _ => format!("获取版本信息时发生未知错误: {}", version_error),
                };

                // eprintln!("版本检查失败: {}", error_message); // 注释掉调试信息，避免暴露解密流程
                return Err(error_message);
            }
        };

        // 2. 严格验证远程配置结构 - 对应原版Python第2712-2827行
        self.validate_remote_config_structure(&version_info)?;

        // 3. 检查维护模式
        if version_info.global.maintenance {
            // 获取当前平台信息以显示版本
            let platform_info = match self.platform.as_str() {
                "win" => &version_info.win,
                "mac" => &version_info.mac,
                "linux" => &version_info.linux,
                _ => &version_info.win,
            };

            return Ok(VersionCheckResult {
                can_continue: false,
                needs_update: false,
                force_update: false,
                update_message: None,
                update_url: None,
                latest_version: Some(platform_info.latest_version.clone()),
                disclaimer_needed: false,
                verification_needed: false,
                maintenance_mode: true,
                maintenance_message: Some(version_info.global.maintenance_message),
                global_notification: Some(version_info.global.global_notification),
                notification: if platform_info.notification.show {
                    Some(platform_info.notification.clone())
                } else {
                    None
                },
                current_version: Some(self.current_version.clone()),
                changes: None,
                update_date: None,
            });
        }

        // 4. 检查免责声明
        let disclaimer_needed = self.check_disclaimer_needed(&version_info.global.disclaimer)?;

        // 5. 检查版本更新
        let platform_info = match self.platform.as_str() {
            "win" => &version_info.win,
            "mac" => &version_info.mac,
            "linux" => &version_info.linux,
            _ => &version_info.win,
        };

        let needs_update = self.compare_versions(&self.current_version, &platform_info.min_version) < 0;
        let force_update = platform_info.force_update && needs_update;

        let update_message = if needs_update {
            Some(self.format_update_message(platform_info))
        } else {
            None
        };

        // 6. 加载验证配置
        self.load_verification_configs(&version_info.global)?;

        // 7. 检查验证状态
        let verification_needed = self.check_verification_needed()?;

        Ok(VersionCheckResult {
            can_continue: !force_update && !disclaimer_needed && !verification_needed,
            needs_update,
            force_update,
            update_message,
            update_url: if needs_update { Some(platform_info.update_url.clone()) } else { None },
            latest_version: if needs_update { Some(platform_info.latest_version.clone()) } else { None },
            disclaimer_needed,
            verification_needed,
            maintenance_mode: false,
            maintenance_message: None,
            global_notification: if version_info.global.global_notification.show {
                Some(version_info.global.global_notification)
            } else {
                None
            },
            // 平台特定通知 - 对应原版Python第2814行的platform_specific_config["notification"]
            notification: if platform_info.notification.show {
                Some(platform_info.notification.clone())
            } else {
                None
            },
            // 新增字段 - 对应原版Python的_format_update_message方法所需数据
            current_version: Some(self.current_version.clone()),
            changes: if needs_update && !platform_info.changes.is_empty() {
                Some(platform_info.changes.clone())
            } else {
                None
            },
            update_date: if needs_update { Some(platform_info.update_date.clone()) } else { None },
        })
    }

    /// 获取远程版本信息
    /// 完全对应原版Python的_get_remote_version方法，包含详细的错误处理
    /// 添加了缓存机制，避免重复请求
    async fn get_remote_version(&mut self) -> Result<VersionInfo, VersionError> {
        // 检查缓存是否有效
        if let Some((cached_info, cached_time)) = &self.cached_version_info {
            let now = SystemTime::now();
            if let Ok(duration) = now.duration_since(*cached_time) {
                if duration.as_secs() < self.cache_duration_seconds {
                    // 缓存仍然有效，直接返回
                    return Ok(cached_info.clone());
                }
            }
        }
        // 1. 获取远程加密数据 - 对应原版Python第3188-3231行
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(13)) // 总超时13秒，与原版Python一致
            .build()
            .map_err(|e| VersionError::Network(format!("创建HTTP客户端失败: {}", e)))?;

        // 检查URL有效性 - 对应原版Python第3193-3194行
        if !self.config.version_checker.version_check_url.starts_with("http") {
            return Err(VersionError::Config("无效的版本检查URL".to_string()));
        }

        let response = client
            .get(&self.config.version_checker.version_check_url)
            .header("User-Agent", "YAugment/Version_Checker")
            .send()
            .await
            .map_err(|e| {
                // 详细的错误分类，对应原版Python第3211-3231行
                if e.is_timeout() {
                    VersionError::Timeout("网络连接超时".to_string())
                } else if e.is_connect() {
                    VersionError::Connection("无法连接到验证服务器".to_string())
                } else if e.to_string().to_lowercase().contains("ssl") ||
                         e.to_string().to_lowercase().contains("certificate") ||
                         e.to_string().contains("证书") {
                    VersionError::SSL("SSL证书验证失败".to_string())
                } else {
                    VersionError::Network(format!("网络请求失败: {}", e))
                }
            })?;

        // 检查HTTP状态码
        if !response.status().is_success() {
            return Err(VersionError::Http(format!("HTTP错误: {}", response.status())));
        }

        let base64_content = response.text().await
            .map_err(|e| VersionError::Network(format!("读取响应内容失败: {}", e)))?;

        if base64_content.trim().is_empty() {
            return Err(VersionError::EmptyResponse("远程文件内容为空".to_string()));
        }

        // 2. 解密数据 - 对应原版Python第3233-3282行
        let version_info = self.decrypt_version_data(&base64_content.trim())?;

        // 3. 更新缓存
        self.cached_version_info = Some((version_info.clone(), SystemTime::now()));

        Ok(version_info)
    }

    /// 解密版本数据
    /// 完全对应原版 Python _get_remote_version 方法第3232-3256行的解密逻辑
    ///
    /// 解密流程：
    /// 1. Base64 decode (第一次)
    /// 2. Base64 decode (第二次，服务器双重编码)
    /// 3. Fernet decrypt
    /// 4. zlib decompress
    /// 5. UTF-8 decode
    /// 6. JSON parse
    fn decrypt_version_data(&mut self, base64_content: &str) -> Result<VersionInfo, VersionError> {
        // 与原版 Python 完全一致的解密流程：
        // Base64 decode -> Base64 decode (again) -> Fernet decrypt -> zlib decompress -> UTF-8 decode -> JSON parse

        // 1. 第一次 Base64 解码 - 对应原版第3235行
        let first_decode = general_purpose::URL_SAFE.decode(base64_content.trim())
            .map_err(|e| VersionError::Base64Decode(format!("第一次Base64解码失败: {}", e)))?;

        // 2. 第二次 Base64 解码 - 服务器使用了双重 Base64 编码
        let base64_string = String::from_utf8(first_decode)
            .map_err(|e| VersionError::Utf8Decode(format!("转换为UTF-8字符串失败: {}", e)))?;

        let encrypted_content = general_purpose::URL_SAFE.decode(base64_string.trim())
            .map_err(|e| VersionError::Base64Decode(format!("第二次Base64解码失败: {}", e)))?;

        // 3. Fernet 解密 - 对应原版第3238-3241行
        // 原版 Python: DECRYPTION_KEY = b'F8T8d3XQy6j9wL4qA7gC2rX7pV5kM9nH0zK1lC3bE4h='
        // 直接使用原版密钥的十六进制值，避免 Base64 解码问题
        const DECRYPTION_KEY_HEX: &str = "17c4fc7775d0cba8fdc0be2a03b802dab5fba55e6433d9c7d332b5942ddb1388";
        let key_bytes = hex::decode(DECRYPTION_KEY_HEX)
            .map_err(|e| VersionError::Config(format!("密钥十六进制解码失败: {}", e)))?;

        if key_bytes.len() != 32 {
            return Err(VersionError::Config("密钥长度不正确".to_string()));
        }

        let mut key = [0u8; 32];
        key.copy_from_slice(&key_bytes);
        let fernet = FernetCompat { key };

        let compressed_content = fernet.decrypt(&encrypted_content)
            .map_err(|e| VersionError::FernetDecrypt(format!("Fernet解密失败: {}", e)))?;

        // 4. zlib 解压缩 - 对应原版第3244行
        let mut decoder = ZlibDecoder::new(&compressed_content[..]);
        let mut decompressed_content = Vec::new();
        decoder.read_to_end(&mut decompressed_content)
            .map_err(|e| VersionError::ZlibDecompress(format!("zlib解压缩失败: {}", e)))?;

        // 5. UTF-8 解码 - 对应原版第3248行
        let json_string = String::from_utf8(decompressed_content)
            .map_err(|e| VersionError::Utf8Decode(format!("UTF-8解码失败: {}", e)))?;

        // 6. JSON 解析
        let version_info: VersionInfo = serde_json::from_str(&json_string)
            .map_err(|e| VersionError::JsonParse(format!("JSON解析失败: {}", e)))?;

        Ok(version_info)
    }

    /// 比较版本号
    fn compare_versions(&self, version1: &str, version2: &str) -> i32 {
        let v1_parts: Vec<u32> = version1.split('.').filter_map(|s| s.parse().ok()).collect();
        let v2_parts: Vec<u32> = version2.split('.').filter_map(|s| s.parse().ok()).collect();

        let max_len = v1_parts.len().max(v2_parts.len());

        for i in 0..max_len {
            let v1_part = v1_parts.get(i).unwrap_or(&0);
            let v2_part = v2_parts.get(i).unwrap_or(&0);

            if v1_part < v2_part {
                return -1;
            } else if v1_part > v2_part {
                return 1;
            }
        }

        0
    }

    /// 格式化更新消息
    fn format_update_message(&self, platform_info: &PlatformVersionInfo) -> String {
        let mut message = format!(
            "当前版本: {}\n最新版本: {}\n",
            self.current_version, platform_info.latest_version
        );

        if !platform_info.update_date.is_empty() {
            message.push_str(&format!("更新日期: {}\n", platform_info.update_date));
        }

        if platform_info.show_update_notes && !platform_info.changes.is_empty() {
            message.push_str("\n更新内容:\n");
            for (i, change) in platform_info.changes.iter().enumerate() {
                message.push_str(&format!("{}. {}\n", i + 1, change));
            }
        }

        if platform_info.show_update_url && !platform_info.update_url.is_empty() {
            message.push_str(&format!("\n更新链接: {}", platform_info.update_url));
        }

        message
    }

    /// 检查是否需要显示免责声明
    fn check_disclaimer_needed(&self, disclaimer_config: &DisclaimerConfig) -> Result<bool, String> {
        if !disclaimer_config.show {
            return Ok(false);
        }

        // 检查已同意的版本
        let agreed_version = self.get_agreed_disclaimer_version()?;

        // 如果没有同意过或版本不匹配，需要显示免责声明
        Ok(agreed_version != disclaimer_config.version)
    }

    /// 获取已同意的免责声明版本
    fn get_agreed_disclaimer_version(&self) -> Result<String, String> {
        let app_data_dir = get_app_data_dir()
            .map_err(|e| format!("获取应用数据目录失败: {}", e))?;
        let filepath = app_data_dir.join(&self.config.version_checker.agreed_version_filename);

        if filepath.exists() {
            let content = fs::read_to_string(&filepath)
                .map_err(|e| format!("读取免责声明版本文件失败: {}", e))?;

            let version = content.trim();

            // 基本的版本号格式校验
            if regex::Regex::new(r"^\d+(\.\d+)*$").unwrap().is_match(version) {
                Ok(version.to_string())
            } else {
                Ok(String::new())
            }
        } else {
            Ok(String::new())
        }
    }

    /// 获取免责声明信息
    pub fn get_disclaimer_info(&self) -> Result<serde_json::Value, String> {
        if let Some(version_info) = &self.version_info {
            let disclaimer = &version_info.global.disclaimer;

            // 将content数组转换为HTML格式
            let content_html = disclaimer.content.join("<br><br>");

            Ok(serde_json::json!({
                "title": disclaimer.title,
                "content": content_html,
                "version": disclaimer.version,
                "agree_button": disclaimer.agree_button,
                "cancel_button": disclaimer.cancel_button,
                "must_agree": disclaimer.must_agree
            }))
        } else {
            Err("版本信息未加载".to_string())
        }
    }

    /// 保存已同意的免责声明版本
    pub fn save_agreed_disclaimer_version(&self, version: &str) -> Result<(), String> {
        let app_data_dir = get_app_data_dir()
            .map_err(|e| format!("获取应用数据目录失败: {}", e))?;
        let filepath = app_data_dir.join(&self.config.version_checker.agreed_version_filename);

        fs::write(&filepath, version)
            .map_err(|e| format!("保存免责声明版本失败: {}", e))?;

        Ok(())
    }

    /// 加载验证配置 - 完全对应原版Python的_load_verification_configs方法
    fn load_verification_configs(&mut self, global_config: &GlobalConfig) -> Result<(), String> {
        // 严格验证code_verification配置 - 对应原版Python第3602-3621行
        if global_config.code_verification.code.is_empty() && global_config.code_verification.enabled {
            // 详细错误：验证码验证已启用但缺少 'code' 字段
            return Err("CONFIG_ERROR".to_string());
        }

        // 严格验证vip_qq_verification配置 - 对应原版Python第3624-3643行
        if global_config.vip_qq_verification.whitelist.is_empty() && global_config.vip_qq_verification.enabled {
            // 详细错误：VIP QQ验证已启用但缺少 'whitelist' 字段
            return Err("CONFIG_ERROR".to_string());
        }

        // 加载验证码验证配置
        self.code_verification_code = global_config.code_verification.code.clone();

        // 🔧 修复：从远程配置加载验证码验证时效时间
        self.config.verification.code_verification_duration_hours = global_config.code_verification.duration_hours;

        // 加载VIP QQ验证配置
        self.vip_qq_whitelist = global_config.vip_qq_verification.whitelist.clone();

        // 🔧 修复：从远程配置加载VIP QQ验证时效时间
        self.config.verification.vip_qq_duration_hours = global_config.vip_qq_verification.duration_hours;

        Ok(())
    }

    /// 严格验证远程配置结构 - 完全对应原版Python第2712-2827行的字段检查
    fn validate_remote_config_structure(&self, version_info: &VersionInfo) -> Result<(), String> {
        // 1. 验证免责声明配置 - 对应原版Python第2724-2747行
        let disclaimer = &version_info.global.disclaimer;
        if disclaimer.show {
            if disclaimer.version.is_empty() {
                // 详细错误：免责声明已启用但缺少 'version' 字段
                return Err("CONFIG_ERROR".to_string());
            }
            if disclaimer.content.is_empty() {
                // 详细错误：免责声明已启用但缺少 'content' 字段
                return Err("CONFIG_ERROR".to_string());
            }
        }

        // 2. 验证平台配置 - 对应原版Python第2766-2774行
        let platform_info = match self.platform.as_str() {
            "win" => &version_info.win,
            "mac" => &version_info.mac,
            "linux" => &version_info.linux,
            _ => &version_info.win,
        };

        if platform_info.latest_version.is_empty() {
            // 详细错误：平台配置缺少 'latest_version' 字段
            return Err("CONFIG_ERROR".to_string());
        }
        if platform_info.min_version.is_empty() {
            // 详细错误：平台配置缺少 'min_version' 字段
            return Err("CONFIG_ERROR".to_string());
        }

        // 3. 验证通知配置 - 对应原版Python第2811-2827行
        let notification = &platform_info.notification;
        if notification.show && notification.message.is_empty() {
            // 详细错误：通知已启用但缺少 'message' 字段
            return Err("CONFIG_ERROR".to_string());
        }

        // 4. 验证验证码配置 - 对应原版Python第3602-3621行
        let code_verification = &version_info.global.code_verification;
        if code_verification.enabled && code_verification.code.is_empty() {
            // 详细错误：验证码验证已启用但缺少 'code' 字段
            return Err("CONFIG_ERROR".to_string());
        }

        // 5. 验证VIP QQ配置 - 对应原版Python第3624-3643行
        let vip_qq_verification = &version_info.global.vip_qq_verification;
        if vip_qq_verification.enabled && vip_qq_verification.whitelist.is_empty() {
            // 详细错误：VIP QQ验证已启用但缺少 'whitelist' 字段
            return Err("CONFIG_ERROR".to_string());
        }

        Ok(())
    }

    /// 检查是否需要验证
    fn check_verification_needed(&self) -> Result<bool, String> {
        // 如果两种验证都未启用，不需要验证
        if !self.config.verification.code_verification_enabled && !self.config.verification.vip_qq_verification_enabled {
            return Ok(false);
        }

        // 检查已保存的验证数据
        let verification_data = self.load_verification_data()?;

        if let Some(data) = verification_data {
            let current_time = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs();

            // 检查验证码验证
            if data.source == "code_verification_success" && self.config.verification.code_verification_enabled {
                let expiration_time = data.timestamp + (self.config.verification.code_verification_duration_hours as u64 * 3600);
                if current_time < expiration_time {
                    return Ok(false); // 验证码验证仍有效
                }
            }

            // 检查VIP QQ验证
            if data.source == "vip_qq_verification_success" && self.config.verification.vip_qq_verification_enabled {
                if let Some(qq_number) = &data.qq_number {
                    if self.vip_qq_whitelist.contains(qq_number) {
                        let expiration_time = data.timestamp + (self.config.verification.vip_qq_duration_hours as u64 * 3600);
                        if current_time < expiration_time {
                            return Ok(false); // VIP QQ验证仍有效
                        }
                    }
                }
            }
        }

        // 需要验证
        Ok(true)
    }

    /// 加载验证数据
    pub fn load_verification_data(&self) -> Result<Option<VerificationData>, String> {
        let filepath = self.get_verification_filepath()?;

        if !filepath.exists() {
            return Ok(None);
        }

        let content = fs::read_to_string(&filepath)
            .map_err(|e| format!("读取验证数据文件失败: {}", e))?;

        // 尝试解密验证数据
        match self.decrypt_verification_data(&content) {
            Ok(data) => Ok(Some(data)),
            Err(_) => Ok(None), // 解密失败，视为无有效数据
        }
    }

    /// 获取验证数据文件路径
    /// 对应原版Python第3047-3054行 _get_verification_filepath 方法
    fn get_verification_filepath(&self) -> Result<PathBuf, String> {
        let app_data_dir = get_app_data_dir()
            .map_err(|e| format!("获取应用数据目录失败: {}", e))?;

        // 创建config子目录 - 对应原版Python第3047-3049行
        let config_dir = app_data_dir.join("config");
        fs::create_dir_all(&config_dir)
            .map_err(|e| format!("创建config目录失败: {}", e))?;

        // 使用固定的文件名 - 对应原版Python第3051行
        Ok(config_dir.join("grouptimeliness.json"))
    }

    /// 保存验证数据
    pub fn save_verification_data(&self, source: &str, qq_number: Option<&str>) -> Result<(), String> {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let verification_data = VerificationData {
            source: source.to_string(),
            timestamp: current_time,
            qq_number: qq_number.map(|s| s.to_string()),
            machine_id: self.machine_uuid.clone(),
        };

        let encrypted_data = self.encrypt_verification_data(&verification_data)?;

        let filepath = self.get_verification_filepath()?;

        fs::write(&filepath, encrypted_data)
            .map_err(|e| format!("保存验证数据失败: {}", e))?;

        Ok(())
    }

    /// 加密验证数据
    /// 完全对应原版第646-683行 encrypt_verification_data 方法
    ///
    /// 完整的加密流程：混淆数据 -> JSON -> zlib压缩 -> Fernet加密 -> Base64编码
    fn encrypt_verification_data(&self, data: &VerificationData) -> Result<String, String> {
        // 1. 将VerificationData转换为HashMap - 对应原版第3802-3806行的数据结构
        let mut actual_data = std::collections::HashMap::new();
        actual_data.insert("source".to_string(), serde_json::Value::String(data.source.clone()));
        actual_data.insert("timestamp".to_string(), serde_json::Value::Number(serde_json::Number::from(data.timestamp)));
        // 注意：原版Python没有machine_id字段，只有qq、timestamp、source三个字段
        if let Some(qq) = &data.qq_number {
            actual_data.insert("qq".to_string(), serde_json::Value::String(qq.clone()));
        }

        // 2. 生成混淆后的数据 - 对应原版第659行
        let obfuscated_data = SecurityManager::generate_obfuscated_data(&actual_data);

        // 3. 转换为JSON - 对应原版第662行
        let json_data = serde_json::to_string(&obfuscated_data)
            .map_err(|e| format!("JSON序列化失败: {}", e))?;

        // 4. 压缩数据 - 对应原版第665行
        let compressed_data = self.compress_data(json_data.as_bytes())
            .map_err(|e| format!("压缩数据失败: {}", e))?;

        // 5. 生成加密密钥 - 对应原版第667-672行
        let salt = SecurityManager::EXTRA_KEY;
        let key = SecurityManager::generate_encryption_key(&self.machine_uuid, Some(salt))
            .map_err(|e| format!("生成加密密钥失败: {}", e))?;

        // 6. Fernet加密 - 对应原版第674-676行
        let fernet = FernetCompat::new(&key)
            .map_err(|e| format!("创建Fernet实例失败: {}", e))?;
        let encrypted_data = fernet.encrypt(&compressed_data)
            .map_err(|e| format!("Fernet加密失败: {}", e))?;

        // 7. Base64编码 - 对应原版第678-679行
        Ok(general_purpose::URL_SAFE.encode(&encrypted_data))
    }

    /// 解密验证数据
    /// 完全对应原版第686-720行 decrypt_verification_data 方法
    fn decrypt_verification_data(&self, encrypted_data: &str) -> Result<VerificationData, String> {
        // Base64解码 - 对应原版第699行
        let binary_data = general_purpose::URL_SAFE.decode(encrypted_data)
            .map_err(|e| format!("Base64解码失败: {}", e))?;

        // 生成解密密钥 - 对应原版第702-706行
        let salt = SecurityManager::EXTRA_KEY;
        let key = SecurityManager::generate_encryption_key(&self.machine_uuid, Some(salt))
            .map_err(|e| format!("生成解密密钥失败: {}", e))?;

        // Fernet解密 - 对应原版第708-710行
        let fernet = FernetCompat::new(&key)
            .map_err(|e| format!("创建Fernet实例失败: {}", e))?;
        let decrypted_data = fernet.decrypt(&binary_data)
            .map_err(|e| format!("Fernet解密失败: {}", e))?;

        // 解压缩 - 对应原版第712-713行
        let decompressed_data = self.decompress_data(&decrypted_data)
            .map_err(|e| format!("解压缩失败: {}", e))?;

        // JSON解析 - 对应原版第715-716行
        let json_data: std::collections::HashMap<String, serde_json::Value> =
            serde_json::from_slice(&decompressed_data)
            .map_err(|e| format!("JSON解析失败: {}", e))?;

        // 提取实际数据 - 对应原版第719行
        let actual_data = SecurityManager::extract_actual_data(&json_data)
            .map_err(|e| format!("提取实际数据失败: {}", e))?;

        // 转换为 VerificationData 结构 - 对应原版第3802-3806行的数据结构
        let verification_data = VerificationData {
            source: actual_data.get("source")
                .and_then(|v| v.as_str())
                .unwrap_or("")
                .to_string(),
            timestamp: actual_data.get("timestamp")
                .and_then(|v| v.as_u64())
                .unwrap_or(0),
            qq_number: actual_data.get("qq")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string()),
            // 注意：原版Python没有machine_id字段，这里使用当前机器UUID作为默认值
            machine_id: self.machine_uuid.clone(),
        };

        Ok(verification_data)
    }

    /// 解压缩数据
    fn decompress_data(&self, data: &[u8]) -> Result<Vec<u8>, std::io::Error> {
        let mut decoder = ZlibDecoder::new(data);
        let mut decompressed = Vec::new();
        decoder.read_to_end(&mut decompressed)?;
        Ok(decompressed)
    }

    /// 压缩数据
    fn compress_data(&self, data: &[u8]) -> Result<Vec<u8>, std::io::Error> {
        let mut encoder = ZlibEncoder::new(Vec::new(), Compression::default());
        encoder.write_all(data)?;
        encoder.finish()
    }

    /// 验证验证码
    pub fn verify_code(&self, input_code: &str) -> bool {
        if !self.config.verification.code_verification_enabled {
            return false;
        }

        input_code.trim().to_uppercase() == self.code_verification_code.trim().to_uppercase()
    }

    /// 验证VIP QQ
    pub fn verify_vip_qq(&self, qq_number: &str) -> bool {
        if !self.config.verification.vip_qq_verification_enabled {
            return false;
        }

        self.vip_qq_whitelist.contains(&qq_number.to_string())
    }

    /// 获取关于信息
    /// 完全对应原版Python的get_about_info()方法
    pub fn get_about_info(&self) -> Option<AboutInfoWithVersion> {
        // 创建基础关于信息，包含本地硬编码的版本信息 - 对应原版Python第3524-3526行
        let mut about_info = AboutInfoWithVersion {
            // 添加本地硬编码的版本信息 - 对应原版Python第3525-3526行
            app_name: "YAugment".to_string(),
            app_version: self.current_version.clone(), // 从版本配置获取，对应原版Python第3525行

            // 默认关于信息 - 对应原版Python第3536-3543行
            copyright: "© 2025 Yan - All rights reserved".to_string(),
            email: "<EMAIL>".to_string(),
            qq_group_1_label: "QQ群:".to_string(),
            qq_group_1: "1051756343".to_string(),
            qq_group_2_prefix: "(老年不交流".to_string(),
            qq_group_2: "Five".to_string(),
            qq_group_2_suffix: ")".to_string(),
            config_help_url: "https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0".to_string(),
            token_help_url: "https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf".to_string(),
        };

        // 从远程配置更新信息（如果可用）
        if let Some(version_info) = &self.version_info {
            let remote_about = &version_info.global.about_info;

            // 更新远程配置中的字段
            about_info.copyright = remote_about.copyright.clone();
            about_info.email = remote_about.email.clone();
            about_info.qq_group_1_label = remote_about.qq_group_1_label.clone();
            about_info.qq_group_1 = remote_about.qq_group_1.clone();
            about_info.qq_group_2_prefix = remote_about.qq_group_2_prefix.clone();
            about_info.qq_group_2 = remote_about.qq_group_2.clone();
            about_info.qq_group_2_suffix = remote_about.qq_group_2_suffix.clone();
            about_info.config_help_url = remote_about.config_help_url.clone();
            about_info.token_help_url = remote_about.token_help_url.clone();
        }

        Some(about_info)
    }

    /// 获取配置帮助链接
    pub fn get_config_help_url(&self) -> String {
        self.version_info
            .as_ref()
            .map(|info| info.global.about_info.config_help_url.clone())
            .unwrap_or_else(|| "https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0".to_string())
    }

    /// 获取Token帮助链接
    pub fn get_token_help_url(&self) -> String {
        self.version_info
            .as_ref()
            .map(|info| info.global.about_info.token_help_url.clone())
            .unwrap_or_else(|| "https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf".to_string())
    }

    /// 获取版本信息
    pub fn get_version_info(&self) -> Option<&VersionInfo> {
        self.version_info.as_ref()
    }

    /// 获取当前版本字符串
    pub fn get_current_version_string(&self) -> &str {
        &self.current_version
    }

    /// 获取平台字符串
    pub fn get_platform_string(&self) -> &str {
        &self.platform
    }

    /// 获取公众号二维码
    /// 对应原版Python VerificationDialog._get_gzh_qrcode()方法
    pub async fn get_gzh_qrcode(&self) -> Result<String, String> {
        if let Some(version_info) = &self.version_info {
            // 从版本信息中获取公众号二维码
            if let Some(qr_code_base64) = &version_info.global.code_verification.gzh_qrcode_base64 {
                Ok(qr_code_base64.clone())
            } else {
                // 如果配置中没有，尝试从qrcode_config获取
                let qrcode_config = &version_info.global.code_verification.qrcode_config;

                if qrcode_config.use_remote {
                    if let Some(remote_url) = &qrcode_config.remote_url {
                        // 实现远程获取二维码
                        return self.fetch_remote_qr_image(remote_url).await;
                    }
                }

                // 使用本地文件路径
                if let Some(local_path) = &qrcode_config.local_path {
                    return self.read_local_qr_image(local_path);
                }

                // 回退到默认路径 icons/gzh.jpg
                self.read_local_qr_image("icons/gzh.jpg")
            }
        } else {
            Err("版本信息未加载".to_string())
        }
    }

    /// 读取本地二维码图片并转换为base64
    /// 对应原版Python的本地文件读取逻辑
    fn read_local_qr_image(&self, local_path: &str) -> Result<String, String> {
        use std::env;

        // 获取程序可执行文件的目录
        let exe_dir = env::current_exe()
            .ok()
            .and_then(|exe_path| exe_path.parent().map(|p| p.to_path_buf()));

        let mut possible_paths = Vec::new();

        // 优先从程序安装目录查找（生产环境）
        if let Some(exe_dir) = exe_dir {
            possible_paths.push(exe_dir.join(local_path));
            // 也尝试程序目录的上级目录
            if let Some(parent_dir) = exe_dir.parent() {
                possible_paths.push(parent_dir.join(local_path));
            }
        }

        // 开发模式：相对于项目根目录
        possible_paths.extend(vec![
            format!("../YAugment/{}", local_path),
            format!("../../YAugment/{}", local_path),
            format!("../../../YAugment/{}", local_path),
            format!("../../../../YAugment/{}", local_path),
            format!("../../../../../YAugment/{}", local_path),
            // 当前工作目录
            local_path.to_string(),
            // 绝对路径尝试（开发环境）
            format!("C:/Users/<USER>/Downloads/test8/YAugmentX/YAugment/{}", local_path),
        ].into_iter().map(|s| std::path::PathBuf::from(s)));

        for path in &possible_paths {
            if path.exists() {
                return self.read_image_to_base64(path);
            }
        }

        // 获取当前工作目录和程序目录用于调试
        let current_dir = std::env::current_dir()
            .map(|d| d.display().to_string())
            .unwrap_or_else(|_| "未知".to_string());

        let exe_dir = std::env::current_exe()
            .ok()
            .and_then(|exe_path| exe_path.parent().map(|p| p.display().to_string()))
            .unwrap_or_else(|| "未知".to_string());

        // 列出尝试过的路径用于调试
        let tried_paths: Vec<String> = possible_paths.iter()
            .map(|p| p.display().to_string())
            .collect();

        Err(format!(
            "公众号二维码文件不存在: {}\n当前工作目录: {}\n程序安装目录: {}\n尝试过的路径: {:?}",
            local_path, current_dir, exe_dir, tried_paths
        ))
    }

    /// 读取图片文件并转换为base64
    fn read_image_to_base64(&self, path: &Path) -> Result<String, String> {
        use std::fs;
        use base64::{Engine as _, engine::general_purpose};

        // 读取文件内容
        let image_data = fs::read(path)
            .map_err(|e| format!("读取图片文件失败: {}", e))?;

        // 转换为base64
        let base64_data = general_purpose::STANDARD.encode(&image_data);

        Ok(base64_data)
    }

    /// 从远程URL获取二维码图片并转换为base64
    /// 对应原版Python的远程二维码获取逻辑
    async fn fetch_remote_qr_image(&self, remote_url: &str) -> Result<String, String> {
        // use base64::{Engine as _, engine::general_purpose}; // 暂时未使用
        use tokio::time::{sleep, Duration};

        const MAX_RETRIES: u32 = 3;
        const RETRY_DELAY_MS: u64 = 1000; // 1秒重试间隔

        // 创建HTTP客户端
        let client = reqwest::Client::builder()
            .timeout(std::time::Duration::from_secs(10))
            .build()
            .map_err(|_| "请求远程二维码失败".to_string())?;

        // 重试机制
        for attempt in 1..=MAX_RETRIES {
            match self.fetch_remote_qr_image_once(&client, remote_url).await {
                Ok(base64_data) => return Ok(base64_data),
                Err(_e) => {
                    if attempt < MAX_RETRIES {
                        // 还有重试机会，等待后重试
                        sleep(Duration::from_millis(RETRY_DELAY_MS)).await;
                        continue;
                    } else {
                        // 最后一次尝试失败，返回简化的错误信息
                        return Err("请求远程二维码失败".to_string());
                    }
                }
            }
        }

        Err("请求远程二维码失败".to_string())
    }

    /// 单次远程二维码获取尝试
    async fn fetch_remote_qr_image_once(&self, client: &reqwest::Client, remote_url: &str) -> Result<String, String> {
        use base64::{Engine as _, engine::general_purpose};

        // 发送GET请求获取图片
        let response = client
            .get(remote_url)
            .send()
            .await
            .map_err(|_| "网络请求失败".to_string())?;

        // 检查响应状态
        if !response.status().is_success() {
            return Err("服务器响应错误".to_string());
        }

        // 获取图片数据
        let image_data = response
            .bytes()
            .await
            .map_err(|_| "读取数据失败".to_string())?;

        // 转换为base64
        let base64_data = general_purpose::STANDARD.encode(&image_data);

        Ok(base64_data)
    }
}
