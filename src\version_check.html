<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YAugment 版本验证</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: #1A1D23;  /* 使用深色背景，避免白屏闪烁 */
            color: #FFFFFF;
            overflow: hidden;
        }

        /* 验证码输入框统一样式 */
        .verification-code-digit {
            transition: border-color 0.3s ease, box-shadow 0.3s ease !important;
        }

        .container {
            background-color: #121317;
            border: none;
            padding: 25px;
            text-align: center;
            width: 100%;
            height: 100%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #FFFFFF;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #9CA2AE;
            margin-bottom: 30px;
            font-size: 14px;
        }

        /* 🗑️ 删除状态相关样式：不再显示连接状态 */

        .warning {
            color: #CBAF67;
        }

        .progress {
            width: 100%;
            height: 3px;
            background: #2A2D35;
            border-radius: 2px;
            margin: 20px 0;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: #2B9D7C;
            border-radius: 2px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .btn {
            background: #2B9D7C;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            margin: 10px 5px;
            min-width: 80px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: #34B892;
        }

        .btn.secondary {
            background: #1A1D23;
            color: #9CA2AE;
            border: 1px solid #2A2D35;
        }

        .btn.secondary:hover {
            background: #2A2D35;
            color: #FFFFFF;
        }

        .dialog {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .dialog.show {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .dialog-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }

        .dialog-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .dialog-message {
            color: #666;
            line-height: 1.5;
            margin-bottom: 20px;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: pre-wrap;
            max-width: 100%;
        }

        .hidden {
            display: none;
        }

        /* 隐藏滚动条但保留滚动功能 - 跨平台兼容 */
        .disclaimer-content::-webkit-scrollbar,
        .styled-dialog-content::-webkit-scrollbar {
            width: 0px !important;
            height: 0px !important;
            background: transparent !important;
        }

        .disclaimer-content::-webkit-scrollbar-track,
        .styled-dialog-content::-webkit-scrollbar-track {
            background: transparent !important;
        }

        .disclaimer-content::-webkit-scrollbar-thumb,
        .styled-dialog-content::-webkit-scrollbar-thumb {
            background: transparent !important;
        }

        /* Firefox 隐藏滚动条 */
        .disclaimer-content,
        .styled-dialog-content {
            scrollbar-width: none !important;
        }

        /* 确保滚动功能正常 */
        .disclaimer-content,
        .styled-dialog-content {
            overflow-y: auto !important;
            -webkit-overflow-scrolling: touch !important;
        }

        /* 确保内容区域有足够的高度触发滚动 */
        .disclaimer-content {
            min-height: 100px !important;
            max-height: 300px !important;
        }

        /* styled-dialog-content 使用动态高度 */
        .styled-dialog-content {
            min-height: auto !important;
            max-height: none !important;
            height: auto !important;
        }

        /* 全局隐藏滚动条 - 确保所有滚动区域都被覆盖 */
        *::-webkit-scrollbar {
            width: 0px !important;
            height: 0px !important;
            background: transparent !important;
        }

        * {
            scrollbar-width: none !important;
        }

        /* 确保 body 和 html 也隐藏滚动条 */
        html, body {
            scrollbar-width: none !important;
        }

        html::-webkit-scrollbar, body::-webkit-scrollbar {
            width: 0px !important;
            height: 0px !important;
            background: transparent !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">YAugment</div>
        <div class="subtitle">正在进行版本验证...</div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <!-- 🗑️ 删除状态提示和操作按钮：使用专门的对话框处理连接错误 -->
    </div>

    <!-- 对话框 -->
    <div class="dialog" id="dialog">
        <div class="dialog-content">
            <div class="dialog-title" id="dialogTitle">标题</div>
            <div class="dialog-message" id="dialogMessage">消息内容</div>
            <div>
                <button class="btn" id="dialogOk">确定</button>
                <button class="btn secondary" id="dialogCancel" style="display: none;">取消</button>
            </div>
        </div>
    </div>

    <script>
        // 版本验证逻辑
        // 🗑️ 删除状态步骤：不再显示连接状态提示

        // 进度条更新函数（只更新进度条，不显示状态文字）
        async function updateProgress(progress) {
            const progressBar = document.getElementById('progressBar');
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
            }
            await new Promise(resolve => setTimeout(resolve, 300));
        }

        async function showDialog(title, message, showCancel = false) {
            return new Promise((resolve) => {
                const dialog = document.getElementById('dialog');
                const dialogTitle = document.getElementById('dialogTitle');
                const dialogMessage = document.getElementById('dialogMessage');
                const dialogOk = document.getElementById('dialogOk');
                const dialogCancel = document.getElementById('dialogCancel');

                dialogTitle.textContent = title;
                dialogMessage.innerHTML = message;
                dialogCancel.style.display = showCancel ? 'inline-block' : 'none';

                dialog.classList.add('show');

                dialogOk.onclick = () => {
                    dialog.classList.remove('show');
                    resolve(true);
                };

                dialogCancel.onclick = () => {
                    dialog.classList.remove('show');
                    resolve(false);
                };
            });
        }

        async function performVersionCheck() {
            try {
                // 步骤1: 连接服务器（只更新进度条，不显示文字）
                await updateProgress(20);
                await new Promise(resolve => setTimeout(resolve, 500));

                // 步骤2: 获取版本信息
                await updateProgress(40);
                const versionResult = await window.__TAURI__.core.invoke('check_version');

                // 步骤3: 验证版本
                await updateProgress(60);
                await new Promise(resolve => setTimeout(resolve, 300));

                // 步骤4: 检查更新
                await updateProgress(80);
                await new Promise(resolve => setTimeout(resolve, 300));

                // 处理验证结果
                if (versionResult.maintenance_mode) {
                    await showDialog('系统维护', versionResult.maintenance_message || '系统正在维护中，请稍后再试');
                    await window.__TAURI__.core.invoke('exit_app');
                    return;
                }

                if (versionResult.force_update) {
                    await showForceUpdateDialog(versionResult);
                    await window.__TAURI__.core.invoke('exit_app');
                    return;
                }

                if (versionResult.disclaimer_needed) {
                    // 从后端获取免责声明内容
                    const disclaimerInfo = await window.__TAURI__.core.invoke('get_disclaimer_info');

                    // 直接显示免责声明对话框，就像原版Python一样
                    const agreed = await showDisclaimerDialog(
                        disclaimerInfo.title || "免责声明",
                        disclaimerInfo.content || "",
                        disclaimerInfo.agree_button || "我同意",
                        disclaimerInfo.cancel_button || "我不同意并退出"
                    );

                    if (!agreed) {
                        await window.__TAURI__.core.invoke('exit_app');
                        return;
                    }

                    // 保存用户同意状态
                    if (disclaimerInfo.version) {
                        await window.__TAURI__.core.invoke('agree_disclaimer', { version: disclaimerInfo.version });
                    }
                }

                // 检查是否需要验证 - 对应原版Python的验证流程
                if (versionResult.verification_needed) {
                    // 显示验证对话框
                    const verificationResult = await showVerificationDialog();

                    if (!verificationResult.success) {
                        // 验证失败，直接退出（不显示额外对话框）
                        await window.__TAURI__.core.invoke('exit_app');
                        return;
                    }
                }

                // 步骤5: 验证完成
                await updateProgress(100);

                // 检查是否需要显示平台特定通知 - 对应原版Python的_show_notification
                // 原版Python使用platform_specific_config["notification"]而不是global_notification
                if (versionResult.notification && versionResult.notification.show) {
                    await showGlobalNotificationDialog(
                        versionResult.notification.message,
                        versionResult.notification.level || 'info'
                    );
                }

                // 标记验证完成状态，防止主程序重复验证
                await window.__TAURI__.core.invoke('mark_verification_completed');

                // 在启动主程序前隐藏窗口，避免显示空内容
                await window.__TAURI__.core.invoke('hide_window');

                // 等待一下然后启动主程序
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 关闭验证窗口，启动主程序
                await window.__TAURI__.core.invoke('start_main_app');

            } catch (error) {
                console.error('版本检查失败:', error);

                // 🗑️ 删除状态显示：直接显示错误对话框
                // 所有版本检查失败都显示错误对话框，对应原版Python的处理逻辑
                if (error.includes && error.includes('CONNECTION_ERROR')) {
                    await showConnectionErrorDialog();
                } else {
                    // 其他错误（如解密失败）也显示错误对话框
                    await showVersionCheckErrorDialog(error);
                }
            }
        }

        // 🗑️ 删除重试和退出按钮事件监听器：使用专门的对话框处理

        // 动态调整窗口大小 - 真正跟随对话框的实际尺寸
        async function adjustWindowSize() {
            try {
                console.log('=== adjustWindowSize called ===');

                // 等待DOM完全渲染
                await new Promise(resolve => setTimeout(resolve, 100));

                let activeDialog = null;

                // 检查是否有对话框显示
                const verificationDialog = document.getElementById('verificationDialog');
                const disclaimerDialog = document.getElementById('disclaimerDialog');
                const styledDialogOverlay = document.getElementById('styledDialogOverlay');

                console.log('Dialog elements check:', {
                    verificationDialog: verificationDialog ? verificationDialog.offsetParent !== null : false,
                    disclaimerDialog: disclaimerDialog ? disclaimerDialog.offsetParent !== null : false,
                    styledDialogOverlay: styledDialogOverlay ? styledDialogOverlay.offsetParent !== null : false
                });

                if (verificationDialog && verificationDialog.offsetParent !== null) {
                    activeDialog = verificationDialog;
                    console.log('Found verification dialog');

                } else if (styledDialogOverlay && styledDialogOverlay.offsetParent !== null) {
                    // 查找 styled dialog 的实际对话框元素
                    activeDialog = styledDialogOverlay.querySelector('div[style*="background-color: #121317"]') ||
                                  styledDialogOverlay.querySelector('div[style*="background-color: rgb(18, 19, 23)"]') ||
                                  styledDialogOverlay.querySelector('div[style*="background"]');
                    console.log('Found styled dialog overlay, inner dialog:', activeDialog);
                } else {
                    // 尝试查找任何可见的对话框
                    const allDialogs = document.querySelectorAll('div[style*="background-color"]');
                    for (let dialog of allDialogs) {
                        if (dialog.offsetParent !== null &&
                            (dialog.style.backgroundColor.includes('18, 19, 23') ||
                             dialog.style.backgroundColor === '#121317')) {
                            activeDialog = dialog;
                            console.log('Found dialog by background color:', activeDialog);
                            break;
                        }
                    }

                    if (!activeDialog) {
                        console.log('No active dialog found, returning');
                        return;
                    }
                }

                // 真正动态计算对话框的实际尺寸
                const dialogSize = await measureDialogActualSize(activeDialog);

                console.log('Active dialog:', activeDialog.id || 'unknown');
                console.log('Active dialog element:', activeDialog);
                console.log('Measured dialog size:', dialogSize);

                // 获取当前窗口信息
                const windowInfo = await window.__TAURI__.core.invoke('get_window_info');
                console.log('Current window info:', windowInfo);

                // 检查是否需要调整大小
                const currentWidth = windowInfo.logical_size.width;
                const currentHeight = windowInfo.logical_size.height;

                console.log('Size comparison:', {
                    current: { width: currentWidth, height: currentHeight },
                    target: { width: dialogSize.width, height: dialogSize.height },
                    widthDiff: Math.abs(currentWidth - dialogSize.width),
                    heightDiff: Math.abs(currentHeight - dialogSize.height)
                });

                if (Math.abs(currentWidth - dialogSize.width) < 10 && Math.abs(currentHeight - dialogSize.height) < 10) {
                    console.log('Window size already matches dialog, skipping resize');
                    return;
                }

                // 使用逻辑像素，Tauri 会自动处理 DPI 缩放
                console.log('Resizing window immediately to match dialog:', dialogSize);
                await window.__TAURI__.core.invoke('resize_window', {
                    width: dialogSize.width,
                    height: dialogSize.height
                });

                // 等待一小段时间确保窗口大小调整完成
                await new Promise(resolve => setTimeout(resolve, 50));

                // 重新居中窗口
                console.log('Centering window after resize...');
                await centerWindowOptimized();

                // 验证窗口位置
                const finalWindowInfo = await window.__TAURI__.core.invoke('get_window_info');
                console.log('Window resize and center completed. Final position:', {
                    size: finalWindowInfo.logical_size,
                    position: finalWindowInfo.position
                });
            } catch (error) {
                console.error('窗口调整失败:', error);
            }
        }

        // 测量对话框的实际尺寸
        async function measureDialogActualSize(dialogElement) {
            if (!dialogElement) {
                console.warn('No dialog element provided for measurement');
                return { width: 450, height: 350 };
            }

            // 等待渲染完成
            await new Promise(resolve => requestAnimationFrame(resolve));

            console.log('Measuring dialog:', dialogElement);
            console.log('Dialog styles:', {
                width: dialogElement.style.width,
                height: dialogElement.style.height,
                backgroundColor: dialogElement.style.backgroundColor
            });

            // 检查对话框类型并使用相应的测量方法
            if (dialogElement.classList.contains('verification-dialog')) {
                console.log('Detected verification dialog, using specialized measurement');
                return measureVerificationDialog(dialogElement);
            }

            // 特殊处理 styled dialog - 检查是否包含 styled-dialog-content
            const hasStyledContent = dialogElement.querySelector('.styled-dialog-content');
            const hasBackgroundColor = dialogElement.style.backgroundColor === 'rgb(18, 19, 23)' ||
                                      dialogElement.style.backgroundColor === '#121317';

            if (hasStyledContent || hasBackgroundColor) {
                console.log('Detected styled dialog, using specialized measurement');
                return measureStyledDialog(dialogElement);
            }

            // 对于其他类型的对话框，使用原来的测量方法
            console.log('Using generic dialog measurement');
            return measureGenericDialog(dialogElement);
        }

        // 测量验证对话框的实际尺寸
        async function measureVerificationDialog(dialogElement) {
            console.log('Measuring verification dialog element:', dialogElement);

            // 等待一帧确保布局完成
            await new Promise(resolve => requestAnimationFrame(resolve));

            // 验证对话框的结构分析：
            // - 左侧面板 (flex: 3): 标题 + 验证码输入框 + 信息文本
            // - 右侧面板 (flex: 2): 二维码 (208x208px) + QQ信息
            // - 底部按钮区域
            // - 容器padding: 20px 15px 15px 15px

            // 根据叠加图片分析，微调宽度以匹配原版
            const leftPanelMinWidth = 380;  // 左侧面板宽度，稍微增加以匹配原版
            const rightPanelWidth = 250;    // 右侧面板宽度，稍微增加
            const gap = 20; // 左右面板间距
            const containerPadding = 30; // 左右padding总和 (15px * 2)

            // 计算总宽度：左侧面板 + 间距 + 右侧面板 + 容器padding
            const totalWidth = leftPanelMinWidth + gap + rightPanelWidth + containerPadding;

            // 重新计算高度，让它更高一些匹配原版
            // - 标题高度: ~35px
            // - 验证码输入框: 40px + 间距
            // - 信息文本: ~50px (2行 + 间距)
            // - 二维码高度: 208px (这是最高的元素)
            // - 按钮区域: ~45px
            // - 各种间距和padding: ~60px
            const contentHeight = Math.max(
                35 + 40 + 50 + 20, // 左侧内容高度 + 更多间距
                208 + 20 // 右侧二维码高度 + 上下间距
            );
            const buttonHeight = 45;
            const containerPaddingVertical = 35; // 上下padding (20px + 15px)
            const buttonMargin = 15;

            const totalHeight = contentHeight + buttonHeight + containerPaddingVertical + buttonMargin;

            // 重新设置尺寸限制，基于叠加图片的对比分析
            const minWidth = 650;   // 增加最小宽度以匹配原版
            const maxWidth = 720;   // 适当增加最大宽度
            const minHeight = 350;  // 保持高度不变（已经匹配）
            const maxHeight = 420;  // 保持高度限制不变

            const finalWidth = Math.max(minWidth, Math.min(maxWidth, totalWidth));
            const finalHeight = Math.max(minHeight, Math.min(maxHeight, totalHeight));

            console.log('Verification dialog measurement result:', {
                leftPanelMinWidth,
                rightPanelWidth,
                totalWidth,
                contentHeight,
                totalHeight,
                finalWidth,
                finalHeight
            });

            return {
                width: finalWidth,
                height: finalHeight
            };
        }

        // 测量 styled dialog 的实际尺寸
        async function measureStyledDialog(dialogElement) {
            console.log('Measuring styled dialog element:', dialogElement);

            // 等待一帧确保布局完成
            await new Promise(resolve => requestAnimationFrame(resolve));

            // 直接测量整个对话框元素的高度
            const dialogRect = dialogElement.getBoundingClientRect();
            console.log('Dialog rect:', dialogRect);

            // 计算实际需要的高度
            let actualHeight = Math.ceil(dialogRect.height);

            // 如果对话框有 scrollHeight，使用它
            if (dialogElement.scrollHeight > actualHeight) {
                actualHeight = dialogElement.scrollHeight;
                console.log('Using dialog scrollHeight:', actualHeight);
            }

            console.log('Dialog measurement details:', {
                dialogHeight: dialogRect.height,
                dialogScrollHeight: dialogElement.scrollHeight,
                calculatedHeight: actualHeight
            });

            // 设置合理的最小和最大高度
            const minHeight = 150;  // 降低最小高度
            const maxHeight = 380;  // 修复：设置最大高度为380px，避免窗口过高
            const finalHeight = Math.max(minHeight, Math.min(maxHeight, actualHeight));

            // 宽度优先使用CSS设置的宽度，而不是实际渲染宽度（可能被父容器限制）
            const cssWidth = parseInt(dialogElement.style.width) || 550;
            let actualWidth = Math.max(cssWidth, Math.ceil(dialogRect.width));
            const minWidth = 300;   // 降低最小宽度
            const maxWidth = 700;   // 增加最大宽度以支持600px对话框
            const finalWidth = Math.max(minWidth, Math.min(maxWidth, actualWidth));

            console.log('Styled dialog measurement result:', {
                cssWidth,
                dialogWidth: dialogRect.width,
                dialogHeight: dialogRect.height,
                dialogScrollHeight: dialogElement.scrollHeight,
                actualWidth,
                actualHeight,
                finalWidth,
                finalHeight,
                minWidth,
                maxWidth,
                minHeight,
                maxHeight
            });

            return {
                width: finalWidth,
                height: finalHeight
            };
        }

        // 测量通用对话框的实际尺寸
        async function measureGenericDialog(dialogElement) {
            // 创建一个临时的测量环境
            const measureContainer = document.createElement('div');
            measureContainer.style.cssText = `
                position: absolute;
                top: -10000px;
                left: -10000px;
                visibility: hidden;
                pointer-events: none;
                width: auto;
                height: auto;
                max-width: none;
                max-height: none;
                overflow: visible;
                z-index: -1000;
                background: transparent;
            `;

            // 克隆对话框元素
            const clonedDialog = dialogElement.cloneNode(true);

            // 重置克隆元素的样式，让它显示真实尺寸
            clonedDialog.style.cssText = `
                position: static;
                width: auto;
                height: auto;
                max-width: none;
                max-height: none;
                min-width: none;
                min-height: none;
                display: block;
                visibility: visible;
                box-sizing: border-box;
                background-color: #121317;
                border: none;
                margin: 0;
                padding: 0;
                overflow: visible;
            `;

            // 确保克隆的对话框内容完全展开
            const clonedContent = clonedDialog.querySelector('.styled-dialog-content') ||
                                 clonedDialog.querySelector('.disclaimer-content');

            if (clonedContent) {
                clonedContent.style.cssText += `
                    max-height: none;
                    overflow: visible;
                    height: auto;
                `;
            }

            measureContainer.appendChild(clonedDialog);
            document.body.appendChild(measureContainer);

            // 强制重新计算布局
            clonedDialog.offsetHeight;

            // 等待布局完成
            await new Promise(resolve => requestAnimationFrame(resolve));

            // 获取真实尺寸
            const rect = clonedDialog.getBoundingClientRect();
            let actualWidth = Math.ceil(rect.width);
            let actualHeight = Math.ceil(rect.height);

            // 如果内容有滚动，使用 scrollWidth 和 scrollHeight
            if (clonedDialog.scrollWidth > actualWidth) {
                actualWidth = clonedDialog.scrollWidth;
            }
            if (clonedDialog.scrollHeight > actualHeight) {
                actualHeight = clonedDialog.scrollHeight;
            }

            // 清理临时元素
            document.body.removeChild(measureContainer);

            // 设置最小尺寸限制
            const minWidth = 300;
            const minHeight = 200;
            const maxWidth = 1200;
            const maxHeight = 800;

            actualWidth = Math.max(minWidth, Math.min(maxWidth, actualWidth));
            actualHeight = Math.max(minHeight, Math.min(maxHeight, actualHeight));

            console.log('Generic dialog measurement details:', {
                originalRect: { width: rect.width, height: rect.height },
                scrollSize: { width: clonedDialog.scrollWidth, height: clonedDialog.scrollHeight },
                finalSize: { width: actualWidth, height: actualHeight }
            });

            return {
                width: actualWidth,
                height: actualHeight
            };
        }

        // 免责声明对话框函数 - 使用公共的showStyledDialog函数，无图标模式
        async function showDisclaimerDialog(title = "免责声明", content = "", agreeText = "我同意", cancelText = "退出") {
            // 使用公共的showStyledDialog函数，设置showIcon为false以隐藏图标
            const result = await showStyledDialog(
                title,           // 标题
                content,         // 内容
                'info',          // 图标类型（虽然不显示，但需要提供）
                [cancelText, agreeText],  // 按钮数组：[取消, 同意] - 调换顺序
                null,            // 无更新链接
                false,           // 不显示更新链接按钮
                false            // 不显示图标
            );

            // 返回结果：0表示点击了第一个按钮（取消），1表示点击了第二个按钮（同意）
            return result === 1;
        }



        // 强制更新对话框函数 - 使用公共的showStyledDialog函数
        async function showForceUpdateDialog(versionResult) {
            // 格式化更新消息 - 完全按照原版Python _format_update_message方法
            // 先显示强制更新提示
            let message = '您必须更新到最新版本才能继续使用\n';

            // 然后显示版本信息
            message += `当前版本: ${versionResult.current_version || '0.1.8'}\n最新版本: ${versionResult.latest_version}`;

            if (versionResult.update_date) {
                message += `\n更新日期: ${versionResult.update_date}`;
            }

            if (versionResult.changes && versionResult.changes.length > 0) {
                message += '\n\n更新内容:\n';
                versionResult.changes.forEach((change, index) => {
                    message += `${index + 1}. ${change}\n`;
                });
            }

            if (versionResult.update_url) {
                message += `\n更新链接: ${versionResult.update_url}`;
            }

            // 使用公共的showStyledDialog函数 - 对应原版Python StyledMessageBox.showMessage
            await showStyledDialog(
                '需要更新',  // 对应原版Python的"需要更新"标题
                message,
                'error',     // 对应原版Python的StyledMessageBox.CRITICAL
                ['退出'],    // 对应原版Python的["退出"]按钮
                versionResult.update_url,  // 更新链接
                true         // 显示更新链接按钮
            );
        }

        // 版本检查错误对话框函数 - 对应原版Python的_show_connection_error方法
        async function showVersionCheckErrorDialog(error) {
            let errorMessage;
            let title;

            // 根据错误类型显示不同的通用错误信息
            if (typeof error === 'string' && error.includes('CONFIG_ERROR')) {
                // 配置错误 - 对应原版Python的_show_critical_config_error
                title = '获取的配置错误';
                errorMessage = "无法加载配置，关键信息缺失或格式不正确\n\n请更换网络或更新 YAugment 版本后重试\n\n";
            } else {
                // 其他版本检查错误 - 对应原版Python的_show_connection_error
                title = '版本检查失败';
                errorMessage = "版本检查失败\n\n• 请关闭重新启动\n• 请不要打开抓包软件\n• 多次失败请联系 <EMAIL>";
            }

            // 使用公共的showStyledDialog函数显示错误
            await showStyledDialog(
                title,          // 标题
                errorMessage,   // 通用错误消息（不暴露详细信息）
                'error',        // 错误图标
                ['退出']        // 只有退出按钮，对应原版Python的["退出"]
            );

            // 显示错误对话框后退出应用，对应原版Python返回False的逻辑
            await window.__TAURI__.core.invoke('exit_app');
        }

        // 连接错误对话框函数 - 使用公共的showStyledDialog函数
        async function showConnectionErrorDialog() {
            const title = '连接错误';
            const content = `无法连接到验证服务器，请检查您的网络连接

YAugment 需要连接上验证服务器才能正常使用

请尝试开关代理、更换节点、更换网络后进行重试

验证服务器在国外，内地网络不一定连的上

如果依旧无法连接，请查看官方文档是否是版本太低，旧的服务器已经弃用了

官方文档地址（往下滑）：
https://docs.qq.com/aio/DV2VKUnNaeFRyRGRH?p=DKRZhtXI98ELAa724va8q8`;

            // 使用公共的showStyledDialog函数
            await showStyledDialog(
                title,           // 标题
                content,         // 内容
                'error',         // 错误图标
                ['确定'],        // 确定按钮
                null,            // 无更新链接
                false,           // 不显示更新链接按钮
                true             // 显示图标
            );

            // 显示错误对话框后退出应用
            await window.__TAURI__.core.invoke('exit_app');
        }



        // 显示验证对话框 - 完全对应原版Python的VerificationDialog
        async function showVerificationDialog() {
            return new Promise(async (resolve) => {
                // 第一步：在开始任何操作前立即隐藏窗口
                try {
                    await window.__TAURI__.core.invoke('hide_window');
                    console.log('Window hidden at start of verification dialog');
                } catch (error) {
                    console.warn('Failed to hide window at start:', error);
                }

                // 第二步：立即隐藏现有对话框，避免显示空框
                const existingOverlay = document.getElementById('verificationDialogOverlay') ||
                                       document.getElementById('styledDialogOverlay');
                if (existingOverlay) {
                    existingOverlay.style.opacity = '0';
                    existingOverlay.style.visibility = 'hidden';
                }

                // 第三步：隐藏页面内容
                document.body.style.opacity = '0';
                document.body.style.visibility = 'hidden';

                // 创建验证对话框HTML - 完全按照原版Python布局
                const dialogHTML = `
                    <div id="verificationDialogOverlay" style="
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: transparent;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 10000;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    ">
                        <div class="verification-dialog" id="verificationDialog" style="
                            background-color: #121317;
                            border: none;
                            width: 100%;
                            height: 100%;
                            box-sizing: border-box;
                            cursor: default;
                            display: flex;
                            flex-direction: column;
                            padding: 20px 15px 15px 15px;
                        ">
                            <!-- 水平布局 - 对应原版Python content_layout -->
                            <div style="
                                display: flex;
                                flex: 1;
                                gap: 20px;
                            ">
                                <!-- 左侧面板 - 对应原版Python left_panel (flex: 3) -->
                                <div style="
                                    flex: 3;
                                    display: flex;
                                    flex-direction: column;
                                    justify-content: center;
                                ">
                                    <!-- 状态提示标签 - 对应原版Python status_label -->
                                    <div id="statusLabel" style="
                                        color: #FFFFFF;
                                        font-weight: bold;
                                        font-size: 16px;
                                        font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                                        text-align: center;
                                        padding: 5px 0;
                                        margin-bottom: 10px;
                                    ">请微信扫码关注公众号获取验证码</div>

                                    <!-- 验证码输入框容器 - 对应原版Python code_input_layout -->
                                    <div id="codeInputContainer" style="
                                        display: flex;
                                        gap: 5px;
                                        justify-content: center;
                                        align-items: center;
                                    ">
                                        <input type="text" class="verification-code-digit" maxlength="1" data-index="0" autocomplete="off" placeholder="" style="
                                            width: 40px;
                                            height: 40px;
                                            text-align: center;
                                            font-size: 18px;
                                            font-weight: bold;
                                            background-color: #1A1D23;
                                            border: 1px solid #2A2E36;
                                            border-radius: 6px;
                                            color: #FFFFFF;
                                            outline: none;
                                            transition: border-color 0.3s ease, box-shadow 0.3s ease, border-width 0.3s ease, transform 0.2s ease;
                                            user-select: none;
                                        ">
                                        <input type="text" class="verification-code-digit" maxlength="1" data-index="1" autocomplete="off" placeholder="" style="
                                            width: 40px;
                                            height: 40px;
                                            text-align: center;
                                            font-size: 18px;
                                            font-weight: bold;
                                            background-color: #1A1D23;
                                            border: 1px solid #2A2E36;
                                            border-radius: 6px;
                                            color: #FFFFFF;
                                            outline: none;
                                            transition: border-color 0.3s ease, box-shadow 0.3s ease, border-width 0.3s ease, transform 0.2s ease;
                                            user-select: none;
                                        ">
                                        <input type="text" class="verification-code-digit" maxlength="1" data-index="2" autocomplete="off" placeholder="" style="
                                            width: 40px;
                                            height: 40px;
                                            text-align: center;
                                            font-size: 18px;
                                            font-weight: bold;
                                            background-color: #1A1D23;
                                            border: 1px solid #2A2E36;
                                            border-radius: 6px;
                                            color: #FFFFFF;
                                            outline: none;
                                            transition: border-color 0.3s ease, box-shadow 0.3s ease, border-width 0.3s ease, transform 0.2s ease;
                                            user-select: none;
                                        ">
                                        <input type="text" class="verification-code-digit" maxlength="1" data-index="3" autocomplete="off" placeholder="" style="
                                            width: 40px;
                                            height: 40px;
                                            text-align: center;
                                            font-size: 18px;
                                            font-weight: bold;
                                            background-color: #1A1D23;
                                            border: 1px solid #2A2E36;
                                            border-radius: 6px;
                                            color: #FFFFFF;
                                            outline: none;
                                            transition: border-color 0.3s ease, box-shadow 0.3s ease, border-width 0.3s ease, transform 0.2s ease;
                                            user-select: none;
                                        ">
                                        <input type="text" class="verification-code-digit" maxlength="1" data-index="4" autocomplete="off" placeholder="" style="
                                            width: 40px;
                                            height: 40px;
                                            text-align: center;
                                            font-size: 18px;
                                            font-weight: bold;
                                            background-color: #1A1D23;
                                            border: 1px solid #2A2E36;
                                            border-radius: 6px;
                                            color: #FFFFFF;
                                            outline: none;
                                            transition: border-color 0.3s ease, box-shadow 0.3s ease, border-width 0.3s ease, transform 0.2s ease;
                                            user-select: none;
                                        ">
                                        <input type="text" class="verification-code-digit" maxlength="1" data-index="5" autocomplete="off" placeholder="" style="
                                            width: 40px;
                                            height: 40px;
                                            text-align: center;
                                            font-size: 18px;
                                            font-weight: bold;
                                            background-color: #1A1D23;
                                            border: 1px solid #2A2E36;
                                            border-radius: 6px;
                                            color: #FFFFFF;
                                            outline: none;
                                            transition: border-color 0.3s ease, box-shadow 0.3s ease, border-width 0.3s ease, transform 0.2s ease;
                                            user-select: none;
                                        ">
                                    </div>

                                    <!-- 信息文本 - 对应原版Python info_text_label -->
                                    <div id="infoTextLabel" style="
                                        color: #9CA2AE;
                                        font-size: 12px;
                                        font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                                        text-align: center;
                                        line-height: 1.5;
                                        margin-top: 15px;
                                        word-wrap: break-word;
                                    ">YAugment为免费软件<br>添加验证并非为了收费，只是为了防止泛滥和倒卖狗</div>
                                </div>

                                <!-- 右侧面板 - 对应原版Python right_panel (flex: 2) -->
                                <div style="
                                    flex: 2;
                                    display: flex;
                                    flex-direction: column;
                                    justify-content: center;
                                    align-items: center;
                                ">
                                    <!-- 二维码容器 - 对应原版Python qr_container (208x208px) -->
                                    <div id="qrContainer" style="
                                        background-color: #1A1D23;
                                        border: 2px solid #2A2E36;
                                        border-radius: 18px;
                                        padding: 12px;
                                        display: flex;
                                        justify-content: center;
                                        align-items: center;
                                        width: 208px;
                                        height: 208px;
                                        box-sizing: border-box;
                                    ">
                                        <img id="qrImage" style="
                                            width: 180px;
                                            height: 180px;
                                            border-radius: 12px;
                                            display: none;
                                        ">
                                        <div id="qrPlaceholder" style="
                                            color: #9CA2AE;
                                            font-size: 14px;
                                            text-align: center;
                                        ">正在加载二维码...</div>
                                    </div>

                                    <!-- QQ信息显示 -->
                                    <div id="qqInfoLabel" style="
                                        color: #9CA2AE;
                                        font-size: 12px;
                                        font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                                        text-align: center;
                                        display: none;
                                    "></div>
                                </div>
                            </div>

                            <!-- 按钮区域 - 对应原版Python button_layout -->
                            <div style="
                                display: flex;
                                gap: 10px;
                                justify-content: flex-end;
                                margin-top: 10px;
                            ">
                                <!-- 退出按钮 -->
                                <button id="cancelBtn" onclick="(async () => await closeVerificationDialog(false))()" style="
                                    background-color: #1A1D23;
                                    color: #9CA2AE;
                                    border: 1px solid #2A2E36;
                                    border-radius: 6px;
                                    padding: 8px 16px;
                                    font-size: 14px;
                                    font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                ">退出</button>

                                <!-- 我是赞赏用户按钮 -->
                                <button id="vipBtn" onclick="switchToVipMode()" style="
                                    background-color: #2B9D7C;
                                    color: white;
                                    border: none;
                                    border-radius: 6px;
                                    padding: 8px 16px;
                                    font-weight: bold;
                                    font-size: 14px;
                                    font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                ">我是赞赏用户</button>

                                <!-- 刷新按钮 -->
                                <button id="refreshBtn" onclick="refreshQRCode()" style="
                                    background-color: #1A1D23;
                                    color: #9CA2AE;
                                    border: 1px solid #2A2E36;
                                    border-radius: 6px;
                                    padding: 8px 16px;
                                    font-size: 14px;
                                    font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                    display: none;
                                ">刷新</button>



                                <!-- 验证码验证按钮（VIP模式返回） -->
                                <button id="backToCodeBtn" onclick="switchToCodeMode()" style="
                                    background-color: #2B9D7C;
                                    color: white;
                                    border: none;
                                    border-radius: 6px;
                                    padding: 8px 16px;
                                    font-weight: bold;
                                    font-size: 14px;
                                    font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                                    cursor: pointer;
                                    transition: all 0.3s ease;
                                    display: none;
                                ">验证码验证</button>
                            </div>
                        </div>
                    </div>
                `;

                // 清除旧内容并添加新的对话框HTML
                document.body.innerHTML = '';  // 清除所有旧内容
                document.body.insertAdjacentHTML('beforeend', dialogHTML);

                // 恢复页面可见性（但保持透明，等待后续显示）
                document.body.style.visibility = 'visible';
                document.body.style.opacity = '0';

                // 存储resolve函数和初始化状态
                window.verificationDialogResolve = resolve;
                window.verificationMode = "code_verification"; // 默认验证码模式

                // 获取对话框元素
                const overlay = document.getElementById('verificationDialogOverlay');
                const dialog = document.getElementById('verificationDialog');

                // 确保新对话框也是隐藏状态
                overlay.style.opacity = '0';
                overlay.style.visibility = 'hidden';

                // 禁用背景滚动
                document.body.style.overflow = 'hidden';

                // 设置输入框事件
                setupVerificationInputs();

                // 强制重绘，确保新内容完全渲染
                overlay.offsetHeight; // 触发重绘

                // 异步预计算对话框大小并调整窗口（窗口已在函数开始时隐藏）
                (async () => {
                    try {
                        // 确保窗口完全隐藏后再开始任何调整
                        await new Promise(resolve => setTimeout(resolve, 100));

                        console.log('Pre-calculating verification dialog size...');

                        // 临时显示对话框以测量大小（但保持透明）
                        overlay.style.opacity = '0';
                        overlay.style.visibility = 'visible';

                        // 等待DOM渲染完成
                        await new Promise(resolve => requestAnimationFrame(resolve));

                        // 测量对话框大小
                        const dialogSize = await measureDialogActualSize(dialog);
                        console.log('Pre-calculated verification dialog size:', dialogSize);

                        // 调整窗口大小和位置
                        await window.__TAURI__.core.invoke('resize_window', {
                            width: dialogSize.width,
                            height: dialogSize.height
                        });

                        // 等待窗口调整完成
                        await new Promise(resolve => setTimeout(resolve, 50));

                        // 居中窗口
                        await centerWindowOptimized();

                        console.log('Verification dialog window pre-adjusted');

                        // 确保隐藏至少1秒后再显示窗口
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // 显示对话框内容
                        overlay.style.visibility = 'visible';
                        overlay.style.opacity = '1';

                        // 恢复页面完全可见性
                        document.body.style.opacity = '1';

                        // 强制重绘并等待DOM完全渲染
                        overlay.offsetHeight; // 触发重绘
                        await new Promise(resolve => {
                            requestAnimationFrame(() => {
                                requestAnimationFrame(() => {
                                    // 再次确保内容已渲染
                                    overlay.offsetHeight;
                                    resolve();
                                });
                            });
                        });

                        // 显示整个应用程序窗口
                        await showWindowMacOptimized();
                        console.log('Verification dialog window shown');

                        // 聚焦第一个输入框
                        setTimeout(() => {
                            const firstInput = document.querySelector('.verification-code-digit[data-index="0"]');
                            if (firstInput) {
                                firstInput.focus();
                            }
                        }, 100);

                    } catch (error) {
                        console.warn('Failed to pre-adjust verification dialog:', error);
                        // 如果预调整失败，确保窗口可见并显示对话框
                        try {
                            // 即使在错误情况下也确保隐藏至少1秒
                            await new Promise(resolve => setTimeout(resolve, 1000));

                            // 显示对话框内容
                            overlay.style.visibility = 'visible';
                            overlay.style.opacity = '1';

                            // 恢复页面完全可见性
                            document.body.style.opacity = '1';

                            // 强制重绘并等待DOM完全渲染
                            overlay.offsetHeight; // 触发重绘
                            await new Promise(resolve => {
                                requestAnimationFrame(() => {
                                    requestAnimationFrame(() => {
                                        // 再次确保内容已渲染
                                        overlay.offsetHeight;
                                        resolve();
                                    });
                                });
                            });

                            await showWindowMacOptimized();
                        } catch (showError) {
                            console.warn('Failed to show window:', showError);
                            overlay.style.visibility = 'visible';
                            overlay.style.opacity = '1';
                            document.body.style.opacity = '1';
                        }

                        // 聚焦第一个输入框
                        setTimeout(() => {
                            const firstInput = document.querySelector('.verification-code-digit[data-index="0"]');
                            if (firstInput) {
                                firstInput.focus();
                            }
                        }, 100);
                    }
                })();

                // 开始加载二维码
                setTimeout(() => {
                    loadQRCode();
                }, 500);
            });
        }

        // 设置验证码输入框事件
        function setupVerificationInputs() {
            const inputs = document.querySelectorAll('.verification-code-digit');

            inputs.forEach((input, index) => {
                // 输入法状态跟踪
                let isComposing = false;

                // 输入法开始事件
                input.addEventListener('compositionstart', (e) => {
                    isComposing = true;
                });

                // 输入法结束事件
                input.addEventListener('compositionend', (e) => {
                    isComposing = false;
                    // 输入法结束后处理最终输入
                    let value = e.target.value;
                    if (value) {
                        // 过滤非法字符，只保留字母和数字
                        value = value.replace(/[^A-Za-z0-9]/g, '');
                        if (value) {
                            // 只取第一个字符，转大写
                            const char = value.charAt(0).toUpperCase();
                            e.target.value = char;
                        } else {
                            // 如果过滤后没有有效字符，清空输入框
                            e.target.value = '';
                            return;
                        }

                        // 清除错误状态
                        clearInputErrors();

                        // 移动到下一个输入框
                        if (index < inputs.length - 1) {
                            inputs[index + 1].focus();
                        }

                        // 检查是否输入完整，自动验证
                        if (window.verificationMode === "code_verification") {
                            const allFilled = Array.from(inputs).every(inp => inp.value.length === 1);
                            if (allFilled && !window.isVerifying) {
                                window.isVerifying = true;
                                setTimeout(() => checkVerification(), 100);
                            }
                        }
                    }
                });

                // 粘贴事件 - 自动填充多个输入框
                input.addEventListener('paste', (e) => {
                    e.preventDefault();
                    const pastedText = (e.clipboardData || window.clipboardData).getData('text');
                    // 只保留字母和数字，转换为大写
                    const cleanText = pastedText.replace(/[^A-Za-z0-9]/g, '').toUpperCase();

                    // 如果粘贴的是完整的6位验证码，从第一个输入框开始填充
                    if (cleanText.length === 6) {
                        // 清空所有输入框
                        inputs.forEach(inp => inp.value = '');

                        // 从第一个输入框开始填充
                        for (let i = 0; i < 6; i++) {
                            inputs[i].value = cleanText[i];
                        }

                        // 聚焦到最后一个输入框
                        inputs[5].focus();
                    } else {
                        // 部分验证码，从当前输入框开始填充
                        for (let i = 0; i < cleanText.length && (index + i) < inputs.length; i++) {
                            inputs[index + i].value = cleanText[i];
                        }

                        // 聚焦到最后一个填充的输入框的下一个，或最后一个
                        const nextIndex = Math.min(index + cleanText.length, inputs.length - 1);
                        inputs[nextIndex].focus();
                    }

                    // 清除错误状态
                    clearInputErrors();

                    // 检查是否填充完整，自动验证（增加延迟确保DOM更新完成）
                    if (window.verificationMode === "code_verification") {
                        setTimeout(() => {
                            const allFilled = Array.from(inputs).every(inp => inp.value.length === 1);
                            if (allFilled && !window.isVerifying) {
                                window.isVerifying = true;
                                setTimeout(() => checkVerification(), 200); // 增加延迟
                            }
                        }, 50); // 确保DOM更新完成
                    }
                });

                // 键盘按下事件 - 阻止非法字符输入和处理导航
                input.addEventListener('keydown', (e) => {
                    // 检查是否处于错误状态
                    const isError = e.target.classList.contains('error');

                    // 处理左右箭头键导航（错误状态下禁用）
                    if (e.key === 'ArrowLeft' && index > 0 && !isError) {
                        e.preventDefault();
                        inputs[index - 1].focus();
                        return;
                    }

                    if (e.key === 'ArrowRight' && index < inputs.length - 1 && !isError) {
                        e.preventDefault();
                        inputs[index + 1].focus();
                        return;
                    }

                    // 错误状态下阻止箭头键操作
                    if (isError && (e.key === 'ArrowLeft' || e.key === 'ArrowRight')) {
                        e.preventDefault();
                        return;
                    }

                    // 特殊处理Backspace键 - 如果当前输入框为空，跳转到前一个（错误状态下禁用）
                    if (e.key === 'Backspace' && !e.target.value && index > 0 && !isError) {
                        inputs[index - 1].focus();
                        return;
                    }

                    // 允许的控制键
                    const allowedKeys = [
                        'Backspace', 'Delete', 'Tab', 'Enter', 'Escape',
                        'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
                        'Home', 'End', 'PageUp', 'PageDown'
                    ];

                    // 如果是控制键，允许通过
                    if (allowedKeys.includes(e.key)) {
                        return;
                    }

                    // 如果是Ctrl+组合键（如Ctrl+A, Ctrl+C, Ctrl+V等），允许通过
                    if (e.ctrlKey || e.metaKey) {
                        return;
                    }

                    // 检查是否为字母或数字
                    const isValidChar = /^[A-Za-z0-9]$/.test(e.key);

                    if (!isValidChar) {
                        e.preventDefault(); // 阻止非法字符输入
                        return;
                    }

                    // 如果当前输入框已有内容，新输入会自动替换
                    if (e.target.value.length > 0) {
                        // 清空当前内容，让新字符替换
                        e.target.value = '';
                    }
                });

                // 输入事件
                input.addEventListener('input', (e) => {
                    // 如果正在使用输入法，跳过处理，等待compositionend事件
                    if (isComposing) {
                        return;
                    }

                    let value = e.target.value;

                    // 强制过滤：只保留字母和数字
                    value = value.replace(/[^A-Za-z0-9]/g, '');

                    // 处理多字符输入（如粘贴或快速输入）
                    if (value.length > 1) {
                        const cleanText = value.toUpperCase();
                        // 只取第一个字符放在当前框
                        e.target.value = cleanText.charAt(0);

                        // 如果有更多字符，填充到后续输入框
                        if (cleanText.length > 1) {
                            for (let i = 1; i < cleanText.length && (index + i) < inputs.length; i++) {
                                inputs[index + i].value = cleanText[i];
                            }
                            // 聚焦到最后填充的下一个框
                            const nextIndex = Math.min(index + cleanText.length, inputs.length - 1);
                            inputs[nextIndex].focus();
                        } else if (index < inputs.length - 1) {
                            inputs[index + 1].focus();
                        }

                        // 清除错误状态
                        clearInputErrors();

                        // 检查是否输入完整，自动验证
                        if (window.verificationMode === "code_verification") {
                            const allFilled = Array.from(inputs).every(inp => inp.value.length === 1);
                            if (allFilled && !window.isVerifying) {
                                window.isVerifying = true;
                                setTimeout(() => checkVerification(), 100);
                            }
                        }
                        return;
                    }

                    // 单字符输入处理
                    if (value.length === 1) {
                        // 自动转换为大写（对应原版Python逻辑）
                        value = value.toUpperCase();
                        e.target.value = value;

                        // 更新光标显示状态
                        updateCursorVisibility();

                        // 清除错误状态（对应原版Python的_clear_all_input_errors）
                        clearInputErrors();

                        // 移动到下一个输入框
                        if (index < inputs.length - 1) {
                            inputs[index + 1].focus();
                        }

                        // 检查是否输入完整，自动验证（对应原版Python逻辑）
                        if (window.verificationMode === "code_verification") {
                            const allFilled = Array.from(inputs).every(inp => inp.value.length === 1);
                            if (allFilled && !window.isVerifying) {
                                window.isVerifying = true; // 设置验证锁
                                setTimeout(() => {
                                    checkVerification(); // 延迟100ms验证，对应原版Python
                                }, 100);
                            }
                        }
                    } else if (value.length === 0) {
                        // 处理删除操作 - 内容被清空时更新光标状态
                        updateCursorVisibility();
                    }
                });

                // 额外的键盘事件处理（Enter键提交）
                input.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        submitVerification();
                    }
                });

                // 焦点事件 - 只在非错误状态下显示白色边框效果
                input.addEventListener('focus', (e) => {
                    // 保存当前状态，用于失焦时恢复
                    e.target.dataset.wasError = e.target.classList.contains('error') ? 'true' : 'false';

                    // 检查是否处于错误状态
                    const isError = e.target.classList.contains('error');

                    if (!isError) {
                        // 只在非错误状态下显示白色边框焦点效果
                        e.target.style.border = '3px solid #FFFFFF';
                        e.target.style.background = 'rgba(255, 255, 255, 0.05)';
                        e.target.style.boxShadow = '0 0 15px rgba(255, 255, 255, 0.4)';
                        e.target.style.transform = 'scale(1.05)';
                    }
                    // 错误状态下不改变样式，保持红色边框
                });

                // 失焦事件 - 根据状态恢复样式
                input.addEventListener('blur', (e) => {
                    const isCurrentlyError = e.target.classList.contains('error');

                    if (isCurrentlyError) {
                        // 恢复到错误状态样式
                        e.target.style.border = '1px solid #BC4A59';
                        e.target.style.background = 'rgba(255, 99, 71, 0.1)';
                        e.target.style.boxShadow = 'none';
                        e.target.style.transform = 'scale(1)';
                    } else {
                        // 恢复到正常状态样式
                        e.target.style.border = '1px solid #2A2E36';
                        e.target.style.background = '#1A1D23';
                        e.target.style.boxShadow = 'none';
                        e.target.style.transform = 'scale(1)';
                    }
                });

                // 粘贴事件
                input.addEventListener('paste', (e) => {
                    e.preventDefault();
                    const paste = (e.clipboardData || window.clipboardData).getData('text');
                    // 保留字母和数字，转换为大写
                    const cleanCode = paste.replace(/[^A-Za-z0-9]/g, '').toUpperCase().slice(0, 6);

                    // 清空所有输入框
                    inputs.forEach(inp => inp.value = '');

                    // 填充验证码到输入框
                    for (let i = 0; i < cleanCode.length && i < inputs.length; i++) {
                        inputs[i].value = cleanCode[i];
                    }

                    // 更新光标显示状态
                    updateCursorVisibility();

                    // 粘贴完整6位验证码后，自动验证
                    if (cleanCode.length === 6 && !window.isVerifying) {
                        window.isVerifying = true;
                        setTimeout(() => {
                            checkVerification();
                        }, 100);
                    }
                });

                // 焦点事件
                input.addEventListener('focus', (e) => {
                    // 不自动选中文本，避免蓝色选中效果
                    // 更新光标显示状态
                    updateCursorVisibility();
                });

                // 失去焦点事件
                input.addEventListener('blur', (e) => {
                    // 更新光标显示状态
                    updateCursorVisibility();
                });
            });
        }

        // 切换到VIP模式 - 对应原版Python的_switch_to_vip_mode
        function switchToVipMode() {
            // 重置验证状态
            window.isVerifying = false;

            // 清除错误状态
            clearInputErrors();

            // 重置状态文字颜色和内容
            const statusLabel = document.getElementById('statusLabel');
            statusLabel.style.color = '#FFFFFF';
            statusLabel.textContent = '请扫码登录QQ'; // 设置初始状态文字

            // 隐藏QQ信息（切换时重置）
            const qqInfoLabel = document.getElementById('qqInfoLabel');
            if (qqInfoLabel) {
                qqInfoLabel.style.display = 'none';
                qqInfoLabel.textContent = '';
            }

            // 清除之前的定时器
            if (window.verificationStatusInterval) {
                clearInterval(window.verificationStatusInterval);
                window.verificationStatusInterval = null;
            }

            window.verificationMode = "vip_qq_verification";
            updateUIForMode();
            loadQRCode();
        }

        // 切换到验证码模式 - 对应原版Python的_switch_to_code_mode
        function switchToCodeMode() {
            // 重置验证状态
            window.isVerifying = false;

            // 清除错误状态
            clearInputErrors();

            // 重置状态文字颜色和内容
            const statusLabel = document.getElementById('statusLabel');
            statusLabel.style.color = '#FFFFFF';
            statusLabel.textContent = ''; // 清除状态文字

            // 隐藏QQ信息
            const qqInfoLabel = document.getElementById('qqInfoLabel');
            if (qqInfoLabel) {
                qqInfoLabel.style.display = 'none';
                qqInfoLabel.textContent = '';
            }

            // 清除QQ验证相关的定时器
            if (window.verificationStatusInterval) {
                clearInterval(window.verificationStatusInterval);
                window.verificationStatusInterval = null;
            }

            // 清空所有验证码输入框（对应原版Python逻辑）
            const inputs = document.querySelectorAll('.verification-code-digit');
            inputs.forEach(input => input.value = '');

            window.verificationMode = "code_verification";
            updateUIForMode();
            loadQRCode();

            // 聚焦第一个输入框 - 增加延迟确保UI更新完成
            setTimeout(() => {
                if (inputs[0]) {
                    inputs[0].focus();
                }
            }, 300);
        }

        // 根据模式更新UI - 对应原版Python的_update_ui_for_mode
        function updateUIForMode() {
            const statusLabel = document.getElementById('statusLabel');
            const infoTextLabel = document.getElementById('infoTextLabel');
            const codeInputContainer = document.getElementById('codeInputContainer');
            const qqInfoLabel = document.getElementById('qqInfoLabel');
            const vipBtn = document.getElementById('vipBtn');
            const refreshBtn = document.getElementById('refreshBtn');
            const backToCodeBtn = document.getElementById('backToCodeBtn');

            if (window.verificationMode === "code_verification") {
                // 验证码模式
                statusLabel.textContent = '请微信扫码关注公众号获取验证码';
                infoTextLabel.innerHTML = 'YAugment为免费软件<br>添加验证并非为了收费，只是为了防止泛滥和倒卖狗';
                codeInputContainer.style.display = 'flex';
                qqInfoLabel.style.display = 'none';
                vipBtn.style.display = 'inline-block';
                refreshBtn.style.display = 'none';
                backToCodeBtn.style.display = 'none';
            } else if (window.verificationMode === "vip_qq_verification") {
                // VIP QQ模式
                statusLabel.textContent = '请扫码登录QQ';
                infoTextLabel.innerHTML = 'YAugment为免费软件，为了防止泛滥和倒卖狗，我添加验证码验证才能使用，但为了更方便支持我的各位老大老板们，只要赞赏超过100￥的，您可以直接QQ扫码通过验证，无需关注公众号获取验证码<br>❤感谢您的支持❤';
                codeInputContainer.style.display = 'none';
                qqInfoLabel.style.display = 'block';
                vipBtn.style.display = 'none';
                refreshBtn.style.display = 'inline-block';
                backToCodeBtn.style.display = 'inline-block';
            }
        }

        // 加载二维码 - 对应原版Python的start_qr_login
        async function loadQRCode() {
            const qrImage = document.getElementById('qrImage');
            const qrPlaceholder = document.getElementById('qrPlaceholder');

            try {
                if (window.verificationMode === "code_verification") {
                    // 获取公众号二维码
                    qrPlaceholder.textContent = '正在加载公众号二维码...';

                    const qrData = await window.__TAURI__.core.invoke('get_gzh_qrcode');

                    if (qrData && qrData.success) {
                        qrImage.src = `data:image/jpeg;base64,${qrData.image_base64}`;
                        qrImage.style.display = 'block';
                        qrPlaceholder.style.display = 'none';
                    } else {
                        qrPlaceholder.textContent = qrData?.error || '公众号二维码图片缺失';
                    }
                } else if (window.verificationMode === "vip_qq_verification") {
                    // 获取QQ登录二维码
                    qrPlaceholder.textContent = '正在获取QQ登录二维码...';

                    const qrData = await window.__TAURI__.core.invoke('generate_verification_qr');

                    if (qrData && qrData.qr_code) {
                        // 后端返回的qr_code已经包含完整的data:image/png;base64,前缀
                        qrImage.src = qrData.qr_code;
                        qrImage.style.display = 'block';
                        qrPlaceholder.style.display = 'none';

                        // 注意：后台QQ状态检查已经在generate_verification_qr中启动了，无需重复调用

                        // 启动验证状态检查，等待后端完成验证
                        startVerificationStatusCheck();
                    } else {
                        qrPlaceholder.textContent = qrData?.error || '获取QQ登录二维码失败，请重试';
                    }
                }
            } catch (error) {
                qrPlaceholder.textContent = '加载二维码失败，请刷新重试';
            }
        }

        // 刷新二维码
        function refreshQRCode() {
            const qrImage = document.getElementById('qrImage');
            const qrPlaceholder = document.getElementById('qrPlaceholder');
            const statusLabel = document.getElementById('statusLabel');
            const qqInfoLabel = document.getElementById('qqInfoLabel');

            // 重置UI状态
            qrImage.style.display = 'none';
            qrPlaceholder.style.display = 'block';
            qrPlaceholder.textContent = '正在刷新二维码...';

            // 重置状态标签
            statusLabel.textContent = '请扫码登录QQ';
            statusLabel.style.color = '#FFFFFF';

            // 隐藏QQ信息
            qqInfoLabel.style.display = 'none';
            qqInfoLabel.textContent = '';

            // 清除之前的状态检查定时器
            if (window.verificationStatusInterval) {
                clearInterval(window.verificationStatusInterval);
                window.verificationStatusInterval = null;
            }

            loadQRCode();
        }

        // 启动验证状态检查 - 等待后端完成QQ验证
        function startVerificationStatusCheck() {
            if (window.verificationStatusInterval) {
                clearInterval(window.verificationStatusInterval);
            }

            window.verificationStatusInterval = setInterval(async () => {
                try {
                    // 检查QQ登录状态 - 对应原版Python的check_qrcode_status
                    const qrStatus = await window.__TAURI__.core.invoke('check_qr_status');
                    const statusLabel = document.getElementById('statusLabel');

                    // 根据状态更新UI - 对应原版Python第2350-2372行
                    if (qrStatus.status === 'waiting') {
                        statusLabel.textContent = '请扫码登录QQ';
                        statusLabel.style.color = '#FFFFFF';
                    } else if (qrStatus.status === 'scanned') {
                        statusLabel.textContent = '扫码成功，请在手机上确认';
                        statusLabel.style.color = '#4CAF50'; // 绿色
                    } else if (qrStatus.status === 'confirmed') {
                        statusLabel.textContent = '登录成功！正在验证...';
                        statusLabel.style.color = '#4CAF50'; // 绿色

                        // 显示QQ信息
                        if (qrStatus.qq_number) {
                            const qqInfoLabel = document.getElementById('qqInfoLabel');
                            qqInfoLabel.textContent = `QQ: ${qrStatus.qq_number}`;
                            qqInfoLabel.style.display = 'block';
                        }
                    } else if (qrStatus.status === 'expired') {
                        statusLabel.textContent = '二维码已失效，请刷新';
                        statusLabel.style.color = '#FF6B6B'; // 红色
                    } else if (qrStatus.status === 'verification_failed') {
                        statusLabel.textContent = qrStatus.message || '抱歉，您的QQ并不在赞助名单里';
                        statusLabel.style.color = '#FF6B6B'; // 红色
                    } else if (qrStatus.status === 'login_rejected') {
                        statusLabel.textContent = qrStatus.message || '您未确认登录，验证失败';
                        statusLabel.style.color = '#FF6B6B'; // 红色
                    } else if (qrStatus.status === 'verification_success') {
                        statusLabel.textContent = qrStatus.message || 'VIP QQ验证通过！';
                        statusLabel.style.color = '#4CAF50'; // 绿色

                        // 验证成功，立即关闭对话框
                        clearInterval(window.verificationStatusInterval);
                        await closeVerificationDialog({
                            success: true,
                            message: "VIP QQ验证通过！",
                            method: "vip_qq_verification",
                            qq_number: qrStatus.qq_number
                        });
                        return; // 停止继续检查
                    }

                    // 检查验证是否完成
                    const isCompleted = await window.__TAURI__.core.invoke('is_verification_completed');

                    if (isCompleted) {
                        clearInterval(window.verificationStatusInterval);

                        // 检查是否验证失败
                        if (qrStatus.status === 'verification_failed') {
                            // 验证失败，显示错误信息
                            await closeVerificationDialog({
                                success: false,
                                message: qrStatus.message || "抱歉，您的QQ并不在赞助名单里",
                                method: "vip_qq_verification"
                            });
                        } else {
                            // 验证成功，关闭对话框
                            await closeVerificationDialog({
                                success: true,
                                message: "VIP QQ验证通过！",
                                method: "vip_qq_verification"
                            });
                        }
                    }
                } catch (error) {
                    // 状态检查失败，继续等待
                }
            }, 1000); // 每秒检查一次验证状态
        }

        // 清除输入框错误状态 - 对应原版Python的_clear_all_input_errors
        function clearInputErrors() {
            const inputs = document.querySelectorAll('.verification-code-digit');
            inputs.forEach(input => {
                // 移除错误类
                input.classList.remove('error');

                // 只有在输入框没有焦点时才恢复样式，避免覆盖焦点效果
                if (document.activeElement !== input) {
                    input.style.border = '1px solid #2A2E36'; // 恢复正常边框
                    input.style.background = '#1A1D23'; // 恢复正常背景
                    input.style.boxShadow = 'none'; // 清除阴影
                    input.style.transform = 'scale(1)'; // 恢复正常大小
                }
            });

            // 更新光标可见性
            updateCursorVisibility();
        }

        // 更新光标可见性 - 有内容时隐藏光标，无内容且获得焦点时显示光标，错误状态下隐藏光标
        function updateCursorVisibility() {
            const inputs = document.querySelectorAll('.verification-code-digit');
            inputs.forEach(input => {
                // 检查是否处于错误状态
                const isError = input.classList.contains('error');

                if (isError) {
                    // 错误状态下隐藏光标
                    input.style.caretColor = 'transparent';
                } else if (input.value.length > 0) {
                    // 有内容时隐藏光标
                    input.style.caretColor = 'transparent';
                } else {
                    // 无内容时显示光标（仅当获得焦点时）
                    if (document.activeElement === input) {
                        input.style.caretColor = '#FFFFFF';
                    } else {
                        input.style.caretColor = 'transparent';
                    }
                }
            });
        }

        // 检查验证码验证 - 对应原版Python的check_verification方法
        async function checkVerification() {
            const inputs = document.querySelectorAll('.verification-code-digit');
            let code = Array.from(inputs).map(input => input.value).join('');

            if (code.length !== 6) {
                window.isVerifying = false; // 释放锁
                return;
            }

            // 转换为大写进行验证（对应原版Python逻辑）
            code = code.toUpperCase();

            try {
                const result = await window.__TAURI__.core.invoke('verify_code', { code });

                if (result.success) {
                    // 验证成功，关闭对话框
                    await closeVerificationDialog(result);
                } else {
                    // 验证失败，显示错误信息但不关闭对话框（对应原版Python逻辑）
                    const statusLabel = document.getElementById('statusLabel');
                    statusLabel.textContent = '验证码不正确，请重试';
                    statusLabel.style.color = '#FF6B6B'; // 红色错误提示

                    // 给所有输入框添加错误状态（对应原版Python的set_error_state）
                    inputs.forEach(input => {
                        // 添加错误类
                        input.classList.add('error');

                        // 设置错误样式（包括当前焦点的输入框）
                        input.style.border = '1px solid #BC4A59'; // 原版Python Theme.ERROR颜色
                        input.style.background = 'rgba(255, 99, 71, 0.1)'; // 错误背景
                        input.style.boxShadow = 'none';
                        input.style.transform = 'scale(1)'; // 取消放大效果
                    });

                    // 清空输入框并聚焦第一个
                    inputs.forEach(input => input.value = '');
                    inputs[0].focus();

                    // 更新光标可见性（错误状态下隐藏光标）
                    updateCursorVisibility();

                    window.isVerifying = false; // 释放锁，允许重新验证
                }
            } catch (error) {
                // 验证码验证失败，静默处理
                const statusLabel = document.getElementById('statusLabel');
                statusLabel.textContent = '验证失败，请重试';
                statusLabel.style.color = '#FF6B6B';
                window.isVerifying = false; // 释放锁
            }
        }

        // 提交验证码验证（保留用于Enter键触发）
        async function submitVerification() {
            if (!window.isVerifying) {
                window.isVerifying = true;
                await checkVerification();
            }
        }

        // 关闭验证对话框
        async function closeVerificationDialog(result) {
            const overlay = document.getElementById('verificationDialogOverlay');
            if (!overlay) return;

            // 立即隐藏窗口，避免显示任何空内容
            try {
                await window.__TAURI__.core.invoke('hide_window');
            } catch (error) {
                console.warn('Failed to hide window during dialog close:', error);
            }

            // 恢复背景滚动
            document.body.style.overflow = '';

            // 立即隐藏对话框，避免显示空框
            overlay.style.opacity = '0';
            overlay.style.visibility = 'hidden';

            // 立即隐藏页面内容
            document.body.style.opacity = '0';
            document.body.style.visibility = 'hidden';

            // 立即移除对话框并处理结果
            overlay.remove();
            if (window.verificationDialogResolve) {
                window.verificationDialogResolve(result || { success: false, message: '用户取消' });
                delete window.verificationDialogResolve;
            }
        }

        /**
         * 在系统默认浏览器中打开更新链接
         * @param {string} url - 要打开的URL
         */
        async function openUpdateUrlInSystemBrowser(url) {
            try {
                // 使用Tauri的open_url命令在系统默认浏览器中打开链接
                await window.__TAURI__.core.invoke('open_url', { url: url });
            } catch (error) {
                console.error('使用系统浏览器打开链接失败:', error);
                // 备用方案：使用window.open
                window.open(url, '_blank');
            }
        }

        // 启动版本验证
        /**
         * 通用样式对话框 - 仿照原版 StyledMessageBox.showMessage()
         * 固定宽度500px，动态计算高度，支持多种图标和按钮
         * @param {string} title - 标题
         * @param {string} message - 消息内容
         * @param {string} icon - 图标类型 ('info', 'warning', 'error', 'question')
         * @param {Array} buttons - 按钮数组，如 ['确定', '取消']
         * @param {string} updateUrl - 更新链接（可选）
         * @param {boolean} showUpdateUrl - 是否显示更新链接按钮
         * @returns {Promise<number>} 返回点击的按钮索引，0表示第一个按钮，-1表示关闭
         */
        async function showStyledDialog(title, message, icon = 'info', buttons = ['确定'], updateUrl = null, showUpdateUrl = true, showIcon = true) {
            return new Promise(async (resolve) => {
                // 第一步：在开始任何操作前立即隐藏窗口
                try {
                    await window.__TAURI__.core.invoke('hide_window');
                    console.log('Window hidden at start of styled dialog');
                } catch (error) {
                    console.warn('Failed to hide window at start:', error);
                }

                // 第二步：立即隐藏现有对话框，避免显示空框
                const existingOverlay = document.getElementById('verificationDialogOverlay') ||
                                       document.getElementById('styledDialogOverlay');
                if (existingOverlay) {
                    existingOverlay.style.opacity = '0';
                    existingOverlay.style.visibility = 'hidden';
                }

                // 第三步：隐藏页面内容
                document.body.style.opacity = '0';
                document.body.style.visibility = 'hidden';

                // 图标配置
                const iconConfigs = {
                    info: { color: '#3B82F6', symbol: 'ℹ' },
                    warning: { color: '#F59E0B', symbol: '!' },
                    error: { color: '#EF4444', symbol: '✕' },
                    question: { color: '#3B82F6', symbol: '?' }
                };

                const iconConfig = iconConfigs[icon] || iconConfigs.info;

                // 创建图标HTML（根据showIcon参数决定是否显示）
                const iconHtml = showIcon ? `<div style="
                    background-color: ${iconConfig.color};
                    color: white;
                    font-size: 18px;
                    font-weight: bold;
                    border-radius: 15px;
                    min-width: 30px;
                    max-width: 30px;
                    min-height: 30px;
                    max-height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    line-height: 1;
                ">${iconConfig.symbol}</div>` : '';

                // 创建按钮HTML
                let buttonsHtml = '';

                // 添加更新链接按钮（如果需要）
                if (showUpdateUrl && updateUrl) {
                    buttonsHtml += `
                        <button onclick="openUpdateUrlInSystemBrowser('${updateUrl}')" style="
                            background-color: #374151;
                            color: #FFFFFF;
                            border: 1px solid #4B5563;
                            border-radius: 6px;
                            padding: 8px 16px;
                            font-size: 14px;
                            font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            min-width: 80px;
                            margin-right: 10px;
                        ">打开更新链接</button>
                    `;
                }

                // 添加标准按钮
                buttons.forEach((buttonText, index) => {
                    const isPrimary = buttonText === '我同意' || (index === 0 && buttonText !== '我不同意并退出');
                    const isExit = buttonText === '退出';

                    let buttonStyle;
                    if (isExit) {
                        buttonStyle = `
                            background-color: #2B9D7C;
                            color: #FFFFFF;
                            border: none;
                            border-radius: 6px;
                            padding: 8px 16px;
                            font-size: 14px;
                            font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            min-width: 60px;
                        `;
                    } else if (isPrimary) {
                        buttonStyle = `
                            background-color: #2B9D7C;
                            color: #FFFFFF;
                            border: none;
                            border-radius: 6px;
                            padding: 8px 16px;
                            font-size: 14px;
                            font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            min-width: 80px;
                        `;
                    } else {
                        buttonStyle = `
                            background-color: #374151;
                            color: #FFFFFF;
                            border: 1px solid #4B5563;
                            border-radius: 6px;
                            padding: 8px 16px;
                            font-size: 14px;
                            font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                            cursor: pointer;
                            transition: all 0.3s ease;
                            min-width: 80px;
                        `;
                    }

                    buttonsHtml += `
                        <button onclick="closeStyledDialog(${index})" style="${buttonStyle}
                            ${index > 0 ? 'margin-left: 10px;' : ''}
                        ">${buttonText}</button>
                    `;
                });

                // 创建对话框HTML - 固定宽度500px，仿照原版
                const dialogHTML = `
                    <div class="styled-dialog-overlay" id="styledDialogOverlay" style="
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: transparent;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 10000;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    ">
                        <div style="
                            background-color: #121317;
                            border: none;
                            width: 550px;
                            height: auto;
                            min-height: 200px;
                            max-height: 380px;
                            box-sizing: border-box;
                            cursor: default;
                            display: flex;
                            flex-direction: column;
                        ">
                            <!-- 可滚动内容区域 -->
                            <div style="padding: 25px 25px 0 25px; display: flex; flex-direction: column; gap: 15px; flex: 1; overflow-y: auto; min-height: 0; max-height: calc(400px - 80px);">
                                <!-- 标题区域 -->
                                <div style="display: flex; align-items: center; ${showIcon ? 'gap: 12px;' : ''}">
                                    ${iconHtml}
                                    <div style="
                                        color: #FFFFFF;
                                        font-weight: bold;
                                        font-size: 18px;
                                        font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                                    ">${title}</div>
                                </div>

                                <!-- 消息内容 -->
                                <div class="styled-dialog-content" style="
                                    color: #9CA2AE;
                                    font-size: 14px;
                                    font-family: 'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
                                    line-height: 1.6;
                                    padding-right: 8px;
                                    padding-bottom: 15px;
                                    word-wrap: break-word;
                                    overflow-wrap: break-word;
                                    white-space: pre-wrap;
                                    max-width: 100%;
                                ">${message.replace(/\n/g, '<br>')}</div>
                            </div>

                            <!-- 固定底部区域 -->
                            <div style="padding: 0 25px 25px 25px; flex-shrink: 0;">
                                <!-- 分隔线 -->
                                <div style="
                                    height: 1px;
                                    background-color: #2A2E36;
                                    margin: 0 0 15px 0;
                                "></div>

                                <!-- 按钮区域 -->
                                <div style="
                                    display: flex;
                                    justify-content: flex-end;
                                    align-items: center;
                                ">
                                    ${buttonsHtml}
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 清除旧内容并添加新的对话框HTML
                document.body.innerHTML = '';  // 清除所有旧内容
                document.body.insertAdjacentHTML('beforeend', dialogHTML);

                // 恢复页面可见性（但保持透明，等待后续显示）
                document.body.style.visibility = 'visible';
                document.body.style.opacity = '0';

                // 获取对话框元素
                const overlay = document.getElementById('styledDialogOverlay');
                const dialog = overlay.querySelector('div[style*="background-color: #121317"]');

                // 确保新对话框也是隐藏状态
                overlay.style.opacity = '0';
                overlay.style.visibility = 'hidden';

                // 强制重绘，确保新内容完全渲染
                overlay.offsetHeight; // 触发重绘

                // 异步预计算对话框大小并调整窗口（窗口已在函数开始时隐藏）
                (async () => {
                    try {
                        // 确保窗口完全隐藏后再开始任何调整
                        await new Promise(resolve => setTimeout(resolve, 100));

                        console.log('Pre-calculating dialog size before showing...');

                        // 临时显示对话框以测量大小（但保持透明）
                        overlay.style.opacity = '0';
                        overlay.style.visibility = 'visible';

                        // 等待DOM渲染完成
                        await new Promise(resolve => requestAnimationFrame(resolve));

                        // 测量对话框大小
                        const dialogSize = await measureDialogActualSize(dialog);
                        console.log('Pre-calculated dialog size:', dialogSize);

                        // 调整窗口大小和位置
                        await window.__TAURI__.core.invoke('resize_window', {
                            width: dialogSize.width,
                            height: dialogSize.height
                        });

                        // 等待窗口调整完成
                        await new Promise(resolve => setTimeout(resolve, 50));

                        // 居中窗口
                        await centerWindowOptimized();

                        console.log('Window pre-adjusted for dialog display');

                        // 确保隐藏至少1秒后再显示窗口
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // 显示对话框内容
                        overlay.style.visibility = 'visible';
                        overlay.style.opacity = '1';

                        // 恢复页面完全可见性
                        document.body.style.opacity = '1';

                        // 强制重绘并等待DOM完全渲染
                        overlay.offsetHeight; // 触发重绘
                        await new Promise(resolve => {
                            requestAnimationFrame(() => {
                                requestAnimationFrame(() => {
                                    // 再次确保内容已渲染
                                    overlay.offsetHeight;
                                    resolve();
                                });
                            });
                        });

                        // 显示整个应用程序窗口
                        await showWindowMacOptimized();
                        console.log('Window shown with dialog ready');

                    } catch (error) {
                        console.warn('Failed to pre-adjust window size:', error);
                        // 如果预调整失败，确保窗口可见并显示对话框
                        try {
                            // 即使在错误情况下也确保隐藏至少1秒
                            await new Promise(resolve => setTimeout(resolve, 1000));

                            // 显示对话框内容
                            overlay.style.visibility = 'visible';
                            overlay.style.opacity = '1';

                            // 恢复页面完全可见性
                            document.body.style.opacity = '1';

                            // 强制重绘并等待DOM完全渲染
                            overlay.offsetHeight; // 触发重绘
                            await new Promise(resolve => {
                                requestAnimationFrame(() => {
                                    requestAnimationFrame(() => {
                                        // 再次确保内容已渲染
                                        overlay.offsetHeight;
                                        resolve();
                                    });
                                });
                            });

                            await showWindowMacOptimized();
                        } catch (showError) {
                            console.warn('Failed to show window:', showError);
                            overlay.style.visibility = 'visible';
                            overlay.style.opacity = '1';
                            document.body.style.opacity = '1';
                        }
                    }
                })();

                // 设置动态窗口大小调整（用于后续内容变化）
                const cleanupResize = setupDynamicWindowResize(dialog);

                // 存储resolve函数和清理函数供关闭时调用
                window.styledDialogResolve = resolve;
                window.styledDialogCleanup = cleanupResize;
            });
        }

        /**
         * 关闭样式对话框
         * @param {number} buttonIndex - 点击的按钮索引
         */
        async function closeStyledDialog(buttonIndex) {
            const overlay = document.getElementById('styledDialogOverlay');
            if (!overlay) return;

            // 立即隐藏窗口，避免显示任何空内容
            try {
                await window.__TAURI__.core.invoke('hide_window');
            } catch (error) {
                console.warn('Failed to hide window during dialog close:', error);
            }

            // 清理ResizeObserver
            if (window.styledDialogCleanup) {
                window.styledDialogCleanup();
                window.styledDialogCleanup = null;
            }

            // 🔧 修改：不恢复窗口大小，保持当前大小
            // 让进度条自动适配当前窗口大小

            // 立即隐藏对话框，避免显示空框
            overlay.style.opacity = '0';
            overlay.style.visibility = 'hidden';

            // 立即隐藏页面内容
            document.body.style.opacity = '0';
            document.body.style.visibility = 'hidden';

            // 立即移除对话框并处理结果
            overlay.remove();
            if (window.styledDialogResolve) {
                window.styledDialogResolve(buttonIndex);
                window.styledDialogResolve = null;
            }
        }

        /**
         * 显示全局通知对话框 - 对应原版Python的_show_notification
         * @param {string} message - 通知消息
         * @param {string} level - 通知级别 (info, warning, error)
         * @returns {Promise<void>}
         */
        async function showGlobalNotificationDialog(message, level = 'info') {
            // 使用通用样式对话框
            let title = '通知';
            if (level === 'warning') {
                title = '警告';
            } else if (level === 'error') {
                title = '错误';
            }

            await showStyledDialog(title, message, level, ['确定']);
        }

        /**
         * 关闭全局通知对话框
         */
        async function closeGlobalNotificationDialog() {
            const overlay = document.getElementById('globalNotificationOverlay');
            if (!overlay) return;

            // 清理ResizeObserver
            if (window.globalNotificationCleanup) {
                window.globalNotificationCleanup();
                window.globalNotificationCleanup = null;
            }

            // 🔧 修改：不恢复窗口大小，保持当前大小
            // 让进度条自动适配当前窗口大小

            // 淡出动画
            overlay.style.opacity = '0';
            setTimeout(() => {
                overlay.remove();
                if (window.globalNotificationResolve) {
                    window.globalNotificationResolve();
                    window.globalNotificationResolve = null;
                }
            }, 300);
        }

        /**
         * 动态调整窗口大小以适应对话框内容
         * 获取对话框的自然尺寸（不受窗口限制）并调整窗口大小
         * @param {HTMLElement} dialogElement - 对话框元素
         */
        function setupDynamicWindowResize(dialogElement) {
            if (!dialogElement) return;



            // 这个函数已被 measureDialogActualSize 替代，保留为兼容性
            async function getDialogNaturalSize() {
                console.warn('getDialogNaturalSize is deprecated, use measureDialogActualSize instead');
                return await measureDialogActualSize(dialogElement);
            }

            // 调整窗口大小的函数（真正跟随对话框动态尺寸）
            async function adjustWindowSizeForDialog() {
                try {
                    // 直接使用全局的 adjustWindowSize 函数，它已经实现了真正的动态测量
                    await adjustWindowSize();
                } catch (error) {
                    console.error('动态调整窗口大小失败:', error);
                }
            }

            // 立即调整一次窗口大小
            adjustWindowSizeForDialog();

            // 创建ResizeObserver来监听对话框尺寸变化（更精确）
            let resizeObserver = null;
            if (window.ResizeObserver) {
                resizeObserver = new ResizeObserver((entries) => {
                    console.log('Dialog size changed, adjusting window...');
                    // 延迟一点时间让DOM更新完成
                    setTimeout(() => adjustWindowSizeForDialog(), 50);
                });
                resizeObserver.observe(dialogElement);
            }

            // 创建MutationObserver来监听对话框内容变化（备用方案）
            const mutationObserver = new MutationObserver((mutations) => {
                let shouldResize = false;
                mutations.forEach(mutation => {
                    // 检查是否有影响布局的变化
                    if (mutation.type === 'childList' ||
                        (mutation.type === 'attributes' &&
                         ['style', 'class', 'hidden'].includes(mutation.attributeName))) {
                        shouldResize = true;
                    }
                });

                if (shouldResize) {
                    console.log('Dialog content changed, adjusting window...');
                    // 延迟一点时间让DOM更新完成
                    setTimeout(() => adjustWindowSizeForDialog(), 100);
                }
            });

            // 监听对话框及其子元素的变化
            mutationObserver.observe(dialogElement, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['style', 'class']
            });

            // 返回清理函数
            return () => {
                if (mutationObserver) {
                    mutationObserver.disconnect();
                }
                if (resizeObserver) {
                    resizeObserver.disconnect();
                }
                console.log('Dialog observers cleaned up');
            };
        }

        // 检测当前平台
        async function getPlatform() {
            try {
                const platform = await window.__TAURI__.os.platform();
                return platform;
            } catch (error) {
                // 如果无法获取平台信息，通过用户代理字符串判断
                const userAgent = navigator.userAgent.toLowerCase();
                if (userAgent.includes('mac')) return 'darwin';
                if (userAgent.includes('win')) return 'win32';
                if (userAgent.includes('linux')) return 'linux';
                return 'unknown';
            }
        }

        // 平台优化的窗口居中函数
        async function centerWindowOptimized() {
            const platform = await getPlatform();

            if (platform === 'darwin') {
                // Mac平台使用强制居中
                console.log('Mac平台：使用强制居中');
                await window.__TAURI__.core.invoke('force_center_window');
            } else {
                // 其他平台使用标准居中
                await window.__TAURI__.core.invoke('center_window');
            }
        }

        // Mac平台优化的窗口显示函数
        async function showWindowMacOptimized() {
            const platform = await getPlatform();
            console.log('Current platform:', platform);

            if (platform === 'darwin') {
                // Mac平台使用强制显示和强制居中
                console.log('Mac平台：使用强制显示窗口');
                try {
                    // 步骤1：强制居中窗口
                    await window.__TAURI__.core.invoke('force_center_window');
                    console.log('Mac平台：强制居中窗口完成');

                    // 步骤2：强制显示窗口
                    await window.__TAURI__.core.invoke('force_show_window');
                    console.log('Mac平台：强制显示窗口成功');

                    // 步骤3：验证窗口是否真的可见
                    await verifyWindowVisibility();
                } catch (error) {
                    console.warn('Mac平台：强制显示窗口失败，尝试标准显示:', error);
                    await window.__TAURI__.core.invoke('show_window');
                    await verifyWindowVisibility();
                }
            } else {
                // 其他平台使用标准显示
                await window.__TAURI__.core.invoke('show_window');
            }
        }

        // 验证窗口可见性并尝试恢复
        async function verifyWindowVisibility() {
            try {
                const isVisible = await window.__TAURI__.core.invoke('is_window_visible');
                console.log('Window visibility check:', isVisible);

                if (!isVisible) {
                    console.warn('窗口显示后仍不可见，尝试恢复...');
                    const platform = await getPlatform();

                    if (platform === 'darwin') {
                        // Mac平台恢复策略
                        await recoverWindowMac();
                    } else {
                        // 其他平台恢复策略
                        await window.__TAURI__.core.invoke('show_window');
                    }
                }
            } catch (error) {
                console.warn('窗口可见性检查失败:', error);
            }
        }

        // Mac平台窗口恢复策略
        async function recoverWindowMac() {
            console.log('Mac平台：开始窗口恢复流程');

            try {
                // 策略1：强制居中窗口
                await window.__TAURI__.core.invoke('force_center_window');
                await new Promise(resolve => setTimeout(resolve, 100));

                // 策略2：再次强制显示
                await window.__TAURI__.core.invoke('force_show_window');
                await new Promise(resolve => setTimeout(resolve, 100));

                // 策略3：检查是否成功
                const isVisible = await window.__TAURI__.core.invoke('is_window_visible');
                if (isVisible) {
                    console.log('Mac平台：窗口恢复成功');
                } else {
                    console.warn('Mac平台：窗口恢复失败，窗口仍不可见');
                }
            } catch (error) {
                console.error('Mac平台：窗口恢复过程中出错:', error);
            }
        }

        // 初始化窗口大小，确保在高 DPI 环境下正确显示
        async function initializeWindow() {
            try {
                // 获取窗口信息
                const windowInfo = await window.__TAURI__.core.invoke('get_window_info');
                console.log('Initial window info:', windowInfo);

                // 设置初始窗口大小（逻辑像素）
                const initialWidth = 450;
                const initialHeight = 250;

                await window.__TAURI__.core.invoke('resize_window', {
                    width: initialWidth,
                    height: initialHeight
                });

                // 等待一小段时间确保窗口大小调整完成
                await new Promise(resolve => setTimeout(resolve, 50));

                // 使用平台优化的居中方法
                await centerWindowOptimized();

                console.log('Window initialized with size:', { initialWidth, initialHeight });
            } catch (error) {
                console.warn('Failed to initialize window:', error);
            }
        }

        // 启动窗口状态监控（仅在Mac平台）
        async function startWindowMonitoring() {
            const platform = await getPlatform();
            if (platform !== 'darwin') return;

            console.log('Mac平台：启动窗口状态监控');

            // 每5秒检查一次窗口状态
            setInterval(async () => {
                try {
                    const isVisible = await window.__TAURI__.core.invoke('is_window_visible');
                    if (!isVisible) {
                        console.warn('Mac平台：检测到窗口不可见，尝试恢复');
                        await recoverWindowMac();
                    }
                } catch (error) {
                    // 静默处理监控错误，避免干扰正常流程
                }
            }, 5000);
        }

        document.addEventListener('DOMContentLoaded', async () => {
            try {
                console.log('Initializing version check window...');

                // 初始化窗口大小（窗口仍然隐藏）
                await initializeWindow();

                // 调整窗口大小以适应加载界面
                await adjustWindowSize();

                // 现在显示窗口（已经调整好大小和位置）
                await showWindowMacOptimized();
                console.log('Version check window shown');

                // 启动窗口状态监控（仅Mac平台）
                await startWindowMonitoring();

                // 开始版本检查
                setTimeout(performVersionCheck, 500);

            } catch (error) {
                console.error('Failed to initialize version check window:', error);
                // 如果初始化失败，确保窗口可见
                try {
                    await showWindowMacOptimized();
                } catch (showError) {
                    console.error('Failed to show window after initialization error:', showError);
                }
                // 仍然尝试进行版本检查
                setTimeout(performVersionCheck, 500);
            }
        });
    </script>
</body>
</html>
