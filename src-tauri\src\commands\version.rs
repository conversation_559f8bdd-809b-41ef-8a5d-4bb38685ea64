use tauri::{State, AppHandle};
use std::sync::Arc;
use tokio::sync::Mutex as TokioMutex;
use crate::app_state::{AppState, ExpiryStatus};
use crate::core::version::{<PERSON><PERSON><PERSON><PERSON>, VersionCheckResult, AboutInfoWithVersion};
use crate::core::expiry_checker::Expiry<PERSON>he<PERSON>;
use serde_json;
use serde::{Deserialize, Serialize};


/// 验证结果结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerificationResult {
    pub success: bool,
    pub message: String,
    pub method: Option<String>,
    pub qq_number: Option<String>,
}

/// 二维码生成结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QRCodeResult {
    pub qr_code: String,
    pub session_id: String,
}

/// 二维码状态结果
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct QRStatusResult {
    pub status: String, // "waiting", "scanned", "confirmed", "expired", "verification_failed"
    pub qq_number: Option<String>,
    pub message: Option<String>, // 用于传递错误信息
}

/// 检查版本
/// 完全对应原版Python的check_version()函数行为
/// 返回版本检查结果，前端根据结果显示相应的对话框
#[tauri::command]
pub async fn check_version(state: State<'_, AppState>, app_handle: AppHandle) -> Result<VersionCheckResult, String> {
    // 检查是否已经验证过（避免重复验证）
    {
        let verification_status = state.verification_completed.lock().await;
        if *verification_status {
            // 已经验证过，直接返回可以继续的结果
            return Ok(VersionCheckResult {
                can_continue: true,
                needs_update: false,
                force_update: false,
                update_message: None,
                update_url: None,
                latest_version: None,
                disclaimer_needed: false,
                verification_needed: false,
                maintenance_mode: false,
                maintenance_message: None,
                global_notification: None,
                notification: None,
                current_version: None,
                changes: None,
                update_date: None,
            });
        }
    }
    let mut version_checker = match VersionChecker::new() {
        Ok(checker) => checker,
        Err(_e) => {
            // eprintln!("版本检查器初始化失败: {}", e); // 注释掉调试信息
            // 返回失败结果，而不是错误
            return Ok(VersionCheckResult {
                can_continue: false,
                needs_update: false,
                force_update: false,
                update_message: None,
                update_url: None,
                latest_version: None,
                disclaimer_needed: false,
                verification_needed: false,
                maintenance_mode: false,
                maintenance_message: None,
                global_notification: None,
                notification: None,
                current_version: None,
                changes: None,
                update_date: None,
            });
        }
    };

    let result = match version_checker.check_version().await {
        Ok(result) => {
            // 版本检查成功，保存版本检查器实例到状态中
            {
                let mut checker_guard = state.version_checker.lock().await;
                *checker_guard = Some(version_checker.clone());
            }

            // 检查现有验证状态并启动时效检查
            tokio::spawn(async move {
                if let Err(e) = ExpiryChecker::start_from_verification_data(app_handle, &version_checker).await {
                    eprintln!("从验证数据启动时效检查失败: {}", e);
                }
            });

            result
        }
        Err(e) => {
            // eprintln!("版本检查失败: {}", e); // 注释掉调试信息，避免暴露解密流程

            // 根据错误类型决定如何处理，对应原版Python的错误处理逻辑
            if e.contains("CONNECTION_ERROR") {
                // 连接错误，前端应显示连接错误对话框
                // 这里我们通过特殊的字段来传递错误信息
                return Err("CONNECTION_ERROR".to_string());
            } else {
                // 其他错误（解密失败等），直接返回错误让前端显示错误对话框
                // 对应原版Python的_show_connection_error()逻辑
                return Err(e);
            }
        }
    };

    Ok(result)
}



/// 获取免责声明信息
#[tauri::command]
pub async fn get_disclaimer_info(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    let checker_guard = state.version_checker.lock().await;
    if let Some(checker) = checker_guard.as_ref() {
        let disclaimer_info = checker.get_disclaimer_info()?;
        Ok(disclaimer_info)
    } else {
        Err("版本检查器未初始化".to_string())
    }
}

/// 同意免责声明
#[tauri::command]
pub async fn agree_disclaimer(state: State<'_, AppState>, version: String) -> Result<(), String> {
    let checker_guard = state.version_checker.lock().await;
    if let Some(checker) = checker_guard.as_ref() {
        checker.save_agreed_disclaimer_version(&version)?;
        Ok(())
    } else {
        Err("版本检查器未初始化".to_string())
    }
}

/// 标记版本验证完成
#[tauri::command]
pub async fn mark_verification_completed(state: State<'_, AppState>) -> Result<(), String> {
    let mut verification_status = state.verification_completed.lock().await;
    *verification_status = true;
    Ok(())
}

/// 检查验证是否完成
#[tauri::command]
pub async fn is_verification_completed(state: State<'_, AppState>) -> Result<bool, String> {
    let verification_status = state.verification_completed.lock().await;
    Ok(*verification_status)
}

/// 获取公众号二维码
/// 对应原版Python VerificationDialog._get_gzh_qrcode()方法
#[tauri::command]
pub async fn get_gzh_qrcode(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    let checker_guard = state.version_checker.lock().await;
    if let Some(checker) = checker_guard.as_ref() {
        match checker.get_gzh_qrcode().await {
            Ok(qr_data) => {
                Ok(serde_json::json!({
                    "success": true,
                    "image_base64": qr_data
                }))
            }
            Err(e) => {
                Ok(serde_json::json!({
                    "success": false,
                    "error": e
                }))
            }
        }
    } else {
        Err("版本检查器未初始化".to_string())
    }
}

/// 验证验证码
/// 完全对应原版Python VerificationDialog.check_verification()方法中的验证码验证逻辑
#[tauri::command]
pub async fn verify_code(code: String, state: State<'_, AppState>, app_handle: AppHandle) -> Result<VerificationResult, String> {
    // 获取VersionChecker实例
    let checker_guard = state.version_checker.lock().await;
    if let Some(checker) = checker_guard.as_ref() {
        // 检查验证码验证是否启用
        if !checker.config.verification.code_verification_enabled {
            return Ok(VerificationResult {
                success: false,
                message: "验证码验证未启用".to_string(),
                method: Some("code_verification".to_string()),
                qq_number: None,
            });
        }

        // 获取期望的验证码 - 对应原版Python第2439行
        let expected_code = &checker.code_verification_code;

        // 检查验证码是否已加载
        if expected_code.is_empty() {
            return Err("验证码配置未加载，请先进行版本检查".to_string());
        }

        // 验证码比较 - 完全对应原版Python第2443-2445行的逻辑
        // 将输入验证码和期望验证码都转换为大写进行比较
        let entered_code_upper = code.trim().to_uppercase();
        let expected_code_upper = expected_code.trim().to_uppercase();

        if entered_code_upper == expected_code_upper {
            // 验证成功 - 对应原版Python第2446-2450行

            // 保存验证结果到grouptimeliness.json - 对应原版Python的验证时效性保存
            if let Err(_e) = checker.save_verification_data("code_verification_success", None) {
                // eprintln!("保存验证数据失败: {}", e); // 注释掉调试信息
                // 不返回错误，因为验证本身是成功的
            }

            // 启动时效检查
            let duration_seconds = (checker.config.verification.code_verification_duration_hours as u64) * 3600;
            tokio::spawn(async move {
                if let Err(e) = ExpiryChecker::start_expiry_check(
                    app_handle,
                    "code_verification".to_string(),
                    duration_seconds,
                ).await {
                    eprintln!("启动时效检查失败: {}", e);
                }
            });

            Ok(VerificationResult {
                success: true,
                message: "验证码验证通过！".to_string(), // 对应原版Python第2448行
                method: Some("code_verification".to_string()),
                qq_number: None,
            })
        } else {
            // 验证失败 - 对应原版Python第2453-2454行
            Ok(VerificationResult {
                success: false,
                message: "验证码不正确，请重试".to_string(), // 对应原版Python第2454行
                method: Some("code_verification".to_string()),
                qq_number: None,
            })
        }
    } else {
        Err("版本检查器未初始化".to_string())
    }
}

/// 验证VIP QQ
/// 完全对应原版Python VerificationDialog.check_verification()方法中的VIP QQ验证逻辑
#[tauri::command]
pub async fn verify_vip_qq(state: State<'_, AppState>, app_handle: AppHandle) -> Result<VerificationResult, String> {
    // 获取VersionChecker实例
    let checker_guard = state.version_checker.lock().await;
    if let Some(checker) = checker_guard.as_ref() {
        // 检查VIP QQ验证是否启用 - 对应原版Python第2472行
        if !checker.config.verification.vip_qq_verification_enabled {
            return Ok(VerificationResult {
                success: false,
                message: "VIP QQ验证未启用".to_string(),
                method: Some("vip_qq_verification".to_string()),
                qq_number: None,
            });
        }

        // 获取当前QQ登录状态
        let qq_login_guard = state.qq_login_state.lock().await;

        // 检查QQ是否已登录 - 对应原版Python第2474-2477行
        if !qq_login_guard.is_logged_in || qq_login_guard.current_qq.is_empty() {
            return Ok(VerificationResult {
                success: false,
                message: "请先扫码登录QQ".to_string(),
                method: Some("vip_qq_verification".to_string()),
                qq_number: None,
            });
        }

        let current_qq = &qq_login_guard.current_qq;

        // 检查QQ号是否在VIP白名单中 - 对应原版Python第2479-2490行
        let is_vip = checker.vip_qq_whitelist.contains(current_qq);

        if is_vip {
            // VIP验证成功 - 对应原版Python第2491-2495行

            // 保存验证结果到grouptimeliness.json
            if let Err(_e) = checker.save_verification_data("vip_qq_verification_success", Some(current_qq)) {
                // eprintln!("保存验证数据失败: {}", e); // 注释掉调试信息
                // 不返回错误，因为验证本身是成功的
            }

            // 启动时效检查
            let duration_seconds = (checker.config.verification.vip_qq_duration_hours as u64) * 3600;
            tokio::spawn(async move {
                if let Err(e) = ExpiryChecker::start_expiry_check(
                    app_handle,
                    "vip_qq_verification".to_string(),
                    duration_seconds,
                ).await {
                    eprintln!("启动时效检查失败: {}", e);
                }
            });

            Ok(VerificationResult {
                success: true,
                message: "VIP QQ验证通过！".to_string(), // 对应原版Python第2493行
                method: Some("vip_qq_verification".to_string()),
                qq_number: Some(current_qq.clone()),
            })
        } else {
            // VIP验证失败 - 对应原版Python第2497-2498行
            Ok(VerificationResult {
                success: false,
                message: "当前QQ不在VIP白名单中".to_string(), // 对应原版Python第2498行
                method: Some("vip_qq_verification".to_string()),
                qq_number: Some(current_qq.clone()),
            })
        }
    } else {
        Err("版本检查器未初始化".to_string())
    }
}

/// 生成验证二维码并启动后台状态检查
/// 完全对应原版Python VerificationDialog.start_qr_login()方法中的QQ登录二维码生成逻辑
#[tauri::command]
pub async fn generate_verification_qr(state: State<'_, AppState>) -> Result<QRCodeResult, String> {
    use crate::core::qq_api::QQGroupAPI;
    use base64::{Engine as _, engine::general_purpose};

    // 创建QQ API实例 - 对应原版Python第2221行
    let mut qq_api = QQGroupAPI::new(|_msg| {}) // 注释掉QQ API调试信息
        .map_err(|e| format!("创建QQ API失败: {}", e))?;

    // 获取登录二维码 - 对应原版Python第2221行
    let qr_data = qq_api.get_login_qrcode().await
        .ok_or("获取QQ登录二维码失败".to_string())?;

    // 保存QQ API实例到应用状态中，以便后续状态检查使用
    {
        let mut qq_api_guard = state.qq_api.lock().await;
        *qq_api_guard = Some(qq_api);
    }

    // 将二维码数据转换为base64格式
    let qr_code_base64 = general_purpose::STANDARD.encode(&qr_data);

    // 保存二维码数据到状态中，并重置所有相关状态
    {
        let mut qq_login_guard = state.qq_login_state.lock().await;
        qq_login_guard.qr_code_data = Some(qr_data);
        qq_login_guard.login_status = "waiting".to_string();
        qq_login_guard.status_message = "请扫码登录QQ".to_string();
        qq_login_guard.is_logged_in = false;
        qq_login_guard.current_qq.clear();
    }

    // 重置验证完成状态，允许重新验证
    {
        let mut verification_status = state.verification_completed.lock().await;
        *verification_status = false;
    }

    // 生成会话ID（用于状态跟踪）
    let session_id = format!("qq_login_{}", std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs());

    // 启动后台状态检查 - 对应原版Python的start_status_check
    let _ = start_qq_status_check(state.clone()).await;

    Ok(QRCodeResult {
        qr_code: format!("data:image/png;base64,{}", qr_code_base64),
        session_id,
    })
}

/// 检查二维码扫描状态
/// 完全对应原版Python VerificationDialog.check_qrcode_status()方法
#[tauri::command]
pub async fn check_qr_status(state: State<'_, AppState>) -> Result<QRStatusResult, String> {
    // 使用增强版检查函数，包含自动VIP验证
    check_qq_login_status_internal_with_auto_verify(
        &state.qq_login_state,
        &state.qq_api,
        &state.version_checker,
        &state.verification_completed
    ).await
}

/// 获取关于信息
#[tauri::command]
pub async fn get_about_info(state: State<'_, AppState>) -> Result<AboutInfoWithVersion, String> {
    let checker_guard = state.version_checker.lock().await;
    if let Some(checker) = checker_guard.as_ref() {
        // 新的get_about_info()方法总是返回Some，因为它会使用默认值
        if let Some(about_info) = checker.get_about_info() {
            Ok(about_info)
        } else {
            // 这种情况理论上不会发生，但为了安全起见保留
            Err("获取关于信息失败".to_string())
        }
    } else {
        Err("版本检查器未初始化".to_string())
    }
}

/// 获取配置帮助链接
#[tauri::command]
pub async fn get_config_help_url(state: State<'_, AppState>) -> Result<String, String> {
    let checker_guard = state.version_checker.lock().await;
    if let Some(checker) = checker_guard.as_ref() {
        Ok(checker.get_config_help_url())
    } else {
        Ok("https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0".to_string())
    }
}

/// 获取Token帮助链接
#[tauri::command]
pub async fn get_token_help_url(state: State<'_, AppState>) -> Result<String, String> {
    let checker_guard = state.version_checker.lock().await;
    if let Some(checker) = checker_guard.as_ref() {
        Ok(checker.get_token_help_url())
    } else {
        Ok("https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf".to_string())
    }
}

// 注意：get_version_info 命令已被移除，因为它会暴露敏感的配置信息
// 如果需要版本信息，请使用 get_about_info 获取安全的公开信息

/// 获取时效状态
#[tauri::command]
pub async fn get_expiry_status(state: State<'_, AppState>) -> Result<ExpiryStatus, String> {
    Ok(ExpiryChecker::get_expiry_status(&state).await)
}

/// 停止时效检查（已禁用）
/// 🚫 此命令已被禁用，时效检查一旦启动就不会被停止，确保时效控制的严格性
#[tauri::command]
pub async fn stop_expiry_check(_state: State<'_, AppState>) -> Result<(), String> {
    // 🔒 安全限制：不允许通过命令停止时效检查
    println!("🚫 时效检查停止命令被拒绝 - 时效检查不允许被停止");
    Err("时效检查不允许被停止".to_string())
}

/// 启动QQ登录状态后台检查 - 对应原版Python的start_status_check
/// 这个函数会在后台持续检查QQ登录状态，一旦登录成功就自动进行VIP验证
#[tauri::command]
pub async fn start_qq_status_check(state: tauri::State<'_, AppState>) -> Result<(), String> {
    use tokio::time::{sleep, Duration};
    use std::sync::Arc;

    // 先取消之前的检查任务（如果存在）
    {
        let mut task_guard = state.qq_status_check_task.lock().await;
        if let Some(old_task) = task_guard.take() {
            old_task.abort(); // 取消之前的任务
            // println!("取消了之前的QQ状态检查任务"); // 注释掉调试信息
        }
    }

    // 克隆各个状态字段以便在异步任务中使用
    let qq_login_state = Arc::clone(&state.qq_login_state);
    let _qq_api = Arc::clone(&state.qq_api);
    let _verification_completed = Arc::clone(&state.verification_completed);
    let _version_checker = Arc::clone(&state.version_checker);

    // 启动后台任务 - 对应原版Python的QTimer
    let task_handle = tokio::spawn(async move {
        // println!("后台QQ状态监控任务已启动，等待前端触发检查"); // 注释掉调试信息

        // 这个任务现在只是一个占位符，实际的状态检查由前端定时器触发
        // 避免重复的API调用，让前端的startVerificationStatusCheck负责UI更新
        loop {
            // 检查是否需要退出
            {
                let qq_login_guard = qq_login_state.lock().await;
                if qq_login_guard.login_status == "expired" ||
                   qq_login_guard.login_status == "verification_failed" ||
                   qq_login_guard.login_status == "confirmed" {
                    // println!("后台监控任务退出，状态: {}", qq_login_guard.login_status); // 注释掉调试信息
                    break;
                }
            }

            // 等待10秒后再检查退出条件
            sleep(Duration::from_secs(10)).await;
        }
    });

    // 保存任务句柄以便后续取消
    {
        let mut task_guard = state.qq_status_check_task.lock().await;
        *task_guard = Some(task_handle);
    }

    Ok(())
}

/// 内部QQ登录状态检查函数 - 增强版，包含自动VIP验证
async fn check_qq_login_status_internal_with_auto_verify(
    qq_login_state: &Arc<TokioMutex<crate::app_state::QQLoginState>>,
    qq_api_state: &Arc<TokioMutex<Option<crate::core::qq_api::QQGroupAPI>>>,
    version_checker: &Arc<TokioMutex<Option<crate::core::version::VersionChecker>>>,
    verification_completed: &Arc<TokioMutex<bool>>
) -> Result<QRStatusResult, String> {
    // 先调用基础检查函数
    let status = check_qq_login_status_internal(qq_login_state, qq_api_state).await?;

    // 如果登录成功且还未验证，进行自动VIP验证
    if status.status == "confirmed" {
        let verification_done = {
            let verification_guard = verification_completed.lock().await;
            *verification_guard
        };

        if !verification_done {
            if let Some(qq_number) = &status.qq_number {
                // println!("检测到登录成功，开始自动VIP验证，QQ号: {}", qq_number); // 注释掉调试信息

                // 进行VIP QQ验证
                match verify_vip_qq_internal(version_checker, qq_number).await {
                    Ok(result) => {
                        // println!("VIP验证完成，结果: success={}, message={}", result.success, result.message); // 注释掉调试信息

                        // 验证完成，更新状态
                        let mut verification_status = verification_completed.lock().await;
                        *verification_status = true;
                        drop(verification_status);

                        // 保存验证结果
                        if result.success {
                            let checker_guard = version_checker.lock().await;
                            if let Some(checker) = checker_guard.as_ref() {
                                let _ = checker.save_verification_data("vip_qq_verification_success", Some(qq_number));
                            }

                            let mut qq_login_guard = qq_login_state.lock().await;
                            qq_login_guard.login_status = "verification_success".to_string();
                            qq_login_guard.status_message = "验证成功！".to_string();
                        } else {
                            let mut qq_login_guard = qq_login_state.lock().await;
                            qq_login_guard.login_status = "verification_failed".to_string();
                            qq_login_guard.status_message = result.message;
                        }
                    }
                    Err(e) => {
                        // eprintln!("VIP QQ验证失败: {}", e); // 注释掉调试信息
                        let mut verification_status = verification_completed.lock().await;
                        *verification_status = true;
                        drop(verification_status);

                        let mut qq_login_guard = qq_login_state.lock().await;
                        qq_login_guard.login_status = "verification_failed".to_string();
                        qq_login_guard.status_message = format!("验证出错: {}", e);
                    }
                }
            }
        }
    }

    Ok(status)
}

/// 内部QQ登录状态检查函数
async fn check_qq_login_status_internal(
    qq_login_state: &Arc<TokioMutex<crate::app_state::QQLoginState>>,
    qq_api_state: &Arc<TokioMutex<Option<crate::core::qq_api::QQGroupAPI>>>
) -> Result<QRStatusResult, String> {
    use crate::core::qq_api::QQLoginStatus;

    // 获取当前QQ登录状态
    let mut qq_login_guard = qq_login_state.lock().await;

    // 如果还没有二维码数据，返回等待状态
    if qq_login_guard.qr_code_data.is_none() {
        return Ok(QRStatusResult {
            status: "waiting".to_string(),
            qq_number: None,
            message: None,
        });
    }

    // 检查是否已经验证失败
    if qq_login_guard.login_status == "verification_failed" {
        return Ok(QRStatusResult {
            status: "verification_failed".to_string(),
            qq_number: qq_login_guard.current_qq.clone().into(),
            message: Some(qq_login_guard.status_message.clone()),
        });
    }

    // 检查是否已经验证成功
    if qq_login_guard.login_status == "verification_success" {
        return Ok(QRStatusResult {
            status: "verification_success".to_string(),
            qq_number: qq_login_guard.current_qq.clone().into(),
            message: Some(qq_login_guard.status_message.clone()),
        });
    }

    // 获取共享的QQ API实例
    let mut qq_api_guard = qq_api_state.lock().await;
    let qq_api = qq_api_guard.as_ref()
        .ok_or("QQ API实例未初始化".to_string())?;

    // 检查二维码状态 - 对应原版Python第2348-2372行
    // println!("调用QQ API检查二维码状态"); // 注释掉调试信息
    let login_status = qq_api.check_qrcode_status().await;
    // println!("QQ API返回状态: {:?}", login_status); // 注释掉调试信息

    match login_status {
        QQLoginStatus::Waiting => {
            // 二维码未失效 - 对应原版Python第2350-2351行
            qq_login_guard.login_status = "waiting".to_string();
            qq_login_guard.status_message = "请扫码登录QQ".to_string();
            Ok(QRStatusResult {
                status: "waiting".to_string(),
                qq_number: None,
                message: None,
            })
        }
        QQLoginStatus::Scanned => {
            // 二维码认证中 - 对应原版Python第2352-2353行
            qq_login_guard.login_status = "scanned".to_string();
            qq_login_guard.status_message = "扫码成功，请在手机上确认".to_string();
            Ok(QRStatusResult {
                status: "scanned".to_string(),
                qq_number: None,
                message: None,
            })
        }
        QQLoginStatus::Success(redirect_url) => {
            // 登录成功 - 对应原版Python第2354-2363行和complete_login方法
            qq_login_guard.login_status = "confirmed".to_string();
            qq_login_guard.status_message = "登录成功！正在进行验证...".to_string();

            // 释放qq_login_guard以避免死锁
            drop(qq_login_guard);

            // 完成登录流程 - 对应原版Python第2412行的finish_login
            // 注意：qq_api_guard已经在上面获取了，直接使用
            if let Some(qq_api) = qq_api_guard.as_mut() {
                // println!("开始完成登录流程，重定向URL: {}", redirect_url); // 注释掉调试信息
                let login_success = qq_api.finish_login(&redirect_url).await;

                if login_success {
                    // 获取QQ号 - 对应原版Python第2416行
                    let qq_number = qq_api.get_current_qq().to_string();
                    // println!("登录完成成功，QQ号: {}", qq_number); // 注释掉调试信息

                    // 释放qq_api_guard，然后重新获取qq_login_guard
                    drop(qq_api_guard);
                    let mut qq_login_guard = qq_login_state.lock().await;
                    qq_login_guard.is_logged_in = true;
                    qq_login_guard.current_qq = qq_number.clone();

                    Ok(QRStatusResult {
                        status: "confirmed".to_string(),
                        qq_number: Some(qq_number),
                        message: None,
                    })
                } else {
                    // 登录完成失败
                    // println!("登录完成失败"); // 注释掉调试信息
                    drop(qq_api_guard);
                    let mut qq_login_guard = qq_login_state.lock().await;
                    qq_login_guard.login_status = "error".to_string();
                    qq_login_guard.status_message = "登录完成出错，请重试".to_string();

                    Err("登录完成失败".to_string())
                }
            } else {
                Err("QQ API实例不可用".to_string())
            }
        }
        QQLoginStatus::Expired => {
            // 二维码已失效 - 对应原版Python第2364-2367行
            qq_login_guard.login_status = "expired".to_string();
            qq_login_guard.status_message = "二维码已失效，请刷新".to_string();
            Ok(QRStatusResult {
                status: "expired".to_string(),
                qq_number: None,
                message: None,
            })
        }
        QQLoginStatus::Error(error_msg) => {
            // 检查是否是用户未确认登录的情况
            if error_msg.contains("您未确认登录") {
                // 用户未确认登录 - 用户取消了登录或拒绝了登录
                qq_login_guard.login_status = "login_rejected".to_string();
                qq_login_guard.status_message = error_msg.clone();
                Ok(QRStatusResult {
                    status: "login_rejected".to_string(),
                    qq_number: None,
                    message: Some(error_msg),
                })
            } else {
                // 其他错误
                qq_login_guard.login_status = "error".to_string();
                qq_login_guard.status_message = "请扫码登录QQ".to_string();
                Err(format!("QQ登录检查失败: {}", error_msg))
            }
        }
    }
}

/// 内部VIP QQ验证函数
async fn verify_vip_qq_internal(version_checker: &Arc<TokioMutex<Option<crate::core::version::VersionChecker>>>, qq_number: &str) -> Result<VerificationResult, String> {

    let checker_guard = version_checker.lock().await;
    if let Some(checker) = checker_guard.as_ref() {
        // 检查VIP QQ验证是否启用
        if !checker.config.verification.vip_qq_verification_enabled {
            return Ok(VerificationResult {
                success: false,
                message: "VIP QQ验证未启用".to_string(),
                method: Some("vip_qq_verification".to_string()),
                qq_number: Some(qq_number.to_string()),
            });
        }

        // 验证QQ号是否在VIP白名单中
        if checker.verify_vip_qq(qq_number) {
            Ok(VerificationResult {
                success: true,
                message: "VIP QQ验证通过！".to_string(),
                method: Some("vip_qq_verification".to_string()),
                qq_number: Some(qq_number.to_string()),
            })
        } else {
            Ok(VerificationResult {
                success: false,
                message: "抱歉，您的QQ并不在赞助名单里".to_string(),
                method: Some("vip_qq_verification".to_string()),
                qq_number: Some(qq_number.to_string()),
            })
        }
    } else {
        Err("版本检查器未初始化".to_string())
    }
}


