/// VSCode 设备代码生成工具
/// 
/// 对应原版 Python 的 augment_tools/vscode/utils/device_codes.py
/// 生成机器 ID 和设备 ID

use rand::Rng;
use uuid::Uuid;

/// 生成机器 ID
/// 对应原版 Python 的 generate_machine_id 函数
/// 
/// 生成一个 64 字符的十六进制字符串作为机器 ID
/// 类似于在 bash 中使用 /dev/urandom，但使用 Rust 的加密函数
/// 
/// Returns:
///     String: 64 字符的十六进制字符串
pub fn generate_machine_id() -> String {
    let mut rng = rand::thread_rng();
    // 生成 32 个随机字节（将变成 64 个十六进制字符）
    let random_bytes: Vec<u8> = (0..32).map(|_| rng.gen()).collect();
    // 转换为十六进制字符串
    hex::encode(random_bytes)
}

/// 生成设备 ID
/// 对应原版 Python 的 generate_device_id 函数
/// 
/// 生成一个随机的 UUID v4 作为设备 ID
/// 
/// Returns:
///     String: 小写的 UUID v4 字符串，格式为：xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
///             其中 x 是任何十六进制数字，y 是 8、9、A 或 B 中的一个
pub fn generate_device_id() -> String {
    // 生成随机的 UUID v4
    let device_id = Uuid::new_v4();
    device_id.to_string().to_lowercase()
}
