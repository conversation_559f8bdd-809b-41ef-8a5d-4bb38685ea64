# Tauri + Vanilla

This template should help get you started developing with <PERSON><PERSON> in vanilla HTML, CSS and Javascript.

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [<PERSON>ri](https://marketplace.visualstudio.com/items?itemName=tauri-apps.tauri-vscode) + [rust-analyzer](https://marketplace.visualstudio.com/items?itemName=rust-lang.rust-analyzer)

JetBrains 

# mac安装rust
## 安装 rustup
brew install rustup-init
rustup-init # 输入完直接回车然后重新打开终端
### 更新 Rust
rustup update
### 安装特定版本
rustup install nightly # 三个版本 [stable] [beta] [nightly] ，这里安装 nightly
### 设置默认工具链
rustup default nightly
=============================

# tauri 启动
cargo install tauri-cli # 安装 tauri-cli
# 启动
cargo tauri dev
# 编译
cargo build
cargo tauri build
# 清理编译缓存
cargo clean