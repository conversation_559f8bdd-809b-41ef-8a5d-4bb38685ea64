use tauri::{State, AppHandle, Emitter};
use serde_json;
use crate::app_state::AppState;
use crate::core::reset::AugmentReset;

/// 重置 Augment
/// 对应原版 Python 的 reset_augment 方法
#[tauri::command]
pub async fn reset_augment(state: State<'_, AppState>, app_handle: AppHandle) -> Result<(), String> {
    // 获取当前选择的编辑器
    let editor_type = {
        let config = state.config_manager.lock().unwrap();
        config.get_editor_type().unwrap_or_default()
    };

    if editor_type.is_empty() {
        return Err("未选择编辑器".to_string());
    }

    // 检查是否已经在重置中，但允许重新启动卡住的重置
    {
        let reset_status = state.reset_status.lock().unwrap();
        if reset_status.is_running {
            // 检查重置是否可能已经卡住（超过5分钟无更新）
            // 这里可以添加更复杂的逻辑来判断是否需要强制重启
            println!("检测到重置正在进行中，状态: {:?}", reset_status);
            return Err("正在重置中，请稍候...".to_string());
        }
    }

    // 更新重置状态为运行中
    {
        let mut reset_status = state.reset_status.lock().unwrap();
        reset_status.is_running = true;
        reset_status.completed = false;
        reset_status.success = false;
        reset_status.message = "正在重置...".to_string();
    }

    // 创建重置管理器
    let reset_manager = match AugmentReset::new(&editor_type) {
        Ok(manager) => manager,
        Err(e) => {
            // 更新状态为失败
            let mut reset_status = state.reset_status.lock().unwrap();
            reset_status.is_running = false;
            reset_status.completed = true;
            reset_status.success = false;
            reset_status.message = format!("启动重置失败: {}", e);
            return Err(e);
        }
    };

    // 设置进度回调 - 发送进度事件到前端
    let app_handle_clone = app_handle.clone();
    let mut reset_manager = reset_manager;
    reset_manager.set_progress_callback(move |message: &str, progress: f64| {
        let _ = app_handle_clone.emit("update_progress", serde_json::json!({
            "taskId": "reset",
            "progress": progress,
            "message": message
        }));
    });

    // 克隆状态用于后台任务
    let reset_status_clone = state.reset_status.clone();
    let app_handle_clone2 = app_handle.clone();
    tokio::spawn(async move {
        let reset_result = reset_manager.reset().await;

        // 更新重置状态
        let mut reset_status = reset_status_clone.lock().unwrap();
        reset_status.is_running = false;
        reset_status.completed = true;
        reset_status.success = reset_result.success;

        if reset_result.success {
            reset_status.message = "重置成功".to_string();
            // 发送重置完成事件（成功）- 与原版Python行为一致
            let _ = app_handle_clone2.emit("reset_complete", serde_json::json!({
                "success": true,
                "message": "重置成功"
            }));
        } else {
            // 生成错误摘要（与原版 Python 逻辑一致）
            let errors: Vec<String> = reset_result.results
                .iter()
                .filter(|r| !r.success)
                .filter_map(|r| r.error.clone())
                .take(3) // 最多显示3个错误
                .collect();

            let error_message = if !errors.is_empty() {
                format!("重置失败:\n{}", errors.join("\n"))
            } else {
                "重置失败: 未知原因".to_string()
            };

            reset_status.message = error_message.clone();

            // 发送重置完成事件（失败）- 与原版Python行为一致
            let _ = app_handle_clone2.emit("reset_complete", serde_json::json!({
                "success": false,
                "message": error_message
            }));
        }
    });

    Ok(())
}

/// 获取重置状态
/// 对应原版 Python 的 get_reset_status 方法
#[tauri::command]
pub async fn get_reset_status(state: State<'_, AppState>) -> Result<String, String> {
    let reset_status = state.reset_status.lock().unwrap();
    let status_json = serde_json::to_string(&*reset_status)
        .map_err(|e| format!("序列化重置状态失败: {}", e))?;
    Ok(status_json)
}

/// 强制清理重置状态
/// 用于处理重置卡住的情况
#[tauri::command]
pub async fn force_clear_reset_status(state: State<'_, AppState>) -> Result<String, String> {
    {
        let mut reset_status = state.reset_status.lock().unwrap();
        reset_status.is_running = false;
        reset_status.completed = true;
        reset_status.success = false;
        reset_status.message = "重置状态已强制清理".to_string();
    }
    Ok("重置状态已清理".to_string())
}

/// 重置编辑器 (保持兼容性)
/// 对应原版 Python 的 reset_editor 方法
#[tauri::command]
#[allow(dead_code)]
pub async fn reset_editor(editor_type: String, state: State<'_, AppState>, app_handle: AppHandle) -> Result<String, String> {
    // 设置选择的编辑器
    {
        let mut config = state.config_manager.lock().unwrap();
        config.set_editor_type(&editor_type).map_err(|e| format!("设置编辑器类型失败: {}", e))?;
    }

    // 调用主重置函数
    reset_augment(state, app_handle).await?;

    Ok("重置已开始".to_string())
}
