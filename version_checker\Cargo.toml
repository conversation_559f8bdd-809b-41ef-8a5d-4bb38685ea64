[package]
name = "yaugment-version-checker"
version = "1.0.0"
description = "YAugment 独立版本检查程序"
authors = ["Yan <<EMAIL>>"]
license = "MIT"
edition = "2021"
rust-version = "1.70"

[[bin]]
name = "version_checker"
path = "src/main.rs"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
reqwest = { version = "0.11", features = ["json"] }
base64 = "0.21"
uuid = { version = "1.0", features = ["v4"] }
thiserror = "1.0"
log = "0.4"
env_logger = "0.10"
eframe = "0.24"
egui = "0.24"
rfd = "0.12"
dirs = "5.0"
std::process = "1.0"

[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winuser", "processthreadsapi"] }

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
