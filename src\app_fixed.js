// 全局变量
let bridge = null;
let currentPage = 'editorSelect';
let selectedEditor = '';
let resetStatusCheckInterval = null;

// 全局错误处理 - 确保所有错误都会显示toast
window.addEventListener('error', function(event) {
    // 过滤掉一些不需要显示toast的错误
    const errorMessage = event.error?.message || event.message || '';

    // 跳过某些类型的错误，避免误报
    if (errorMessage.includes('save_config failed') ||
        errorMessage.includes('save_config error') ||
        errorMessage.includes('handleConfigUpdate') ||
        errorMessage.includes('config.editor_type') ||
        errorMessage.includes('ResizeObserver') ||
        errorMessage.includes('Non-Error promise rejection') ||
        event.filename?.includes('tauri')) {
        console.warn('Filtered global error:', errorMessage);
        return;
    }

    if (typeof showToast === 'function') {
        console.error('Global error caught:', event.error || event.message);
        showToast('操作失败', 'error', 6000);
    }
});

// 处理未捕获的Promise rejection
window.addEventListener('unhandledrejection', function(event) {
    // 过滤掉一些不需要显示toast的错误
    const errorMessage = event.reason?.message || event.reason || '';

    // 跳过某些类型的错误，避免误报
    if (errorMessage.includes('save_config failed') ||
        errorMessage.includes('save_config error') ||
        errorMessage.includes('handleConfigUpdate') ||
        errorMessage.includes('config.editor_type') ||
        errorMessage.includes('ResizeObserver') ||
        errorMessage.includes('Non-Error promise rejection')) {
        console.warn('Filtered unhandled rejection:', errorMessage);
        event.preventDefault(); // 阻止默认的错误处理
        return;
    }

    if (typeof showToast === 'function') {
        console.error('Unhandled promise rejection:', event.reason);
        showToast('操作失败', 'error', 6000);
    }
    event.preventDefault(); // 阻止默认的错误处理
});

window.addEventListener('unhandledrejection', function(event) {
    if (typeof showToast === 'function') {
        showToast('网络错误', 'error', 6000);
    }
});

// 包装所有异步函数调用，确保错误被捕获
function safeCall(fn, ...args) {
    try {
        const result = fn(...args);
        if (result && typeof result.catch === 'function') {
            result.catch(error => {
                // 过滤掉配置保存相关的错误，避免误报
                if (error.message && (error.message.includes('save_config') || error.message.includes('config'))) {
                    console.warn('Config-related error filtered in safeCall:', error.message);
                    return;
                }

                // 简化错误信息，不显示技术细节
                if (error.message && (error.message.includes('dns error') || error.message.includes('connect') || error.message.includes('network'))) {
                    showToast('网络错误', 'error', 5000);
                } else {
                    showToast('操作失败', 'error', 5000);
                }
            });
        }
        return result;
    } catch (error) {
        // 过滤掉配置保存相关的错误，避免误报
        if (error.message && (error.message.includes('save_config') || error.message.includes('config'))) {
            console.warn('Config-related error filtered in safeCall:', error.message);
            throw error; // 重新抛出错误，但不显示toast
        }

        // 简化错误信息，不显示技术细节
        if (error.message && (error.message.includes('dns error') || error.message.includes('connect') || error.message.includes('network'))) {
            showToast('网络错误', 'error', 5000);
        } else {
            showToast('操作失败', 'error', 5000);
        }
        throw error;
    }
}

// 通用invoke函数，用于调用Tauri命令
async function invoke(command, args = {}) {
    if (window.__TAURI__ && window.__TAURI__.core) {
        return await window.__TAURI__.core.invoke(command, args);
    } else {
        throw new Error('Tauri API not available');
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    try {
        detectEnvironment();
        initWebChannel();
        setupEventListeners();

        // 启动版本检查和应用初始化流程
        initializeApp();

        // 监听版本检查完成事件，进行重新初始化
        setupVersionCheckCompletedListener();
    } catch (error) {
        if (typeof showToast === 'function') {
            showToast('应用初始化失败', 'error', 6000);
        }
    }
});

// 环境检测函数
function detectEnvironment() {
    const env = {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        currentUrl: window.location.href,
        referrer: document.referrer,
        timestamp: new Date().toISOString()
    };

    return env;
}

// 初始化 Tauri Bridge
function initWebChannel() {
    // 检测运行环境
    detectEnvironment();

    // 检查 Tauri API 是否可用
    if (typeof window.__TAURI__ === 'undefined') {
        return;
    }

    // 创建 Tauri bridge 对象
    bridge = {
        // 配置管理
        get_config: (callback) => {
            window.__TAURI__.core.invoke('get_config').then(callback).catch(err => {
                callback(null);
            });
        },
        set_config: async (key, value) => await window.__TAURI__.core.invoke('set_config_value', { keyPath: key, value }),
        save_config: (config, callback) => {
            window.__TAURI__.core.invoke('save_config', { config }).then(result => {
                callback(result);
            }).catch(err => {
                console.warn('save_config error (handled):', err);
                callback(false);
            });
        },

        // 编辑器检测
        detect_editors: async () => await window.__TAURI__.core.invoke('detect_editors'),
        select_editor: (editorType, callback) => {
            window.__TAURI__.core.invoke('select_editor', { editorType }).then(result => {
                callback(result);
            }).catch(err => {
                console.error('select_editor failed:', err);
                callback(false);
            });
        },
        set_selected_editor: async (editor) => await window.__TAURI__.core.invoke('select_editor', { editorType: editor }),
        get_editor_status: (callback) => {
            window.__TAURI__.core.invoke('get_editor_status').then(callback).catch(err => {
                console.error('get_editor_status failed:', err);
                callback({});
            });
        },
        get_selected_editor: (callback) => {
            window.__TAURI__.core.invoke('get_selected_editor').then(callback).catch(err => {
                console.error('get_selected_editor failed:', err);
                callback('');
            });
        },
        is_first_run: (callback) => {
            window.__TAURI__.core.invoke('is_first_run').then(callback).catch(err => {
                console.error('is_first_run failed:', err);
                callback(false);
            });
        },

        // 窗口控制
        minimize_window: () => window.__TAURI__.core.invoke('minimize_window'),
        toggle_maximize: () => window.__TAURI__.core.invoke('toggle_maximize'),
        close_window: () => window.__TAURI__.core.invoke('close_window'),
        start_window_drag: (x, y) => window.__TAURI__.core.invoke('start_window_drag', { x, y }),
        update_window_drag: (x, y) => window.__TAURI__.core.invoke('update_window_drag', { x, y }),
        end_window_drag: () => window.__TAURI__.core.invoke('end_window_drag'),

        // 日志
        log_message: async (message) => await window.__TAURI__.core.invoke('log_message', { message }),

        // 邮箱功能
        generate_email: (callback) => {
            window.__TAURI__.core.invoke('generate_email').then(result => {
                console.log('generate_email result:', result);
                callback(result);
            }).catch(err => {
                console.error('generate_email failed:', err);
                callback(null); // 使用null而不是空字符串，更明确表示失败
            });
        },
        get_verification_code: () => window.__TAURI__.core.invoke('get_verification_code'),
        get_verification_status: (callback) => {
            if (callback) {
                // 回调模式
                window.__TAURI__.core.invoke('get_verification_status').then(callback).catch(err => {
                    console.error('get_verification_status failed:', err);
                    callback('{}');
                });
            } else {
                // 直接返回Promise模式
                return window.__TAURI__.core.invoke('get_verification_status');
            }
        },
        test_email_connection: () => window.__TAURI__.core.invoke('test_email_connection'),
        start_verification_code: async () => await window.__TAURI__.core.invoke('start_verification_code'),
        stop_verification_code: async () => await window.__TAURI__.core.invoke('stop_verification_code'),

        // 账户管理
        get_account_info: async () => await window.__TAURI__.core.invoke('get_account_info'),
        set_account_info: async (info) => await window.__TAURI__.core.invoke('set_account_info', { info }),

        // 重置功能
        reset_augment: () => window.__TAURI__.core.invoke('reset_augment'),
        get_reset_status: (callback) => {
            window.__TAURI__.core.invoke('get_reset_status').then(callback).catch(err => {
                console.error('get_reset_status failed:', err);
                callback('{}');
            });
        },
        reset_all: async () => await window.__TAURI__.core.invoke('reset_all'),

        // 版本检查
        check_version: async () => await window.__TAURI__.core.invoke('check_version'),
        agree_disclaimer: async () => await window.__TAURI__.core.invoke('agree_disclaimer'),
        verify_code: async (code) => await window.__TAURI__.core.invoke('verify_code', { code }),
        verify_vip_qq: async (qq_number) => await window.__TAURI__.core.invoke('verify_vip_qq', { qq_number }),
        get_about_info: async () => await window.__TAURI__.core.invoke('get_about_info'),
        get_config_help_url: () => window.__TAURI__.core.invoke('get_config_help_url'),
        get_token_help_url: () => window.__TAURI__.core.invoke('get_token_help_url'),
        get_expiry_status: async () => await window.__TAURI__.core.invoke('get_expiry_status'),
        stop_expiry_check: async () => await window.__TAURI__.core.invoke('stop_expiry_check'),

        // 账号管理
        query_augment_account: async (token) => await window.__TAURI__.core.invoke('query_augment_account', { token }),

        // 新增账号管理功能
        get_portal_token: async (token) => await window.__TAURI__.core.invoke('get_portal_token', { token }),
        query_account_with_portal: async (portalToken) => await window.__TAURI__.core.invoke('query_account_with_portal', { portalToken }),
        query_account_with_portal_and_update_time: async (portalToken) => await window.__TAURI__.core.invoke('query_account_with_portal_and_update_time', { portalToken }),
        get_saved_accounts: async () => await window.__TAURI__.core.invoke('get_saved_accounts'),
        get_saved_account_by_email: async (email) => await window.__TAURI__.core.invoke('get_saved_account_by_email', { email }),
        remove_saved_account: async (email) => await window.__TAURI__.core.invoke('remove_saved_account', { email }),
        get_machine_uuid: async () => await window.__TAURI__.core.invoke('get_machine_uuid'),

        // 工具相关
        get_cursor_tools: async () => await window.__TAURI__.core.invoke('get_cursor_tools'),
        get_vscode_tools: async () => await window.__TAURI__.core.invoke('get_vscode_tools'),
        execute_cursor_tool: async (tool, params) => await window.__TAURI__.core.invoke('execute_cursor_tool', { tool, params }),
        execute_vscode_tool: async (tool, params) => await window.__TAURI__.core.invoke('execute_vscode_tool', { tool, params }),

        // 剪贴板
        copy_text_to_clipboard: async (text) => await window.__TAURI__.core.invoke('copy_text_to_clipboard', { text }),

        // 打开外部链接
        open_external_url: async (url) => await window.__TAURI__.core.invoke('open_url', { url })
    };

    window.bridge = bridge;  // 设置为全局变量

    // 延迟设置Tauri事件监听器，确保Tauri完全初始化
    setTimeout(() => {
        setupTauriEventListeners();
    }, 100);
}

// 设置Tauri事件监听器
function setupTauriEventListeners() {
    // 检查Tauri API是否可用 (Tauri 2.0)
    if (!window.__TAURI__ || !window.__TAURI__.event || !window.__TAURI__.event.listen) {
        return;
    }

    // 监听来自Rust后端的事件 (Tauri 2.0 API)
    window.__TAURI__.event.listen('show_toast', (event) => {
        const { message, type } = event.payload;
        showToast(message, type);
    });

    window.__TAURI__.event.listen('update_progress', (event) => {
        const { taskId, progress, message } = event.payload;
        updateProgress(taskId, progress, message);
    });

    window.__TAURI__.event.listen('update_editor_status', (event) => {
        const { status } = event.payload;
        updateEditorStatus(status);
    });

    window.__TAURI__.event.listen('show_email_result', (event) => {
        const { email } = event.payload;
        showEmailResultDirect(email);
    });

    window.__TAURI__.event.listen('show_verification_code', (event) => {
        const { code } = event.payload;

        // 停止轮询
        if (verificationStatusCheckInterval) {
            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;
        }

        // 显示验证码
        showVerificationCodeInWorkflow(code);
        updateWorkspaceStatus('success', '验证码获取成功');
        updateWorkflowProgress(2, true); // 标记第二步完成
        updateVerificationDetailedStatus('success', '验证码获取成功', `验证码: ${code}`);

        // 强制显示成功toast - 确保在所有环境下都能显示
        const toastResult = showToast(`验证码获取成功: ${code}`, 'success', 6000);

        // 恢复按钮状态
        setTimeout(() => {
            updateWorkflowButtonState('getCodeBtn', 'ready', '获取验证码');

            // 重新启用生成邮箱按钮
            const generateEmailBtn = document.getElementById('generateEmailBtn');
            if (generateEmailBtn) {
                generateEmailBtn.disabled = false;
                console.log('验证码获取成功：重新启用生成邮箱按钮');
            }
        }, 100);
    });

    window.__TAURI__.event.listen('verification_complete', (event) => {
        const { success, message } = event.payload;
        console.log('收到验证码完成事件:', success, message);

        // 停止轮询
        if (verificationStatusCheckInterval) {
            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;
        }

        // verification_complete 信号只处理失败情况
        // 成功情况由 show_verification_code 信号处理
        if (success) {
            updateVerificationStatus('success', message);
            // 更新实时状态显示为绿色
            updateWorkspaceStatus('success', '验证码获取成功');
            // 成功时不显示toast，等待show_verification_code信号
        } else {
            updateVerificationStatus('error', message);
            // 失败时显示toast并恢复按钮状态 - 这是唯一的失败toast
            showToast(message || '验证码获取失败', 'error');
            updateWorkspaceStatus('error', message || '验证码获取失败');
            // 恢复按钮状态
            setTimeout(() => {
                updateWorkflowButtonState('getCodeBtn', 'ready', '获取验证码');

                // 重新启用生成邮箱按钮
                const generateEmailBtn = document.getElementById('generateEmailBtn');
                if (generateEmailBtn) {
                    generateEmailBtn.disabled = false;
                    console.log('验证码获取失败：重新启用生成邮箱按钮');
                }
            }, 100);
        }
    });

    window.__TAURI__.event.listen('verification_progress', (event) => {
        const { message, progress } = event.payload;
        updateVerificationDetailedStatus('running', message, '');
        // 只更新进度条，不显示数字进度格式（与原版一致）
        const progressBar = document.getElementById('verificationProgressBar');
        if (progressBar && progress !== undefined) {
            const percentage = progress * 100;
            progressBar.style.width = `${percentage}%`;
        }
    });

    window.__TAURI__.event.listen('reset_complete', (event) => {
        const { success, message } = event.payload;
        // 停止轮询
        if (resetStatusCheckInterval) {
            clearInterval(resetStatusCheckInterval);
            resetStatusCheckInterval = null;
        }
        // 直接处理重置完成
        handleResetComplete(success, message);
    });

    window.__TAURI__.event.listen('config_updated', (event) => {
        const { config } = event.payload;
        handleConfigUpdate(config);
    });
}

// 设置事件监听器
function setupEventListeners() {
    // 优化的鼠标跟踪效果，使用节流避免过度触发
    let mouseTrackingThrottle = false;
    document.querySelectorAll('.feature-card').forEach(card => {
        card.addEventListener('mousemove', (e) => {
            if (mouseTrackingThrottle) return;
            mouseTrackingThrottle = true;

            requestAnimationFrame(() => {
                const hoverEffect = card.querySelector('.card-hover-effect');
                if (hoverEffect) {
                    const rect = card.getBoundingClientRect();
                    const x = ((e.clientX - rect.left) / rect.width) * 100;
                    const y = ((e.clientY - rect.top) / rect.height) * 100;
                    hoverEffect.style.setProperty('--mouse-x', `${x}%`);
                    hoverEffect.style.setProperty('--mouse-y', `${y}%`);
                }
                mouseTrackingThrottle = false;
            });
        });
    });

    // 设置标题栏事件（双击最大化等）
    setupTitlebarEvents();
}

// 设置标题栏事件（双击最大化等）
function setupTitlebarEvents() {
    const titlebar = document.getElementById('titlebar');
    const windowControls = titlebar.querySelector('.window-controls');

    // 双击标题栏最大化/还原窗口
    titlebar.addEventListener('dblclick', (e) => {
        // 如果双击的是窗口控制按钮区域，不触发最大化
        if (windowControls.contains(e.target) || e.target.closest('.window-controls')) {
            return;
        }
        e.preventDefault();
        e.stopPropagation();
        toggleMaximize();
    });

    // 阻止标题栏上的文本选择
    titlebar.addEventListener('selectstart', (e) => {
        e.preventDefault();
    });

    // 阻止标题栏上的右键菜单
    titlebar.addEventListener('contextmenu', (e) => {
        e.preventDefault();
    });
}

// 初始化应用
async function initializeApp() {
    try {
        // 确保最小加载时间，避免闪烁
        const minLoadingTime = 1200; // 1.2秒最小加载时间
        const startTime = Date.now();

        // 显示加载页面
        showPage('loadingPage');

        // 检查 bridge 是否可用
        if (!bridge) {
            setTimeout(() => showPage('editorSelectPage'), minLoadingTime);
            return;
        }

        // 检查是否在Tauri环境中且窗口是否可见
        if (window.__TAURI__ && window.__TAURI__.window) {
            try {
                const currentWindow = window.__TAURI__.window.getCurrentWindow();
                const isVisible = await currentWindow.isVisible();

                if (!isVisible) {
                    // 停留在加载页面，等待版本检查完成事件
                    return;
                }
            } catch (error) {
                console.warn('检查窗口可见性失败:', error);
            }
        }

        // 版本检查已在独立的version_check.html中完成
        // 主程序直接开始初始化流程
        continueAppInitialization(startTime, minLoadingTime);

    } catch (error) {
        console.error('应用初始化失败:', error);
        // 出错时显示编辑器选择页面
        setTimeout(() => {
            showPage('editorSelectPage');
        }, 1200);
    }
}



// 设置版本检查完成事件监听器
function setupVersionCheckCompletedListener() {
    if (window.__TAURI__ && window.__TAURI__.event) {
        // 监听版本检查完成事件
        window.__TAURI__.event.listen('version_check_completed', async (event) => {
            try {
                // 重新初始化应用状态
                await reinitializeAfterVersionCheck();
            } catch (error) {
                showToast('重新初始化失败', 'error');
            }
        });
    }
}

// 版本检查完成后的重新初始化
async function reinitializeAfterVersionCheck() {
    // 显示加载页面
    showPage('loadingPage');

    // 重置应用状态
    resetApplicationState();

    // 重新开始初始化流程
    const startTime = Date.now();
    const minLoadingTime = 1200;

    // 延迟一点时间确保UI更新
    setTimeout(() => {
        continueAppInitialization(startTime, minLoadingTime);
    }, 300);
}

// 重置应用状态
function resetApplicationState() {
    // 重置全局变量
    currentPage = 'loading';

    // 重置账号管理状态
    if (typeof augmentAccountState !== 'undefined') {
        augmentAccountState.isInitialized = false;
        augmentAccountState.selectedAccount = null;
        augmentAccountState.accountData = null;
        augmentAccountState.token = null;
        augmentAccountState.isTokenValid = false;
    }

    // 清除可能的定时器
    if (resetStatusCheckInterval) {
        clearInterval(resetStatusCheckInterval);
        resetStatusCheckInterval = null;
    }


}

// 继续应用初始化流程（版本检查通过后）
function continueAppInitialization(startTime, minLoadingTime) {
    bridge.is_first_run((isFirstRun) => {
        const elapsedTime = Date.now() - startTime;
        const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

        setTimeout(() => {
            if (!isFirstRun) {
                // 不是首次运行，直接显示主页面
                bridge.get_selected_editor((editor) => {
                    selectedEditor = editor;

                    // 获取配置并更新显示
                    bridge.get_config((config) => {

                        // 更新浮动卡片信息
                        updateFloatingCards(config);

                        // 更新编辑器显示
                        updateEditorDisplay();

                        // 延迟初始化关于页面，确保版本检查完成
                        setTimeout(() => {
                            initializeAboutPage().catch(error => {
                                console.error('关于页面初始化失败:', error);
                            });
                        }, 2000); // 延迟2秒，确保版本检查完成

                        // 启动时效状态检查
                        startExpiryStatusCheck();

                        // 初始化账号管理组件（如果尚未初始化）
                        if (!augmentAccountState.isInitialized) {
                            initAugmentAccountManager().catch(error => {
                                console.error('账号管理组件初始化失败:', error);
                            });
                        }

                        hideLoadingPageAndShow('main');
                    });
                });
            } else {
                // 首次运行，显示编辑器选择页面
                console.log('首次运行，显示编辑器选择页面');
                hideLoadingPageAndShow('editorSelect');
            }
        }, remainingTime);
    });
}

// 更新浮动卡片信息
function updateFloatingCards(config) {
    const domainElement = document.getElementById('floatingDomain');
    const modeElement = document.getElementById('floatingMode');

    if (config && config.email) {
        const emailConfig = config.email;

        // 更新域名
        if (domainElement) {
            domainElement.textContent = emailConfig.domain || '未配置';
        }

        // 更新模式
        if (modeElement) {
            const mode = emailConfig.use_temp_mail ? '临时邮箱' : 'IMAP';
            modeElement.textContent = mode;
        }
    } else {
        // 如果没有配置，显示默认值
        if (domainElement) {
            domainElement.textContent = '未配置';
        }
        if (modeElement) {
            modeElement.textContent = 'IMAP';
        }
    }
}

// 初始化关于页面信息
async function initializeAboutPage() {
    if (!bridge || !bridge.get_about_info) {
        console.warn('Bridge或get_about_info方法不可用');
        return;
    }

    try {
        // 如果版本检查器未初始化，尝试执行版本检查
        let aboutInfo = null;
        try {
            aboutInfo = await bridge.get_about_info();
        } catch (error) {
            if (error.includes('版本检查器未初始化')) {
                console.log('版本检查器未初始化，尝试执行版本检查...');
                try {
                    // 执行版本检查以初始化版本检查器
                    await bridge.check_version();

                    // 重新尝试获取关于信息
                    aboutInfo = await bridge.get_about_info();
                } catch (versionCheckError) {
                    console.error('版本检查失败:', versionCheckError);
                    throw error; // 抛出原始错误
                }
            } else {
                throw error;
            }
        }

        if (aboutInfo) {
            updateAboutPageInfo(aboutInfo);
        }
    } catch (error) {
        console.error('获取关于信息失败:', error);
    }
}

// 时效状态检查
let expiryCheckInterval = null;

// 启动时效状态检查
function startExpiryStatusCheck() {
    if (!bridge || !bridge.get_expiry_status) {
        console.warn('Bridge或get_expiry_status方法不可用');
        return;
    }

    // 立即检查一次
    checkExpiryStatus();

    // 每30秒检查一次时效状态
    expiryCheckInterval = setInterval(() => {
        checkExpiryStatus();
    }, 30000);
}

// 检查时效状态
async function checkExpiryStatus() {
    try {
        const expiryStatus = await bridge.get_expiry_status();

        if (expiryStatus && expiryStatus.enabled) {
            const remainingHours = Math.floor(expiryStatus.remaining_seconds / 3600);
            const remainingMinutes = Math.floor((expiryStatus.remaining_seconds % 3600) / 60);

            // 将内部验证方法转换为用户友好的显示文本
            const verificationTypeDisplay = getVerificationTypeDisplay(expiryStatus.verification_method);

            console.log(`认证时效剩余: ${remainingHours}小时${remainingMinutes}分钟 (${verificationTypeDisplay})`);

            // Mac平台特殊处理：在时效即将过期时显示更明显的提醒
            if (navigator.platform.toLowerCase().includes('mac')) {
                if (expiryStatus.remaining_seconds <= 300) { // 剩余5分钟时
                    console.warn('Mac平台：认证时效即将过期，剩余时间不足5分钟');
                    showToast(`认证时效即将过期，剩余${remainingMinutes}分钟`, 'warning', 10000);
                }
            }

            // 🗑️ 删除警告toast：不再显示即将过期的警告toast通知
        }
    } catch (error) {
        // 时效检查失败，可能是未启用或其他原因
        // console.warn('时效状态检查失败:', error);
    }
}

// 将内部验证方法转换为用户友好的显示文本
function getVerificationTypeDisplay(verificationMethod) {
    switch (verificationMethod) {
        case 'code_verification':
            return '验证码验证';
        case 'vip_qq_verification':
            return '赞助用户';
        default:
            return '已认证';
    }
}

// 🗑️ 删除时效警告函数：不再需要显示警告toast通知

// 停止时效状态检查
function stopExpiryStatusCheck() {
    if (expiryCheckInterval) {
        clearInterval(expiryCheckInterval);
        expiryCheckInterval = null;
    }
}

// 更新关于页面信息
function updateAboutPageInfo(aboutInfo) {


    // 更新应用名称和版本
    const appNameElement = document.querySelector('.app-name');
    if (appNameElement) {
        // 使用固定的应用名称和版本，或从aboutInfo获取
        const appName = aboutInfo.app_name || 'YAugment';
        const appVersion = aboutInfo.app_version || '1.0.0';
        appNameElement.textContent = `${appName} v${appVersion}`;

    }

    // 更新版权信息
    const copyrightElement = document.querySelector('.copyright-item .info-value');
    if (copyrightElement && aboutInfo.copyright) {
        copyrightElement.textContent = aboutInfo.copyright;

    }

    // 更新邮箱信息
    const emailElement = document.querySelector('.email-item .info-value');
    if (emailElement && aboutInfo.email) {
        emailElement.textContent = aboutInfo.email;

    }

    // 更新QQ群信息
    const qqElement = document.querySelector('.qq-item .info-value');
    if (qqElement && aboutInfo.qq_group_1) {
        let qqText = aboutInfo.qq_group_1;

        // 如果有QQ群2前缀，组合显示
        if (aboutInfo.qq_group_2_prefix && aboutInfo.qq_group_2 && aboutInfo.qq_group_2_suffix) {
            qqText += ` <span class="group-note">${aboutInfo.qq_group_2_prefix}${aboutInfo.qq_group_2}${aboutInfo.qq_group_2_suffix}</span>`;
        }

        qqElement.innerHTML = qqText;

    }
}

// 页面显示函数
function showPage(pageId) {
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => page.classList.add('hidden'));

    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        targetPage.classList.remove('hidden');
        currentPage = pageId.replace('Page', '');
    }
}

// 隐藏加载页面并显示目标页面（带平滑过渡）
function hideLoadingPageAndShow(targetPage) {
    const loadingPage = document.getElementById('loadingPage');
    const loadingContainer = loadingPage.querySelector('.loading-container');

    // 开始加载页面淡出动画
    if (loadingContainer) {
        loadingContainer.classList.add('fade-out');
    }

    // 等待淡出动画完成后切换页面
    setTimeout(() => {
        loadingPage.classList.add('hidden');

        if (targetPage === 'main') {
            showMainPageDirectly();
        } else if (targetPage === 'editorSelect') {
            showEditorSelectPage();
        }
    }, 800); // 等待淡出动画完成
}

// 显示编辑器选择页面
function showEditorSelectPage() {
    const editorSelectPage = document.getElementById('editorSelectPage');
    const mainPage = document.getElementById('mainPage');

    // 确保主页面隐藏
    mainPage.classList.add('hidden');

    // 显示编辑器选择页面并添加淡入动画
    editorSelectPage.classList.remove('hidden');
    editorSelectPage.classList.add('fade-in');

    // 更新当前页面状态
    currentPage = 'editorSelect';

    // 动画完成后移除动画类
    setTimeout(() => {
        editorSelectPage.classList.remove('fade-in');
    }, 800);

    // 初始化关于页面信息（确保首次运行时也能获取正确的关于信息）
    initializeAboutPage();

    // 获取编辑器状态
    bridge.get_editor_status((status) => {
        updateEditorStatus(status);
    });
}

// 直接显示主页面（带淡入动画）
function showMainPageDirectly() {
    const editorSelectPage = document.getElementById('editorSelectPage');
    const mainPage = document.getElementById('mainPage');

    // 确保编辑器选择页面隐藏
    editorSelectPage.classList.add('hidden');

    // 显示主页面并添加淡入动画
    mainPage.classList.remove('hidden');
    mainPage.classList.add('fade-in');

    // 更新当前页面状态
    currentPage = 'main';

    // 更新显示的编辑器
    updateEditorDisplay();

    // 缩短动画时间并立即触发页面动画
    setTimeout(() => {
        mainPage.classList.remove('fade-in');
        // 立即触发动画
        initPageAnimations();
    }, 400);
}

// 更新编辑器状态
function updateEditorStatus(status) {
    if (!status || typeof status !== 'object') {
        console.warn('updateEditorStatus: 无效的状态对象', status);
        return;
    }

    console.log('updateEditorStatus 收到状态:', status);

    // 处理数据结构 - 检查是否有 detection 字段
    let editorData;
    if (status.detection) {
        // 新格式: { detection: { vscode: {...}, cursor: {...} }, selected: "..." }
        editorData = status.detection;
        console.log('使用 detection 字段:', editorData);
    } else {
        // 旧格式: { vscode: {...}, cursor: {...} }
        editorData = status;
        console.log('使用直接格式:', editorData);
    }

    // VS Code状态
    const vscodeStatus = document.getElementById('vscodeStatus');
    if (vscodeStatus) {
        try {
            const vscodeInstalled = editorData.vscode?.installed || false;
            const indicator = vscodeStatus.querySelector('.status-indicator');
            const text = vscodeStatus.querySelector('.status-text');

            console.log('VS Code 安装状态:', vscodeInstalled, editorData.vscode);

            if (indicator && text) {
                indicator.className = `status-indicator ${vscodeInstalled ? 'installed' : 'not-installed'}`;
                text.className = `status-text ${vscodeInstalled ? 'installed' : 'not-installed'}`;
                text.textContent = vscodeInstalled ? '已安装' : '未安装';

                // 检查文字滚动
                checkAndEnableTextScrolling(text);
            }
        } catch (error) {
            console.error('更新VS Code状态时出错:', error);
        }
    }

    // Cursor状态
    const cursorStatus = document.getElementById('cursorStatus');
    if (cursorStatus) {
        try {
            const cursorInstalled = editorData.cursor?.installed || false;
            const indicator = cursorStatus.querySelector('.status-indicator');
            const text = cursorStatus.querySelector('.status-text');

            console.log('Cursor 安装状态:', cursorInstalled, editorData.cursor);

            if (indicator && text) {
                indicator.className = `status-indicator ${cursorInstalled ? 'installed' : 'not-installed'}`;
                text.className = `status-text ${cursorInstalled ? 'installed' : 'not-installed'}`;
                text.textContent = cursorInstalled ? '已安装' : '未安装';

                // 检查文字滚动
                checkAndEnableTextScrolling(text);
            }
        } catch (error) {
            console.error('更新Cursor状态时出错:', error);
        }
    }

    // IntelliJ IDEA状态
    const intellijStatus = document.getElementById('intellijStatus');
    if (intellijStatus) {
        try {
            const intellijInstalled = editorData.intellij?.installed || false;
            const indicator = intellijStatus.querySelector('.status-indicator');
            const text = intellijStatus.querySelector('.status-text');

            console.log('IntelliJ IDEA 安装状态:', intellijInstalled, editorData.intellij);

            if (indicator && text) {
                indicator.className = `status-indicator ${intellijInstalled ? 'installed' : 'not-installed'}`;
                text.className = `status-text ${intellijInstalled ? 'installed' : 'not-installed'}`;
                text.textContent = intellijInstalled ? '已安装' : '未安装';

                // 检查文字滚动
                checkAndEnableTextScrolling(text);
            }
        } catch (error) {
            console.error('更新IntelliJ IDEA状态时出错:', error);
        }
    }

    // PyCharm状态
    const pycharmStatus = document.getElementById('pycharmStatus');
    if (pycharmStatus) {
        try {
            const pycharmInstalled = editorData.pycharm?.installed || false;
            const indicator = pycharmStatus.querySelector('.status-indicator');
            const text = pycharmStatus.querySelector('.status-text');

            console.log('PyCharm 安装状态:', pycharmInstalled, editorData.pycharm);

            if (indicator && text) {
                indicator.className = `status-indicator ${pycharmInstalled ? 'installed' : 'not-installed'}`;
                text.className = `status-text ${pycharmInstalled ? 'installed' : 'not-installed'}`;
                text.textContent = pycharmInstalled ? '已安装' : '未安装';

                // 检查文字滚动
                checkAndEnableTextScrolling(text);
            }
        } catch (error) {
            console.error('更新PyCharm状态时出错:', error);
        }
    }

    // WebStorm状态
    const webstormStatus = document.getElementById('webstormStatus');
    if (webstormStatus) {
        try {
            const webstormInstalled = editorData.webstorm?.installed || false;
            const indicator = webstormStatus.querySelector('.status-indicator');
            const text = webstormStatus.querySelector('.status-text');

            console.log('WebStorm 安装状态:', webstormInstalled, editorData.webstorm);

            if (indicator && text) {
                indicator.className = `status-indicator ${webstormInstalled ? 'installed' : 'not-installed'}`;
                text.className = `status-text ${webstormInstalled ? 'installed' : 'not-installed'}`;
                text.textContent = webstormInstalled ? '已安装' : '未安装';

                // 检查文字滚动
                checkAndEnableTextScrolling(text);
            }
        } catch (error) {
            console.error('更新WebStorm状态时出错:', error);
        }
    }

    // 更新其他JetBrains IDE状态
    const jetbrainsIdes = ['phpstorm', 'rubymine', 'clion', 'goland', 'rider', 'datagrip', 'androidstudio'];
    jetbrainsIdes.forEach(ide => {
        const statusElement = document.getElementById(`${ide}Status`);
        if (statusElement) {
            try {
                const installed = editorData[ide]?.installed || false;
                const indicator = statusElement.querySelector('.status-indicator');
                const text = statusElement.querySelector('.status-text');

                console.log(`${ide} 安装状态:`, installed, editorData[ide]);

                if (indicator && text) {
                    indicator.className = `status-indicator ${installed ? 'installed' : 'not-installed'}`;
                    text.className = `status-text ${installed ? 'installed' : 'not-installed'}`;
                    text.textContent = installed ? '已安装' : '未安装';

                    // 检查文字滚动
                    checkAndEnableTextScrolling(text);
                }
            } catch (error) {
                console.error(`更新${ide}状态时出错:`, error);
            }
        }
    });
}

// 选择编辑器
function selectEditor(editorType) {
    try {
        // 添加选择动画效果
        const selectedCard = document.getElementById(`${editorType}Card`);
        if (selectedCard) {
            selectedCard.style.transform = 'scale(0.95)';
            selectedCard.style.opacity = '0.7';

            setTimeout(() => {
                selectedCard.style.transform = '';
                selectedCard.style.opacity = '';
            }, 150);
        }

        if (!bridge || !bridge.set_selected_editor) {
            throw new Error('Bridge未初始化或set_selected_editor方法不可用');
        }

        // 使用async/await而不是回调
        bridge.set_selected_editor(editorType).then((success) => {
            try {
                console.log('编辑器选择结果:', success, '类型:', typeof success);
                if (success) {
                    selectedEditor = editorType;
                    // 移除toast通知，直接显示视觉反馈

                    // 添加成功选择的视觉反馈
                    if (selectedCard) {
                        selectedCard.style.borderColor = 'rgba(34, 197, 94, 0.5)';
                        selectedCard.style.boxShadow = '0 0 30px rgba(34, 197, 94, 0.3)';
                    }

                    setTimeout(() => {
                        showMainPage();
                    }, 800);
                } else {
                    showToast('选择编辑器失败', 'error');

                    // 添加失败的视觉反馈
                    if (selectedCard) {
                        selectedCard.style.borderColor = 'rgba(239, 68, 68, 0.5)';
                        selectedCard.style.boxShadow = '0 0 30px rgba(239, 68, 68, 0.3)';

                        setTimeout(() => {
                            selectedCard.style.borderColor = '';
                            selectedCard.style.boxShadow = '';
                        }, 2000);
                    }
                }
            } catch (error) {
                showToast('选择编辑器时发生错误', 'error');
                console.error('Error in selectEditor callback:', error);
            }
        });
    } catch (error) {
        showToast('选择编辑器失败', 'error');
        console.error('Error in selectEditor:', error);
    }
}

// 显示主页面（带动画，用于编辑器选择后）
function showMainPage() {
    const editorSelectPage = document.getElementById('editorSelectPage');
    const mainPage = document.getElementById('mainPage');
    const editorSelectContainer = editorSelectPage.querySelector('.editor-select-container');

    // 添加出场动画类
    if (editorSelectContainer) {
        editorSelectContainer.classList.add('fade-out');
    }

    // 缩短等待时间，让主页更快出现
    setTimeout(() => {
        editorSelectPage.classList.add('hidden');
        mainPage.classList.remove('hidden');

        // 更新当前页面状态
        currentPage = 'main';

        // 更新显示的编辑器
        updateEditorDisplay();

        // 立即淡入主页面并触发动画
        mainPage.style.opacity = '1';
        initPageAnimations();
    }, 400); // 缩短等待时间
}

// 窗口控制
function minimizeWindow() {
    bridge.minimize_window();
}

function toggleMaximize() {
    bridge.toggle_maximize();
}

function closeWindow() {
    bridge.close_window();
}

// 获取编辑器的友好显示名称（全局函数）
function getEditorDisplayName(editorType) {
    switch (editorType) {
        case 'vscode':
            return 'VS CODE';
        case 'cursor':
            return 'CURSOR';
        case 'intellij':
            return 'INTELLIJ IDEA';
        case 'pycharm':
            return 'PYCHARM';
        case 'webstorm':
            return 'WEBSTORM';
        case 'phpstorm':
            return 'PHPSTORM';
        case 'rubymine':
            return 'RUBYMINE';
        case 'clion':
            return 'CLION';
        case 'goland':
            return 'GOLAND';
        case 'rider':
            return 'RIDER';
        case 'datagrip':
            return 'DATAGRIP';
        case 'androidstudio':
            return 'ANDROID STUDIO';
        default:
            return editorType ? editorType.toUpperCase() : '';
    }
}

// 更新编辑器显示
function updateEditorDisplay() {

    // 生成智能的副标题，确保单行显示
    function generateSmartSubtitle(editorType) {
        const displayName = getEditorDisplayName(editorType);

        // 根据编辑器名称长度选择不同的模板
        if (displayName.length <= 8) {
            // 短名称：使用完整模板 (VS CODE, CURSOR, PYCHARM, WEBSTORM, CLION, GOLAND, RIDER)
            return `一键重置 ${displayName} 里的 Augment 插件`;
        } else if (displayName.length <= 13) {
            // 中等长度：简化模板 (INTELLIJ IDEA, PHPSTORM, RUBYMINE, DATAGRIP)
            return `一键重置 ${displayName} 的 Augment`;
        } else {
            // 长名称：最简模板 (ANDROID STUDIO)
            return `重置 ${displayName} Augment`;
        }
    }

    // 调试函数：测试所有编辑器的副标题长度
    function testSubtitleLengths() {
        const editors = ['vscode', 'cursor', 'intellij', 'pycharm', 'webstorm', 'phpstorm', 'rubymine', 'clion', 'goland', 'rider', 'datagrip', 'androidstudio'];
        console.log('=== 副标题长度测试 ===');
        editors.forEach(editor => {
            const displayName = getEditorDisplayName(editor);
            const subtitle = generateSmartSubtitle(editor);
            console.log(`${editor.padEnd(12)} | ${displayName.padEnd(15)} | ${subtitle} (${subtitle.length}字符)`);
        });
    }

    // 将测试函数暴露到全局，方便调试
    window.testSubtitleLengths = testSubtitleLengths;

    // 快速测试副标题显示效果的函数
    window.testSubtitleDisplay = function(editorType) {
        if (editorType) {
            selectedEditor = editorType;
            updateEditorDisplay();
            console.log(`已切换到 ${editorType}，副标题: "${generateSmartSubtitle(editorType)}"`);
        } else {
            console.log('用法: testSubtitleDisplay("androidstudio") 或其他编辑器类型');
            console.log('可用编辑器:', ['vscode', 'cursor', 'intellij', 'pycharm', 'webstorm', 'phpstorm', 'rubymine', 'clion', 'goland', 'rider', 'datagrip', 'androidstudio']);
        }
    };

    // 测试智能错误信息的函数
    window.testSmartErrorMessages = function(editorType) {
        if (editorType) {
            selectedEditor = editorType;
        }

        const testErrors = [
            '配置目录未找到',
            'JetBrains配置目录未找到',
            '存储文件不存在',
            '权限不足',
            '文件正在使用中',
            '网络连接失败',
            '未知错误'
        ];

        console.log(`=== ${getEditorDisplayName(selectedEditor)} 智能错误信息测试 ===`);
        testErrors.forEach(error => {
            const smartMessage = getSmartErrorMessage(error);
            console.log(`原始错误: "${error}" → 智能提示: "${smartMessage}"`);
        });
    };

    // 测试编辑器代理设置支持情况
    window.testEditorProxySupport = function() {
        const editors = ['vscode', 'cursor', 'intellij', 'pycharm', 'webstorm', 'phpstorm', 'rubymine', 'clion', 'goland', 'rider', 'datagrip', 'androidstudio'];
        console.log('=== 编辑器代理设置支持情况 ===');
        editors.forEach(editor => {
            const displayName = getEditorDisplayName(editor);
            const isJetBrains = ['intellij', 'pycharm', 'webstorm', 'phpstorm', 'rubymine', 'clion', 'goland', 'rider', 'datagrip', 'androidstudio'].includes(editor);
            const method = isJetBrains ? 'JetBrains XML (proxy.settings)' : 'JSON (settings.json)';
            console.log(`${editor.padEnd(12)} | ${displayName.padEnd(15)} | ${method}`);
        });
    };

    // 更新主页面的编辑器显示
    const selectedEditorEl = document.getElementById('selectedEditor');
    if (selectedEditorEl) {
        const displayName = getEditorDisplayName(selectedEditor);
        selectedEditorEl.textContent = `当前选择的编辑器: ${displayName}`;
    }

    // 更新重置区域的副标题
    const resetDescriptionEl = document.querySelector('.reset-description');
    if (resetDescriptionEl && selectedEditor) {
        resetDescriptionEl.textContent = generateSmartSubtitle(selectedEditor);
    }

    // 保持原有的编辑器名称更新（如果还有其他地方使用）
    const currentEditorNameEl = document.getElementById('currentEditorName');
    if (currentEditorNameEl && selectedEditor) {
        const displayName = getEditorDisplayName(selectedEditor);
        currentEditorNameEl.textContent = displayName;
    }
}

// Toast提示系统 - 符合项目主题风格
let toastHistory = [];
let toastIdCounter = 0;

function showToast(message, type = 'info', duration = 4000) {
    const container = document.getElementById('toastContainer');
    if (!container) {
        return null;
    }

    const toast = document.createElement('div');
    const toastId = ++toastIdCounter;

    toast.className = `toast ${type}`;
    toast.setAttribute('data-toast-id', toastId);

    const icons = {
        'success': '✓',
        'error': '✗',
        'info': 'ℹ',
        'warning': '⚠'
    };

    const icon = icons[type] || 'ℹ';

    toast.innerHTML = `
        <span class="toast-icon">${icon}</span>
        <span class="toast-message">${message}</span>
    `;

    // 自动移除逻辑 - 支持鼠标悬停暂停
    let autoCloseTimer = null;
    let remainingTime = duration;
    let startTime = Date.now();
    let isPaused = false;

    function startAutoClose() {
        if (remainingTime > 0) {
            startTime = Date.now();
            autoCloseTimer = setTimeout(() => {
                closeToast(toastId);
            }, remainingTime);
        }
    }

    function pauseAutoClose() {
        if (autoCloseTimer && !isPaused) {
            clearTimeout(autoCloseTimer);
            remainingTime -= (Date.now() - startTime);
            isPaused = true;
        }
    }

    function resumeAutoClose() {
        if (isPaused && remainingTime > 0) {
            isPaused = false;
            startAutoClose();
        }
    }

    // 添加点击事件监听器，实现点击复制功能
    toast.addEventListener('click', async function(event) {
        // 阻止事件冒泡，避免触发其他点击事件
        event.stopPropagation();

        try {
            // 优先使用Tauri的剪贴板API
            if (window.bridge && window.bridge.copy_text_to_clipboard) {
                await window.bridge.copy_text_to_clipboard(message);
                // 显示复制成功的toast通知
                showToast('Toast内容已复制到剪贴板', 'success', 2000);
            } else {
                // 降级到传统剪贴板方案
                copyToClipboardFallback(message);
                // 显示复制成功的toast通知
                showToast('Toast内容已复制到剪贴板', 'success', 2000);
            }
        } catch (error) {
            // 显示复制失败的toast通知
            showToast('Toast内容复制失败，请重试', 'error', 3000);
        }
    });

    // 添加鼠标悬停效果和计时器控制
    toast.addEventListener('mouseenter', function() {
        toast.style.cursor = 'pointer';
        toast.style.transform = toast.style.transform.replace('scale(1)', 'scale(1.02)');
        // 暂停自动关闭计时器
        pauseAutoClose();
    });

    toast.addEventListener('mouseleave', function() {
        toast.style.cursor = 'default';
        toast.style.transform = toast.style.transform.replace('scale(1.02)', 'scale(1)');
        // 恢复自动关闭计时器
        resumeAutoClose();
    });

    // 记录到历史（用于错误追踪）
    const historyItem = {
        id: toastId,
        message: message,
        type: type,
        timestamp: new Date(),
        read: false
    };
    toastHistory.unshift(historyItem);

    // 确保toastHistory是数组
    if (!Array.isArray(toastHistory)) {
        toastHistory = [];
    }

    // 限制历史记录数量 - 添加安全检查
    if (toastHistory && toastHistory.length > 50) {
        toastHistory = toastHistory.slice(0, 50);
    }

    // 先添加到容器中，这样可以获取实际尺寸
    container.appendChild(toast);

    // 计算toast的位置（基于现有toast的数量和实际高度）
    const existingToasts = container.children.length - 1; // 减去刚添加的这个
    let totalHeight = 0;

    // 计算前面所有toast的总高度
    for (let i = 0; i < existingToasts; i++) {
        const existingToast = container.children[i];
        if (existingToast !== toast) {
            totalHeight += existingToast.offsetHeight + 12; // 12px gap
        }
    }

    // 设置初始位置
    toast.style.top = `${totalHeight}px`;

    // toast已经在上面添加到容器中了，这里不需要重复添加

    // 触发入场动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // 启动自动关闭计时器
    if (duration > 0) {
        startAutoClose();
    }

    // 如果是错误类型，记录到控制台（避免重复记录）
    if (type === 'error') {
        // 错误已通过toast显示给用户
    }


    return toastId;
}

function closeToast(toastId) {
    const toast = document.querySelector(`[data-toast-id="${toastId}"]`);
    if (toast) {
        const container = toast.parentNode;

        // 开始退场动画
        toast.classList.remove('show');
        toast.classList.add('hide');

        // 在退场动画完成后重新排列剩余的toast
        setTimeout(() => {
            if (toast.parentNode) {
                // 获取当前要移除的toast的位置信息
                const currentTop = parseInt(toast.style.top) || 0;

                // 移除当前toast
                toast.parentNode.removeChild(toast);

                // 重新计算并更新所有剩余toast的位置
                updateToastPositions(container);
            }
        }, 400); // 与CSS中的toastSlideOut动画时间一致
    }
}

// 新增函数：更新所有toast的位置
function updateToastPositions(container) {
    const toasts = Array.from(container.children);
    let currentTop = 0;

    toasts.forEach((toast) => {
        toast.style.top = `${currentTop}px`;
        // 使用实际的toast高度而不是估算值
        currentTop += toast.offsetHeight + 12; // 12px gap
    });
}

// 对话框控制
function showDialog(title, content, footer = null) {
    const overlay = document.getElementById('dialogOverlay');
    const dialog = document.getElementById('dialog');
    const dialogTitle = document.getElementById('dialogTitle');
    const dialogContent = document.getElementById('dialogContent');
    const dialogHelp = document.getElementById('dialogHelp');

    if (!overlay || !dialog || !dialogTitle || !dialogContent) {
        console.error('对话框元素不存在');
        return;
    }

    dialogTitle.textContent = title;
    dialogContent.innerHTML = content;

    // 只在设置对话框中显示帮助按钮
    if (dialogHelp) {
        if (title === '设置') {
            dialogHelp.style.display = 'flex';
        } else {
            dialogHelp.style.display = 'none';
        }
    }

    // 处理底部按钮区域
    let existingFooter = dialog.querySelector('.dialog-footer');
    if (existingFooter) {
        existingFooter.remove();
    }

    if (footer) {
        dialog.insertAdjacentHTML('beforeend', footer);
    }

    // 禁用背景页面滚动
    document.body.style.overflow = 'hidden';

    // 清除可能存在的hiding类
    overlay.classList.remove('hiding');

    // 显示对话框
    overlay.style.display = 'flex';

    // 强制重绘后添加show类，确保动画正常播放
    requestAnimationFrame(() => {
        overlay.classList.add('show');
    });
}

function closeDialog(event) {
    // 如果没有event参数，或者点击的是overlay背景，则关闭对话框
    if (!event || event.target.id === 'dialogOverlay' || event.target.classList.contains('dialog-close')) {
        const overlay = document.getElementById('dialogOverlay');
        if (!overlay) return;

        // 恢复背景页面滚动
        document.body.style.overflow = '';

        // 添加hiding类开始关闭动画
        overlay.classList.add('hiding');
        overlay.classList.remove('show');

        // 等待动画完成后隐藏对话框
        setTimeout(() => {
            overlay.style.display = 'none';
            overlay.classList.remove('hiding');
        }, 300); // 与CSS动画时间保持一致
    }
}

// ===== 版本检查已在独立的version_check.html中完成，这里不再需要相关函数 =====
































// 打开配置说明
async function openConfigHelp() {
    try {
        // 从远程配置获取配置说明链接
        if (typeof bridge !== 'undefined' && bridge.get_config_help_url) {
            let helpUrl;
            try {
                const result = bridge.get_config_help_url();

                // 检查是否是Promise
                if (result && typeof result.then === 'function') {
                    helpUrl = await result;
                } else if (typeof result === 'string') {
                    helpUrl = result;
                } else {
                    helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0';
                }

                // 确保最终结果是字符串
                if (typeof helpUrl !== 'string') {
                    helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0';
                }
            } catch (bridgeError) {
                console.error('调用get_config_help_url失败:', bridgeError);
                helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0';
            }

            // 使用bridge的open_external_url方法在系统默认浏览器中打开链接
            try {
                await bridge.open_external_url(helpUrl);
            } catch (openError) {
                console.error('使用open_external_url打开链接失败:', openError);
                // 备用方案：使用window.open
                window.open(helpUrl, '_blank');
            }
        } else {
            // 备用方案：使用默认链接
            const defaultHelpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=%E6%AC%A2%E8%BF%8E%E4%BD%BF%E7%94%A8%E2%80%85YAugment&p=riGhtR290lGebARYVfDFza';
            try {
                await bridge.open_external_url(defaultHelpUrl);
            } catch (openError) {
                console.error('使用open_external_url打开默认链接失败:', openError);
                window.open(defaultHelpUrl, '_blank');
            }
        }
    } catch (error) {
        console.error('打开配置说明失败:', error);
        // 最终备用方案：直接使用默认链接
        const defaultHelpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?isNewEmptyDoc=1&electronTabTitle=%E7%A9%BA%E7%99%BD%E6%99%BA%E8%83%BD%E6%96%87%E6%A1%A3&no_promotion=1&nlc=1&p=riGhtR290lGebARYVfDFza&client_hint=0';
        try {
            await bridge.open_external_url(defaultHelpUrl);
        } catch (openError) {
            console.error('最终备用方案打开链接失败:', openError);
            window.open(defaultHelpUrl, '_blank');
        }
    }
}

// 打开Token获取帮助
async function openTokenHelp() {
    try {
        // 从远程配置获取Token帮助链接
        if (typeof bridge !== 'undefined' && bridge.get_token_help_url) {
            let helpUrl;
            try {
                const result = bridge.get_token_help_url();

                // 检查是否是Promise
                if (result && typeof result.then === 'function') {
                    helpUrl = await result;
                } else if (typeof result === 'string') {
                    helpUrl = result;
                } else {
                    helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf';
                }

                // 确保最终结果是字符串
                if (typeof helpUrl !== 'string') {
                    helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf';
                }
            } catch (bridgeError) {
                console.error('调用get_token_help_url失败:', bridgeError);
                helpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf';
            }

            // 使用bridge的open_external_url方法在系统默认浏览器中打开链接
            try {
                await bridge.open_external_url(helpUrl);
            } catch (openError) {
                console.error('使用open_external_url打开链接失败:', openError);
                // 备用方案：使用window.open
                window.open(helpUrl, '_blank');
            }
        } else {
            // 备用方案：使用默认链接
            const defaultHelpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf';
            try {
                await bridge.open_external_url(defaultHelpUrl);
            } catch (openError) {
                console.error('使用open_external_url打开默认链接失败:', openError);
                window.open(defaultHelpUrl, '_blank');
            }
        }
    } catch (error) {
        console.error('打开Token帮助失败:', error);
        // 最终备用方案：直接使用默认链接
        const defaultHelpUrl = 'https://docs.qq.com/aio/DV0FPZERhVnVNTkxk?electronTabTitle=&p=FqDPdgXI3vlYeri2SjhSvf';
        try {
            await bridge.open_external_url(defaultHelpUrl);
        } catch (openError) {
            console.error('最终备用方案打开链接失败:', openError);
            window.open(defaultHelpUrl, '_blank');
        }
    }
}

// 重置Augment - 改进的执行方式
function executeReset() {
    try {
        console.log('开始执行重置操作');

        if (!bridge || !bridge.reset_augment) {
            throw new Error('Bridge未初始化或reset_augment方法不可用');
        }

        if (!selectedEditor) {
            throw new Error('未选择编辑器');
            return;
        }

        // 更新UI状态
        updateResetUI('running', '正在重置...');

        // 调用重置命令，使用Promise处理
        const resetPromise = new Promise((resolve, reject) => {
            bridge.reset_augment((result) => {
                if (result && result.error) {
                    reject(new Error(result.error));
                } else {
                    resolve(result);
                }
            }, (error) => {
                reject(new Error(error));
            });
        });

        // 处理重置命令的直接响应
        resetPromise.then(() => {
            console.log('重置命令已发送，开始状态轮询');
            // 开始轮询状态
            startResetStatusPolling();
        }).catch((error) => {
            console.error('重置命令执行失败:', error);

            // 根据错误类型提供更准确的提示
            let errorMessage = '重置失败';
            if (error.message.includes('正在重置中')) {
                errorMessage = '重置正在进行中，请稍候...';
                // 如果是正在重置中，直接开始轮询状态
                startResetStatusPolling();
                return;
            } else if (error.message.includes('未选择编辑器')) {
                errorMessage = '请先选择编辑器';
            } else if (error.message.includes('Bridge')) {
                errorMessage = '系统初始化失败';
            } else {
                // 使用智能错误信息处理
                errorMessage = getSmartErrorMessage(error.message);
            }

            showToast(errorMessage, 'error');
            updateResetUI('error', errorMessage);
        });

    } catch (error) {
        console.error('executeReset异常:', error);
        let errorMessage = '重置失败';
        if (error.message.includes('未选择编辑器')) {
            errorMessage = '请先选择编辑器';
        } else if (error.message.includes('Bridge')) {
            errorMessage = '系统初始化失败';
        } else {
            // 使用智能错误信息处理
            errorMessage = getSmartErrorMessage(error.message);
        }
        showToast(errorMessage, 'error');
        updateResetUI('error', errorMessage);
    }
}

function updateResetUI(state, message) {
    const resetBtn = document.getElementById('resetActionBtn');
    const progressCircle = document.querySelector('.progress-circle');
    const resetIcon = document.querySelector('.reset-icon svg');
    const resetIconContainer = document.querySelector('.reset-icon');
    const resetContainer = document.querySelector('.reset-container');

    // 安全检查 - 如果元素不存在则返回
    if (!resetBtn) {
        console.warn('updateResetUI: resetBtn元素不存在');
        return;
    }

    const btnText = resetBtn.querySelector('.btn-text');
    const btnLoading = resetBtn.querySelector('.btn-loading');

    switch(state) {
        case 'ready':
            resetBtn.disabled = false;
            resetBtn.classList.remove('resetting');
            if (btnText) {
                btnText.style.display = 'block';
                updateTextWithAnimation(btnText, '开始重置');
            }
            if (btnLoading) btnLoading.classList.add('hidden');
            if (progressCircle) {
                progressCircle.style.strokeDashoffset = '314';
            }
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'paused';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.remove('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.remove('resetting');
            }
            break;

        case 'running':
            resetBtn.disabled = true;
            resetBtn.classList.add('resetting');
            if (btnText) btnText.style.display = 'none';
            if (btnLoading) btnLoading.classList.remove('hidden');
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'running';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.add('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.add('resetting');
            }
            break;

        case 'success':
            resetBtn.disabled = false;
            resetBtn.classList.remove('resetting');
            if (btnText) {
                btnText.style.display = 'block';
                updateTextWithAnimation(btnText, '重置完成');
            }
            if (btnLoading) btnLoading.classList.add('hidden');
            if (progressCircle) {
                progressCircle.style.strokeDashoffset = '0';
            }
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'paused';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.remove('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.remove('resetting');
            }
            break;

        case 'error':
            resetBtn.disabled = false;
            resetBtn.classList.remove('resetting');
            if (btnText) {
                btnText.style.display = 'block';
                updateTextWithAnimation(btnText, '重试');
            }
            if (btnLoading) btnLoading.classList.add('hidden');
            if (progressCircle) {
                progressCircle.style.strokeDashoffset = '314';
            }
            if (resetIcon) {
                resetIcon.style.animationPlayState = 'paused';
            }
            if (resetIconContainer) {
                resetIconContainer.classList.remove('resetting');
            }
            if (resetContainer) {
                resetContainer.classList.remove('resetting');
            }
            break;
    }
}

function startResetStatusPolling() {
    console.log('开始重置状态轮询');

    // 清除之前的轮询
    if (resetStatusCheckInterval) {
        clearInterval(resetStatusCheckInterval);
    }

    let pollCount = 0;
    let lastProgressTime = Date.now(); // 记录最后一次进度更新时间
    let lastProgressMessage = ''; // 记录最后一次进度消息
    const maxIdleTime = 120000; // 最大空闲时间：2分钟无进度更新才认为超时
    const maxTotalTime = 600000; // 最大总时间：10分钟绝对超时

    resetStatusCheckInterval = setInterval(() => {
        pollCount++;
        const currentTime = Date.now();
        const elapsedTime = currentTime - lastProgressTime;

        // 检查是否真正超时（基于进度更新，而不是固定时间）
        if (elapsedTime > maxIdleTime) {
            console.warn(`重置操作空闲超时: ${elapsedTime}ms 无进度更新`);
            clearInterval(resetStatusCheckInterval);
            resetStatusCheckInterval = null;

            // 尝试强制清理重置状态
            if (bridge && bridge.force_clear_reset_status) {
                bridge.force_clear_reset_status(() => {
                    console.log('重置状态已强制清理');
                });
            }

            handleResetComplete(false, "重置操作长时间无响应，状态已清理。如有大量文件，请重试");
            return;
        }

        // 绝对超时保护（10分钟）
        if (pollCount * 500 > maxTotalTime) {
            console.warn('重置操作绝对超时: 超过10分钟');
            clearInterval(resetStatusCheckInterval);
            resetStatusCheckInterval = null;

            // 尝试强制清理重置状态
            if (bridge && bridge.force_clear_reset_status) {
                bridge.force_clear_reset_status(() => {
                    console.log('重置状态已强制清理');
                });
            }

            handleResetComplete(false, "重置操作超时，状态已清理。请检查是否有大量文件需要处理");
            return;
        }

        if (bridge && bridge.get_reset_status) {
            try {
                // 使用回调方式处理异步调用
                bridge.get_reset_status((statusJson) => {
                    // 解析JSON字符串
                    let status;
                    try {
                        status = JSON.parse(statusJson);

                        // 处理状态
                        if (status && typeof status === 'object') {
                            if (status.completed) {
                                // 重置完成，停止轮询
                                console.log('重置操作完成:', status);
                                clearInterval(resetStatusCheckInterval);
                                resetStatusCheckInterval = null;
                                handleResetComplete(status.success, status.message);
                                return;
                            } else if (status.is_running) {
                                // 检查进度是否有更新
                                if (status.message && status.message !== lastProgressMessage) {
                                    lastProgressTime = currentTime; // 更新最后进度时间
                                    lastProgressMessage = status.message;
                                    console.log('重置进度更新:', status.message);
                                }

                                // 更新进度显示
                                const progressText = document.getElementById('resetProgressText');
                                if (progressText) {
                                    updateTextWithAnimation(progressText, status.message || '正在重置...');
                                }
                            } else {
                                console.log('重置状态未知:', status);
                            }
                        } else {
                            console.warn('无效的重置状态响应:', statusJson);
                        }
                    } catch (parseError) {
                        console.error('解析重置状态JSON失败:', parseError);
                    }
                });
            } catch (error) {
                console.error('获取重置状态失败:', error);
            }
        } else {
            console.warn('Bridge或get_reset_status方法不可用');
        }
    }, 500); // 每500ms检查一次
}

function updateProgress(taskId, progress, message) {
    if (taskId === 'reset') {
        const progressBar = document.getElementById('resetProgressBar');
        const progressText = document.getElementById('resetProgressText');
        const statusText = document.getElementById('resetStatusText');

        if (progressBar && progressText) {
            progressBar.style.width = `${progress * 100}%`;
            updateTextWithAnimation(progressText, message);
        }

        if (statusText) {
            updateTextWithAnimation(statusText, message);
        }
    } else if (taskId === 'verification') {
        updateVerificationStatus('running', message);
    }
}

let resetCompleteHandled = false;

// 智能处理重置错误信息，根据当前编辑器提供更准确的提示
function getSmartErrorMessage(originalMessage) {
    if (!originalMessage) return '重置失败';

    const message = originalMessage.toLowerCase();
    const editorDisplayName = getEditorDisplayName(selectedEditor);

    // 配置目录相关错误
    if (message.includes('配置目录未找到') || message.includes('目录未找到') || message.includes('存储文件不存在')) {
        if (selectedEditor === 'vscode') {
            return `VS Code 配置目录未找到，请确保已安装 VS Code`;
        } else if (selectedEditor === 'cursor') {
            return `Cursor 配置目录未找到，请确保已安装 Cursor`;
        } else if (['intellij', 'pycharm', 'webstorm', 'phpstorm', 'rubymine', 'clion', 'goland', 'rider', 'datagrip', 'androidstudio'].includes(selectedEditor)) {
            return `${editorDisplayName} 配置目录未找到，请确保已安装 ${editorDisplayName}`;
        } else {
            return `${editorDisplayName} 配置目录未找到`;
        }
    }

    // JetBrains 相关错误
    if (message.includes('jetbrains')) {
        if (selectedEditor === 'androidstudio') {
            return `Android Studio 配置目录未找到，请确保已安装 Android Studio`;
        } else if (['intellij', 'pycharm', 'webstorm', 'phpstorm', 'rubymine', 'clion', 'goland', 'rider', 'datagrip'].includes(selectedEditor)) {
            return `${editorDisplayName} 配置目录未找到，请确保已安装 ${editorDisplayName}`;
        } else {
            return `JetBrains IDE 配置目录未找到`;
        }
    }

    // 权限相关错误
    if (message.includes('权限') || message.includes('permission')) {
        return `重置 ${editorDisplayName} 时权限不足，请以管理员身份运行`;
    }

    // 文件占用相关错误
    if (message.includes('占用') || message.includes('使用中') || message.includes('in use')) {
        return `${editorDisplayName} 正在运行，请先关闭 ${editorDisplayName} 后重试`;
    }

    // 网络相关错误
    if (message.includes('网络') || message.includes('network') || message.includes('连接')) {
        return `重置 ${editorDisplayName} 时网络错误，请检查网络连接`;
    }

    // 默认情况：保持原始信息但添加编辑器名称
    if (originalMessage.includes('重置失败')) {
        return originalMessage;
    } else {
        return `重置 ${editorDisplayName} 失败: ${originalMessage}`;
    }
}

function handleResetComplete(success, message) {

    // 防止重复处理
    if (resetCompleteHandled) {

        return;
    }
    resetCompleteHandled = true;

    // 停止轮询
    if (resetStatusCheckInterval) {
        clearInterval(resetStatusCheckInterval);
        resetStatusCheckInterval = null;
    }

    if (success) {
        const editorDisplayName = getEditorDisplayName(selectedEditor);
        updateResetUI('success', '重置成功');
        showToast(`${editorDisplayName} 重置成功！`, 'success');
        setTimeout(() => {
            updateResetUI('ready', '');
            resetCompleteHandled = false; // 重置标志
        }, 3000);
    } else {
        const smartErrorMessage = getSmartErrorMessage(message);
        updateResetUI('error', `重置失败: ${smartErrorMessage}`);
        showToast(smartErrorMessage, 'error');
        setTimeout(() => {
            updateResetUI('ready', '');
            resetCompleteHandled = false; // 重置标志
        }, 3000);
    }
}

// 验证邮箱配置是否完整
// 异步验证域名配置（用于生成邮箱）
function validateDomainConfigAsync(callback) {
    try {
        if (!bridge || !bridge.get_config) {
            callback({ valid: false, message: 'Bridge未初始化' });
            return;
        }

        // 异步获取当前配置
        bridge.get_config((config) => {
            try {

                if (!config || !config.email) {

                    callback({ valid: false, message: '请先在设置中配置域名' });
                    return;
                }

                const emailConfig = config.email;

                // 验证域名
                if (!emailConfig.domain || emailConfig.domain.trim() === '' || emailConfig.domain === 'xx.com') {

                    callback({ valid: false, message: '请先在设置中配置域名' });
                    return;
                }

                callback({ valid: true, message: '域名配置验证通过' });
            } catch (error) {

                callback({ valid: false, message: `域名配置验证失败: ${error.message}` });
            }
        });
    } catch (error) {

        callback({ valid: false, message: `域名配置验证失败: ${error.message}` });
    }
}

// 异步验证邮箱配置（用于获取验证码）
function validateEmailConfigAsync(callback) {
    try {
        if (!bridge || !bridge.get_config) {
            callback({ valid: false, message: 'Bridge未初始化' });
            return;
        }

        // 异步获取当前配置
        bridge.get_config((config) => {
            try {

                if (!config || !config.email) {
                    callback({ valid: false, message: '请先在设置中配置邮箱' });
                    return;
                }

                const emailConfig = config.email;

                // 获取验证码不需要验证域名，只验证对应的邮箱获取方式配置
                // 根据邮箱获取方式验证不同的字段
                if (emailConfig.use_temp_mail) {
                    // 临时邮箱模式验证
                    if (!emailConfig.temp_mail?.email || emailConfig.temp_mail.email.trim() === '') {

                        callback({ valid: false, message: '请先在设置中配置临时邮箱地址' });
                        return;
                    }
                    if (!emailConfig.temp_mail?.pin || emailConfig.temp_mail.pin.trim() === '') {

                        callback({ valid: false, message: '请先在设置中配置临时邮箱PIN码' });
                        return;
                    }

                } else {
                    // IMAP模式验证
                    const imap = emailConfig.imap || {};
                    if (!imap.server || imap.server.trim() === '') {
                        callback({ valid: false, message: '请先在设置中配置IMAP服务器' });
                        return;
                    }
                    if (!imap.user || imap.user.trim() === '') {
                        callback({ valid: false, message: '请先在设置中配置IMAP邮箱地址' });
                        return;
                    }
                    if (!imap.password || imap.password.trim() === '') {
                        callback({ valid: false, message: '请先在设置中配置IMAP密码/授权码' });
                        return;
                    }

                }

                callback({ valid: true, message: '邮箱配置验证通过' });
            } catch (error) {

                callback({ valid: false, message: `邮箱配置验证失败: ${error.message}` });
            }
        });
    } catch (error) {

        callback({ valid: false, message: `邮箱配置验证失败: ${error.message}` });
    }
}

// 同步验证域名配置（保留用于其他地方）
// 注意：Rust版本不支持同步获取配置，此函数已弃用，请使用 validateDomainConfigAsync
function validateDomainConfig() {
    console.warn('validateDomainConfig 已弃用，请使用 validateDomainConfigAsync');
    return { valid: false, message: '请使用异步配置验证方法' };
}

// 验证完整邮箱配置（用于获取验证码）
// 注意：Rust版本不支持同步获取配置，此函数已弃用，请使用 validateEmailConfigAsync
function validateEmailConfig() {
    console.warn('validateEmailConfig 已弃用，请使用 validateEmailConfigAsync');
    return { valid: false, message: '请使用异步配置验证方法' };
}

// 邮箱工作流 - 现代化邮箱生成
function generateEmailDirect() {
    try {
        if (!bridge || !bridge.generate_email) {
            throw new Error('Bridge未初始化或generate_email方法不可用');
        }

        // 验证域名配置（异步）
        validateDomainConfigAsync((validation) => {
            if (!validation.valid) {
                showToast(validation.message, 'error');
                return;
            }

            // 验证通过，继续生成邮箱
            continueGenerateEmail();
        });
    } catch (error) {
        showToast('生成邮箱失败', 'error');
        console.error('Error in generateEmailDirect:', error);
    }
}

// 继续获取验证码的逻辑
function continueGetVerificationCode() {
    try {

        // 更新工作流状态
        updateWorkflowButtonState('getCodeBtn', 'loading', '获取中...');
        updateWorkspaceStatus('active', '正在获取验证码');
        updateWorkflowProgress(2, false); // 激活第二步

        // 禁用生成邮箱按钮，防止在获取验证码过程中误操作
        const generateEmailBtn = document.getElementById('generateEmailBtn');
        if (generateEmailBtn) {
            generateEmailBtn.disabled = true;
        }

        // 测试信号连接状态

        if (bridge.verification_complete) {

        } else {

        }

        // 重置验证码显示区域
        resetVerificationDisplay();

        // 显示进度条容器
        const progressContainer = document.getElementById('verificationProgressContainer');
        if (progressContainer) {
            progressContainer.style.display = 'block';

        }

        // 初始状态

        updateVerificationStatus('running', '正在获取验证码...');
        updateVerificationDetailedStatus('starting', '正在启动验证码获取...', '');

        bridge.get_verification_code();

        // 启动轮询机制作为信号的备用方案

        startVerificationStatusPolling();

    } catch (error) {
        updateVerificationStatus('error', '获取失败');
        updateVerificationDetailedStatus('error', '获取失败', error.message);

        // 发生异常时也要重新启用生成邮箱按钮
        const generateEmailBtn = document.getElementById('generateEmailBtn');
        if (generateEmailBtn) {
            generateEmailBtn.disabled = false;
            console.log('验证码获取异常：重新启用生成邮箱按钮');
        }
    }
}

// 继续生成邮箱的逻辑
function continueGenerateEmail() {
    try {
        // 检查是否已经在生成邮箱
        if (isGeneratingEmail) {
            return;
        }

        // 设置生成状态
        isGeneratingEmail = true;

        // 直接调用生成邮箱，不显示loading状态
        bridge.generate_email((email) => {
            try {
                console.log('邮箱生成回调收到结果:', email);
                if (email && email.trim() !== '') {
                    // 使用新的工作流显示方式
                    const displayZone = document.getElementById('emailDisplayZone');
                    if (displayZone) {
                        showEmailResultInWorkflow(email);
                        updateWorkflowProgress(1, true); // 标记第一步完成
                        enableWorkflowButton('getCodeBtn'); // 启用获取验证码按钮
                    } else {
                        // 兼容旧版本
                        const displayArea = document.getElementById('emailDisplayArea');
                        if (displayArea) {
                            showEmailResultInCard(email);
                        } else {
                            showEmailResultDirect(email);
                        }
                    }
                    updateWorkspaceStatus('success', '邮箱生成成功');
                    console.log('邮箱生成成功，状态已更新');
                } else {
                    console.log('邮箱生成失败，email为:', email);
                    updateWorkspaceStatus('error', '邮箱生成失败');
                    showToast('邮箱生成失败', 'error');
                }
            } catch (error) {
                console.error('处理邮箱生成结果时出错:', error);
                updateWorkspaceStatus('error', '处理邮箱结果时出错');
                showToast('处理邮箱结果时出错', 'error');
            } finally {
                // 无论成功失败都重置生成状态
                isGeneratingEmail = false;
            }
        });
    } catch (error) {
        console.error('生成邮箱时发生异常:', error);
        updateWorkspaceStatus('error', '生成邮箱失败');
        showToast('生成邮箱失败', 'error');
        isGeneratingEmail = false; // 重置生成状态
    }
}

function showEmailResultDirect(email) {
    const emailResult = document.getElementById('emailResult');
    const emailInput = document.getElementById('generatedEmail');

    if (emailResult && emailInput) {
        emailInput.value = email;
        emailResult.classList.remove('hidden');

        // 添加一个淡入动画
        emailResult.style.opacity = '0';
        emailResult.style.transform = 'translateY(10px)';

        setTimeout(() => {
            emailResult.style.transition = 'all 0.3s ease';
            emailResult.style.opacity = '1';
            emailResult.style.transform = 'translateY(0)';
        }, 50);
    } else {
        // 如果旧的UI元素不存在，尝试使用新的UI
        const displayArea = document.getElementById('emailDisplayArea');
        if (displayArea) {
            showEmailResultInCard(email);
        } else {
            console.warn('showEmailResultDirect: 邮箱结果显示元素不存在');
            // 移除toast通知，由信号处理
        }
    }
}

function copyEmailDirect() {
    const emailInput = document.getElementById('generatedEmail');
    if (emailInput && emailInput.value) {
        try {
            // 直接使用可靠的降级方案，避免权限问题
            copyToClipboardFallback(emailInput.value);
            showToast('邮箱地址已复制', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            showToast('复制失败，请手动选择', 'error');
        }
    } else {
        showToast('没有邮箱地址可复制', 'error');
    }
}

function showEmailResult(email) {
    // 重定向到新的直接显示方式
    showEmailResultDirect(email);
}

// 新的工作流邮箱显示函数
function showEmailResultInWorkflow(email) {
    const displayZone = document.getElementById('emailDisplayZone');
    if (!displayZone) {
        console.warn('showEmailResultInWorkflow: 邮箱显示区域不存在');
        return;
    }

    // 创建现代化邮箱结果显示
    displayZone.innerHTML = `
        <div class="email-result-modern">
            <input type="text" class="email-text-modern" value="${email}" readonly>
            <button class="copy-email-btn-modern" onclick="copyEmailFromWorkflow()">
                <svg width="16" height="16" viewBox="0 0 24 24">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z" fill="currentColor"/>
                </svg>
                复制
            </button>
        </div>
    `;

    // 添加淡入动画
    const emailResult = displayZone.querySelector('.email-result-modern');
    if (emailResult) {
        emailResult.style.opacity = '0';
        emailResult.style.transform = 'translateY(20px)';

        setTimeout(() => {
            emailResult.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
            emailResult.style.opacity = '1';
            emailResult.style.transform = 'translateY(0)';
        }, 100);
    }

    // 更新信息卡片
    updateEmailInfoCards(email);
}

// 兼容旧版本的邮箱卡片显示函数
function showEmailResultInCard(email) {
    const displayArea = document.getElementById('emailDisplayArea');
    if (!displayArea) {
        console.warn('showEmailResultInCard: 邮箱显示区域不存在');
        return;
    }

    // 创建邮箱结果显示
    displayArea.innerHTML = `
        <div class="email-result">
            <input type="text" class="email-text" value="${email}" readonly>
            <button class="copy-email-btn" onclick="copyEmailFromCard()">
                <svg width="14" height="14" viewBox="0 0 24 24">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z" fill="currentColor"/>
                </svg>
                复制
            </button>
        </div>
    `;

    // 显示浮动卡片
    const floatingCards = document.querySelectorAll('.floating-card');
    floatingCards.forEach(card => {
        card.style.opacity = '1';
        card.style.transform = card.classList.contains('card-1') ? 'rotate(8deg)' : 'rotate(-5deg)';
    });
}

// 辅助函数：不选中文本的复制
function copyToClipboardFallback(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (!successful) {
            throw new Error('execCommand copy failed');
        }
    } finally {
        document.body.removeChild(textArea);
    }
}

// 工作流复制邮箱函数
function copyEmailFromWorkflow() {

    const emailText = document.querySelector('.email-text-modern');

    if (emailText && emailText.value) {
        try {

            // 直接使用可靠的降级方案，避免权限问题
            copyToClipboardFallback(emailText.value);

            showToast('邮箱地址已复制', 'success');
        } catch (error) {

            showToast('复制失败，请手动选择', 'error');
        }
    } else {

        showToast('没有邮箱地址可复制', 'error');
    }
}

function copyEmailFromCard() {
    const emailText = document.querySelector('.email-text');
    if (emailText && emailText.value) {
        try {
            // 直接使用可靠的降级方案，避免权限问题
            copyToClipboardFallback(emailText.value);
            showToast('邮箱地址已复制', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            showToast('复制失败，请手动复制', 'error');
        }
    } else {
        console.warn('copyEmailFromCard: 邮箱输入元素不存在或没有值');
        showToast('没有邮箱地址可复制', 'error');
    }
}

function copyEmail() {
    const emailInput = document.getElementById('generatedEmail');
    if (emailInput && emailInput.value) {
        try {
            // 直接使用可靠的降级方案，避免权限问题
            copyToClipboardFallback(emailInput.value);
            showToast('邮箱地址已复制', 'success');
        } catch (error) {
            console.error('复制失败:', error);
            showToast('复制失败，请手动选择', 'error');
        }
    } else {
        console.warn('copyEmail: 邮箱输入元素不存在或没有值');
        showToast('没有邮箱地址可复制', 'error');
    }
}

function getVerificationCodeDirect() {
    try {

        if (!bridge || !bridge.get_verification_code) {
            throw new Error('Bridge未初始化或get_verification_code方法不可用');
        }

        // 验证邮箱配置（异步）
        validateEmailConfigAsync((validation) => {
            if (!validation.valid) {
                showToast(validation.message, 'error');
                return;
            }

            // 验证通过，继续获取验证码
            continueGetVerificationCode();
        });

    } catch (error) {

        // 移除toast通知，由信号处理
        updateVerificationStatus('error', '获取失败');
        updateVerificationDetailedStatus('error', '获取失败', error.message);
    }
}

function updateVerificationStatus(state, message) {

    // 首先尝试更新工作区状态显示
    const workspaceStatus = document.getElementById('workspaceStatus');
    if (workspaceStatus) {
        const statusDot = workspaceStatus.querySelector('.status-dot');
        const statusText = workspaceStatus.querySelector('.status-text');

        if (statusDot && statusText) {
            // 清除之前的状态类
            statusDot.className = 'status-dot';

            // 根据状态设置样式和使用动画更新文本
            switch (state) {
                case 'running':
                    statusDot.classList.add('running');
                    updateTextWithAnimation(statusText, message || '获取中...');
                    break;
                case 'success':
                    statusDot.classList.add('success');
                    updateTextWithAnimation(statusText, message || '获取成功');
                    break;
                case 'error':
                    statusDot.classList.add('error');
                    updateTextWithAnimation(statusText, message || '获取失败');
                    break;
                default:
                    statusDot.classList.add('idle');
                    updateTextWithAnimation(statusText, message || '就绪');
            }

        }
    }

    // 不在这里更新按钮状态，由工作流函数统一管理
    // updateVerificationButtonState(state);
}

// 更新验证码按钮状态
function updateVerificationButtonState(state) {
    // 尝试找到获取验证码按钮
    const verificationBtn = document.getElementById('getCodeBtn') || document.querySelector('.verification-btn');
    if (!verificationBtn) {

        return;
    }

    switch(state) {
        case 'running':
            verificationBtn.disabled = true;
            verificationBtn.classList.add('loading');
            // 保存原始内容
            if (!verificationBtn.dataset.originalContent) {
                verificationBtn.dataset.originalContent = verificationBtn.innerHTML;
            }
            // 更新按钮文本为加载状态 - 使用动画
            let btnTextRunning = verificationBtn.querySelector('.btn-text');
            if (btnTextRunning) {
                updateTextWithAnimation(btnTextRunning, '获取中...');
            }

            break;
        case 'success':
        case 'error':
        default:
            verificationBtn.disabled = false;
            verificationBtn.classList.remove('loading');
            // 恢复按钮文本 - 使用动画
            let btnTextDefault = verificationBtn.querySelector('.btn-text');
            if (btnTextDefault) {
                updateTextWithAnimation(btnTextDefault, '获取验证码');
            }
            // 清除保存的内容
            if (verificationBtn.dataset.originalContent) {
                delete verificationBtn.dataset.originalContent;
            }

            break;
    }
}

// 存储按钮恢复定时器
const buttonRestoreTimers = {};

// 邮箱生成状态标志
let isGeneratingEmail = false;

// 工作流状态管理函数
function updateWorkflowButtonState(buttonId, state, text) {
    const button = document.getElementById(buttonId);
    if (!button) {

        return;
    }

    const btnContent = button.querySelector('.btn-content');
    const btnLoading = button.querySelector('.btn-loading');
    const btnText = button.querySelector('.btn-text');

    // 清除之前的恢复定时器
    if (buttonRestoreTimers[buttonId]) {
        clearTimeout(buttonRestoreTimers[buttonId]);
        delete buttonRestoreTimers[buttonId];
    }

    // 重置所有状态
    button.classList.remove('loading', 'success', 'error');

    switch (state) {
        case 'loading':
            // 生成邮箱按钮不显示加载状态，其他按钮正常显示
            if (buttonId === 'generateEmailBtn') {
                // 生成邮箱按钮保持正常状态，不显示加载动画
                button.disabled = true;
                if (btnText) {
                    updateTextWithAnimation(btnText, '生成邮箱');
                }
            } else {
                // 其他按钮正常显示加载状态
                button.classList.add('loading');
                button.disabled = true;
                // 使用CSS动画过渡，不直接设置display
                if (btnLoading) {
                    btnLoading.classList.remove('hidden');
                    btnLoading.style.display = 'flex';
                    // 确保加载文字正确显示 - 使用动画
                    const loadingText = btnLoading.querySelector('.loading-text');
                    if (loadingText) {
                        updateTextWithAnimation(loadingText, text || '获取中...');
                    }
                }
                // 同时更新主按钮文本 - 使用动画
                if (btnText) {
                    updateTextWithAnimation(btnText, text || '获取中...');
                }
            }

            break;
        case 'success':
        case 'error':
        case 'ready':
        default:
            // 恢复正常状态
            button.disabled = false;
            button.classList.remove('loading');
            // 使用CSS动画过渡，延迟隐藏加载状态
            if (btnLoading) {
                setTimeout(() => {
                    btnLoading.classList.add('hidden');
                    btnLoading.style.display = 'none';
                }, 300); // 等待动画完成
            }
            if (btnText) {
                // 生成邮箱按钮始终显示"生成邮箱"，不显示状态变化
                if (buttonId === 'generateEmailBtn') {
                    updateTextWithAnimation(btnText, '生成邮箱');
                } else {
                    updateTextWithAnimation(btnText, text || '获取验证码');
                }
            }

            break;
    }
}

// 通用的文字更新动画函数
function updateTextWithAnimation(element, newText, animationDuration = 400) {
    if (!element) return;

    const currentText = element.textContent || '';
    if (currentText === newText) return; // 文字相同，无需更新

    // 添加变化动画类
    element.classList.add('changing');

    // 在动画中间点更新文字内容
    setTimeout(() => {
        element.textContent = newText;
        element.classList.remove('changing');

        // 检查是否需要滚动
        checkAndEnableTextScrolling(element);
    }, animationDuration / 2);
}

// 检查文字是否需要滚动并启用滚动效果
function checkAndEnableTextScrolling(element) {
    if (!element) {
        console.warn('checkAndEnableTextScrolling: element is null or undefined');
        return;
    }

    // 检查元素是否有classList属性
    if (!element.classList) {
        console.warn('checkAndEnableTextScrolling: element does not have classList');
        return;
    }

    // 检查是否是状态文字元素
    if (!element.classList.contains('status-text')) return;

    // 等待DOM更新后再检查
    setTimeout(() => {
        try {
            // 再次检查元素是否仍然存在
            if (!element || !element.parentNode) {
                console.warn('checkAndEnableTextScrolling: element no longer exists in DOM');
                return;
            }

            const containerWidth = 200; // 状态文字容器的最大宽度
            const textWidth = element.scrollWidth || 0;

            // 移除之前的滚动类
            if (element.classList) {
                element.classList.remove('text-scrolling');
            }

            // 如果文字宽度超过容器宽度，启用滚动
            if (textWidth > containerWidth) {
                if (element.classList) {
                    element.classList.add('text-scrolling');
                }

                // 动态计算滚动距离并设置CSS变量
                const scrollDistance = textWidth - containerWidth + 20; // 额外20px边距
                if (element.style) {
                    element.style.setProperty('--scroll-distance', `-${scrollDistance}px`);
                }
            }
        } catch (error) {
            console.error('checkAndEnableTextScrolling error:', error);
        }
    }, 50);
}

function updateWorkspaceStatus(state, message) {
    const statusElement = document.getElementById('workspaceStatus');
    if (!statusElement) return;

    const statusDot = statusElement.querySelector('.status-dot');
    const statusText = statusElement.querySelector('.status-text');

    // 使用通用动画函数更新文字（会自动检查是否需要滚动）
    const newMessage = message || '就绪';
    updateTextWithAnimation(statusText, newMessage);

    // 同步更新状态点和容器的样式，避免宽度闪烁
    if (statusDot) {
        // 移除所有状态类并立即添加新状态
        statusDot.classList.remove('active', 'success', 'error', 'running', 'idle');
        statusDot.classList.add(state);
    }

    // 同步更新整个状态容器的样式
    statusElement.classList.remove('active', 'success', 'error', 'running', 'idle');
    statusElement.classList.add(state);
}

function updateWorkflowProgress(step, completed) {
    const progressSteps = document.querySelectorAll('.progress-step');

    progressSteps.forEach((stepElement, index) => {
        const stepNumber = index + 1;

        if (stepNumber < step || (stepNumber === step && completed)) {
            stepElement.classList.add('completed');
            stepElement.classList.remove('active');
        } else if (stepNumber === step && !completed) {
            stepElement.classList.add('active');
            stepElement.classList.remove('completed');
        } else {
            stepElement.classList.remove('active', 'completed');
        }
    });
}

function enableWorkflowButton(buttonId) {
    const button = document.getElementById(buttonId);
    if (button) {
        button.disabled = false;
        button.classList.remove('disabled');
    }
}

function updateEmailInfoCards(email) {
    // 提取域名
    const domain = email.split('@')[1] || 'unknown';

    // 更新域名信息
    const domainInfo = document.getElementById('emailDomainInfo');
    if (domainInfo) {
        updateTextWithAnimation(domainInfo, domain);
    }

    // 更新模式信息（这里可以根据实际配置来设置）
    const modeInfo = document.getElementById('emailModeInfo');
    if (modeInfo) {
        // 这里可以根据实际的邮箱配置来显示模式
        updateTextWithAnimation(modeInfo, domain.includes('tempmail') ? '临时邮箱' : 'IMAP邮箱');
    }
}

// 保留旧的邮箱功能以兼容性（如果需要）
function generateEmail() {
    generateEmailDirect();
}

function getVerificationCode() {
    getVerificationCodeDirect();
}

function testVerificationSignals() {

    if (!bridge || !bridge.test_verification_signals) {

        showToast('测试功能不可用', 'error');
        return;
    }

    bridge.test_verification_signals();

}

function copyEmail() {
    copyEmailDirect();
}

function getVerificationCodeForEmail() {
    getVerificationCodeDirect();
}

function copyVerificationCode() {
    // 优先查找新工作流中的验证码元素
    let codeElement = document.querySelector('#verificationResultZone .verification-code');

    // 如果没找到，查找旧版本的验证码元素
    if (!codeElement) {
        codeElement = document.getElementById('verificationCode');
    }

    if (codeElement && codeElement.textContent) {
        // 创建临时文本区域来复制文本
        const textArea = document.createElement('textarea');
        textArea.value = codeElement.textContent;
        document.body.appendChild(textArea);
        textArea.select();

        try {
            document.execCommand('copy');
            // 复制成功，不在这里显示toast，由调用方决定

            // 更新复制按钮状态（如果是新工作流）
            const copyBtn = document.querySelector('#verificationResultZone .result-copy-btn');
            if (copyBtn) {
                const originalHTML = copyBtn.innerHTML;
                copyBtn.innerHTML = `
                    <svg width="18" height="18" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.58L19 8l-9 9z" fill="currentColor"/>
                    </svg>
                `;
                copyBtn.style.background = 'linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(22, 163, 74, 0.1))';
                copyBtn.style.borderColor = 'rgba(34, 197, 94, 0.4)';

                setTimeout(() => {
                    copyBtn.innerHTML = originalHTML;
                    copyBtn.style.background = '';
                    copyBtn.style.borderColor = '';
                }, 2000);
            }
        } catch (err) {
            console.error('复制失败:', err);
            showToast('复制失败，请手动复制', 'error');
        }

        document.body.removeChild(textArea);
    } else {
        showToast('没有验证码可复制', 'warning');
    }
}

function showVerificationCode(code) {
    const resultContainer = document.getElementById('verificationResult');
    const codeElement = document.getElementById('verificationCode');

    if (resultContainer && codeElement) {
        codeElement.textContent = code;
        resultContainer.classList.remove('hidden');

        // 更新状态为成功
        updateVerificationStatus('success', '验证码获取成功');

        // 添加一个淡入动画
        resultContainer.style.opacity = '0';
        resultContainer.style.transform = 'translateY(10px)';

        setTimeout(() => {
            resultContainer.style.transition = 'all 0.3s ease';
            resultContainer.style.opacity = '1';
            resultContainer.style.transform = 'translateY(0)';
        }, 50);

        // 移除toast通知，由最终结果处理
    } else {
        console.warn('showVerificationCode: 验证码显示元素不存在');
        // 移除toast通知，由最终结果处理
    }
}

// 新增的详细验证码状态处理函数
function updateVerificationDetailedStatus(status, message, details) {

    // 更新主状态显示
    updateVerificationStatus(getStatusFromDetailedStatus(status), message);

    // 更新详细信息显示
    updateVerificationDetails(status, message, details);
}



function updateVerificationConnectionTest(success, message) {

    if (success) {
        updateVerificationStatus('running', '连接测试成功，开始获取验证码...');
        // 移除toast通知，减少干扰
    } else {
        updateVerificationStatus('error', '连接测试失败');
        // 移除toast通知，由最终结果处理
    }
}

function handleVerificationCompleted(success, code, message) {

    // 停止轮询
    if (verificationStatusCheckInterval) {
        clearInterval(verificationStatusCheckInterval);
        verificationStatusCheckInterval = null;
    }

    // 防止重复处理
    if (verificationCompleteHandled) {

        return;
    }
    verificationCompleteHandled = true;

    // 无论成功还是失败，都直接恢复按钮为原始状态
    setTimeout(() => {
        updateWorkflowButtonState('getCodeBtn', 'ready', '获取验证码');

        // 重新启用生成邮箱按钮
        const generateEmailBtn = document.getElementById('generateEmailBtn');
        if (generateEmailBtn) {
            generateEmailBtn.disabled = false;
            console.log('验证码获取完成：重新启用生成邮箱按钮');
        }
    }, 100);

    if (success && code) {
        // 显示验证码
        showVerificationCodeInWorkflow(code);
        updateVerificationDetailedStatus('success', message, `验证码: ${code}`);
    } else {
        // 失败时只更新状态显示，不更新按钮
        updateWorkspaceStatus('error', message || '验证码获取失败');
        updateVerificationStatus('error', message || '获取验证码失败');
        updateVerificationDetailedStatus('failed', message || '获取验证码失败', '');
    }
}

// 在工作流中显示验证码 - 直接在邮箱显示区域显示，使用和邮箱相同的样式
function showVerificationCodeInWorkflow(code) {
    const emailDisplayZone = document.getElementById('emailDisplayZone');
    if (!emailDisplayZone) {
        console.warn('showVerificationCodeInWorkflow: 邮箱显示区域不存在');
        // 兼容旧版本
        showVerificationCode(code);
        return;
    }

    // 直接在邮箱显示区域显示验证码，使用和邮箱相同的样式
    emailDisplayZone.innerHTML = `
        <div class="email-result-modern">
            <input type="text" class="email-text-modern" value="${code}" readonly>
            <button class="copy-email-btn-modern" onclick="copyVerificationCodeFromWorkflow()">
                <svg width="16" height="16" viewBox="0 0 24 24">
                    <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z" fill="currentColor"/>
                </svg>
                复制
            </button>
        </div>
    `;

    // 添加淡入动画
    emailDisplayZone.style.opacity = '0';
    emailDisplayZone.style.transform = 'translateY(10px)';

    setTimeout(() => {
        emailDisplayZone.style.transition = 'all 0.4s ease';
        emailDisplayZone.style.opacity = '1';
        emailDisplayZone.style.transform = 'translateY(0)';
    }, 50);

    // 自动复制验证码到剪贴板
    copyVerificationCodeToClipboard(code);
}

// 从消息中提取验证码的函数
function extractCodeFromMessage(message) {
    if (!message) return '';

    // 尝试从消息中提取6位数字验证码
    const codeMatch = message.match(/\b\d{6}\b/);
    if (codeMatch) {
        return codeMatch[0];
    }

    // 如果没有找到6位数字，尝试查找其他格式的验证码
    const codeMatch2 = message.match(/验证码[：:]\s*(\d+)/);
    if (codeMatch2) {
        return codeMatch2[1];
    }

    // 如果消息本身就是验证码格式
    if (/^\d{4,8}$/.test(message.trim())) {
        return message.trim();
    }

    return '';
}

// 从工作流中复制验证码
function copyVerificationCodeFromWorkflow() {
    const emailInput = document.querySelector('#emailDisplayZone .email-text-modern');
    if (emailInput) {
        const code = emailInput.value;
        copyVerificationCodeToClipboard(code);
        // 显示复制成功提示
        showToast('验证码已复制到剪贴板', 'success', 2000);
    } else {

        showToast('复制失败：找不到验证码', 'error');
    }
}

// 自动复制验证码到剪贴板的函数
function copyVerificationCodeToClipboard(code) {
    try {
        const textArea = document.createElement('textarea');
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // 移除toast通知，由最终结果处理
    } catch (err) {

    }
}

function resetVerificationDisplay() {
    // 重置验证码结果显示
    const resultContainer = document.getElementById('verificationResult');
    if (resultContainer) {
        resultContainer.classList.add('hidden');
    }

    // 重置进度条
    const progressBar = document.getElementById('verificationProgressBar');
    if (progressBar) {
        progressBar.style.width = '0%';
    }

    // 隐藏进度条容器
    const progressContainer = document.getElementById('verificationProgressContainer');
    if (progressContainer) {
        progressContainer.style.display = 'none';
    }

    // 清除详细信息
    updateVerificationDetails('', '', '');
}

function updateVerificationDetails(status, message, details) {
    // 更新详细信息显示区域
    const detailsElement = document.getElementById('verificationDetails');
    if (detailsElement) {
        let statusIcon = getStatusIcon(status);
        let detailsHtml = `
            <div class="verification-detail-item">
                <span class="status-icon">${statusIcon}</span>
                <span class="status-message">${message}</span>
            </div>
        `;

        if (details) {
            detailsHtml += `
                <div class="verification-detail-sub">
                    <span class="detail-text">${details}</span>
                </div>
            `;
        }

        detailsElement.innerHTML = detailsHtml;
    }
}

function getStatusFromDetailedStatus(detailedStatus) {
    const statusMap = {
        'starting': 'running',
        'testing_connection': 'running',
        'connection_success': 'running',
        'connection_failed': 'error',
        'attempting': 'running',
        'fetching_temp_mail': 'running',
        'fetching_imap': 'running',
        'success': 'success',
        'waiting_retry': 'running',
        'attempt_failed': 'running',
        'network_error': 'error',
        'failed': 'error',
        'copied': 'success'
    };

    return statusMap[detailedStatus] || 'running';
}

function getStatusIcon(status) {
    const iconMap = {
        'starting': '🚀',
        'testing_connection': '🔧',
        'connection_success': '✅',
        'connection_failed': '❌',
        'attempting': '⏳',
        'fetching_temp_mail': '📧',
        'fetching_imap': '📬',
        'success': '✅',
        'waiting_retry': '⏰',
        'attempt_failed': '⚠️',
        'network_error': '🌐',
        'failed': '❌',
        'copied': '📋'
    };

    return iconMap[status] || '📄';
}

// 设置功能
function showSettings() {
    try {
        if (!bridge || !bridge.get_config) {
            throw new Error('Bridge未初始化或get_config方法不可用');
        }

        bridge.get_config((config) => {
            try {
                const content = generateSettingsContent(config);
                const footer = `
                    <div class="dialog-footer">
                        <div class="dialog-actions">
                            <button class="action-btn primary" onclick="saveSettings()">
                                <div class="btn-content">
                                    <span class="btn-text">保存设置</span>
                                </div>
                                <div class="btn-loading">
                                    <div class="loading-spinner"></div>
                                    <span class="loading-text">保存中...</span>
                                </div>
                            </button>
                            <button class="action-btn secondary" onclick="closeDialog()">
                                <div class="btn-content">
                                    <span class="btn-text">取消</span>
                                </div>
                            </button>
                        </div>
                    </div>
                `;
                showDialog('设置', content, footer);

                // 初始化单选框状态
                setTimeout(() => {
                    updateRadioItemsState('editor');
                    updateRadioItemsState('emailType');

                    // 添加单选框变化监听器
                    document.querySelectorAll('input[name="editor"]').forEach(radio => {
                        radio.addEventListener('change', () => updateRadioItemsState('editor'));
                    });

                    document.querySelectorAll('input[name="emailType"]').forEach(radio => {
                        radio.addEventListener('change', () => {
                            updateRadioItemsState('emailType');
                            toggleEmailType();
                        });
                    });
                }, 100);
            } catch (error) {
                showToast('生成设置界面失败', 'error');
                console.error('Error in showSettings callback:', error);
            }
        });
    } catch (error) {
        showToast('打开设置失败', 'error');
        console.error('Error in showSettings:', error);
    }
}

function generateSettingsContent(config) {
    const emailConfig = config.email || {};
    return `
        <div class="settings-content">
            <div class="settings-tabs">
                <button class="tab-btn active" onclick="switchTab('editor', this)">编辑器</button>
                <button class="tab-btn" onclick="switchTab('email', this)">邮箱设置</button>
                <button class="tab-btn" onclick="switchTab('advanced', this)">高级设置</button>
            </div>

            <div class="settings-body">
                <div class="tab-content" id="editorTab">
                    <h4>编辑器选择</h4>
                    <div class="radio-setting-group">
                        <div class="radio-setting-item ${selectedEditor === 'vscode' ? 'checked' : ''}" onclick="selectEditorInSettings('vscode')">
                            <input type="radio" name="editor" value="vscode" ${selectedEditor === 'vscode' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/vscode.png" alt="VS Code" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">Visual Studio Code</div>
                                <div class="setting-description">微软开发的现代代码编辑器</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'cursor' ? 'checked' : ''}" onclick="selectEditorInSettings('cursor')">
                            <input type="radio" name="editor" value="cursor" ${selectedEditor === 'cursor' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/Cursor.ico" alt="Cursor" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">Cursor</div>
                                <div class="setting-description">AI驱动的代码编辑器</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'intellij' ? 'checked' : ''}" onclick="selectEditorInSettings('intellij')">
                            <input type="radio" name="editor" value="intellij" ${selectedEditor === 'intellij' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/intellij.svg" alt="IntelliJ IDEA" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">IntelliJ IDEA</div>
                                <div class="setting-description">Java/Kotlin 开发IDE</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'pycharm' ? 'checked' : ''}" onclick="selectEditorInSettings('pycharm')">
                            <input type="radio" name="editor" value="pycharm" ${selectedEditor === 'pycharm' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/pycharm.svg" alt="PyCharm" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">PyCharm</div>
                                <div class="setting-description">Python 开发IDE</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'webstorm' ? 'checked' : ''}" onclick="selectEditorInSettings('webstorm')">
                            <input type="radio" name="editor" value="webstorm" ${selectedEditor === 'webstorm' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/webstorm.svg" alt="WebStorm" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">WebStorm</div>
                                <div class="setting-description">Web 开发IDE</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'phpstorm' ? 'checked' : ''}" onclick="selectEditorInSettings('phpstorm')">
                            <input type="radio" name="editor" value="phpstorm" ${selectedEditor === 'phpstorm' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/phpstorm.svg" alt="PhpStorm" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">PhpStorm</div>
                                <div class="setting-description">PHP 开发IDE</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'rubymine' ? 'checked' : ''}" onclick="selectEditorInSettings('rubymine')">
                            <input type="radio" name="editor" value="rubymine" ${selectedEditor === 'rubymine' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/rubymine.svg" alt="RubyMine" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">RubyMine</div>
                                <div class="setting-description">Ruby 开发IDE</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'clion' ? 'checked' : ''}" onclick="selectEditorInSettings('clion')">
                            <input type="radio" name="editor" value="clion" ${selectedEditor === 'clion' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/clion.svg" alt="CLion" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">CLion</div>
                                <div class="setting-description">C/C++ 开发IDE</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'goland' ? 'checked' : ''}" onclick="selectEditorInSettings('goland')">
                            <input type="radio" name="editor" value="goland" ${selectedEditor === 'goland' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/goland.svg" alt="GoLand" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">GoLand</div>
                                <div class="setting-description">Go 开发IDE</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'rider' ? 'checked' : ''}" onclick="selectEditorInSettings('rider')">
                            <input type="radio" name="editor" value="rider" ${selectedEditor === 'rider' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/rider.svg" alt="Rider" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">Rider</div>
                                <div class="setting-description">.NET 开发IDE</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'datagrip' ? 'checked' : ''}" onclick="selectEditorInSettings('datagrip')">
                            <input type="radio" name="editor" value="datagrip" ${selectedEditor === 'datagrip' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/datagrip.svg" alt="DataGrip" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">DataGrip</div>
                                <div class="setting-description">数据库管理IDE</div>
                            </div>
                        </div>
                        <div class="radio-setting-item ${selectedEditor === 'androidstudio' ? 'checked' : ''}" onclick="selectEditorInSettings('androidstudio')">
                            <input type="radio" name="editor" value="androidstudio" ${selectedEditor === 'androidstudio' ? 'checked' : ''} />
                            <div class="radio-setting-icon">
                                <img src="editor-icons/androidstudio.svg" alt="Android Studio" class="setting-editor-icon" onerror="this.style.display='none';">
                            </div>
                            <div>
                                <div class="setting-label">Android Studio</div>
                                <div class="setting-description">Android 开发IDE</div>
                            </div>
                        </div>
                    </div>
                </div>
            
            <div class="tab-content hidden" id="emailTab">
                <h4>邮箱配置</h4>

                <!-- 域名设置 -->
                <div class="setting-item">
                    <div>
                        <div class="setting-label">域名</div>
                        <div class="setting-description">已经托管到 Cloudflare 并且设置好邮件路由规则的域名</div>
                    </div>
                    <div class="setting-control">
                        <input type="text" id="emailDomain" value="${emailConfig.domain || ''}" placeholder="xx.com"
                               autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                               data-form-type="other" data-lpignore="true" data-1p-ignore="true"
                               readonly onfocus="this.removeAttribute('readonly');" />
                    </div>
                </div>

                <!-- 邮箱获取方式 -->
                <div class="setting-group-title">邮箱获取方式</div>
                <div class="radio-setting-group">
                    <div class="radio-setting-item ${emailConfig.use_temp_mail ? 'checked' : ''}" onclick="selectEmailType('temp')">
                        <input type="radio" name="emailType" value="temp" ${emailConfig.use_temp_mail ? 'checked' : ''} />
                        <div>
                            <div class="setting-label">临时邮箱</div>
                            <div class="setting-description">使用 tempmail.plus 临时邮箱服务</div>
                        </div>
                    </div>
                    <div class="radio-setting-item ${!emailConfig.use_temp_mail ? 'checked' : ''}" onclick="selectEmailType('imap')">
                        <input type="radio" name="emailType" value="imap" ${!emailConfig.use_temp_mail ? 'checked' : ''} />
                        <div>
                            <div class="setting-label">IMAP邮箱</div>
                            <div class="setting-description">使用自己的邮箱通过IMAP协议接收</div>
                        </div>
                    </div>
                </div>
                
                <div id="tempMailConfig" ${!emailConfig.use_temp_mail ? 'style="display:none"' : ''}>
                    <div class="setting-group-title">临时邮箱设置</div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">邮箱地址</div>
                            <div class="setting-description">在 tempmail.plus 创建的邮箱地址</div>
                        </div>
                        <div class="setting-control">
                            <input type="text" id="tempMailEmail" value="${emailConfig.temp_mail?.email || ''}" placeholder="<EMAIL>"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">PIN码</div>
                            <div class="setting-description">tempmail.plus 上设置的PIN码</div>
                        </div>
                        <div class="setting-control password-input-container">
                            <input type="password" id="tempMailPin" value="${emailConfig.temp_mail?.pin || ''}" placeholder="你设置的PIN"
                                   autocomplete="new-password" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" />
                            <button type="button" class="password-toggle-btn" onclick="togglePasswordVisibility('tempMailPin')">
                                <svg class="eye-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="imapConfig" ${emailConfig.use_temp_mail ? 'style="display:none"' : ''}>
                    <div class="setting-group-title">IMAP设置</div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">IMAP服务器</div>
                            <div class="setting-description">IMAP服务器地址</div>
                        </div>
                        <div class="setting-control">
                            <input type="text" id="imapServer" value="${emailConfig.imap?.server || 'imap.qq.com'}"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">端口</div>
                            <div class="setting-description">IMAP服务端口，通常为993</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="imapPort" value="${emailConfig.imap?.port || 993}"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" class="no-spinner" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">邮箱地址</div>
                            <div class="setting-description">邮箱完整地址</div>
                        </div>
                        <div class="setting-control">
                            <input type="email" id="imapUser" value="${emailConfig.imap?.user || ''}" placeholder="<EMAIL>"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">密码/授权码</div>
                            <div class="setting-description">邮箱密码或应用专用授权码</div>
                        </div>
                        <div class="setting-control password-input-container">
                            <input type="password" id="imapPassword" value="${emailConfig.imap?.password || ''}" placeholder="password"
                                   autocomplete="new-password" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" />
                            <button type="button" class="password-toggle-btn" onclick="togglePasswordVisibility('imapPassword')">
                                <svg class="eye-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                                    <circle cx="12" cy="12" r="3"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">收件箱</div>
                            <div class="setting-description">IMAP收件箱名称</div>
                        </div>
                        <div class="setting-control">
                            <input type="text" id="imapFolder" value="${emailConfig.imap?.folder || 'INBOX'}"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" />
                        </div>
                    </div>

                </div>
            </div>
            
            <div class="tab-content hidden" id="advancedTab">
                <h4>高级设置</h4>

                <!-- 自动复制验证码开关 -->
                <div class="switch-group">
                    <div>
                        <div class="switch-label">自动复制验证码</div>
                        <div class="switch-description">获取验证码后自动复制到剪贴板</div>
                    </div>
                    <label class="toggle-switch" onclick="toggleAutoCopy();">
                        <input type="checkbox" id="autoCopy" ${emailConfig.auto_copy ? 'checked' : ''} />
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <!-- 邮箱生成配置 -->
                <div class="setting-group-title">邮箱生成配置</div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">用户名长度</div>
                        <div class="setting-description">生成邮箱地址的用户名字符数量</div>
                    </div>
                    <div class="setting-control">
                        <input type="number" id="usernameLength" value="${emailConfig.generation?.username_length || 9}" min="5" max="20"
                               autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                               data-form-type="other" class="no-spinner" />
                    </div>
                </div>

                <!-- 数字配置 -->
                <div class="switch-group">
                    <div>
                        <div class="switch-label">包含数字</div>
                        <div class="switch-description">在生成的用户名中包含数字</div>
                    </div>
                    <label class="toggle-switch" onclick="toggleIncludeNumbers();">
                        <input type="checkbox" id="includeNumbers" ${emailConfig.generation?.include_numbers !== false ? 'checked' : ''} />
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <div id="numberConfig" ${emailConfig.generation?.include_numbers === false ? 'style="display:none"' : ''}>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">数字概率</div>
                            <div class="setting-description">包含数字的概率 (0-100%)</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="numberProbability" value="${Math.round((emailConfig.generation?.number_probability || 0.7) * 100)}" min="0" max="100"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" class="no-spinner" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">最少数字个数</div>
                            <div class="setting-description">用户名中最少包含的数字个数</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="minNumbers" value="${emailConfig.generation?.min_numbers || 1}" min="1" max="5"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" class="no-spinner" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">最多数字个数</div>
                            <div class="setting-description">用户名中最多包含的数字个数</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="maxNumbers" value="${emailConfig.generation?.max_numbers || 3}" min="1" max="8"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" class="no-spinner" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">前缀无数字长度</div>
                            <div class="setting-description">前几位不允许数字，使邮箱更真实</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="noDigitPrefixLength" value="${emailConfig.generation?.no_digit_prefix_length || 3}" min="1" max="5"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" class="no-spinner" />
                        </div>
                    </div>
                </div>

                <!-- 大写字母配置 -->
                <div class="switch-group">
                    <div>
                        <div class="switch-label">包含大写字母</div>
                        <div class="switch-description">在生成的用户名中包含大写字母</div>
                    </div>
                    <label class="toggle-switch" onclick="toggleIncludeUppercase();">
                        <input type="checkbox" id="includeUppercase" ${emailConfig.generation?.include_uppercase ? 'checked' : ''} />
                        <span class="toggle-slider"></span>
                    </label>
                </div>

                <div id="uppercaseConfig" ${!emailConfig.generation?.include_uppercase ? 'style="display:none"' : ''}>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">大写字母概率</div>
                            <div class="setting-description">包含大写字母的概率 (0-100%)</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="uppercaseProbability" value="${Math.round((emailConfig.generation?.uppercase_probability || 0.3) * 100)}" min="0" max="100"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" class="no-spinner" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">最少大写字母个数</div>
                            <div class="setting-description">用户名中最少包含的大写字母个数</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="minUppercase" value="${emailConfig.generation?.min_uppercase || 1}" min="1" max="3"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" class="no-spinner" />
                        </div>
                    </div>
                    <div class="setting-item">
                        <div>
                            <div class="setting-label">最多大写字母个数</div>
                            <div class="setting-description">用户名中最多包含的大写字母个数</div>
                        </div>
                        <div class="setting-control">
                            <input type="number" id="maxUppercase" value="${emailConfig.generation?.max_uppercase || 2}" min="1" max="5"
                                   autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                                   data-form-type="other" class="no-spinner" />
                        </div>
                    </div>
                </div>

                <!-- 重试配置 -->
                <div class="setting-group-title">重试配置</div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">最大重试次数</div>
                        <div class="setting-description">获取验证码失败时的最大重试次数</div>
                    </div>
                    <div class="setting-control">
                        <input type="number" id="maxRetries" value="${emailConfig.retry?.max_retries || 30}" min="1" max="100"
                               autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                               data-form-type="other" class="no-spinner" />
                    </div>
                </div>
                <div class="setting-item">
                    <div>
                        <div class="setting-label">重试间隔</div>
                        <div class="setting-description">每次重试之间的等待时间（秒）</div>
                    </div>
                    <div class="setting-control">
                        <input type="number" id="retryInterval" value="${emailConfig.retry?.retry_interval || 1}" min="1" max="10"
                               autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"
                               data-form-type="other" class="no-spinner" />
                    </div>
                </div>
            </div>

        </div>
    `;
}

function switchTab(tabName, clickedElement) {
    // 切换标签按钮状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // 如果没有传入点击的元素，尝试通过事件获取
    const targetElement = clickedElement || event?.target;
    if (targetElement) {
        targetElement.classList.add('active');
    }

    // 切换内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    document.getElementById(`${tabName}Tab`).classList.remove('hidden');
}

function selectEditorInSettings(editorType) {
    // 更新选中的编辑器
    const radioInput = document.querySelector(`input[name="editor"][value="${editorType}"]`);
    if (radioInput) {
        radioInput.checked = true;
        // 更新视觉状态
        updateRadioItemsState('editor');

        // 更新全局选中的编辑器并刷新主页面显示
        selectedEditor = editorType;
        updateEditorDisplay();
    }
}

function selectEmailType(emailType) {
    // 更新选中的邮箱类型
    const radioInput = document.querySelector(`input[name="emailType"][value="${emailType}"]`);
    if (radioInput) {
        radioInput.checked = true;
        // 触发切换逻辑
        toggleEmailType();
    }
}

function toggleEmailType() {
    const checkedInput = document.querySelector('input[name="emailType"]:checked');
    if (!checkedInput) return;

    const useTempMail = checkedInput.value === 'temp';
    const tempMailConfig = document.getElementById('tempMailConfig');
    const imapConfig = document.getElementById('imapConfig');

    if (tempMailConfig) {
        tempMailConfig.style.display = useTempMail ? 'block' : 'none';
    }
    if (imapConfig) {
        imapConfig.style.display = useTempMail ? 'none' : 'block';
    }

    // 更新单选框项的视觉状态
    updateRadioItemsState('emailType');
}

// 更新单选框项的视觉状态
function updateRadioItemsState(radioName) {
    const radioItems = document.querySelectorAll('.radio-setting-item');
    radioItems.forEach(item => {
        const radio = item.querySelector(`input[name="${radioName}"]`);
        if (radio) {
            if (radio.checked) {
                item.classList.add('checked');
            } else {
                item.classList.remove('checked');
            }
        }
    });
}

function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.parentElement.querySelector('.password-toggle-btn');
    const eyeIcon = button.querySelector('.eye-icon');

    if (input.type === 'password') {
        input.type = 'text';
        // 切换为闭眼图标（眼睛上有斜线）
        eyeIcon.innerHTML = `
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
            <circle cx="12" cy="12" r="3"/>
            <path d="M2 2l20 20"/>
        `;
    } else {
        input.type = 'password';
        // 切换为睁眼图标
        eyeIcon.innerHTML = `
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
            <circle cx="12" cy="12" r="3"/>
        `;
    }
}

function toggleIncludeNumbers() {
    const checkbox = document.getElementById('includeNumbers');
    if (checkbox) {
        checkbox.checked = !checkbox.checked;

        // 立即更新开关的视觉状态
        const toggleSwitch = checkbox.closest('.toggle-switch');
        if (toggleSwitch) {
            if (checkbox.checked) {
                toggleSwitch.classList.add('checked');
            } else {
                toggleSwitch.classList.remove('checked');
            }
        }

        // 显示/隐藏数字配置区域
        const numberConfig = document.getElementById('numberConfig');
        if (numberConfig) {
            numberConfig.style.display = checkbox.checked ? 'block' : 'none';
        }
    }
}

function toggleIncludeUppercase() {
    const checkbox = document.getElementById('includeUppercase');
    if (checkbox) {
        checkbox.checked = !checkbox.checked;

        // 立即更新开关的视觉状态
        const toggleSwitch = checkbox.closest('.toggle-switch');
        if (toggleSwitch) {
            if (checkbox.checked) {
                toggleSwitch.classList.add('checked');
            } else {
                toggleSwitch.classList.remove('checked');
            }
        }

        // 显示/隐藏大写字母配置区域
        const uppercaseConfig = document.getElementById('uppercaseConfig');
        if (uppercaseConfig) {
            uppercaseConfig.style.display = checkbox.checked ? 'block' : 'none';
        }
    }
}

function toggleAutoCopy() {
    const checkbox = document.getElementById('autoCopy');
    if (checkbox) {
        checkbox.checked = !checkbox.checked;

        // 立即更新开关的视觉状态
        const toggleSwitch = checkbox.closest('.toggle-switch');
        if (toggleSwitch) {
            if (checkbox.checked) {
                toggleSwitch.classList.add('checked');
            } else {
                toggleSwitch.classList.remove('checked');
            }
        }

        // 触发change事件以便其他代码能够监听到变化
        checkbox.dispatchEvent(new Event('change'));
    }
}

function collectSettingsData() {
    try {
        const getElementValue = (id, defaultValue = '') => {
            const element = document.getElementById(id);
            return element ? element.value.trim() : defaultValue;
        };

        // 获取元素值并去除所有空格（用于邮箱、服务器地址等不应包含空格的字段）
        const getElementValueNoSpaces = (id, defaultValue = '') => {
            const element = document.getElementById(id);
            return element ? element.value.replace(/\s+/g, '') : defaultValue;
        };

        const getElementChecked = (id, defaultValue = false) => {
            const element = document.getElementById(id);
            return element ? element.checked : defaultValue;
        };

        const getRadioValue = (name, defaultValue = '') => {
            const element = document.querySelector(`input[name="${name}"]:checked`);
            return element ? element.value : defaultValue;
        };

        return {
            editor_type: getRadioValue('editor', selectedEditor),
            email: {
                domain: getElementValueNoSpaces('emailDomain', 'xx.com'),
                use_temp_mail: getRadioValue('emailType', 'temp') === 'temp',
                temp_mail: {
                    email: getElementValueNoSpaces('tempMailEmail'),
                    pin: getElementValueNoSpaces('tempMailPin')
                },
                imap: {
                    server: getElementValueNoSpaces('imapServer', 'imap.qq.com'),
                    port: parseInt(getElementValueNoSpaces('imapPort', '993')) || 993,
                    user: getElementValueNoSpaces('imapUser'),
                    password: getElementValueNoSpaces('imapPassword'),
                    folder: getElementValue('imapFolder', 'INBOX')
                },
                generation: {
                    username_length: parseInt(getElementValue('usernameLength', '9')) || 9,
                    include_numbers: getElementChecked('includeNumbers', true),
                    number_probability: (parseInt(getElementValue('numberProbability', '70')) || 70) / 100,
                    min_numbers: parseInt(getElementValue('minNumbers', '1')) || 1,
                    max_numbers: parseInt(getElementValue('maxNumbers', '3')) || 3,
                    no_digit_prefix_length: parseInt(getElementValue('noDigitPrefixLength', '3')) || 3,
                    include_uppercase: getElementChecked('includeUppercase', false),
                    uppercase_probability: (parseInt(getElementValue('uppercaseProbability', '30')) || 30) / 100,
                    min_uppercase: parseInt(getElementValue('minUppercase', '1')) || 1,
                    max_uppercase: parseInt(getElementValue('maxUppercase', '2')) || 2
                },
                retry: {
                    max_retries: parseInt(getElementValue('maxRetries', '30')) || 30,
                    retry_interval: parseInt(getElementValue('retryInterval', '1')) || 1
                },
                auto_copy: getElementChecked('autoCopy', false)
            }
        };
    } catch (error) {
        console.error('Error collecting settings data:', error);
        showToast('收集设置数据失败', 'error');
        return null;
    }
}

function saveSettings() {
    try {
        if (!bridge || !bridge.save_config) {
            throw new Error('Bridge未初始化或save_config方法不可用');
        }

        const config = collectSettingsData();
        if (!config) {
            throw new Error('收集设置数据失败');
        }

        // 显示加载状态
        const saveBtn = document.querySelector('.action-btn.primary');
        if (saveBtn) {
            saveBtn.classList.add('loading');
            saveBtn.disabled = true;
        }

        bridge.save_config(config, (success) => {
            try {
                // 恢复按钮状态
                if (saveBtn) {
                    saveBtn.classList.remove('loading');
                    saveBtn.disabled = false;
                }

                if (success) {
                    showToast('设置已保存', 'success');

                    // 如果切换了编辑器，更新选择
                    const newEditor = config.editor_type;
                    if (newEditor !== selectedEditor) {
                        selectedEditor = newEditor;
                        updateEditorDisplay();
                    }

                    // 更新浮动卡片信息
                    updateFloatingCards(config);

                    // 立即关闭对话框
                    closeDialog();
                } else {
                    showToast('保存设置失败', 'error');
                }
            } catch (error) {
                // 恢复按钮状态
                if (saveBtn) {
                    saveBtn.classList.remove('loading');
                    saveBtn.disabled = false;
                }
                showToast('处理保存结果时发生错误', 'error');
                console.error('Error in saveSettings callback:', error);
            }
        });
    } catch (error) {
        // 恢复按钮状态
        const saveBtn = document.querySelector('.action-btn.primary');
        if (saveBtn) {
            saveBtn.classList.remove('loading');
            saveBtn.disabled = false;
        }
        showToast('保存设置失败', 'error');
        console.error('Error in saveSettings:', error);
    }
}

function handleConfigUpdate(config) {
    // 配置更新时的处理 - 与原版Python行为一致
    console.log('配置已更新:', config);

    // 检查config是否为有效对象
    if (!config || typeof config !== 'object') {
        console.warn('handleConfigUpdate: 收到无效的config参数:', config);
        return;
    }

    // 更新浮动卡片信息
    updateFloatingCards(config);

    // 如果编辑器类型发生变化，更新编辑器显示
    if (config.editor_type && config.editor_type !== selectedEditor) {
        selectedEditor = config.editor_type;
        updateEditorDisplay();
    }

    // 可以在这里添加其他需要响应配置变化的逻辑
}

// 添加必要的样式
const style = document.createElement('style');
style.textContent = `
    /* CSS变量定义 */
    :root {
        --bg-primary: #000000;
        --bg-secondary: #0a0a0a;
        --bg-card: #111111;
        --bg-hover: #1a1a1a;

        --purple-primary: #8b5cf6;
        --purple-light: #a78bfa;
        --purple-dark: #6d28d9;
        --purple-glow: rgba(139, 92, 246, 0.5);

        --text-primary: #ffffff;
        --text-secondary: #a0a0a0;
        --text-muted: #666666;

        --border-color: rgba(139, 92, 246, 0.2);
        --shadow-color: rgba(139, 92, 246, 0.3);

        --radius-sm: 16px;
        --radius-md: 20px;
        --radius-lg: 28px;
        --radius-xl: 36px;

        --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .reset-dialog ul {
        margin: 20px 0;
        padding-left: 20px;
        color: var(--text-secondary);
    }
    
    .reset-dialog li {
        margin: 8px 0;
    }
    
    .warning-text {
        color: var(--purple-light);
        margin-top: 20px;
    }
    
    .dialog-actions {
        display: flex;
        gap: 16px;
        margin-top: 30px;
        justify-content: center;
    }
    
    .progress-container {
        margin-top: 30px;
    }
    
    .progress-bar {
        width: 100%;
        height: 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        overflow: hidden;
    }
    
    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--purple-primary), var(--purple-light));
        transition: width 0.3s ease;
        width: 0%;
    }
    
    .progress-text {
        text-align: center;
        margin-top: 12px;
        color: var(--text-secondary);
    }
    
    .email-display {
        display: flex;
        gap: 12px;
        margin: 20px 0;
    }
    
    .email-display input {
        flex: 1;
        padding: 12px 16px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-sm);
        color: var(--text-primary);
        font-size: 16px;
    }
    
    .copy-btn {
        padding: 12px 24px;
        background: rgba(139, 92, 246, 0.1);
        border: 1px solid var(--purple-primary);
        border-radius: var(--radius-sm);
        color: var(--purple-light);
        cursor: pointer;
        transition: var(--transition);
    }
    
    /* 移除copy-btn的悬浮动画 */
    
    .loading-spinner {
        width: 50px;
        height: 50px;
        margin: 20px auto;
        border: 3px solid rgba(255, 255, 255, 0.1);
        border-top-color: var(--purple-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    
    .verification-code {
        font-size: 48px;
        font-weight: 900;
        color: var(--purple-primary);
        letter-spacing: 8px;
        text-shadow: 0 0 30px var(--purple-glow);
        display: block;
        text-align: center;
        margin: 30px 0;
    }
    
    .info-text {
        text-align: center;
        color: var(--text-secondary);
        margin: 20px 0;
    }
    
    .settings-content {
        min-width: 600px;
        display: flex;
        flex-direction: column;
        height: min(500px, 70vh); /* 固定高度，但不超过屏幕的70%，避免切换标签时高度变化 */
        overflow: hidden;
    }

    .settings-body {
        flex: 1;
        overflow-y: auto;
        padding: 20px 0;
        margin-right: -6px;
        padding-right: 6px;
        min-height: 0; /* 确保flex子项可以收缩 */
    }

    /* 确保标签页内容可以正常滚动 */
    .tab-content {
        min-height: 0;
        overflow: visible;
    }

    /* 为高级设置添加底部间距，确保最后一项可见 */
    #advancedTab {
        padding-bottom: 20px;
    }

    #emailTab {
        padding-bottom: 20px;
    }

    .settings-footer {
        border-top: 1px solid var(--border-color);
        padding: 20px 0 0 0;
        margin-top: auto;
    }
    
    .settings-tabs {
        display: flex;
        gap: 16px;
        margin-bottom: 30px;
        border-bottom: 1px solid var(--border-color);
    }
    
    .tab-btn {
        padding: 12px 24px;
        background: transparent;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        transition: var(--transition);
        position: relative;
    }
    
    .tab-btn.active {
        color: var(--purple-primary);
    }
    
    .tab-btn.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--purple-primary);
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: var(--text-secondary);
        font-size: 14px;
    }
    
    .form-group input[type="text"],
    .form-group input[type="email"],
    .form-group input[type="password"],
    .form-group input[type="number"] {
        width: 100%;
        padding: 12px 16px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid var(--border-color);
        border-radius: var(--radius-sm);
        color: var(--text-primary);
        font-size: 16px;
        transition: var(--transition);
    }
    
    .form-group input:focus {
        outline: none;
        border-color: var(--purple-primary);
        background: rgba(139, 92, 246, 0.05);
    }
    
    .radio-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .radio-group label {
        display: flex;
        align-items: center;
        cursor: pointer;
    }
    
    .radio-group input[type="radio"] {
        margin-right: 8px;
    }
    
    .email-config-section {
        margin-top: 20px;
        padding: 20px;
        background: rgba(255, 255, 255, 0.03);
        border-radius: var(--radius-md);
    }
    
    .email-config-section h5 {
        margin-bottom: 16px;
        color: var(--purple-light);
    }
    
    .editor-switch {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .editor-switch label {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: var(--radius-sm);
        transition: var(--transition);
    }
    
    .editor-switch label:hover {
        background: rgba(139, 92, 246, 0.1);
    }
    
    .editor-switch input[type="radio"] {
        margin-right: 12px;
    }

    /* 单选框组的现代化样式 */
    .radio-setting-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .radio-setting-item {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid rgba(139, 92, 246, 0.1);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        user-select: none;
    }

    .radio-setting-item:hover {
        background: rgba(139, 92, 246, 0.05);
        border-color: rgba(139, 92, 246, 0.2);
    }

    .radio-setting-item input[type="radio"] {
        margin-right: 12px;
        accent-color: var(--purple-primary);
    }

    .radio-setting-item.checked {
        background: rgba(139, 92, 246, 0.1);
        border-color: var(--purple-primary);
    }

    /* 单选框项中的标签和描述样式 - 与setting-item保持一致 */
    .radio-setting-item .setting-label {
        color: var(--text-primary);
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 0;
    }

    .radio-setting-item .setting-description {
        color: var(--text-muted);
        font-size: 13px;
        margin-top: 4px;
    }

    /* 密码输入框容器样式 */
    .password-input-container {
        position: relative;
        display: flex;
        align-items: center;
    }

    .password-input-container input {
        flex: 1;
        padding-right: 45px; /* 为眼睛按钮留出空间 */
    }

    .password-toggle-btn {
        position: absolute;
        right: 12px;
        background: none;
        border: none;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
    }

    /* 移除password-toggle-btn的悬浮动画 */

    .eye-icon {
        width: 16px;
        height: 16px;
        color: var(--text-secondary);
        transition: color 0.2s ease;
    }

    /* Toggle Switch 样式 */
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 24px;
        flex-shrink: 0;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.1);
        transition: 0.3s ease;
        border-radius: 24px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        transition: 0.3s ease;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    /* 选中状态 - 支持:checked伪类和.checked类 */
    .toggle-switch input:checked + .toggle-slider,
    .toggle-switch.checked .toggle-slider {
        background-color: var(--purple-primary);
        border-color: var(--purple-primary);
    }

    .toggle-switch input:checked + .toggle-slider:before,
    .toggle-switch.checked .toggle-slider:before {
        transform: translateX(20px);
    }

    /* Switch Group 样式 */
    .switch-group {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid rgba(139, 92, 246, 0.1);
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        user-select: none;
        margin-bottom: 16px;
    }

    /* 移除switch-group的悬浮动画 */

    .switch-label {
        color: var(--text-primary);
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 0;
    }

    .switch-description {
        color: var(--text-muted);
        font-size: 13px;
        margin-top: 4px;
    }

`;
document.head.appendChild(style);

// 验证码状态轮询机制
let verificationStatusCheckInterval = null;
let verificationCompleteHandled = false;

function startVerificationStatusPolling() {

    // 重置完成标志
    verificationCompleteHandled = false;

    // 清除之前的轮询
    if (verificationStatusCheckInterval) {
        clearInterval(verificationStatusCheckInterval);
        verificationStatusCheckInterval = null;
    }

    let pollCount = 0;
    const maxPolls = 300; // 5分钟超时 (300 * 1秒)

    verificationStatusCheckInterval = setInterval(() => {
        pollCount++;

        if (pollCount >= maxPolls) {

            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;
            handleVerificationCompleted(false, "", "获取验证码超时");
            return;
        }

        if (bridge && bridge.get_verification_status) {
            try {

                // 检查方法是否存在

                // 尝试直接调用同步方法
                try {

                    const statusJson = bridge.get_verification_status();

                    // 检查返回值
                    if (statusJson === undefined) {

                        return;
                    }

                    // 检查是否是Promise对象
                    if (statusJson && typeof statusJson.then === 'function') {

                        statusJson.then(actualJson => {

                            processVerificationStatus(actualJson);
                        }).catch(error => {

                        });
                    } else {

                        processVerificationStatus(statusJson);
                    }
                } catch (directError) {

                    // 回退到回调方式
                    try {
                        bridge.get_verification_status((statusJson) => {

                            processVerificationStatus(statusJson);
                        });
                    } catch (callbackError) {

                    }
                }

            } catch (error) {

            }
        } else {

        }
    }, 1000); // 每秒轮询一次
}

// 新的验证码状态轮询机制
function startVerificationStatusPollingNew() {

    // 重置完成标志
    verificationCompleteHandled = false;

    // 清除可能存在的旧轮询
    if (verificationStatusCheckInterval) {
        clearInterval(verificationStatusCheckInterval);
        verificationStatusCheckInterval = null;
    }

    let pollCount = 0;
    const maxPolls = 300; // 5分钟超时

    verificationStatusCheckInterval = setInterval(() => {
        pollCount++;

        if (pollCount >= maxPolls) {

            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;
            handleVerificationCompleted(false, "", "获取验证码超时");
            return;
        }

        if (bridge && bridge.get_verification_status) {
            try {

                // 直接同步调用
                const statusJson = bridge.get_verification_status();

                if (statusJson) {
                    processVerificationStatusNew(statusJson);
                } else {

                }

            } catch (error) {

            }
        } else {

        }
    }, 1000); // 每秒轮询一次
}

// 处理验证码状态的通用函数
function processVerificationStatus(statusJson) {
    try {
        const status = JSON.parse(statusJson);

        // 检查是否完成
        if (status.completed && !verificationCompleteHandled) {

            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;

            // 移除轮询中的toast显示，由信号处理

            // 处理完成结果
            handleVerificationCompleted(status.success, status.code || "", status.message || "");
        } else if (status.is_running) {

            // 更新进度显示
            if (status.message) {
                updateVerificationStatus('running', status.message);

                // 更新进度条
                if (status.progress !== undefined) {
                    const progressBar = document.getElementById('verificationProgressBar');
                    if (progressBar) {
                        const percentage = status.progress * 100;
                        progressBar.style.width = `${percentage}%`;
                    }
                }
            }
        }
    } catch (parseError) {

    }
}

// 新版处理验证码状态的函数
function processVerificationStatusNew(statusJson) {
    try {
        const status = JSON.parse(statusJson);

        // 检查是否完成
        if (status.completed && !verificationCompleteHandled) {

            clearInterval(verificationStatusCheckInterval);
            verificationStatusCheckInterval = null;

            // 处理完成结果
            handleVerificationCompleted(status.success, status.code || "", status.message || "");
        } else if (status.is_running) {

            // 可以在这里更新进度显示
            if (status.message) {
                updateVerificationStatus('running', status.message);
            }
        }
    } catch (parseError) {

    }
}

// 添加按钮加载状态的CSS样式
const buttonLoadingStyle = document.createElement('style');
buttonLoadingStyle.textContent = `
    /* 小型加载图标 */
    .loading-spinner-small {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-top-color: rgba(139, 92, 246, 1);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        flex-shrink: 0;
        margin-top: 2px;     /* 向下微调2px */
        align-self: center;
    }

    /* 加载文字 - 与按钮原始文字样式保持一致 */
    .loading-text {
        color: white;
        font-size: 15px;
        font-weight: 600;
        line-height: 1.2;    /* 稍微增加行高 */
        align-self: center;
        margin-top: 1px;     /* 向下微调1px */
    }

    /* 按钮加载状态布局 - 完美对齐 */
    .btn-loading {
        display: none;
        align-items: center;
        justify-content: center;
        gap: 8px;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
    }

    /* 按钮加载状态显示时 */
    .workflow-btn.loading .btn-loading {
        display: flex !important;
        opacity: 1;
        transform: scale(1);
    }

    /* 隐藏原始内容当加载时 */
    .workflow-btn.loading .btn-content {
        opacity: 0;
        transform: scale(0.95);
        pointer-events: none;
    }

    /* 按钮内容过渡动画 */
    .btn-content {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        opacity: 1;
        transform: scale(1);
    }

    /* 加载状态过渡动画 */
    .btn-loading {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        opacity: 0;
        transform: scale(0.95);
    }

    /* 旋转动画 */
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(buttonLoadingStyle);

// ===== Augment账号管理功能 =====

// 账号管理状态
let augmentAccountState = {
    token: null,
    isTokenValid: false,
    accountData: null,
    isQuerying: false,
    isAddingAccount: false,
    savedAccounts: [],
    selectedAccount: null,
    isInitialized: false  // 添加初始化标志，防止重复初始化
};

// 保存选中账号到本地存储
function saveSelectedAccount(email) {
    try {
        localStorage.setItem('yaugment_selected_account', email || '');
    } catch (error) {
        console.error('保存选中账号失败:', error);
    }
}

// 从本地存储加载选中账号
function loadSelectedAccount() {
    try {
        return localStorage.getItem('yaugment_selected_account') || '';
    } catch (error) {
        console.error('加载选中账号失败:', error);
        return '';
    }
}

// 恢复上次选择的账号
async function restoreSelectedAccount() {
    const savedEmail = loadSelectedAccount();
    if (savedEmail && augmentAccountState.savedAccounts.length > 0) {
        // 检查保存的邮箱是否还存在于账号列表中
        const accountExists = augmentAccountState.savedAccounts.some(account => account.email === savedEmail);
        if (accountExists) {
            // 使用静默模式恢复选择，不显示toast提示
            await selectAccount(savedEmail, true);
        } else {
            saveSelectedAccount('');
        }
    }
}

// Token对话框管理
function showTokenDialog() {
    const overlay = document.getElementById('tokenDialogOverlay');
    const input = document.getElementById('tokenInput');

    if (overlay && input) {
        // 如果已有token，预填充
        if (augmentAccountState.token) {
            input.value = augmentAccountState.token;
        }

        overlay.classList.add('show');

        // 延迟聚焦，确保动画完成
        setTimeout(() => {
            input.focus();
        }, 300);
    }
}

function hideTokenDialog() {
    const overlay = document.getElementById('tokenDialogOverlay');
    if (overlay) {
        overlay.classList.remove('show');
    }
}

function saveToken() {
    const input = document.getElementById('tokenInput');
    if (!input) return;

    let token = input.value.trim();

    // 清理token中的所有空格
    token = token.replace(/\s/g, '');

    // 保存token到内存（允许空token，相当于清空）
    augmentAccountState.token = token;
    augmentAccountState.isTokenValid = false;

    if (!token) {
        // 如果是清空token，显示相应提示
        showToast('Token已清空', 'info');
        // 清空账号信息显示
        clearAccountDisplay();
    } else {
        showToast('Token已保存', 'success');
    }

    // 隐藏对话框
    hideTokenDialog();

    // 清空之前的数据
    clearAccountData();

    // 更新按钮状态
    updateButtonStates();
}

// 启用账号管理按钮
function enableAccountButtons() {
    const queryBtn = document.getElementById('queryAccountBtn');

    if (queryBtn) {
        queryBtn.disabled = false;
    }
}

// 加载保存的账号列表
async function loadSavedAccounts() {
    try {
        const result = await window.__TAURI__.core.invoke('get_saved_accounts');
        if (result.success) {
            augmentAccountState.savedAccounts = result.data || [];
            updateAccountSelectOptions();
        } else {
            console.warn('获取保存的账号失败:', result.message);
            augmentAccountState.savedAccounts = [];
        }
    } catch (error) {
        console.error('加载保存的账号失败:', error);
        augmentAccountState.savedAccounts = [];
    }
}

// 更新账号选择下拉框选项
function updateAccountSelectOptions() {
    const dropdown = document.getElementById('accountDropdown');
    const selectText = document.querySelector('.select-text');
    const container = document.querySelector('.account-select-container');
    if (!dropdown || !selectText || !container) return;

    // 保存当前选中的账号邮箱
    const currentSelectedEmail = augmentAccountState.selectedAccount?.email;

    // 清空现有选项
    dropdown.innerHTML = '';

    // 按账号创建时间排序（有时间的从新到旧，未知时间的排后面）
    const sortedAccounts = [...augmentAccountState.savedAccounts].sort((a, b) => {
        const timeA = a.account_creation_time || a.created_at;
        const timeB = b.account_creation_time || b.created_at;

        // 如果两个都没有时间，保持原顺序
        if (!timeA && !timeB) return 0;

        // 如果A没有时间，B有时间，A排后面
        if (!timeA && timeB) return 1;

        // 如果A有时间，B没有时间，A排前面
        if (timeA && !timeB) return -1;

        // 如果两个都有时间，按时间从新到旧排序
        const dateA = new Date(timeA);
        const dateB = new Date(timeB);

        // 检查日期是否有效
        const isValidA = !isNaN(dateA.getTime());
        const isValidB = !isNaN(dateB.getTime());

        if (!isValidA && !isValidB) return 0;
        if (!isValidA && isValidB) return 1;
        if (isValidA && !isValidB) return -1;

        return dateB - dateA;
    });

    // 如果没有保存的账号，显示提示
    if (sortedAccounts.length === 0) {
        const emptyOption = document.createElement('div');
        emptyOption.className = 'select-option empty-option';
        emptyOption.innerHTML = '<span class="option-content">暂无保存的账号</span>';
        dropdown.appendChild(emptyOption);
        return;
    }

    // 添加保存的账号选项
    sortedAccounts.forEach(account => {
        const option = document.createElement('div');
        option.className = 'select-option';
        option.setAttribute('data-email', account.email);

        // 优先使用真实创建时间，否则使用添加时间
        let displayTime = account.account_creation_time || account.created_at;
        let formattedTime = displayTime;

        try {
            const date = new Date(displayTime);
            // 确保时间是有效的
            if (isNaN(date.getTime())) {
                formattedTime = '未知时间';
            } else {
                formattedTime = date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                }).replace(/\//g, '-');
            }
        } catch (e) {
            // 如果时间格式化失败，保持原格式
            formattedTime = displayTime || '未知时间';
        }

        // 检查是否为当前选中的账号
        const isSelected = currentSelectedEmail && account.email === currentSelectedEmail;
        const selectedIndicator = isSelected ? '<span class="selected-dot"></span>' : '';

        option.innerHTML = `
            <div class="option-content" onclick="selectAccount('${account.email}')">
                <div class="account-info">
                    ${selectedIndicator}
                    <span class="account-email">${account.email}</span>
                </div>
                <span class="account-time">${formattedTime}</span>
            </div>
            <button class="delete-btn" onclick="confirmDeleteAccount('${account.email}', event)" title="删除账号">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                </svg>
            </button>
        `;

        // 为选中的选项添加特殊样式
        if (isSelected) {
            option.classList.add('selected');
        }

        dropdown.appendChild(option);
    });

    // 恢复之前选中的账号状态
    if (currentSelectedEmail) {
        const matchingAccount = augmentAccountState.savedAccounts.find(acc => acc.email === currentSelectedEmail);
        if (matchingAccount) {
            augmentAccountState.selectedAccount = matchingAccount;
            selectText.textContent = currentSelectedEmail;
        }
    }
}



// 账号选择变更处理
async function onAccountSelectChange() {
    const accountSelect = document.getElementById('accountSelect');
    if (!accountSelect) return;

    const selectedEmail = accountSelect.value;

    if (!selectedEmail) {
        // 清空选择
        augmentAccountState.selectedAccount = null;
        clearAccountData();
        updateButtonStates();
        return;
    }

    try {
        // 获取选中账号的详细信息
        const result = await window.__TAURI__.core.invoke('get_saved_account_by_email', { email: selectedEmail });

        if (result.success) {
            augmentAccountState.selectedAccount = result.data;

            // 如果有Session Token，优先使用Session Token
            if (augmentAccountState.token) {
                showToast('您输入了Session Token，那么并不会进行查询你当前选择的账号', 'info');
                return;
            }

            // 使用Portal Token查询账号信息
            await queryAccountWithPortalToken(result.data.portal_token);
        } else {
            showToast('获取账号信息失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('账号选择处理失败:', error);
        showToast('账号选择处理失败', 'error');
    }
}

// 使用Portal Token查询账号信息
async function queryAccountWithPortalToken(portalToken, silent = false) {
    if (augmentAccountState.isQuerying) return;

    const queryBtn = document.getElementById('queryAccountBtn');
    if (!queryBtn) return;

    // 检查portalToken参数
    if (!portalToken) {
        console.error('queryAccountWithPortalToken: portalToken参数为空');
        showToast('Portal Token为空，无法查询账号信息', 'error');
        return;
    }

    try {
        augmentAccountState.isQuerying = true;
        setButtonLoading(queryBtn, true);

        if (!silent) {
            showToast('正在查询账号信息...', 'info');
        }

        // 使用新的命令，会自动更新账号创建时间（如果需要）
        const result = await bridge.query_account_with_portal_and_update_time(portalToken);

        if (result.success) {
            augmentAccountState.accountData = result.data;
            updateAccountDisplay(result.data);
            switchToRefreshMode(queryBtn);

            // 刷新账号列表以显示可能更新的创建时间
            await loadSavedAccounts();

            if (!silent) {
                showToast('账号信息获取成功', 'success');
            }
            updateButtonStates();
        } else {
            if (!silent) {
                // 简化错误信息，不暴露URL和技术细节
                let errorMessage = '查询失败';
                if (result.message) {
                    const message = result.message.toLowerCase();
                    if (message.includes('timeout') || message.includes('timed out')) {
                        errorMessage = '网络超时';
                    } else if (message.includes('dns error') || message.includes('connect') || message.includes('network') || message.includes('不知道这样的主机')) {
                        errorMessage = '网络错误';
                    } else if (message.includes('unauthorized') || message.includes('invalid') || message.includes('token')) {
                        errorMessage = 'Token无效';
                    } else if (message.includes('forbidden') || message.includes('access denied')) {
                        errorMessage = '访问被拒绝';
                    } else if (message.includes('not found')) {
                        errorMessage = '资源不存在';
                    } else if (message.includes('server error') || message.includes('internal error')) {
                        errorMessage = '服务器错误';
                    }
                }
                showToast(errorMessage, 'error');
            }
        }
    } catch (error) {
        console.error('Token查询失败:', error);
        if (!silent) {
            showToast('查询失败', 'error');
        }
    } finally {
        augmentAccountState.isQuerying = false;
        setButtonLoading(queryBtn, false);
    }
}

// 切换下拉框显示状态
function toggleAccountDropdown() {
    const customSelect = document.getElementById('customAccountSelect');
    if (!customSelect) return;

    const isOpen = customSelect.classList.contains('open');

    if (isOpen) {
        closeAccountDropdown();
    } else {
        openAccountDropdown();
    }
}

// 打开下拉框
function openAccountDropdown() {
    const customSelect = document.getElementById('customAccountSelect');
    if (!customSelect) return;

    customSelect.classList.add('open');

    // 点击外部关闭下拉框
    setTimeout(() => {
        document.addEventListener('click', closeDropdownOnClickOutside);
    }, 0);
}

// 关闭下拉框
function closeAccountDropdown() {
    const customSelect = document.getElementById('customAccountSelect');
    if (!customSelect) return;

    customSelect.classList.remove('open');
    document.removeEventListener('click', closeDropdownOnClickOutside);
}

// 点击外部关闭下拉框
function closeDropdownOnClickOutside(event) {
    const customSelect = document.getElementById('customAccountSelect');
    if (!customSelect) return;

    if (!customSelect.contains(event.target)) {
        closeAccountDropdown();
    }
}

// 选择账号
async function selectAccount(email, silent = false) {
    closeAccountDropdown();

    if (!email) {
        // 清空当前账号信息
        augmentAccountState.selectedAccount = null;
        clearAccountData();
        document.querySelector('.select-text').textContent = '选择已保存账号';
        saveSelectedAccount(''); // 保存空选择
        updateButtonStates();
        return;
    }

    try {
        // 获取选中账号的详细信息
        const result = await window.__TAURI__.core.invoke('get_saved_account_by_email', { email: email });

        if (result.success) {
            augmentAccountState.selectedAccount = result.data;

            // 更新选择框显示文本
            const selectText = document.querySelector('.select-text');
            if (selectText) {
                selectText.textContent = result.data.email;
            }

            // 保存选中的账号到本地存储
            saveSelectedAccount(email);

            // 刷新下拉列表以显示选中状态
            updateAccountSelectOptions();

            // 如果有Session Token，优先使用Session Token
            if (augmentAccountState.token) {
                if (!silent) {
                    showToast('您输入了Session Token，那么并不会进行查询你当前选择的账号', 'info');
                }
                updateButtonStates();
                return;
            }

            // 使用Portal Token查询账号信息
            await queryAccountWithPortalToken(result.data.portal_token, silent);
        } else {
            if (!silent) {
                showToast('获取账号信息失败: ' + result.message, 'error');
            }
        }
    } catch (error) {
        console.error('选择账号失败:', error);
        if (!silent) {
            showToast('选择账号失败', 'error');
        }
    }
}

// 确认删除账号
function confirmDeleteAccount(email, event) {
    // 阻止事件冒泡，避免触发选择账号
    if (event) {
        event.stopPropagation();
    }

    // 关闭下拉框
    closeAccountDropdown();

    // 显示确认对话框
    showDeleteAccountDialog(email);
}

// 显示删除账号确认对话框
function showDeleteAccountDialog(email) {
    // 处理邮箱过长显示
    const displayEmail = email.length > 30 ? email.substring(0, 30) + '...' : email;

    const dialogHTML = `
        <div class="delete-dialog-overlay" id="deleteAccountDialogOverlay">
            <div class="delete-dialog">
                <div class="delete-dialog-header">
                    <div class="delete-dialog-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h3 class="delete-dialog-title">确认删除账号</h3>
                </div>
                <div class="delete-dialog-content">
                    <div class="delete-dialog-info">
                        <div class="delete-dialog-email-label">确定要删除账号吗？</div>
                        <div class="delete-dialog-email" title="${email}">${displayEmail}</div>
                    </div>
                    <div class="delete-dialog-notice">
                        <svg width="16" height="16" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" fill="currentColor"/>
                        </svg>
                        <span>此操作不可撤销，您删除后可以重新添加该账号</span>
                    </div>
                    <div class="delete-dialog-actions">
                        <button class="delete-dialog-btn cancel" onclick="closeDeleteAccountDialog()">取消</button>
                        <button class="delete-dialog-btn confirm" onclick="deleteAccount('${email}')">删除</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', dialogHTML);

    const overlay = document.getElementById('deleteAccountDialogOverlay');
    setTimeout(() => overlay.classList.add('show'), 10);
    document.body.style.overflow = 'hidden';
}

// 关闭删除账号确认对话框
function closeDeleteAccountDialog() {
    const overlay = document.getElementById('deleteAccountDialogOverlay');
    if (!overlay) return;

    document.body.style.overflow = '';
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
    }, 300);
}

// 删除账号
async function deleteAccount(email) {
    closeDeleteAccountDialog();

    try {
        const result = await window.__TAURI__.core.invoke('remove_saved_account', { email: email });

        if (result.success) {
            showToast('账号删除成功', 'success');

            // 如果删除的是当前选中的账号，清空选择
            if (augmentAccountState.selectedAccount && augmentAccountState.selectedAccount.email === email) {
                augmentAccountState.selectedAccount = null;
                clearAccountData();
                document.querySelector('.select-text').textContent = '选择已保存账号';
                saveSelectedAccount(''); // 清除本地存储的选择
            }

            // 重新加载账号列表
            await loadSavedAccounts();
            updateButtonStates();
        } else {
            showToast('删除账号失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('删除账号失败:', error);
        showToast('删除账号失败', 'error');
    }
}

// 更新按钮状态
function updateButtonStates() {
    const queryBtn = document.getElementById('queryAccountBtn');
    const switchPlanBtn = document.getElementById('switchPlanBtn');

    // 账号查询按钮：有Session Token或有选中账号时启用
    if (queryBtn) {
        queryBtn.disabled = !augmentAccountState.token && !augmentAccountState.selectedAccount;
    }

    // 切换社区计划按钮：只有Session Token时启用
    if (switchPlanBtn) {
        switchPlanBtn.disabled = !augmentAccountState.token;
    }
}

// 清空账号数据显示（带动画）
function clearAccountData() {
    const elements = {
        'accountEmail': '未获取',
        'accountPlan': '未获取',
        'accountStatus': '未获取',
        'accountExpiry': '未获取',
        'queryTime': '未查询',
        'creditsUsed': '0',
        'creditsTotal': '0'
    };

    // 添加清空动画的通用函数
    function clearElementWithAnimation(element, newContent) {
        if (!element) return;

        // 淡出
        element.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
        element.style.opacity = '0';
        element.style.transform = 'translateY(-10px)';

        setTimeout(() => {
            // 更新内容
            element.textContent = newContent;

            // 重置颜色为默认（与其他数据项保持一致）
            if (element.id === 'accountStatus') {
                element.style.color = ''; // 清除内联样式，使用CSS默认样式
            }

            // 淡入
            element.style.transform = 'translateY(0)';
            element.style.opacity = '1';
        }, 200);
    }

    // 使用动画清空所有元素
    Object.entries(elements).forEach(([id, defaultValue]) => {
        const element = document.getElementById(id);
        clearElementWithAnimation(element, defaultValue);
    });

    // 重置进度圆环（带动画）
    setTimeout(() => {
        updateCreditsProgress(0);
    }, 100);

    // 重置百分比显示
    const percentageElement = document.getElementById('creditsPercentage');
    clearElementWithAnimation(percentageElement, '0%');

    // 重置查询按钮为初始状态
    const queryBtn = document.getElementById('queryAccountBtn');
    resetQueryButtonToInitial(queryBtn);

    augmentAccountState.accountData = null;
}

// 更新积分进度圆环（带动画）
function updateCreditsProgress(percentage) {
    const progressBar = document.getElementById('circleProgressBar');
    const percentageElement = document.getElementById('creditsPercentage');

    if (progressBar && percentageElement) {
        const circumference = 471; // 2 * π * 75
        const targetOffset = circumference - (percentage / 100) * circumference;

        // 获取当前进度
        const currentOffset = parseFloat(progressBar.style.strokeDashoffset) || circumference;

        // 添加过渡动画
        progressBar.style.transition = 'stroke-dashoffset 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
        progressBar.style.strokeDashoffset = targetOffset;

        // 数字动画
        const currentPercentage = parseFloat(percentageElement.textContent) || 0;
        animateNumber(percentageElement, currentPercentage, percentage, 800, '%');
    }
}

// 数字动画函数
function animateNumber(element, from, to, duration, suffix = '') {
    const startTime = performance.now();

    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeProgress = 1 - Math.pow(1 - progress, 3);
        const currentValue = from + (to - from) * easeProgress;

        element.textContent = `${Math.round(currentValue)}${suffix}`;

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    requestAnimationFrame(update);
}

// 账号查询功能
async function queryAccount() {
    // 优先使用Session Token
    if (augmentAccountState.token) {
        await queryAccountWithSessionToken();
    } else if (augmentAccountState.selectedAccount) {
        // 重新获取完整的账号信息（包括完整的portal_token）
        try {
            const result = await window.__TAURI__.core.invoke('get_saved_account_by_email', {
                email: augmentAccountState.selectedAccount.email
            });

            if (result.success && result.data.portal_token) {
                await queryAccountWithPortalToken(result.data.portal_token);
            } else {
                console.error('获取完整账号信息失败:', result);
                showToast('获取账号信息失败，请重新选择账号', 'error');
            }
        } catch (error) {
            console.error('获取账号信息异常:', error);
            showToast('获取账号信息异常，请重新选择账号', 'error');
        }
    } else {
        showToast('请先输入Session Token或选择已保存账号', 'error');
        showTokenDialog();
    }
}

// 使用Session Token直接查询账号信息
async function queryAccountWithSessionToken() {
    if (augmentAccountState.isQuerying) return;

    const queryBtn = document.getElementById('queryAccountBtn');
    if (!queryBtn) return;

    try {
        augmentAccountState.isQuerying = true;
        setButtonLoading(queryBtn, true);
        showToast('正在查询账号信息...', 'info');

        // 直接使用Session Token查询账号信息
        const accountResult = await window.__TAURI__.core.invoke('query_augment_account', {
            token: augmentAccountState.token
        });

        if (accountResult.success) {
            augmentAccountState.accountData = accountResult.data;
            augmentAccountState.isTokenValid = true;
            updateAccountDisplay(accountResult.data);
            switchToRefreshMode(queryBtn);
            showToast('账号信息获取成功', 'success');
            updateButtonStates();
        } else {
            if (accountResult.error === 'invalid_token') {
                showToast('Session Token无效，请重新输入', 'error');
                augmentAccountState.isTokenValid = false;
                showTokenDialog();
            } else {
                showToast('账号信息查询失败', 'error');
            }
        }
    } catch (error) {
        console.error('Session Token查询失败:', error);
        // 简化错误信息
        if (error.message && (error.message.includes('dns error') || error.message.includes('connect') || error.message.includes('network') || error.message.includes('不知道这样的主机'))) {
            showToast('网络错误', 'error');
        } else {
            showToast('查询失败', 'error');
        }
    } finally {
        augmentAccountState.isQuerying = false;
        setButtonLoading(queryBtn, false);
    }
}

// 添加账号到保存列表（获取Portal Token并保存）
async function addAccountToSaved() {
    if (!augmentAccountState.token) {
        showToast('要先输入Session Token才能进行添加保存账号', 'error');
        showTokenDialog();
        return;
    }

    if (augmentAccountState.isAddingAccount) return;

    const addBtn = document.getElementById('addAccountBtn');
    if (!addBtn) return;

    try {
        augmentAccountState.isAddingAccount = true;

        // 设置按钮加载状态
        setButtonLoading(addBtn, true);

        // 第一步：验证Session Token有效性
        showToast('正在验证Session Token...', 'info');
        const verifyResult = await window.__TAURI__.core.invoke('verify_session_token', {
            token: augmentAccountState.token
        });

        if (!verifyResult.success) {
            if (verifyResult.error === 'invalid_token') {
                showToast('Session Token无效，请重新输入', 'error');
                augmentAccountState.isTokenValid = false;
                showTokenDialog();
            } else {
                showToast('Session Token验证失败', 'error');
            }
            return;
        }

        // 第二步：获取Portal Token并保存账号
        showToast('正在获取Token并保存账号...', 'info');
        const portalResult = await window.__TAURI__.core.invoke('get_portal_token', {
            token: augmentAccountState.token
        });

        if (portalResult.success) {
            // 检查返回消息来判断是新增还是更新
            const isNewAccount = !portalResult.message || !portalResult.message.includes('已存在');



            if (portalResult.message && portalResult.message.includes('已存在')) {
                showToast('账号已存在，无需重复保存', 'info');
            } else {
                showToast('账号保存成功', 'success');
            }

            // 刷新保存的账号列表
            await loadSavedAccounts();

            // 自动点击账号查询按钮
            setTimeout(async () => {
                try {
                    await queryAccount();
                } catch (error) {
                    console.error('自动查询账号失败:', error);
                }
            }, 500); // 延迟500ms确保UI更新完成

            // 如果是新添加的账号，自动选择它
            if (isNewAccount) {
                let emailToSelect = null;

                // 优先从 accountData 获取邮箱
                if (augmentAccountState.accountData && augmentAccountState.accountData.email) {
                    emailToSelect = augmentAccountState.accountData.email;
                }
                // 如果 accountData 为空，尝试从返回消息中提取邮箱
                else if (portalResult.message) {
                    const emailMatch = portalResult.message.match(/邮箱:\s*([^\s]+)/);
                    if (emailMatch && emailMatch[1]) {
                        emailToSelect = emailMatch[1];
                    }
                }

                if (emailToSelect) {
                    // 使用静默模式自动选择，避免显示toast提示
                    await selectAccount(emailToSelect, true);
                }
            }
        } else {
            if (portalResult.error === 'invalid_token') {
                showToast('Session Token无效，请重新输入', 'error');
                augmentAccountState.isTokenValid = false;
                showTokenDialog();
            } else {
                // 检查是否为网络相关错误
                const errorMessage = portalResult.message || '';
                const isNetworkError = errorMessage.includes('timeout') ||
                                     errorMessage.includes('connect') ||
                                     errorMessage.includes('network') ||
                                     errorMessage.includes('dns') ||
                                     errorMessage.includes('HTTP') ||
                                     errorMessage.includes('connection') ||
                                     errorMessage.includes('refused') ||
                                     errorMessage.includes('unreachable') ||
                                     errorMessage.includes('不知道这样的主机');

                if (isNetworkError) {
                    showToast('连接服务器失败，请检查网络连接', 'error');
                } else {
                    showToast('账号保存失败，无法获取Token，请更换其他账号', 'error');
                }
            }
        }
    } catch (error) {
        console.error('添加账号失败:', error);
        // 检查是否为网络相关错误
        const errorMessage = error.message || error.toString() || '';
        const isNetworkError = errorMessage.includes('timeout') ||
                             errorMessage.includes('connect') ||
                             errorMessage.includes('network') ||
                             errorMessage.includes('dns') ||
                             errorMessage.includes('HTTP') ||
                             errorMessage.includes('connection') ||
                             errorMessage.includes('refused') ||
                             errorMessage.includes('unreachable') ||
                             errorMessage.includes('不知道这样的主机') ||
                             errorMessage.includes('dns error');

        if (isNetworkError) {
            showToast('连接服务器失败，请检查网络连接', 'error');
        } else {
            showToast('添加账号失败', 'error');
        }
    } finally {
        augmentAccountState.isAddingAccount = false;

        // 恢复按钮状态
        setButtonLoading(addBtn, false);
    }
}









// 清空账号信息显示
function clearAccountDisplay() {
    // 清空用户信息
    const userEmailElement = document.getElementById('userEmail');
    if (userEmailElement) {
        userEmailElement.textContent = '未登录';
        userEmailElement.className = 'data-value';
    }

    // 清空订阅信息
    const planNameElement = document.getElementById('planName');
    if (planNameElement) {
        planNameElement.textContent = '无订阅';
        planNameElement.className = 'data-value';
    }

    // 清空积分信息
    const creditsElement = document.getElementById('credits');
    if (creditsElement) {
        creditsElement.textContent = '0';
        creditsElement.className = 'data-value';
    }


}

// 更新账号信息显示（带动画）
function updateAccountDisplay(data) {
    if (!data) return;

    // 检测数据格式：Portal Token格式 vs Session Token格式
    const isPortalTokenData = data.hasOwnProperty('email') && data.hasOwnProperty('validity');

    let user, subscription, credits;

    if (isPortalTokenData) {
        // Portal Token数据格式：扁平化结构
        user = {
            email: data.email
        };
        subscription = {
            planName: data.plan_name,
            planIsExpired: data.validity === '❌ 无效',

            numberOfSeatsThisBillingCycle: null
        };
        credits = {
            // Portal Token使用usage_display字段
            usageDisplay: data.usage_display
        };

        // 处理到期时间
        if (data.end_date && data.end_date !== 'N/A') {
            subscription.trialPeriodEnd = data.end_date;
        } else if (data.end_date === 'N/A') {
            subscription.trialPeriodEnd = 'N/A';
        }
    } else {
        // Session Token数据格式：嵌套结构
        user = data.user || {};
        subscription = data.subscription || {};
        credits = data.credits || {};
    }

    // 添加淡入动画的通用函数
    function updateElementWithAnimation(element, newContent, isHtml = false) {
        if (!element) return;

        // 淡出
        element.style.transition = 'opacity 0.3s ease';
        element.style.opacity = '0';

        setTimeout(() => {
            // 更新内容
            if (isHtml) {
                element.innerHTML = newContent;
            } else {
                element.textContent = newContent;
            }

            // 淡入
            element.style.opacity = '1';
        }, 150);
    }

    // 更新邮箱
    const emailElement = document.getElementById('accountEmail');
    const emailText = user.email || '未知';
    const truncatedEmail = truncateText(emailText, 30);
    updateElementWithAnimation(emailElement, truncatedEmail);

    // 更新计划信息
    const planElement = document.getElementById('accountPlan');
    if (planElement) {
        const planName = subscription.planName || '未知';
        const planType = subscription.augmentPlanType || '';
        const planText = planType ? `${planName} (${planType})` : planName;
        updateElementWithAnimation(planElement, planText);
    }

    // 更新有效性状态
    const statusElement = document.getElementById('accountStatus');
    if (statusElement) {
        const isExpired = subscription.planIsExpired || false;
        const statusText = isExpired ? '❌ 已过期' : '✅ 有效';
        const statusColor = isExpired ? '#ef4444' : '#22c55e';

        // 淡出
        statusElement.style.transition = 'opacity 0.3s ease';
        statusElement.style.opacity = '0';

        setTimeout(() => {
            statusElement.textContent = statusText;
            statusElement.style.color = statusColor;
            statusElement.style.opacity = '1';
        }, 150);
    }

    // 更新到期时间
    const expiryElement = document.getElementById('accountExpiry');
    if (expiryElement) {
        const trialEnd = subscription.trialPeriodEnd || subscription.subscriptionEndDate || '';
        let expiryText = 'N/A';

        if (trialEnd && trialEnd !== 'N/A') {
            // 统一使用Session Token的格式：YYYY-MM-DD HH:MM
            if (trialEnd.includes('北京时间')) {
                // Portal Token返回的中文格式，需要转换为Session Token格式
                try {
                    // 解析中文格式：2025年07月19日 15:42:40 (北京时间)
                    const match = trialEnd.match(/(\d{4})年(\d{2})月(\d{2})日\s+(\d{2}):(\d{2}):(\d{2})/);
                    if (match) {
                        const [, year, month, day, hour, minute] = match;
                        expiryText = `${year}-${month}-${day} ${hour}:${minute}`;
                    } else {
                        expiryText = trialEnd;
                    }
                } catch {
                    expiryText = trialEnd;
                }
            } else {
                // Session Token返回的是ISO格式，转换为统一格式
                try {
                    const date = new Date(trialEnd);
                    expiryText = date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                } catch {
                    expiryText = trialEnd.substring(0, 16);
                }
            }
        }

        updateElementWithAnimation(expiryElement, expiryText);
    }

    // 更新查询时间
    const queryTimeElement = document.getElementById('queryTime');
    if (queryTimeElement) {
        const now = new Date();
        const queryTimeText = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        updateElementWithAnimation(queryTimeElement, queryTimeText);
    }

    // 更新积分信息
    let available, used, total, percentage;

    if (isPortalTokenData) {
        // Portal Token数据：从usage_display解析或使用原始数据
        if (data.credits_balance !== null && data.total_credits !== null) {
            available = data.credits_balance;
            total = data.total_credits;
            used = total - available;
            percentage = total > 0 ? (used / total * 100) : 0;
        } else {
            // 如果没有具体数值，显示N/A
            available = 0;
            used = 0;
            total = 0;
            percentage = 0;
        }
    } else {
        // Session Token数据：使用原有逻辑
        available = credits.usageUnitsAvailable || 0;
        used = credits.usageUnitsUsedThisBillingCycle || 0;
        total = available + used;
        percentage = total > 0 ? (used / total * 100) : 0;
    }

    const creditsUsedElement = document.getElementById('creditsUsed');
    const creditsTotalElement = document.getElementById('creditsTotal');

    // 使用动画更新积分数字
    if (isPortalTokenData && data.usage_display && data.usage_display !== 'N/A') {
        // Portal Token：解析usage_display格式，例如 "0/300 (0.0%)"
        const usageText = data.usage_display;
        const match = usageText.match(/^(\d+)\/(\d+)\s*\([\d.]+%\)$/);

        if (match) {
            // 如果能解析出数字，按Session Token格式显示
            const usedCount = match[1];
            const totalCount = match[2];
            updateElementWithAnimation(creditsUsedElement, usedCount);
            updateElementWithAnimation(creditsTotalElement, totalCount);
            // 确保显示分隔符
            const separator = document.querySelector('.credits-separator');
            if (separator) separator.style.display = 'inline';
        } else {
            // 如果格式不匹配，隐藏分隔符并显示完整字符串
            updateElementWithAnimation(creditsUsedElement, usageText);
            updateElementWithAnimation(creditsTotalElement, '');
            // 隐藏分隔符
            const separator = document.querySelector('.credits-separator');
            if (separator) separator.style.display = 'none';
        }
    } else if (isPortalTokenData) {
        // Portal Token但没有使用量数据
        updateElementWithAnimation(creditsUsedElement, '未知');
        updateElementWithAnimation(creditsTotalElement, '');
        // 隐藏分隔符
        const separator = document.querySelector('.credits-separator');
        if (separator) separator.style.display = 'none';
    } else {
        // Session Token：显示数字
        updateElementWithAnimation(creditsUsedElement, used.toString());
        updateElementWithAnimation(creditsTotalElement, total.toString());
        // 显示分隔符
        const separator = document.querySelector('.credits-separator');
        if (separator) separator.style.display = 'inline';
    }

    // 更新进度圆环
    updateCreditsProgress(percentage);
}

// 切换查询按钮为刷新模式
function switchToRefreshMode(button) {
    if (!button) return;

    button.classList.add('refresh-mode');
    const btnText = button.querySelector('.btn-text');
    if (btnText) {
        btnText.textContent = '刷新';
    }

    // 更新图标为刷新图标
    const btnIcon = button.querySelector('.btn-icon svg path');
    if (btnIcon) {
        btnIcon.setAttribute('d', 'M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z');
    }
}

// 重置查询按钮为初始状态
function resetQueryButtonToInitial(button) {
    if (!button) return;

    button.classList.remove('refresh-mode');
    const btnText = button.querySelector('.btn-text');
    if (btnText) {
        btnText.textContent = '账号查询';
    }

    // 重置图标为查询图标
    const btnIcon = button.querySelector('.btn-icon svg path');
    if (btnIcon) {
        btnIcon.setAttribute('d', 'M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z');
    }
}





// 文本截断工具函数
function truncateText(text, maxLength) {
    if (!text || typeof text !== 'string') {
        return text;
    }

    if (text.length <= maxLength) {
        return text;
    }

    return text.substring(0, maxLength) + '...';
}

// 设置按钮加载状态
function setButtonLoading(button, isLoading) {
    if (!button) return;

    const btnContent = button.querySelector('.btn-content');
    const btnLoading = button.querySelector('.btn-loading');

    if (isLoading) {
        button.classList.add('loading');
        button.disabled = true;

        // 使用CSS类控制显示，而不是直接设置style
        if (btnContent) {
            btnContent.style.opacity = '0';
            btnContent.style.visibility = 'hidden';
        }
        if (btnLoading) {
            btnLoading.classList.remove('hidden');
            btnLoading.style.opacity = '1';
            btnLoading.style.visibility = 'visible';
        }
    } else {
        button.classList.remove('loading');
        button.disabled = false;

        if (btnContent) {
            btnContent.style.opacity = '1';
            btnContent.style.visibility = 'visible';
        }
        if (btnLoading) {
            btnLoading.classList.add('hidden');
            btnLoading.style.opacity = '0';
            btnLoading.style.visibility = 'hidden';
        }
    }
}

// 初始化账号管理组件
async function initAugmentAccountManager() {
    // 防止重复初始化
    if (augmentAccountState.isInitialized) {
        return;
    }

    augmentAccountState.isInitialized = true;
    // 添加Token对话框点击外部关闭功能
    const tokenOverlay = document.getElementById('tokenDialogOverlay');
    if (tokenOverlay) {
        tokenOverlay.addEventListener('click', (e) => {
            if (e.target === tokenOverlay) {
                hideTokenDialog();
            }
        });
    }

    // 添加ESC键关闭对话框
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const tokenOverlay = document.getElementById('tokenDialogOverlay');
            if (tokenOverlay && tokenOverlay.classList.contains('show')) {
                hideTokenDialog();
            }
        }
    });

    // 添加Token输入框回车键提交功能
    const tokenInput = document.getElementById('tokenInput');
    if (tokenInput) {
        tokenInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                submitToken();
            }
        });
    }

    // 初始化进度圆环
    updateCreditsProgress(0);

    // 清空初始数据
    clearAccountData();

    // 加载保存的账号列表
    await loadSavedAccounts();

    // 恢复上次选择的账号
    await restoreSelectedAccount();

    // 初始化按钮状态
    updateButtonStates();
}

// 在页面加载完成后初始化账号管理
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保其他组件先加载
    // 但是要检查是否已经在主应用初始化中调用过了
    // 只有在窗口可见时才初始化
    setTimeout(async () => {
        if (!augmentAccountState.isInitialized) {
            // 检查窗口是否可见
            let isVisible = true;
            if (window.__TAURI__ && window.__TAURI__.window) {
                try {
                    const currentWindow = window.__TAURI__.window.getCurrentWindow();
                    isVisible = await currentWindow.isVisible();
                } catch (error) {
                    console.warn('检查窗口可见性失败:', error);
                }
            }

            // 只有窗口可见时才初始化账号管理
            if (isVisible) {
                initAugmentAccountManager();
            }
        }
    }, 500);
});

/**
 * 显示黑名单消息对话框
 * 对应原版Python的_show_blacklist_message方法
 * @param {string} qqNumber - QQ号码
 * @returns {Promise<void>}
 */
function showBlacklistDialog(qqNumber) {
    return new Promise((resolve) => {
        const dialogHTML = `
            <div class="error-dialog-overlay" id="blacklistDialogOverlay">
                <div class="error-dialog">
                    <div class="error-dialog-content">
                        <div class="error-dialog-icon">🚫</div>
                        <h3 class="error-dialog-title">访问拒绝</h3>
                        <div class="error-dialog-text">
                            您的QQ账号 ${qqNumber} 已被列入黑名单，无法使用YAugment。<br><br>
                            如有疑问，请发送电子邮件给我 <EMAIL>
                        </div>
                        <div class="error-dialog-actions">
                            <button class="error-dialog-btn" onclick="closeBlacklistDialog()">退出</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.blacklistDialogResolve = resolve;

        const overlay = document.getElementById('blacklistDialogOverlay');
        setTimeout(() => overlay.classList.add('show'), 10);
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭黑名单对话框
 */
function closeBlacklistDialog() {
    const overlay = document.getElementById('blacklistDialogOverlay');
    if (!overlay) return;

    document.body.style.overflow = '';
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.blacklistDialogResolve) {
            window.blacklistDialogResolve();
            delete window.blacklistDialogResolve;
        }
    }, 300);
}

/**
 * 显示验证错误对话框
 * 对应原版Python的_show_verification_error方法
 * @param {string} errorMessage - 错误消息
 * @returns {Promise<void>}
 */
function showVerificationErrorDialog(errorMessage) {
    return new Promise((resolve) => {
        const dialogHTML = `
            <div class="error-dialog-overlay" id="verificationErrorDialogOverlay">
                <div class="error-dialog">
                    <div class="error-dialog-content">
                        <div class="error-dialog-icon">❌</div>
                        <h3 class="error-dialog-title">验证错误</h3>
                        <div class="error-dialog-text">
                            ${errorMessage}<br><br>
                            无法完成QQ群验证，程序将退出。
                        </div>
                        <div class="error-dialog-actions">
                            <button class="error-dialog-btn" onclick="closeVerificationErrorDialog()">退出</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.verificationErrorDialogResolve = resolve;

        const overlay = document.getElementById('verificationErrorDialogOverlay');
        setTimeout(() => overlay.classList.add('show'), 10);
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭验证错误对话框
 */
function closeVerificationErrorDialog() {
    const overlay = document.getElementById('verificationErrorDialogOverlay');
    if (!overlay) return;

    document.body.style.overflow = '';
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.verificationErrorDialogResolve) {
            window.verificationErrorDialogResolve();
            delete window.verificationErrorDialogResolve;
        }
    }, 300);
}

/**
 * 显示SSL错误对话框
 * 对应原版Python的_show_ssl_error_message方法
 * @param {string} errorDetails - SSL错误详细信息
 * @returns {Promise<boolean>} - 用户是否选择继续
 */
function showSSLErrorDialog(errorDetails) {
    return new Promise((resolve) => {
        const dialogHTML = `
            <div class="error-dialog-overlay" id="sslErrorDialogOverlay">
                <div class="error-dialog">
                    <div class="error-dialog-content">
                        <div class="error-dialog-icon">⚠️</div>
                        <h3 class="error-dialog-title">SSL证书问题</h3>
                        <div class="error-dialog-text">
                            连接到验证服务器时遇到SSL证书问题：<br><br>
                            ${errorDetails}<br><br>
                            这可能是因为系统时间不正确、系统证书过期或网络环境问题。<br><br>
                            您可以选择忽略此错误并继续使用程序，但这可能存在安全风险。
                        </div>
                        <div class="error-dialog-actions">
                            <button class="error-dialog-btn secondary" onclick="closeSSLErrorDialog(false)">退出</button>
                            <button class="error-dialog-btn" onclick="closeSSLErrorDialog(true)">忽略并继续</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.sslErrorDialogResolve = resolve;

        const overlay = document.getElementById('sslErrorDialogOverlay');
        setTimeout(() => overlay.classList.add('show'), 10);
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭SSL错误对话框
 * @param {boolean} continueAnyway - 是否继续
 */
function closeSSLErrorDialog(continueAnyway) {
    const overlay = document.getElementById('sslErrorDialogOverlay');
    if (!overlay) return;

    document.body.style.overflow = '';
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.sslErrorDialogResolve) {
            window.sslErrorDialogResolve(continueAnyway);
            delete window.sslErrorDialogResolve;
        }
    }, 300);
}

/**
 * 显示严重配置错误对话框
 * 对应原版Python的_show_critical_config_error方法
 * @param {string} detailMessage - 详细错误信息
 * @returns {Promise<void>}
 */
function showCriticalConfigErrorDialog(detailMessage) {
    return new Promise((resolve) => {
        const dialogHTML = `
            <div class="error-dialog-overlay" id="criticalConfigErrorDialogOverlay">
                <div class="error-dialog">
                    <div class="error-dialog-content">
                        <div class="error-dialog-icon">💥</div>
                        <h3 class="error-dialog-title">获取的配置错误</h3>
                        <div class="error-dialog-text">
                            无法加载配置，关键信息缺失或格式不正确<br><br>
                            请更换网络或更新 YAugment 版本后重试<br><br>
                            ${detailMessage ? `详细信息：${detailMessage}` : ''}
                        </div>
                        <div class="error-dialog-actions">
                            <button class="error-dialog-btn" onclick="closeCriticalConfigErrorDialog()">退出</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dialogHTML);
        window.criticalConfigErrorDialogResolve = resolve;

        const overlay = document.getElementById('criticalConfigErrorDialogOverlay');
        setTimeout(() => overlay.classList.add('show'), 10);
        document.body.style.overflow = 'hidden';
    });
}

/**
 * 关闭严重配置错误对话框
 */
function closeCriticalConfigErrorDialog() {
    const overlay = document.getElementById('criticalConfigErrorDialogOverlay');
    if (!overlay) return;

    document.body.style.overflow = '';
    overlay.classList.remove('show');

    setTimeout(() => {
        overlay.remove();
        if (window.criticalConfigErrorDialogResolve) {
            window.criticalConfigErrorDialogResolve();
            delete window.criticalConfigErrorDialogResolve;
        }
    }, 300);
}

/**
 * 显示通知消息
 * @param {string} message - 通知消息
 * @param {string} level - 通知级别 (info, warning, error)
 */
function showNotification(message, level = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${level}`;
    notification.innerHTML = `
        <div class="notification-content">
            <div class="notification-icon">
                ${level === 'error' ? '❌' : level === 'warning' ? '⚠️' : 'ℹ️'}
            </div>
            <div class="notification-message">${message}</div>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // 自动关闭（除了错误通知）
    if (level !== 'error') {
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }
}

// ===== 网络优化器功能 =====

// 网络优化器状态
const networkOptimizerState = {
    config: {
        enabled: false,
        proxy_type: 'v2rayN',
        custom_proxy_url: null,
        auto_optimize_enabled: true,
        auto_optimize_interval: 10,
        auto_set_editor_proxy: true,
        last_optimize_time: null,
        last_optimize_api: null,
        last_optimize_latency: null
    },
    isOptimizing: false,
    hasAutoTimer: false,
    // 新增状态字段
    currentStatus: 'disabled', // disabled, enabled, testing, success, failed_permission, failed_proxy_connection, failed_proxy_error, failed_all_apis
    statusMessage: '未开启',
    testingProgress: {
        current: 0,
        total: 0,
        currentDomain: ''
    }
};

// 初始化网络优化器
async function initNetworkOptimizer() {
    try {
        // 加载配置
        await loadNetworkOptimizerConfig();

        // 初始化UI
        updateNetworkOptimizerUI();

        // 绑定事件
        bindNetworkOptimizerEvents();

        // 监听后端事件
        listenNetworkOptimizerEvents();


    } catch (error) {
        console.error('网络优化器初始化失败:', error);
    }
}

// 加载网络优化器配置
async function loadNetworkOptimizerConfig() {
    try {
        const config = await window.__TAURI__.core.invoke('get_network_optimizer_config');
        networkOptimizerState.config = { ...networkOptimizerState.config, ...config };

        // 获取状态
        const status = await window.__TAURI__.core.invoke('get_network_optimization_status');
        networkOptimizerState.isOptimizing = status.is_optimizing;
        networkOptimizerState.hasAutoTimer = status.has_auto_timer;

        // 设置初始状态
        if (networkOptimizerState.config.enabled) {
            if (networkOptimizerState.config.last_optimize_time) {
                networkOptimizerState.currentStatus = 'success';
            } else {
                networkOptimizerState.currentStatus = 'enabled';
            }
        } else {
            networkOptimizerState.currentStatus = 'disabled';
        }
    } catch (error) {
        console.error('加载网络优化器配置失败:', error);
    }
}

// 保存网络优化器配置
async function saveNetworkOptimizerConfig() {
    try {
        await window.__TAURI__.core.invoke('set_network_optimizer_config', {
            config: networkOptimizerState.config
        });
    } catch (error) {
        console.error('保存网络优化器配置失败:', error);
        throw error;
    }
}

// 更新网络优化器UI
function updateNetworkOptimizerUI() {
    const config = networkOptimizerState.config;

    // 更新主开关
    const enabledSwitch = document.getElementById('networkOptimizerEnabled');
    if (enabledSwitch) {
        enabledSwitch.checked = config.enabled;
    }

    // 更新代理类型
    const proxyTypeRadios = document.querySelectorAll('input[name="proxyType"]');
    proxyTypeRadios.forEach(radio => {
        radio.checked = radio.value === config.proxy_type;
    });

    // 更新自定义代理输入框
    const customProxyInput = document.getElementById('customProxyInput');
    const customProxyUrl = document.getElementById('customProxyUrl');
    if (customProxyInput && customProxyUrl) {
        if (config.proxy_type === 'custom') {
            customProxyInput.classList.remove('hidden');
            customProxyUrl.value = config.custom_proxy_url || '';
        } else {
            customProxyInput.classList.add('hidden');
        }
    }

    // 更新优化设置
    const autoOptimizeEnabled = document.getElementById('autoOptimizeEnabled');
    const optimizeInterval = document.getElementById('optimizeInterval');
    const autoSetEditorProxy = document.getElementById('autoSetEditorProxy');

    if (autoOptimizeEnabled) autoOptimizeEnabled.checked = config.auto_optimize_enabled;
    if (optimizeInterval) optimizeInterval.value = config.auto_optimize_interval;
    if (autoSetEditorProxy) autoSetEditorProxy.checked = config.auto_set_editor_proxy;

    // 更新状态显示
    updateNetworkOptimizerStatus();

    // 更新按钮状态
    updateNetworkOptimizerButtons();

    // 更新副标题状态
    updateNetworkOptimizerSubtitle();
}

// 更新网络优化器副标题状态
function updateNetworkOptimizerSubtitle() {
    const subtitleElement = document.querySelector('.optimizer-subtitle');
    if (!subtitleElement) return;

    const config = networkOptimizerState.config;
    let statusText = '';
    let statusColor = '#a0a0a0'; // 默认颜色

    // 根据状态设置文本和颜色
    switch (networkOptimizerState.currentStatus) {
        case 'disabled':
            statusText = '未开启';
            statusColor = '#a0a0a0';
            break;
        case 'enabled':
            statusText = '已开启';
            statusColor = '#a0a0a0';
            break;
        case 'testing':
            const progress = networkOptimizerState.testingProgress;
            if (progress.currentDomain) {
                // 隐藏域名信息，只显示API编号
                statusText = `正在测试 API${progress.current} (${progress.current}/${progress.total})`;
            } else {
                statusText = `正在测试域名速度 (${progress.current}/${progress.total})`;
            }
            statusColor = '#4CAF50';
            break;
        case 'success':
            statusText = 'API优化策略成功';
            statusColor = '#a0a0a0';
            break;
        case 'failed_permission':
            statusText = 'API优化策略失败，因为没有权限，请使用管理员模式打开YAugment后再尝试';
            statusColor = '#ff4444';
            break;
        case 'failed_proxy_connection':
            statusText = 'API优化策略失败，因为代理连接不上，请检查';
            statusColor = '#ff4444';
            break;
        case 'failed_proxy_error':
            statusText = 'API优化策略失败，因为代理异常，请检查';
            statusColor = '#ff4444';
            break;
        case 'failed_all_apis':
            statusText = 'API优化策略失败，因为全部API无法连接';
            statusColor = '#ff4444';
            break;
        default:
            statusText = '智能测速优化 API 连接速度';
            statusColor = '#a0a0a0';
    }

    subtitleElement.textContent = statusText;
    subtitleElement.style.color = statusColor;
}

// 更新网络优化器状态显示
function updateNetworkOptimizerStatus() {
    const config = networkOptimizerState.config;

    const lastOptimizeTime = document.getElementById('lastOptimizeTime');
    const selectedApi = document.getElementById('selectedApi');
    const averageLatency = document.getElementById('averageLatency');

    if (lastOptimizeTime) {
        lastOptimizeTime.textContent = config.last_optimize_time || '未优化';
    }

    if (selectedApi) {
        // 隐藏域名信息，用API编号代替
        const apiText = config.last_optimize_api || '-';
        if (apiText !== '-' && apiText.includes('.')) {
            // 如果包含域名，替换为API编号
            selectedApi.textContent = 'API1';
        } else {
            selectedApi.textContent = apiText;
        }
    }

    if (averageLatency) {
        if (config.last_optimize_latency) {
            averageLatency.textContent = `${config.last_optimize_latency.toFixed(1)}ms`;
        } else {
            averageLatency.textContent = '-';
        }
    }
}

// 更新网络优化器按钮状态
function updateNetworkOptimizerButtons() {
    const startBtn = document.getElementById('startOptimizeBtn');
    const clearBtn = document.getElementById('clearOptimizeBtn');

    if (startBtn) {
        if (networkOptimizerState.isOptimizing) {
            startBtn.classList.add('loading');
            startBtn.disabled = true;
        } else {
            startBtn.classList.remove('loading');
            startBtn.disabled = false;
        }
    }

    if (clearBtn) {
        clearBtn.disabled = networkOptimizerState.isOptimizing;
    }
}

// 绑定网络优化器事件
function bindNetworkOptimizerEvents() {
    // 主开关事件
    const enabledSwitch = document.getElementById('networkOptimizerEnabled');
    if (enabledSwitch) {
        enabledSwitch.addEventListener('change', handleNetworkOptimizerToggle);
    }

    // 代理类型选择事件
    const proxyTypeRadios = document.querySelectorAll('input[name="proxyType"]');
    proxyTypeRadios.forEach(radio => {
        radio.addEventListener('change', handleProxyTypeChange);
    });

    // 自定义代理URL输入事件
    const customProxyUrl = document.getElementById('customProxyUrl');
    if (customProxyUrl) {
        customProxyUrl.addEventListener('input', handleCustomProxyUrlChange);
    }

    // 优化设置事件
    const autoOptimizeEnabled = document.getElementById('autoOptimizeEnabled');
    const optimizeInterval = document.getElementById('optimizeInterval');
    const autoSetEditorProxy = document.getElementById('autoSetEditorProxy');

    if (autoOptimizeEnabled) {
        autoOptimizeEnabled.addEventListener('change', handleAutoOptimizeToggle);
    }

    if (optimizeInterval) {
        optimizeInterval.addEventListener('input', handleOptimizeIntervalChange);
    }

    if (autoSetEditorProxy) {
        autoSetEditorProxy.addEventListener('change', handleAutoSetEditorProxyToggle);
    }
}

// 监听网络优化器后端事件
function listenNetworkOptimizerEvents() {
    // 监听测试进度事件（静默处理）
    window.__TAURI__.event.listen('network_test_progress', (event) => {
        // 静默处理，不输出调试信息
    });

    // 监听域名测试完成事件（静默处理，不输出调试信息）
    window.__TAURI__.event.listen('network_test_domain_complete', (event) => {
        // 静默处理，不输出域名信息
    });

    // 监听域名测试开始事件
    window.__TAURI__.event.listen('network_test_started', (event) => {
        const data = event.payload;
        networkOptimizerState.currentStatus = 'testing';
        networkOptimizerState.testingProgress = {
            current: 0,
            total: data.total_domains || 20,
            currentDomain: ''
        };
        updateNetworkOptimizerSubtitle();
    });

    // 监听单个域名测试完成事件
    window.__TAURI__.event.listen('network_test_domain_complete', (event) => {
        const data = event.payload;
        networkOptimizerState.testingProgress.current++;
        networkOptimizerState.testingProgress.currentDomain = data.domain;
        updateNetworkOptimizerSubtitle();
    });

    // 监听自动优化触发事件
    window.__TAURI__.event.listen('auto_optimization_trigger', async (event) => {
        const data = event.payload;
        console.log('收到定时优化触发事件:', data);

        // 防止重复执行
        if (networkOptimizerState.isOptimizing) {
            console.log('网络优化正在进行中，跳过定时优化');
            return;
        }

        // 执行优化并更新前端状态
        try {
            await executeNetworkOptimizationInternal(
                data.proxy_type,
                data.custom_proxy_url,
                data.auto_set_editor_proxy
            );

            // 定时优化完成，状态已更新，无需toast通知
        } catch (error) {
            console.error('定时优化失败:', error);
            showToast(`定时优化失败: ${error.message}`, 'error', 6000);
        }
    });

    // 监听自动优化完成事件（保留兼容性）
    window.__TAURI__.event.listen('auto_optimization_completed', () => {
        // 移除toast通知，状态已经显示了
        // 重新加载配置以更新状态
        loadNetworkOptimizerConfig().then(() => {
            updateNetworkOptimizerStatus();
        });
    });
}

// 处理网络优化器主开关切换
async function handleNetworkOptimizerToggle(event) {
    const enabled = event.target.checked;

    if (enabled) {
        // 显示提醒对话框
        showNetworkOptimizerWarning();
        // 暂时取消选中，等用户确认后再启用
        event.target.checked = false;
    } else {
        // 直接关闭
        await disableNetworkOptimizer();
    }
}

// 显示网络优化提醒对话框
function showNetworkOptimizerWarning() {
    const overlay = document.getElementById('networkOptimizerWarningOverlay');
    if (overlay) {
        overlay.classList.add('show');
    }
}

// 隐藏网络优化提醒对话框
function hideNetworkOptimizerWarning() {
    const overlay = document.getElementById('networkOptimizerWarningOverlay');
    if (overlay) {
        overlay.classList.remove('show');
    }
}

// 确认开启网络优化
async function confirmNetworkOptimization() {
    hideNetworkOptimizerWarning();
    try {
        await enableNetworkOptimizer();
    } catch (error) {
        console.error('确认开启网络优化失败:', error);
        // 确保错误被正确处理，不显示成功消息
    }
}

// 启用网络优化器
async function enableNetworkOptimizer() {
    try {
        // 先检查管理员权限
        const hasAdminPrivileges = await window.__TAURI__.core.invoke('check_admin_privileges_cmd');
        if (!hasAdminPrivileges) {
            networkOptimizerState.currentStatus = 'failed_permission';
            updateNetworkOptimizerSubtitle();
            showToast('需要管理员权限才能启用网络优化策略', 'error');

            // 重置开关状态
            const enabledSwitch = document.getElementById('networkOptimizerEnabled');
            if (enabledSwitch) {
                enabledSwitch.checked = false;
            }
            return;
        }

        networkOptimizerState.config.enabled = true;
        networkOptimizerState.currentStatus = 'enabled';
        await saveNetworkOptimizerConfig();

        // 更新UI
        const enabledSwitch = document.getElementById('networkOptimizerEnabled');
        if (enabledSwitch) {
            enabledSwitch.checked = true;
        }
        updateNetworkOptimizerSubtitle();

        // 立即执行一次网络优化
        await executeNetworkOptimizationInternal(
            networkOptimizerState.config.proxy_type,
            networkOptimizerState.config.custom_proxy_url,
            networkOptimizerState.config.auto_set_editor_proxy
        );

        // 只有网络优化真正成功后才执行后续操作
        // 如果启用了定时优化，重新启动定时器（重置计时）
        if (networkOptimizerState.config.auto_optimize_enabled) {
            // 先停止现有定时器，再启动新的定时器，这样可以重置计时
            await stopAutoOptimization();
            await startAutoOptimization();
        }

        // 只有所有操作都成功后才显示成功消息
        showToast('网络优化策略已启用并完成首次优化', 'success');
    } catch (error) {
        console.error('启用网络优化器失败:', error);

        // 重置开关状态
        networkOptimizerState.config.enabled = false;

        // 只有在没有具体错误状态时才设置为disabled
        // 保持executeNetworkOptimizationInternal中设置的具体错误状态
        if (networkOptimizerState.currentStatus === 'enabled') {
            networkOptimizerState.currentStatus = 'disabled';
        }

        const enabledSwitch = document.getElementById('networkOptimizerEnabled');
        if (enabledSwitch) {
            enabledSwitch.checked = false;
        }
        updateNetworkOptimizerSubtitle();

        // 如果不是权限错误，显示通用错误信息
        if (!error.message || !error.message.includes('管理员权限')) {
            showToast('启用网络优化器失败', 'error');
        }
    }
}

// 禁用网络优化器
async function disableNetworkOptimizer() {
    try {
        networkOptimizerState.config.enabled = false;
        networkOptimizerState.currentStatus = 'disabled';
        await saveNetworkOptimizerConfig();

        // 停止定时优化
        await stopAutoOptimization();

        // 自动清除网络优化策略
        await clearNetworkOptimizationInternal();

        updateNetworkOptimizerSubtitle();
        showToast('网络优化策略已关闭并清除', 'info');
    } catch (error) {
        console.error('禁用网络优化器失败:', error);
        showToast('禁用网络优化器失败', 'error');
    }
}

// 处理代理类型变更
async function handleProxyTypeChange(event) {
    const proxyType = event.target.value;
    networkOptimizerState.config.proxy_type = proxyType;

    // 显示/隐藏自定义代理输入框
    const customProxyInput = document.getElementById('customProxyInput');
    if (customProxyInput) {
        if (proxyType === 'custom') {
            customProxyInput.classList.remove('hidden');
        } else {
            customProxyInput.classList.add('hidden');
        }
    }

    try {
        await saveNetworkOptimizerConfig();
    } catch (error) {
        console.error('保存代理类型失败:', error);
    }
}

// 处理自定义代理URL变更
async function handleCustomProxyUrlChange(event) {
    const customProxyUrl = event.target.value.trim();
    networkOptimizerState.config.custom_proxy_url = customProxyUrl || null;

    try {
        await saveNetworkOptimizerConfig();
    } catch (error) {
        console.error('保存自定义代理URL失败:', error);
    }
}

// 处理定时优化开关切换
async function handleAutoOptimizeToggle(event) {
    const enabled = event.target.checked;
    networkOptimizerState.config.auto_optimize_enabled = enabled;

    try {
        await saveNetworkOptimizerConfig();

        if (networkOptimizerState.config.enabled) {
            if (enabled) {
                await startAutoOptimization();
            } else {
                await stopAutoOptimization();
            }
        }
    } catch (error) {
        console.error('切换定时优化失败:', error);
        showToast('切换定时优化失败', 'error');
    }
}

// 处理优化间隔变更
async function handleOptimizeIntervalChange(event) {
    const interval = parseInt(event.target.value);
    if (interval >= 1 && interval <= 1440) {
        networkOptimizerState.config.auto_optimize_interval = interval;

        try {
            await saveNetworkOptimizerConfig();

            // 如果定时优化已启用，重新启动定时器
            if (networkOptimizerState.config.enabled && networkOptimizerState.config.auto_optimize_enabled) {
                await startAutoOptimization();
            }
        } catch (error) {
            console.error('保存优化间隔失败:', error);
        }
    }
}

// 处理自动设置编辑器代理开关切换
async function handleAutoSetEditorProxyToggle(event) {
    const enabled = event.target.checked;
    networkOptimizerState.config.auto_set_editor_proxy = enabled;

    try {
        await saveNetworkOptimizerConfig();
    } catch (error) {
        console.error('保存自动设置编辑器代理配置失败:', error);
    }
}

// 启动定时优化
async function startAutoOptimization() {
    try {
        await window.__TAURI__.core.invoke('start_auto_optimization', {
            intervalMinutes: networkOptimizerState.config.auto_optimize_interval,
            proxyType: networkOptimizerState.config.proxy_type,
            customProxyUrl: networkOptimizerState.config.custom_proxy_url,
            autoSetEditorProxy: networkOptimizerState.config.auto_set_editor_proxy
        });

        networkOptimizerState.hasAutoTimer = true;
        // 不显示toast，因为在启用时已经显示了总体成功信息
    } catch (error) {
        console.error('启动定时优化失败:', error);
        showToast('启动定时优化失败', 'error');
    }
}

// 停止定时优化
async function stopAutoOptimization() {
    try {
        await window.__TAURI__.core.invoke('stop_auto_optimization');
        networkOptimizerState.hasAutoTimer = false;
    } catch (error) {
        console.error('停止定时优化失败:', error);
    }
}

// 开始网络优化 (保留以防需要)
async function startNetworkOptimization() {
    if (!networkOptimizerState.config.enabled) {
        showToast('请先启用网络优化策略', 'warning');
        return;
    }

    await executeNetworkOptimizationInternal(
        networkOptimizerState.config.proxy_type,
        networkOptimizerState.config.custom_proxy_url,
        networkOptimizerState.config.auto_set_editor_proxy
    );
}

// 执行网络优化的内部实现
async function executeNetworkOptimizationInternal(proxyType, customProxyUrl, autoSetEditorProxy) {
    if (networkOptimizerState.isOptimizing) {
        console.log('网络优化正在进行中，跳过重复执行');
        return;
    }

    try {
        networkOptimizerState.isOptimizing = true;
        networkOptimizerState.currentStatus = 'enabled';
        updateNetworkOptimizerSubtitle();
        updateNetworkOptimizerButtons();

        // 检查curl可用性
        const curlAvailable = await window.__TAURI__.core.invoke('check_curl_available');
        if (!curlAvailable) {
            throw new Error('curl命令不可用，请确保系统已安装curl');
        }

        // 检查管理员权限
        const hasAdminPrivileges = await window.__TAURI__.core.invoke('check_admin_privileges_cmd');
        if (!hasAdminPrivileges) {
            networkOptimizerState.currentStatus = 'failed_permission';
            updateNetworkOptimizerSubtitle();

            // 如果是启用时的首次优化失败，需要禁用网络优化器
            if (networkOptimizerState.config.enabled) {
                networkOptimizerState.config.enabled = false;
                await saveNetworkOptimizerConfig();

                const enabledSwitch = document.getElementById('networkOptimizerEnabled');
                if (enabledSwitch) {
                    enabledSwitch.checked = false;
                }
            }

            throw new Error('需要管理员权限才能应用网络优化策略');
        }

        // 测试代理连接（如果不是Tun模式）
        if (proxyType !== 'tun') {
            try {
                const proxyConnected = await window.__TAURI__.core.invoke('test_proxy_connection', {
                    proxyType: proxyType,
                    customProxyUrl: customProxyUrl
                });

                if (!proxyConnected) {
                    networkOptimizerState.currentStatus = 'failed_proxy_connection';
                    updateNetworkOptimizerSubtitle();

                    // 如果是启用时的首次优化失败，需要禁用网络优化器
                    if (networkOptimizerState.config.enabled) {
                        networkOptimizerState.config.enabled = false;
                        await saveNetworkOptimizerConfig();

                        const enabledSwitch = document.getElementById('networkOptimizerEnabled');
                        if (enabledSwitch) {
                            enabledSwitch.checked = false;
                        }
                    }

                    throw new Error('代理连接失败，请检查代理设置');
                }
            } catch (proxyError) {
                // 区分代理连接失败和代理异常
                if (proxyError.message && proxyError.message.includes('异常')) {
                    networkOptimizerState.currentStatus = 'failed_proxy_error';
                } else {
                    networkOptimizerState.currentStatus = 'failed_proxy_connection';
                }
                updateNetworkOptimizerSubtitle();

                // 如果是启用时的首次优化失败，需要禁用网络优化器
                if (networkOptimizerState.config.enabled) {
                    networkOptimizerState.config.enabled = false;
                    await saveNetworkOptimizerConfig();

                    const enabledSwitch = document.getElementById('networkOptimizerEnabled');
                    if (enabledSwitch) {
                        enabledSwitch.checked = false;
                    }
                }

                throw proxyError;
            }
        }

        // 执行网络优化
        const result = await window.__TAURI__.core.invoke('execute_network_optimization', {
            proxyType: proxyType,
            customProxyUrl: customProxyUrl,
            autoSetEditorProxy: autoSetEditorProxy
        });

        if (result.success) {
            networkOptimizerState.currentStatus = 'success';
            updateNetworkOptimizerSubtitle();

            // 处理编辑器代理设置结果
            if (result.editor_proxy_result) {
                const proxyResult = result.editor_proxy_result;
                if (!proxyResult.success) {
                    // 编辑器代理设置失败，显示友好提示
                    const editorDisplayName = getEditorDisplayName(proxyResult.editor_type);
                    showToast(`无法自动跟随设置 ${editorDisplayName} 的代理，请您手动设置`, 'warning', 5000);
                }
            }

            // 重新加载配置以更新状态显示
            await loadNetworkOptimizerConfig();
            updateNetworkOptimizerStatus();
        } else {
            // 检查是否是所有API都无法连接的错误
            if (result.message && result.message.includes('所有域名测试失败')) {
                networkOptimizerState.currentStatus = 'failed_all_apis';
            } else {
                networkOptimizerState.currentStatus = 'failed_all_apis'; // 默认为API连接失败
            }
            updateNetworkOptimizerSubtitle();

            // 如果是启用时的首次优化失败，需要禁用网络优化器（像权限失败一样处理）
            if (networkOptimizerState.config.enabled) {
                networkOptimizerState.config.enabled = false;
                await saveNetworkOptimizerConfig();

                const enabledSwitch = document.getElementById('networkOptimizerEnabled');
                if (enabledSwitch) {
                    enabledSwitch.checked = false;
                }
            }

            throw new Error(result.message || '网络优化失败');
        }

    } catch (error) {
        console.error('网络优化失败:', error);

        // 如果还没有设置具体的错误状态，根据错误信息判断
        if (networkOptimizerState.currentStatus === 'enabled') {
            if (error.message && error.message.includes('管理员权限')) {
                networkOptimizerState.currentStatus = 'failed_permission';
            } else if (error.message && error.message.includes('代理')) {
                if (error.message.includes('异常')) {
                    networkOptimizerState.currentStatus = 'failed_proxy_error';
                } else {
                    networkOptimizerState.currentStatus = 'failed_proxy_connection';
                }
            } else {
                networkOptimizerState.currentStatus = 'failed_all_apis';
            }
            updateNetworkOptimizerSubtitle();

            // 对于所有失败情况，都需要重置开关状态（除了权限失败，因为已经在前面处理了）
            if (networkOptimizerState.config.enabled && !error.message.includes('管理员权限')) {
                networkOptimizerState.config.enabled = false;
                await saveNetworkOptimizerConfig();

                const enabledSwitch = document.getElementById('networkOptimizerEnabled');
                if (enabledSwitch) {
                    enabledSwitch.checked = false;
                }
            }
        }

        showToast(error.message || '网络优化失败', 'error');

        // 重新抛出异常，确保调用方知道操作失败
        throw error;
    } finally {
        networkOptimizerState.isOptimizing = false;
        updateNetworkOptimizerButtons();
    }
}

// 清除网络优化的内部实现
async function clearNetworkOptimizationInternal() {
    try {
        const success = await window.__TAURI__.core.invoke('clear_network_optimization', {
            autoRemoveEditorProxy: networkOptimizerState.config.auto_set_editor_proxy
        });

        if (success) {
            // 清除状态显示
            networkOptimizerState.config.last_optimize_time = null;
            networkOptimizerState.config.last_optimize_api = null;
            networkOptimizerState.config.last_optimize_latency = null;

            await saveNetworkOptimizerConfig();
            updateNetworkOptimizerStatus();
        } else {
            throw new Error('清除网络优化策略失败');
        }

    } catch (error) {
        console.error('清除网络优化失败:', error);
        throw error;
    }
}

// 清除网络优化 (按钮点击) - 保留以防需要
async function clearNetworkOptimization() {
    if (networkOptimizerState.isOptimizing) {
        showToast('网络优化正在进行中，请稍后再试', 'warning');
        return;
    }

    try {
        showToast('正在清除网络优化策略...', 'info');
        await clearNetworkOptimizationInternal();
        showToast('网络优化策略已清除', 'success');
    } catch (error) {
        showToast(error.message || '清除网络优化失败', 'error');
    }
}

// 在页面初始化时调用网络优化器初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他组件已加载
    setTimeout(() => {
        initNetworkOptimizer();
    }, 1000);
});

// ============================================================================
// 切换社区计划功能
// ============================================================================

// 切换社区计划对话框管理
function showSwitchPlanDialog() {
    const overlay = document.getElementById('switchPlanDialogOverlay');
    if (overlay) {
        overlay.classList.add('show');
    }
}

function hideSwitchPlanDialog() {
    const overlay = document.getElementById('switchPlanDialogOverlay');
    if (overlay) {
        overlay.classList.remove('show');
    }
}

// 切换到社区计划主函数
async function switchToCommunityPlan() {
    // 检查是否有Session Token
    if (!augmentAccountState.token) {
        showToast('请先输入Session Token', 'error');
        showTokenDialog();
        return;
    }

    // 显示确认对话框
    showSwitchPlanDialog();
}

// 确认切换到社区计划
async function confirmSwitchToCommunityPlan() {
    // 隐藏对话框
    hideSwitchPlanDialog();

    // 显示加载状态
    const switchBtn = document.getElementById('switchPlanBtn');
    if (switchBtn) {
        setButtonLoading(switchBtn, true);
    }

    try {
        showToast('正在切换到社区计划...', 'info');

        // 调用后端API
        const result = await invoke('switch_to_community_plan', {
            sessionToken: augmentAccountState.token
        });

        if (result.success) {
            showToast('成功切换到社区计划！', 'success');

            // 恢复按钮状态
            if (switchBtn) {
                setButtonLoading(switchBtn, false);
            }

            // 自动刷新账号信息 - 后端已保证数据完整性
            try {
                if (augmentAccountState.token) {
                    await queryAccountWithSessionToken();
                } else {
                    await queryAccount();
                }
            } catch (error) {
                console.error('自动刷新失败:', error);
                showToast('自动刷新失败，请手动点击刷新按钮', 'warning');
            }

            return; // 成功时直接返回，避免执行finally块
        } else {
            const errorMessage = result.message || '切换失败';
            if (result.error === 'invalid_token') {
                showToast('Session Token无效，请重新输入', 'error');
                augmentAccountState.isTokenValid = false;
                showTokenDialog();
            } else {
                showToast(`切换失败: ${errorMessage}`, 'error');
            }
        }
    } catch (error) {
        console.error('切换社区计划失败:', error);
        showToast('切换失败，请检查网络连接', 'error');
    } finally {
        // 恢复按钮状态
        if (switchBtn) {
            setButtonLoading(switchBtn, false);
        }
        // 使用updateButtonStates来正确设置按钮状态
        updateButtonStates();
    }
}

// 添加对话框点击外部关闭功能
document.addEventListener('DOMContentLoaded', function() {
    const switchPlanOverlay = document.getElementById('switchPlanDialogOverlay');
    if (switchPlanOverlay) {
        switchPlanOverlay.addEventListener('click', (e) => {
            if (e.target === switchPlanOverlay) {
                hideSwitchPlanDialog();
            }
        });
    }

    // 添加ESC键关闭对话框
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            const switchPlanOverlay = document.getElementById('switchPlanDialogOverlay');
            if (switchPlanOverlay && switchPlanOverlay.classList.contains('show')) {
                hideSwitchPlanDialog();
            }
        }
    });
});
