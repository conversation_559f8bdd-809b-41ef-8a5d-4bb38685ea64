use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::Mutex as TokioMutex;
use tokio::time::{sleep, Duration};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use notify_rust::Notification;

use crate::app_state::{AppState, ExpiryStatus};
use crate::core::version::VersionChecker;

/// 时效检查器
/// 负责全局的认证时效检查和倒计时
pub struct ExpiryChecker;

impl ExpiryChecker {
    /// 启动时效检查任务
    /// 在验证成功后调用，开始全局倒计时
    pub async fn start_expiry_check(
        app_handle: AppHandle,
        verification_method: String,
        duration_seconds: u64,
    ) -> Result<(), String> {
        let state = app_handle.state::<AppState>();
        
        // 计算过期时间戳
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        let expiry_timestamp = current_time + duration_seconds;
        
        // 更新时效状态
        {
            let mut expiry_status = state.expiry_status.lock().await;
            *expiry_status = ExpiryStatus {
                enabled: true,
                expiry_timestamp,
                verification_method,
                remaining_seconds: duration_seconds,
            };
        }
        
        // 取消之前的任务（如果存在）
        {
            let mut task_guard = state.expiry_check_task.lock().await;
            if let Some(task) = task_guard.take() {
                task.abort();
            }
        }
        
        // 启动新的检查任务
        let app_handle_clone = app_handle.clone();
        let expiry_status_clone = Arc::clone(&state.expiry_status);
        let expiry_task_clone = Arc::clone(&state.expiry_check_task);
        
        let task = tokio::spawn(async move {
            Self::expiry_check_loop(app_handle_clone, expiry_status_clone, expiry_task_clone).await;
        });
        
        // 保存任务句柄
        {
            let mut task_guard = state.expiry_check_task.lock().await;
            *task_guard = Some(task);
        }
        
        // 计算精确的时效时间（小时、分钟、秒）
        let total_seconds = duration_seconds;
        let hours = total_seconds / 3600;
        let minutes = (total_seconds % 3600) / 60;
        let seconds = total_seconds % 60;

        if hours > 0 {
            if minutes > 0 || seconds > 0 {
                println!("✅ 时效检查已启动，有效期: {}小时{}分钟{}秒", hours, minutes, seconds);
            } else {
                println!("✅ 时效检查已启动，有效期: {}小时", hours);
            }
        } else if minutes > 0 {
            if seconds > 0 {
                println!("✅ 时效检查已启动，有效期: {}分钟{}秒", minutes, seconds);
            } else {
                println!("✅ 时效检查已启动，有效期: {}分钟", minutes);
            }
        } else {
            println!("✅ 时效检查已启动，有效期: {}秒", seconds);
        }
        Ok(())
    }
    
    /// 停止时效检查任务（仅限应用关闭时清理资源）
    /// 🔒 安全限制：除了应用关闭时的资源清理，其他任何情况都不允许停止时效检查
    pub async fn stop_expiry_check_for_cleanup(state: &AppState) {
        // 取消任务
        {
            let mut task_guard = state.expiry_check_task.lock().await;
            if let Some(task) = task_guard.take() {
                task.abort();
            }
        }

        // 重置状态
        {
            let mut expiry_status = state.expiry_status.lock().await;
            *expiry_status = ExpiryStatus::default();
        }

        println!("⏹️ 时效检查已停止（应用关闭清理）");
    }

    /// 停止时效检查任务（已禁用）
    /// 🚫 此方法已被禁用，时效检查一旦启动就不会被停止，确保时效控制的严格性
    #[deprecated(note = "时效检查不允许被停止，除非应用关闭")]
    pub async fn stop_expiry_check(_state: &AppState) {
        println!("🚫 时效检查停止请求被拒绝 - 时效检查不允许被停止");
        // 不执行任何停止操作，确保时效检查持续运行
    }
    
    /// 时效检查循环
    /// 每秒检查一次，倒计时到期时强制退出应用
    async fn expiry_check_loop(
        app_handle: AppHandle,
        expiry_status: Arc<TokioMutex<ExpiryStatus>>,
        _task_handle: Arc<TokioMutex<Option<tokio::task::JoinHandle<()>>>>,
    ) {
        loop {
            // 等待1秒
            sleep(Duration::from_secs(1)).await;
            
            let should_exit = {
                let mut status = expiry_status.lock().await;
                
                if !status.enabled {
                    break; // 时效检查已禁用，退出循环
                }
                
                let current_time = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                
                if current_time >= status.expiry_timestamp {
                    // 时效已过期
                    true
                } else {
                    // 更新剩余时间
                    status.remaining_seconds = status.expiry_timestamp - current_time;

                    // 🗑️ 删除警告通知：不再显示即将过期的警告

                    false
                }
            };
            
            if should_exit {
                // 时效过期，强制退出应用
                Self::handle_expiry(app_handle).await;
                break;
            }
        }
    }
    
    /// 处理时效过期
    /// 显示系统原生通知并强制退出应用
    async fn handle_expiry(
        #[cfg(target_os = "macos")] app_handle: AppHandle,
        #[cfg(not(target_os = "macos"))] _app_handle: AppHandle,
    ) {
        println!("❌ YAugment 认证时效已过期，应用即将退出");

        // 显示系统原生通知
        if let Err(e) = Self::show_native_expiry_notification().await {
            eprintln!("显示系统通知失败: {}", e);
        }

        // Mac平台优化：给用户更多时间看到通知，并使用更优雅的退出方式
        #[cfg(target_os = "macos")]
        {
            // 等待3秒让用户看到通知
            tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;

            // 使用优雅退出
            app_handle.exit(0);
        }

        // 其他平台使用原有逻辑
        #[cfg(not(target_os = "macos"))]
        {
            std::process::exit(0);
        }
    }
    
    /// 显示系统原生过期通知
    /// 支持 Windows/macOS/Linux 跨平台系统通知
    async fn show_native_expiry_notification() -> Result<(), String> {
        // Mac平台优化的通知设置
        #[cfg(target_os = "macos")]
        let notification_result = {
            println!("Mac平台：尝试显示系统通知");
            Notification::new()
                .summary("YAugment 认证时效已过期")
                .body("您的 YAugment 认证时效已过期，请重新启动软件进行认证后才能使用")
                .appname("YAugment") // 设置应用名称
                .timeout(0) // 不自动消失
                .show()
        };

        // 其他平台使用原有设置
        #[cfg(not(target_os = "macos"))]
        let notification_result = Notification::new()
            .summary("YAugment 认证时效已过期")
            .body("您的 YAugment 认证时效已过期，请重新启动软件进行认证后才能使用")
            .icon("dialog-warning") // 使用系统警告图标
            .timeout(0) // 不自动消失，需要用户手动关闭
            .show();

        match notification_result {
            Ok(_) => {
                println!("✅ 系统通知已显示：认证时效已过期");

                // Mac平台额外的通知确认
                #[cfg(target_os = "macos")]
                {
                    println!("Mac平台：系统通知已发送到通知中心");
                    // 尝试使用系统命令显示额外的通知
                    if let Err(e) = Self::show_mac_fallback_notification().await {
                        println!("Mac备用通知失败: {}", e);
                    }
                }

                Ok(())
            }
            Err(e) => {
                eprintln!("❌ 系统通知显示失败: {}", e);

                // Mac平台使用备用通知方案
                #[cfg(target_os = "macos")]
                {
                    println!("Mac平台：尝试备用通知方案");
                    if let Ok(_) = Self::show_mac_fallback_notification().await {
                        println!("✅ Mac备用通知显示成功");
                        return Ok(());
                    }
                }

                // 如果系统通知失败，回退到控制台输出
                let separator = "=".repeat(60);
                println!("{}", separator);
                println!("🚨 YAugment 时效过期");
                println!("您的 YAugment 认证时效已过期，请重新启动软件进行认证后才能使用");
                println!("{}", separator);

                Err(format!("系统通知失败: {}", e))
            }
        }
    }

    /// Mac平台备用通知方案
    /// 使用系统的osascript命令显示对话框
    #[cfg(target_os = "macos")]
    async fn show_mac_fallback_notification() -> Result<(), String> {
        use std::process::Command;

        let script = r#"
        display dialog "您的 YAugment 认证时效已过期，请重新启动软件进行认证后才能使用" with title "YAugment 认证时效已过期" buttons {"确定"} default button "确定" with icon caution
        "#;

        let output = Command::new("osascript")
            .arg("-e")
            .arg(script)
            .output()
            .map_err(|e| format!("执行osascript失败: {}", e))?;

        if output.status.success() {
            println!("Mac备用通知（AppleScript对话框）显示成功");
            Ok(())
        } else {
            let error = String::from_utf8_lossy(&output.stderr);
            Err(format!("AppleScript对话框显示失败: {}", error))
        }
    }

    /// 获取当前时效状态
    pub async fn get_expiry_status(state: &AppState) -> ExpiryStatus {
        let status = state.expiry_status.lock().await;
        status.clone()
    }
    
    /// 从验证数据中启动时效检查
    /// 在应用启动时检查现有的验证状态并启动倒计时
    pub async fn start_from_verification_data(
        app_handle: AppHandle,
        version_checker: &VersionChecker,
    ) -> Result<(), String> {
        // 加载验证数据
        let verification_data = version_checker.load_verification_data()?;
        
        if let Some(data) = verification_data {
            let current_time = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs();
            
            // 检查验证码验证
            if data.source == "code_verification_success" && version_checker.config.verification.code_verification_enabled {
                let expiration_time = data.timestamp + (version_checker.config.verification.code_verification_duration_hours as u64 * 3600);
                if current_time < expiration_time {
                    let remaining_seconds = expiration_time - current_time;
                    Self::start_expiry_check(
                        app_handle,
                        "code_verification".to_string(),
                        remaining_seconds,
                    ).await?;
                    return Ok(());
                }
            }
            
            // 检查VIP QQ验证
            if data.source == "vip_qq_verification_success" && version_checker.config.verification.vip_qq_verification_enabled {
                if let Some(qq_number) = &data.qq_number {
                    // 🔧 修复：添加VIP QQ白名单检查，与版本验证逻辑保持一致
                    if version_checker.vip_qq_whitelist.contains(qq_number) {
                        let expiration_time = data.timestamp + (version_checker.config.verification.vip_qq_duration_hours as u64 * 3600);
                        if current_time < expiration_time {
                            let remaining_seconds = expiration_time - current_time;
                            Self::start_expiry_check(
                                app_handle,
                                "vip_qq_verification".to_string(),
                                remaining_seconds,
                            ).await?;
                            return Ok(());
                        }
                    }
                }
            }
        }
        
        Ok(())
    }
}
