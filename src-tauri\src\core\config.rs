use serde_json::Value;
use std::path::PathBuf;
use anyhow::Result;
use dirs;

// 编译时内嵌版本配置文件 - 敏感信息不暴露给用户
#[allow(dead_code)]
const VERSION_CONFIG_TOML: &str = include_str!("../../version_config.toml");

/// 配置管理器
/// 对应原版 Python 的 ConfigManager 类
#[derive(Debug)]
pub struct ConfigManager {
    pub config: Value,
    #[allow(dead_code)]
    config_dir: PathBuf,
    config_file: PathBuf,
    default_config: Value,
}

// 配置结构现在使用 serde_json::Value 来保持与原版 Python 的完全兼容性
// 这样可以确保配置文件格式完全一致，支持动态字段和嵌套结构

impl ConfigManager {
    /// 创建新的配置管理器
    /// 对应原版 Python 的 __init__ 方法
    pub fn new() -> Result<Self> {
        let config_dir = Self::get_config_dir()?;
        let config_file = config_dir.join("settings.json");
        let default_config = Self::get_default_config();
        let config = Self::load_config_from_file(&config_file, &default_config)?;

        Ok(Self {
            config,
            config_dir,
            config_file,
            default_config,
        })
    }

    /// 获取配置目录路径
    /// 对应原版 Python 的 _get_config_dir 方法
    fn get_config_dir() -> Result<PathBuf> {
        let config_dir = if cfg!(target_os = "windows") {
            // Windows: %APPDATA%/YAugment/config
            dirs::data_dir()
                .ok_or_else(|| anyhow::anyhow!("无法获取 Windows 数据目录"))?
                .join("YAugment")
                .join("config")
        } else if cfg!(target_os = "macos") {
            // macOS: ~/Library/Application Support/YAugment/config
            dirs::data_dir()
                .ok_or_else(|| anyhow::anyhow!("无法获取 macOS 数据目录"))?
                .join("YAugment")
                .join("config")
        } else {
            // Linux: ~/.config/YAugment/config
            dirs::config_dir()
                .ok_or_else(|| anyhow::anyhow!("无法获取 Linux 配置目录"))?
                .join("YAugment")
                .join("config")
        };

        // 创建目录（如果不存在）
        std::fs::create_dir_all(&config_dir)?;
        Ok(config_dir)
    }

    /// 获取默认配置
    /// 对应原版 Python 的 _get_default_config 方法
    fn get_default_config() -> Value {
        serde_json::json!({
            "editor_type": null,
            "email": {
                "domain": "",
                "use_temp_mail": true,
                "temp_mail": {
                    "email": "",
                    "pin": ""
                },
                "imap": {
                    "server": "imap.qq.com",
                    "port": 993,
                    "user": "",
                    "password": "",
                    "folder": "INBOX"
                },
                "generation": {
                    "username_length": 9,
                    "include_numbers": true,
                    "number_probability": 0.7,
                    "min_numbers": 1,
                    "max_numbers": 3,
                    "no_digit_prefix_length": 3,
                    "include_uppercase": false,
                    "uppercase_probability": 0.3,
                    "min_uppercase": 1,
                    "max_uppercase": 2
                },
                "retry": {
                    "max_retries": 30,
                    "retry_interval": 1
                },
                "auto_copy": true
            },
            "ui": {
                "theme": "dark_purple",
                "animations_enabled": true,
                "sound_enabled": false
            },
            "network_optimizer": {
                "enabled": false,
                "proxy_type": "v2rayN",
                "custom_proxy_url": null,
                "auto_optimize_enabled": true,
                "auto_optimize_interval": 10,
                "auto_set_editor_proxy": true,
                "last_optimize_time": null,
                "last_optimize_api": null,
                "last_optimize_latency": null
            }
        })
    }

    /// 从文件加载配置
    /// 对应原版 Python 的 load_config 方法
    fn load_config_from_file(config_file: &PathBuf, default_config: &Value) -> Result<Value> {
        if config_file.exists() {
            match std::fs::read_to_string(config_file) {
                Ok(content) => {
                    match serde_json::from_str::<Value>(&content) {
                        Ok(loaded_config) => {
                            // 合并默认配置和加载的配置
                            Ok(Self::merge_configs(default_config.clone(), loaded_config))
                        }
                        Err(e) => {
                            eprintln!("加载配置文件失败: {}", e);
                            Ok(default_config.clone())
                        }
                    }
                }
                Err(e) => {
                    eprintln!("读取配置文件失败: {}", e);
                    Ok(default_config.clone())
                }
            }
        } else {
            // 配置文件不存在，创建默认配置文件
            println!("配置文件不存在，创建默认配置文件: {:?}", config_file);
            match std::fs::write(
                config_file,
                serde_json::to_string_pretty(default_config).unwrap_or_default(),
            ) {
                Ok(_) => println!("默认配置文件创建成功"),
                Err(_) => eprintln!("创建默认配置文件失败"),
            }
            Ok(default_config.clone())
        }
    }

    /// 递归合并配置，保留加载的值，补充缺失的默认值
    /// 对应原版 Python 的 _merge_configs 方法
    fn merge_configs(default: Value, loaded: Value) -> Value {
        match (default, loaded) {
            (Value::Object(mut default_map), Value::Object(loaded_map)) => {
                for (key, value) in loaded_map {
                    if let Some(default_value) = default_map.get(&key) {
                        if default_value.is_object() && value.is_object() {
                            default_map.insert(key, Self::merge_configs(default_value.clone(), value));
                        } else {
                            default_map.insert(key, value);
                        }
                    } else {
                        default_map.insert(key, value);
                    }
                }
                Value::Object(default_map)
            }
            (_, loaded) => loaded,
        }
    }

    /// 保存配置到文件
    /// 对应原版 Python 的 save_config 方法
    pub fn save_config(&self) -> Result<bool> {
        match std::fs::write(
            &self.config_file,
            serde_json::to_string_pretty(&self.config)?,
        ) {
            Ok(_) => Ok(true),
            Err(_e) => {
                eprintln!("保存配置文件失败");
                Ok(false)
            }
        }
    }

    /// 获取配置值
    /// 对应原版 Python 的 get 方法
    /// key_path: 使用点号分隔的路径，如 "email.domain"
    pub fn get(&self, key_path: &str, default: Option<Value>) -> Value {
        let keys: Vec<&str> = key_path.split('.').collect();
        let mut value = &self.config;

        for key in keys {
            if let Some(obj) = value.as_object() {
                if let Some(v) = obj.get(key) {
                    value = v;
                } else {
                    return default.unwrap_or(Value::Null);
                }
            } else {
                return default.unwrap_or(Value::Null);
            }
        }

        value.clone()
    }

    /// 设置配置值
    /// 对应原版 Python 的 set 方法
    /// key_path: 使用点号分隔的路径，如 "email.domain"
    pub fn set(&mut self, key_path: &str, value: Value) -> Result<bool> {
        let keys: Vec<&str> = key_path.split('.').collect();
        let mut config = &mut self.config;

        // 导航到最后一个键的父级
        for key in &keys[..keys.len()-1] {
            if !config.is_object() {
                *config = Value::Object(serde_json::Map::new());
            }

            let obj = config.as_object_mut().unwrap();
            if !obj.contains_key(*key) {
                obj.insert(key.to_string(), Value::Object(serde_json::Map::new()));
            }
            config = obj.get_mut(*key).unwrap();
        }

        // 设置值
        if let Some(last_key) = keys.last() {
            if !config.is_object() {
                *config = Value::Object(serde_json::Map::new());
            }
            config.as_object_mut().unwrap().insert(last_key.to_string(), value);
        }

        // 保存配置
        self.save_config()
    }

    /// 获取选择的编辑器类型
    /// 对应原版 Python 的 get_editor_type 方法
    pub fn get_editor_type(&self) -> Option<String> {
        match self.config.get("editor_type") {
            Some(Value::String(s)) => Some(s.clone()),
            _ => None,
        }
    }

    /// 设置编辑器类型
    /// 对应原版 Python 的 set_editor_type 方法
    /// 现在支持所有JetBrains IDE
    pub fn set_editor_type(&mut self, editor_type: &str) -> Result<bool> {
        let supported_editors = [
            "vscode", "cursor",
            "intellij", "pycharm", "webstorm", "phpstorm",
            "rubymine", "clion", "goland", "rider",
            "datagrip", "androidstudio"
        ];

        if supported_editors.contains(&editor_type) {
            self.config.as_object_mut().unwrap().insert(
                "editor_type".to_string(),
                Value::String(editor_type.to_string())
            );
            self.save_config()
        } else {
            Ok(false)
        }
    }

    /// 检查是否是首次运行（未选择编辑器）
    /// 对应原版 Python 的 is_first_run 方法
    pub fn is_first_run(&self) -> bool {
        self.get_editor_type().is_none()
    }

    /// 重置配置到默认值
    /// 对应原版 Python 的 reset_config 方法
    pub fn reset_config(&mut self) -> Result<bool> {
        self.config = self.default_config.clone();
        self.save_config()
    }

    /// 导出配置到指定路径
    /// 对应原版 Python 的 export_config 方法
    pub fn export_config(&self, export_path: &str) -> Result<bool> {
        match std::fs::write(
            export_path,
            serde_json::to_string_pretty(&self.config)?,
        ) {
            Ok(_) => Ok(true),
            Err(_e) => {
                eprintln!("导出配置失败");
                Ok(false)
            }
        }
    }

    /// 从指定路径导入配置
    /// 对应原版 Python 的 import_config 方法
    pub fn import_config(&mut self, import_path: &str) -> Result<bool> {
        match std::fs::read_to_string(import_path) {
            Ok(content) => {
                match serde_json::from_str::<Value>(&content) {
                    Ok(imported_config) => {
                        self.config = Self::merge_configs(self.default_config.clone(), imported_config);
                        self.save_config()
                    }
                    Err(e) => {
                        eprintln!("导入配置失败: {}", e);
                        Ok(false)
                    }
                }
            }
            Err(e) => {
                eprintln!("导入配置失败: {}", e);
                Ok(false)
            }
        }
    }

    /// 获取配置的引用
    /// 用于其他模块访问配置
    pub fn get_config(&self) -> &Value {
        &self.config
    }

    /// 获取配置的可变引用
    /// 用于直接修改配置
    pub fn get_config_mut(&mut self) -> &mut Value {
        &mut self.config
    }
}

// 删除旧的结构体定义，因为我们现在使用 serde_json::Value
