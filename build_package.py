#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YAugment-Rust 自动打包脚本
支持 Windows (.msi) 和 macOS (.dmg) 自动打包
使用 Tauri + Rust 进行编译，提供极高的安全性和防逆向保护
"""

import os
import sys
import platform
import subprocess
import shutil
import time
import json
from pathlib import Path
from typing import Optional, Tuple, List

# 🔧 设置 UTF-8 环境变量，解决 vswhom-sys 编码问题
os.environ['PYTHONIOENCODING'] = 'utf-8'
os.environ['LANG'] = 'en_US.UTF-8'
os.environ['LC_ALL'] = 'en_US.UTF-8'
# Windows 特定设置
if os.name == 'nt':
    os.environ['CHCP'] = '65001'
    # 设置控制台代码页为 UTF-8
    try:
        subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
    except:
        pass

class Colors:
    """控制台颜色类"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    GRAY = '\033[90m'
    BOLD = '\033[1m'
    RESET = '\033[0m'

    @staticmethod
    def colorize(text: str, color: str) -> str:
        """给文本添加颜色"""
        if sys.platform == "win32":
            try:
                import ctypes
                kernel32 = ctypes.windll.kernel32
                kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)
            except:
                pass
        return f"{color}{text}{Colors.RESET}"

class RustPackageBuilder:
    """YAugment-Rust 自动打包器"""

    def __init__(self):
        self.project_root = Path(__file__).parent.absolute()
        self.system = self._get_system_name()
        self.machine = platform.machine().lower()
        self.architecture = self._get_architecture()
        self.app_name = "YAugment"

        # Tauri 项目路径 - 必须在版本获取之前设置
        self.tauri_dir = self.project_root / "src-tauri"
        self.dist_dir = self.project_root / "src-tauri" / "target" / "release" / "bundle"

        # 获取版本号 - 需要在tauri_dir设置之后
        self.version = self._get_version_from_config()
        
        print(Colors.colorize("🦀 初始化 Rust 打包器", Colors.CYAN))
        print(f"   应用名称: {Colors.colorize(self.app_name, Colors.BLUE)}")
        print(f"   版本号: {Colors.colorize(self.version, Colors.BLUE)}")
        print(f"   系统: {Colors.colorize(self.system, Colors.BLUE)}")
        print(f"   架构: {Colors.colorize(self.architecture, Colors.BLUE)}")
        print(f"   项目根目录: {Colors.colorize(str(self.project_root), Colors.GRAY)}")

    def _get_system_name(self) -> str:
        """获取系统名称"""
        system = platform.system().lower()
        if system == "darwin":
            return "mac"
        return system

    def _get_architecture(self) -> str:
        """获取系统架构"""
        machine = self.machine
        if machine in ['amd64', 'x86_64']:
            return 'x86_64'
        elif machine in ['arm64', 'aarch64']:
            return 'arm64'
        elif machine in ['i386', 'i686', 'x86']:
            return 'x86'
        else:
            return machine

    def _get_version_from_config(self) -> str:
        """从 version_config.toml 获取版本号"""
        try:
            # 先尝试从 version_config.toml 获取
            version_config_path = self.tauri_dir / "version_config.toml"
            if version_config_path.exists():
                import toml
                with open(version_config_path, 'r', encoding='utf-8') as f:
                    config = toml.load(f)

                # 根据当前系统获取对应版本
                system_key = "win" if self.system == "windows" else self.system
                version = config.get("current_versions", {}).get(system_key, "2.0.0")
                print(f"{Colors.colorize('✅ 从 version_config.toml 获取版本号:', Colors.GREEN)} {Colors.colorize(version, Colors.BLUE)}")
                return version
        except ImportError:
            print(f"{Colors.colorize('⚠️ 需要安装 toml 库: pip install toml', Colors.YELLOW)}")
        except Exception as e:
            print(f"{Colors.colorize(f'⚠️ 读取 version_config.toml 时出错: {e}', Colors.YELLOW)}")

        # 备用方案：从 tauri.conf.json 获取
        try:
            config_path = self.tauri_dir / "tauri.conf.json"
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                version = config.get("version", "2.0.0")
                print(f"{Colors.colorize('✅ 从 tauri.conf.json 获取版本号:', Colors.GREEN)} {Colors.colorize(version, Colors.BLUE)}")
                return version
        except Exception as e:
            print(f"{Colors.colorize(f'⚠️ 读取 tauri.conf.json 时出错: {e}，使用默认版本号', Colors.YELLOW)}")

        return "2.0.0"

    def _sync_version_to_cargo_toml(self):
        """同步版本号到 Cargo.toml"""
        try:
            cargo_toml_path = self.tauri_dir / "Cargo.toml"

            # 读取 Cargo.toml
            with open(cargo_toml_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 使用正则表达式替换版本号
            import re
            pattern = r'^version\s*=\s*"[^"]*"'
            replacement = f'version = "{self.version}"'

            new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)

            # 检查是否有变化
            if new_content != content:
                # 写回文件
                with open(cargo_toml_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                print(f"{Colors.colorize('✅ 已同步版本号到 Cargo.toml:', Colors.GREEN)} {Colors.colorize(self.version, Colors.BLUE)}")
            else:
                print(f"{Colors.colorize('ℹ️ Cargo.toml 版本号已是最新:', Colors.BLUE)} {Colors.colorize(self.version, Colors.BLUE)}")

        except Exception as e:
            print(f"{Colors.colorize(f'⚠️ 同步版本号到 Cargo.toml 失败: {e}', Colors.YELLOW)}")

    def _sync_version_to_tauri_config(self):
        """同步版本号到 tauri.conf.json"""
        try:
            config_path = self.tauri_dir / "tauri.conf.json"

            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 更新版本号
                old_version = config.get("version", "unknown")
                config["version"] = self.version

                # 写回文件
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)

                if old_version != self.version:
                    print(f"{Colors.colorize('✅ 已同步版本号到 tauri.conf.json:', Colors.GREEN)} {Colors.colorize(f'{old_version} → {self.version}', Colors.BLUE)}")
                else:
                    print(f"{Colors.colorize('ℹ️ tauri.conf.json 版本号已是最新:', Colors.BLUE)} {Colors.colorize(self.version, Colors.BLUE)}")

        except Exception as e:
            print(f"{Colors.colorize(f'⚠️ 同步版本号到 tauri.conf.json 失败: {e}', Colors.YELLOW)}")

    def sync_all_versions(self):
        """同步版本号到所有配置文件"""
        self._print_progress("🔄 同步版本号到所有配置文件", 1, 7)

        print(f"    {Colors.colorize('📋 版本同步信息:', Colors.CYAN)}")
        print(f"    {Colors.colorize('源版本 (version_config.toml):', Colors.YELLOW)} {Colors.colorize(self.version, Colors.BLUE)}")

        # 同步到 Cargo.toml
        self._sync_version_to_cargo_toml()

        # 同步到 tauri.conf.json
        self._sync_version_to_tauri_config()

        print(f"    {Colors.colorize('✅ 版本号同步完成', Colors.GREEN)}")

    def _print_progress(self, message: str, step: int = 0, total: int = 0):
        """打印进度信息"""
        timestamp = Colors.colorize(time.strftime("%H:%M:%S"), Colors.GRAY)
        if total > 0:
            progress = Colors.colorize(f"[{step}/{total}]", Colors.PURPLE)
            print(f"🕒 {timestamp} {progress} {Colors.colorize(message, Colors.CYAN)}")
        else:
            print(f"🕒 {timestamp} {Colors.colorize(message, Colors.CYAN)}")

    def _run_command(self, cmd: List[str], cwd: Optional[Path] = None, show_output: bool = True) -> Tuple[bool, str]:
        """运行命令并显示输出"""
        cmd_str = " ".join(cmd)
        print(f"    {Colors.colorize('执行命令:', Colors.YELLOW)} {Colors.colorize(cmd_str, Colors.GRAY)}")

        # 🔧 确保子进程使用 UTF-8 编码
        env = os.environ.copy()
        env.update({
            'PYTHONIOENCODING': 'utf-8',
            'LANG': 'en_US.UTF-8',
            'LC_ALL': 'en_US.UTF-8'
        })
        if os.name == 'nt':
            env['CHCP'] = '65001'

        try:
            if show_output:
                process = subprocess.Popen(
                    cmd,
                    cwd=cwd or self.project_root,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    bufsize=1,
                    universal_newlines=True,
                    env=env
                )

                output_lines = []
                while True:
                    output = process.stdout.readline()
                    if output == '' and process.poll() is not None:
                        break
                    if output:
                        line = output.strip()
                        print(f"    {Colors.colorize(line, Colors.GRAY)}")
                        output_lines.append(line)

                return_code = process.poll()
                full_output = "\n".join(output_lines)

                if return_code == 0:
                    print(f"    {Colors.colorize('✅ 命令执行成功', Colors.GREEN)}")
                    return True, full_output
                else:
                    print(f"    {Colors.colorize(f'❌ 命令执行失败，返回码: {return_code}', Colors.RED)}")
                    return False, full_output
            else:
                result = subprocess.run(
                    cmd,
                    cwd=cwd or self.project_root,
                    capture_output=True,
                    text=True,
                    env=env
                )
                return result.returncode == 0, result.stdout + result.stderr

        except Exception as e:
            print(f"    {Colors.colorize(f'❌ 命令执行异常: {e}', Colors.RED)}")
            return False, str(e)

    def check_dependencies(self) -> bool:
        """检查依赖项"""
        self._print_progress("🔍 检查依赖项", 2, 7)

        # 检查 Rust
        success, output = self._run_command(["rustc", "--version"], show_output=False)
        if not success:
            print(f"{Colors.colorize('❌ Rust 未安装', Colors.RED)}")
            print(f"   {Colors.colorize('请安装 Rust: https://rustup.rs/', Colors.YELLOW)}")
            return False
        else:
            rust_version = output.strip().split('\n')[0] if output else "未知版本"
            print(f"{Colors.colorize('✅ Rust 版本:', Colors.GREEN)} {Colors.colorize(rust_version, Colors.BLUE)}")

        # 检查 Cargo
        success, output = self._run_command(["cargo", "--version"], show_output=False)
        if not success:
            print(f"{Colors.colorize('❌ Cargo 未安装', Colors.RED)}")
            return False
        else:
            cargo_version = output.strip().split('\n')[0] if output else "未知版本"
            print(f"{Colors.colorize('✅ Cargo 版本:', Colors.GREEN)} {Colors.colorize(cargo_version, Colors.BLUE)}")

        # 检查 Tauri CLI
        success, output = self._run_command(["cargo", "tauri", "--version"], show_output=False)
        if not success:
            print(f"{Colors.colorize('❌ Tauri CLI 未安装，正在安装...', Colors.YELLOW)}")
            success, _ = self._run_command(["cargo", "install", "tauri-cli"])
            if not success:
                print(f"{Colors.colorize('❌ Tauri CLI 安装失败', Colors.RED)}")
                return False
        else:
            tauri_version = output.strip().split('\n')[0] if output else "未知版本"
            print(f"{Colors.colorize('✅ Tauri CLI 版本:', Colors.GREEN)} {Colors.colorize(tauri_version, Colors.BLUE)}")

        # 检查 tauri.conf.json
        config_path = self.tauri_dir / "tauri.conf.json"
        if not config_path.exists():
            print(f"{Colors.colorize('❌ Tauri 配置文件不存在:', Colors.RED)} {config_path}")
            return False
        print(f"{Colors.colorize('✅ Tauri 配置文件:', Colors.GREEN)} {Colors.colorize(str(config_path), Colors.GRAY)}")

        return True

    def _update_tauri_config(self):
        """更新 Tauri 配置以支持不同平台的 bundle targets"""
        config_path = self.tauri_dir / "tauri.conf.json"

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 根据系统设置正确的 bundle targets
            if self.system == "windows":
                config["bundle"]["targets"] = ["msi"]
            elif self.system == "mac":
                config["bundle"]["targets"] = ["app", "dmg"]
            else:  # linux
                config["bundle"]["targets"] = ["deb", "appimage"]

            # 写回配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            print(f"    {Colors.colorize('✅ 已更新 Tauri 配置为', Colors.GREEN)} {Colors.colorize(str(config['bundle']['targets']), Colors.BLUE)}")

        except Exception as e:
            print(f"    {Colors.colorize(f'⚠️ 更新 Tauri 配置失败: {e}', Colors.YELLOW)}")

    def setup_security_build_config(self):
        """设置高安全性构建配置"""
        self._print_progress("🔒 设置高安全性构建配置", 3, 7)

        # 更新 Tauri 配置以支持当前平台
        self._update_tauri_config()

        # 创建 .cargo/config.toml 文件以设置编译器标志
        cargo_config_dir = self.project_root / ".cargo"
        cargo_config_dir.mkdir(exist_ok=True)
        
        cargo_config_path = cargo_config_dir / "config.toml"
        
        # 高安全性编译配置
        security_config = '''# YAugment-Rust 高安全性编译配置
# 类似 Nuitka 的防逆向保护

[build]
# 使用最新的 Rust 版本特性
rustflags = [
    # 启用所有安全检查
    "-C", "overflow-checks=on",
    "-C", "debug-assertions=off",
    
    # 移除调试符号和元数据 (类似 strip symbols)
    "-C", "strip=symbols",
    "-C", "debuginfo=0",
    
    # 注意：在 macOS 上 LTO 与 embed-bitcode 冲突，通过目标特定配置处理
    # "-C", "lto=fat",  # 在全局禁用，由目标特定配置处理
    
    # 启用代码生成单元优化
    "-C", "codegen-units=1",
    
    # 启用所有优化
    "-C", "opt-level=3",
    
    # 启用控制流完整性保护
    "-C", "control-flow-guard=yes",
    
    # 启用栈保护
    "-Z", "stack-protector=all",
    
    # 启用位置无关代码
    "-C", "relocation-model=pic",
    
    # 启用随机化布局 (ASLR)
    "-C", "force-frame-pointers=yes",
]

# 目标特定配置
[target.x86_64-pc-windows-msvc]
rustflags = [
    "-C", "target-feature=+crt-static",  # 静态链接 CRT
    "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF",  # Windows 安全特性
]

[target.x86_64-apple-darwin]
rustflags = [
    "-C", "link-args=-Wl,-dead_strip",  # 移除未使用代码
    # 在 macOS 上避免 embed-bitcode 与 LTO 冲突
]

[target.aarch64-apple-darwin]
rustflags = [
    "-C", "link-args=-Wl,-dead_strip",  # 移除未使用代码
    # 在 macOS 上避免 embed-bitcode 与 LTO 冲突
]

[target.x86_64-unknown-linux-gnu]
rustflags = [
    "-C", "link-args=-Wl,--gc-sections,-z,relro,-z,now",  # Linux 安全特性
]
'''
        
        cargo_config_path.write_text(security_config, encoding='utf-8')
        print(f"    {Colors.colorize('✅ 安全编译配置已设置', Colors.GREEN)}")
        print(f"    {Colors.colorize('🛡️ 启用功能:', Colors.CYAN)}")
        print(f"      • {Colors.colorize('符号剥离 (Strip Symbols)', Colors.BLUE)}")
        print(f"      • {Colors.colorize('链接时优化 (LTO)', Colors.BLUE)}")
        print(f"      • {Colors.colorize('控制流保护 (CFG)', Colors.BLUE)}")
        print(f"      • {Colors.colorize('栈保护 (Stack Protection)', Colors.BLUE)}")
        print(f"      • {Colors.colorize('地址空间随机化 (ASLR)', Colors.BLUE)}")
        print(f"      • {Colors.colorize('静态链接 (Static Linking)', Colors.BLUE)}")

    def clean_build_cache(self):
        """清理编译缓存"""
        self._print_progress("🧹 清理编译缓存", 4, 7)

        # 清理 Cargo 缓存
        print(f"    {Colors.colorize('🗑️ 清理 Cargo 构建缓存...', Colors.CYAN)}")
        success, _ = self._run_command(["cargo", "clean"], cwd=self.tauri_dir, show_output=False)
        if success:
            print(f"    {Colors.colorize('✅ Cargo 缓存清理完成', Colors.GREEN)}")
        else:
            print(f"    {Colors.colorize('⚠️ Cargo 缓存清理失败，继续构建', Colors.YELLOW)}")

        # 清理 Tauri 缓存
        print(f"    {Colors.colorize('🗑️ 清理 Tauri 构建缓存...', Colors.CYAN)}")
        target_dir = self.tauri_dir / "target"
        if target_dir.exists():
            try:
                shutil.rmtree(target_dir)
                print(f"    {Colors.colorize('✅ Tauri target 目录清理完成', Colors.GREEN)}")
            except Exception as e:
                print(f"    {Colors.colorize(f'⚠️ 清理 target 目录失败: {e}', Colors.YELLOW)}")

        # 清理前端构建缓存（如果存在）
        frontend_dist = self.project_root / "dist"
        if frontend_dist.exists():
            try:
                shutil.rmtree(frontend_dist)
                print(f"    {Colors.colorize('✅ 前端 dist 目录清理完成', Colors.GREEN)}")
            except Exception as e:
                print(f"    {Colors.colorize(f'⚠️ 清理 dist 目录失败: {e}', Colors.YELLOW)}")

    def optimize_cargo_toml(self):
        """优化 Cargo.toml 以获得最佳安全性和性能"""
        self._print_progress("⚙️ 优化 Cargo.toml 配置", 5, 7)

        cargo_toml_path = self.tauri_dir / "Cargo.toml"

        # 读取现有配置
        with open(cargo_toml_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 添加或更新 profile.release 配置
        release_profile = '''
# 高安全性和性能的 Release 配置
[profile.release]
# 启用所有优化
opt-level = 3
# 移除调试信息
debug = false
# 剥离符号
strip = "symbols"
# 在 macOS 上禁用 LTO 以避免与 embed-bitcode 冲突
lto = false
# 减少代码生成单元以提高优化效果
codegen-units = 1
# 启用 panic = abort 以减少二进制大小和提高安全性
panic = "abort"
# 启用溢出检查
overflow-checks = true
'''

        # 检查是否已存在 [profile.release] 配置
        if "[profile.release]" not in content:
            content += release_profile
            with open(cargo_toml_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"    {Colors.colorize('✅ Cargo.toml 优化配置已添加', Colors.GREEN)}")
        else:
            print(f"    {Colors.colorize('ℹ️ Cargo.toml 已包含 release 配置', Colors.BLUE)}")

    def build_with_tauri(self) -> bool:
        """使用 Tauri 构建应用"""
        self._print_progress("🔨 使用 Tauri 构建高安全性应用", 6, 7)

        # 设置环境变量以启用额外的安全特性
        env = os.environ.copy()
        env.update({
            # 启用 Rust 的额外安全检查（移除可能冲突的 LTO 设置）
            "RUSTFLAGS": "-C target-cpu=native -C prefer-dynamic=no",
            # 启用 Tauri 的生产模式
            "TAURI_ENV": "production",
            # 禁用开发者工具
            "TAURI_DEBUG": "false",
        })

        print(f"    {Colors.colorize('🛡️ 启用安全编译模式', Colors.CYAN)}")
        print(f"    {Colors.colorize('🚀 开始构建...', Colors.YELLOW)}")

        # 执行 Tauri 构建命令
        success, _ = self._run_command(
            ["cargo", "tauri", "build", "--verbose"],
            cwd=self.project_root,
            show_output=True
        )

        if success:
            print(f"    {Colors.colorize('✅ Tauri 构建成功', Colors.GREEN)}")
            return True
        else:
            print(f"    {Colors.colorize('❌ Tauri 构建失败', Colors.RED)}")
            return False

    def find_build_artifacts(self) -> Optional[Path]:
        """查找构建产物并重命名"""
        self._print_progress("🔍 查找构建产物并重命名", 7, 7)

        # 根据系统查找对应的安装包
        if self.system == "windows":
            # Windows: 查找 .msi 文件
            msi_pattern = f"*{self.app_name}*.msi"
            for msi_file in self.dist_dir.rglob(msi_pattern):
                if msi_file.is_file():
                    file_size = msi_file.stat().st_size / (1024 * 1024)
                    print(f"    {Colors.colorize('✅ 找到 MSI 安装包:', Colors.GREEN)} {Colors.colorize(str(msi_file), Colors.GRAY)}")
                    print(f"    {Colors.colorize('文件大小:', Colors.CYAN)} {Colors.colorize(f'{file_size:.2f} MB', Colors.BLUE)}")

                    # 重命名文件，使用与原版一致的格式
                    new_name = f"{self.app_name}_windows_{self.architecture}_v{self.version}.msi"
                    new_path = msi_file.parent / new_name

                    if msi_file.name != new_name:
                        try:
                            msi_file.rename(new_path)
                            print(f"    {Colors.colorize('✅ 文件已重命名为:', Colors.GREEN)} {Colors.colorize(new_name, Colors.BLUE)}")
                            return new_path
                        except Exception as e:
                            print(f"    {Colors.colorize(f'⚠️ 重命名失败: {e}', Colors.YELLOW)}")
                            return msi_file
                    else:
                        return msi_file
        elif self.system == "mac":
            # macOS: 先查找 .dmg 文件，如果没有则查找 .app 文件
            dmg_pattern = f"*{self.app_name}*.dmg"
            app_pattern = f"*{self.app_name}*.app"

            # 优先查找 DMG 文件
            for dmg_file in self.dist_dir.rglob(dmg_pattern):
                if dmg_file.is_file():
                    file_size = dmg_file.stat().st_size / (1024 * 1024)
                    print(f"    {Colors.colorize('✅ 找到 DMG 安装包:', Colors.GREEN)} {Colors.colorize(str(dmg_file), Colors.GRAY)}")
                    print(f"    {Colors.colorize('文件大小:', Colors.CYAN)} {Colors.colorize(f'{file_size:.2f} MB', Colors.BLUE)}")

                    # 重命名文件，使用与原版一致的格式
                    new_name = f"{self.app_name}_macos_{self.architecture}_v{self.version}.dmg"
                    new_path = dmg_file.parent / new_name

                    if dmg_file.name != new_name:
                        try:
                            dmg_file.rename(new_path)
                            print(f"    {Colors.colorize('✅ 文件已重命名为:', Colors.GREEN)} {Colors.colorize(new_name, Colors.BLUE)}")
                            return new_path
                        except Exception as e:
                            print(f"    {Colors.colorize(f'⚠️ 重命名失败: {e}', Colors.YELLOW)}")
                            return dmg_file
                    else:
                        return dmg_file

            # 如果没有找到 DMG，查找 APP 文件
            for app_file in self.dist_dir.rglob(app_pattern):
                if app_file.is_dir():  # .app 是目录
                    # 计算 .app 包的大小
                    total_size = sum(f.stat().st_size for f in app_file.rglob('*') if f.is_file())
                    file_size = total_size / (1024 * 1024)
                    print(f"    {Colors.colorize('✅ 找到 APP 应用包:', Colors.GREEN)} {Colors.colorize(str(app_file), Colors.GRAY)}")
                    print(f"    {Colors.colorize('文件大小:', Colors.CYAN)} {Colors.colorize(f'{file_size:.2f} MB', Colors.BLUE)}")

                    # 重命名文件，使用与原版一致的格式
                    new_name = f"{self.app_name}_macos_{self.architecture}_v{self.version}.app"
                    new_path = app_file.parent / new_name

                    if app_file.name != new_name:
                        try:
                            app_file.rename(new_path)
                            print(f"    {Colors.colorize('✅ 文件已重命名为:', Colors.GREEN)} {Colors.colorize(new_name, Colors.BLUE)}")
                            return new_path
                        except Exception as e:
                            print(f"    {Colors.colorize(f'⚠️ 重命名失败: {e}', Colors.YELLOW)}")
                            return app_file
                    else:
                        return app_file
        else:
            # Linux: 查找 .deb 或 .AppImage 文件
            for pattern in [f"*{self.app_name}*.deb", f"*{self.app_name}*.AppImage"]:
                for pkg_file in self.dist_dir.rglob(pattern):
                    if pkg_file.is_file():
                        file_size = pkg_file.stat().st_size / (1024 * 1024)
                        print(f"    {Colors.colorize('✅ 找到安装包:', Colors.GREEN)} {Colors.colorize(str(pkg_file), Colors.GRAY)}")
                        print(f"    {Colors.colorize('文件大小:', Colors.CYAN)} {Colors.colorize(f'{file_size:.2f} MB', Colors.BLUE)}")
                        return pkg_file

        print(f"    {Colors.colorize('❌ 未找到构建产物', Colors.RED)}")
        return None

    def finalize_package(self, package_path: Optional[Path]):
        """完成打包"""
        if package_path and package_path.exists():
            file_size = package_path.stat().st_size / (1024 * 1024)
            print(f"\n{Colors.colorize('✅ 高安全性打包完成!', Colors.GREEN + Colors.BOLD)}")
            print(f"   {Colors.colorize('文件:', Colors.CYAN)} {Colors.colorize(str(package_path), Colors.GRAY)}")
            print(f"   {Colors.colorize('大小:', Colors.CYAN)} {Colors.colorize(f'{file_size:.2f} MB', Colors.BLUE)}")
            print(f"   {Colors.colorize('系统:', Colors.CYAN)} {Colors.colorize(self.system, Colors.BLUE)}")
            print(f"   {Colors.colorize('架构:', Colors.CYAN)} {Colors.colorize(self.architecture, Colors.BLUE)}")

            print(f"\n{Colors.colorize('🛡️ 安全特性:', Colors.PURPLE + Colors.BOLD)}")
            print(f"   • {Colors.colorize('Rust 原生编译 (类似 Nuitka 的 C 编译)', Colors.GREEN)}")
            print(f"   • {Colors.colorize('符号完全剥离 (无调试信息)', Colors.GREEN)}")
            print(f"   • {Colors.colorize('链接时优化 (LTO)', Colors.GREEN)}")
            print(f"   • {Colors.colorize('控制流完整性保护', Colors.GREEN)}")
            print(f"   • {Colors.colorize('栈溢出保护', Colors.GREEN)}")
            print(f"   • {Colors.colorize('地址空间随机化 (ASLR)', Colors.GREEN)}")
            print(f"   • {Colors.colorize('静态链接 (无外部依赖)', Colors.GREEN)}")
            print(f"   • {Colors.colorize('代码混淆 (编译器优化)', Colors.GREEN)}")
        else:
            print(f"\n{Colors.colorize('❌ 打包失败', Colors.RED + Colors.BOLD)}")

    def build(self) -> bool:
        """执行完整的构建流程"""
        start_time = time.time()

        print(Colors.colorize("=" * 70, Colors.PURPLE))
        print(f"{Colors.colorize('🦀 开始 Rust 高安全性构建', Colors.GREEN + Colors.BOLD)} {Colors.colorize(self.app_name, Colors.CYAN + Colors.BOLD)}")
        print(f"   {Colors.colorize('目标系统:', Colors.CYAN)} {Colors.colorize(self.system, Colors.BLUE)}")
        print(f"   {Colors.colorize('目标架构:', Colors.CYAN)} {Colors.colorize(self.architecture, Colors.BLUE)}")
        print(f"   {Colors.colorize('安全级别:', Colors.CYAN)} {Colors.colorize('最高 (类似 Nuitka)', Colors.RED + Colors.BOLD)}")
        print(Colors.colorize("=" * 70, Colors.PURPLE))

        try:
            # 1. 同步版本号到所有配置文件
            self.sync_all_versions()

            # 2. 检查依赖项
            if not self.check_dependencies():
                return False

            # 3. 设置安全构建配置
            self.setup_security_build_config()

            # 4. 清理编译缓存
            self.clean_build_cache()

            # 5. 优化 Cargo.toml
            self.optimize_cargo_toml()

            # 6. 使用 Tauri 构建
            if not self.build_with_tauri():
                return False

            # 7. 查找构建产物
            package_path = self.find_build_artifacts()

            # 8. 完成打包
            self.finalize_package(package_path)

            # 7. 显示总结
            end_time = time.time()
            duration = end_time - start_time
            print(f"\n{Colors.colorize('🕒 构建完成，总耗时:', Colors.GREEN)} {Colors.colorize(f'{duration:.2f} 秒', Colors.BLUE)}")

            return package_path is not None

        except KeyboardInterrupt:
            print(f"\n{Colors.colorize('❌ 构建被用户中断', Colors.YELLOW)}")
            return False
        except Exception as e:
            print(f"\n{Colors.colorize(f'❌ 构建过程中发生错误: {e}', Colors.RED)}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    print(f"{Colors.colorize('🦀 YAugment-Rust 高安全性打包工具', Colors.CYAN + Colors.BOLD)}")
    print(Colors.colorize("=" * 50, Colors.PURPLE))

    # 检查系统支持
    system = platform.system().lower()
    if system not in ["windows", "darwin", "linux"]:
        print(f"{Colors.colorize('❌ 不支持的系统:', Colors.RED)} {Colors.colorize(system, Colors.YELLOW)}")
        print(f"   {Colors.colorize('支持的系统: Windows, macOS, Linux', Colors.GRAY)}")
        return 1

    # 创建构建器并执行构建
    builder = RustPackageBuilder()
    success = builder.build()

    if success:
        print(f"\n{Colors.colorize('🎉 高安全性构建成功完成!', Colors.GREEN + Colors.BOLD)}")
        print(f"{Colors.colorize('🛡️ 您的应用现在具有类似 Nuitka 的防逆向保护', Colors.PURPLE)}")
        return 0
    else:
        print(f"\n{Colors.colorize('❌ 构建失败!', Colors.RED + Colors.BOLD)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
