/// 通用进程终止工具
/// 
/// 负责终止正在运行的IDE进程，确保文件修改不被占用
/// 对应 augment-vip-rust-master 的 terminate_ides 函数

use sysinfo::{System, Pid, Process};
use std::collections::HashSet;

/// 进程终止结果结构体
#[derive(Debug, Clone, serde::Serialize)]
pub struct ProcessTerminationResult {
    pub terminated_processes: Vec<String>,
    pub failed_processes: Vec<String>,
    pub total_found: usize,
    pub total_terminated: usize,
}

/// JetBrains IDE进程名称列表
const JETBRAINS_PROCESS_NAMES: &[&str] = &[
    "idea64.exe", "idea.exe",           // IntelliJ IDEA
    "pycharm64.exe", "pycharm.exe",     // PyCharm
    "webstorm64.exe", "webstorm.exe",   // WebStorm
    "phpstorm64.exe", "phpstorm.exe",   // PhpStorm
    "rubymine64.exe", "rubymine.exe",   // RubyMine
    "clion64.exe", "clion.exe",         // CLion
    "goland64.exe", "goland.exe",       // GoLand
    "rider64.exe", "rider.exe",         // Rider
    "datagrip64.exe", "datagrip.exe",   // DataGrip
    "studio64.exe", "studio.exe",       // Android Studio
    // Unix/macOS 版本（不带.exe）
    "idea", "pycharm", "webstorm", "phpstorm", "rubymine", 
    "clion", "goland", "rider", "datagrip", "studio",
    // macOS 应用包名称
    "IntelliJ IDEA", "PyCharm", "WebStorm", "PhpStorm", "RubyMine",
    "CLion", "GoLand", "Rider", "DataGrip", "Android Studio",
];

/// VSCode相关进程名称（对应原始项目的vscode检查）
const VSCODE_PROCESS_NAMES: &[&str] = &[
    "code.exe", "code",                 // VSCode
    "cursor.exe", "cursor",             // Cursor
    "codium.exe", "codium",             // VSCodium
    "code-insiders.exe", "code-insiders", // VSCode Insiders
];

/// 终止所有IDE进程（JetBrains + VSCode）
/// 对应原始项目的 terminate_ides 函数
/// 
/// Returns:
///     Result<ProcessTerminationResult, String>: 终止结果或错误信息
pub fn terminate_all_ide_processes() -> Result<ProcessTerminationResult, String> {
    println!("开始终止所有IDE进程...");
    
    let mut system = System::new_all();
    system.refresh_all();
    
    let mut result = ProcessTerminationResult {
        terminated_processes: Vec::new(),
        failed_processes: Vec::new(),
        total_found: 0,
        total_terminated: 0,
    };
    
    let mut processes_to_kill = HashSet::new();
    
    // 查找所有目标进程
    for (pid, process) in system.processes() {
        if is_target_process(process) {
            result.total_found += 1;
            let process_name = process.name().to_string();
            println!("发现目标进程: {} (PID: {})", process_name, pid);
            
            // 添加进程及其父进程到终止列表
            processes_to_kill.insert(*pid);
            if let Some(parent_pid) = process.parent() {
                processes_to_kill.insert(parent_pid);
            }
        }
    }
    
    // 终止找到的进程
    for pid in processes_to_kill {
        if let Some(process) = system.process(pid) {
            let process_name = process.name().to_string();
            match terminate_process_tree(pid) {
                Ok(_) => {
                    println!("成功终止进程: {} (PID: {})", process_name, pid);
                    result.terminated_processes.push(format!("{} ({})", process_name, pid));
                    result.total_terminated += 1;
                }
                Err(e) => {
                    eprintln!("终止进程失败: {} (PID: {}): {}", process_name, pid, e);
                    result.failed_processes.push(format!("{} ({}): {}", process_name, pid, e));
                }
            }
        }
    }
    
    if result.total_found == 0 {
        println!("未发现正在运行的IDE进程");
    } else {
        println!("进程终止完成: 发现 {} 个，成功终止 {} 个", 
                result.total_found, result.total_terminated);
    }
    
    Ok(result)
}

/// 终止JetBrains IDE进程
/// 
/// Returns:
///     Result<ProcessTerminationResult, String>: 终止结果或错误信息
pub fn terminate_jetbrains_processes() -> Result<ProcessTerminationResult, String> {
    println!("开始终止JetBrains IDE进程...");
    
    let mut system = System::new_all();
    system.refresh_all();
    
    let mut result = ProcessTerminationResult {
        terminated_processes: Vec::new(),
        failed_processes: Vec::new(),
        total_found: 0,
        total_terminated: 0,
    };
    
    let mut processes_to_kill = HashSet::new();
    
    // 查找JetBrains进程
    for (pid, process) in system.processes() {
        if is_jetbrains_process(process) {
            result.total_found += 1;
            let process_name = process.name().to_string();
            println!("发现JetBrains进程: {} (PID: {})", process_name, pid);
            
            processes_to_kill.insert(*pid);
            if let Some(parent_pid) = process.parent() {
                processes_to_kill.insert(parent_pid);
            }
        }
    }
    
    // 终止找到的进程
    for pid in processes_to_kill {
        if let Some(process) = system.process(pid) {
            let process_name = process.name().to_string();
            match terminate_process_tree(pid) {
                Ok(_) => {
                    println!("成功终止JetBrains进程: {} (PID: {})", process_name, pid);
                    result.terminated_processes.push(format!("{} ({})", process_name, pid));
                    result.total_terminated += 1;
                }
                Err(e) => {
                    eprintln!("终止JetBrains进程失败: {} (PID: {}): {}", process_name, pid, e);
                    result.failed_processes.push(format!("{} ({}): {}", process_name, pid, e));
                }
            }
        }
    }
    
    if result.total_found == 0 {
        println!("未发现正在运行的JetBrains IDE进程");
    } else {
        println!("JetBrains进程终止完成: 发现 {} 个，成功终止 {} 个", 
                result.total_found, result.total_terminated);
    }
    
    Ok(result)
}

/// 终止VSCode相关进程
/// 
/// Returns:
///     Result<ProcessTerminationResult, String>: 终止结果或错误信息
pub fn terminate_vscode_processes() -> Result<ProcessTerminationResult, String> {
    println!("开始终止VSCode相关进程...");
    
    let mut system = System::new_all();
    system.refresh_all();
    
    let mut result = ProcessTerminationResult {
        terminated_processes: Vec::new(),
        failed_processes: Vec::new(),
        total_found: 0,
        total_terminated: 0,
    };
    
    let mut processes_to_kill = HashSet::new();
    
    // 查找VSCode进程
    for (pid, process) in system.processes() {
        if is_vscode_process(process) {
            result.total_found += 1;
            let process_name = process.name().to_string();
            println!("发现VSCode进程: {} (PID: {})", process_name, pid);
            
            processes_to_kill.insert(*pid);
            if let Some(parent_pid) = process.parent() {
                processes_to_kill.insert(parent_pid);
            }
        }
    }
    
    // 终止找到的进程
    for pid in processes_to_kill {
        if let Some(process) = system.process(pid) {
            let process_name = process.name().to_string();
            match terminate_process_tree(pid) {
                Ok(_) => {
                    println!("成功终止VSCode进程: {} (PID: {})", process_name, pid);
                    result.terminated_processes.push(format!("{} ({})", process_name, pid));
                    result.total_terminated += 1;
                }
                Err(e) => {
                    eprintln!("终止VSCode进程失败: {} (PID: {}): {}", process_name, pid, e);
                    result.failed_processes.push(format!("{} ({}): {}", process_name, pid, e));
                }
            }
        }
    }
    
    if result.total_found == 0 {
        println!("未发现正在运行的VSCode相关进程");
    } else {
        println!("VSCode进程终止完成: 发现 {} 个，成功终止 {} 个", 
                result.total_found, result.total_terminated);
    }
    
    Ok(result)
}

/// 检查进程是否为目标进程（JetBrains IDE或VSCode）
/// 对应原始项目的进程名称检查逻辑
fn is_target_process(process: &Process) -> bool {
    is_jetbrains_process(process) || is_vscode_process(process)
}

/// 检查是否为JetBrains进程
fn is_jetbrains_process(process: &Process) -> bool {
    let process_name = process.name().to_lowercase();
    
    for &jetbrains_name in JETBRAINS_PROCESS_NAMES {
        if process_name.contains(&jetbrains_name.to_lowercase()) {
            return true;
        }
    }
    
    false
}

/// 检查是否为VSCode进程
fn is_vscode_process(process: &Process) -> bool {
    let process_name = process.name().to_lowercase();
    let cmd_line = process.cmd().join(" ").to_lowercase();
    
    // 检查VSCode进程名称
    for &vscode_name in VSCODE_PROCESS_NAMES {
        if process_name.contains(&vscode_name.to_lowercase()) {
            return true;
        }
    }
    
    // 检查命令行参数（对应原始项目的.augmentcode检查）
    if cmd_line.contains("vscode") || cmd_line.contains(".augmentcode") {
        return true;
    }
    
    false
}

/// 终止进程树
/// 对应原始项目的 kill_tree 调用
fn terminate_process_tree(pid: Pid) -> Result<(), String> {
    #[cfg(windows)]
    {
        terminate_process_windows(pid)
    }
    
    #[cfg(unix)]
    {
        terminate_process_unix(pid)
    }
}

/// Windows平台进程终止
#[cfg(windows)]
fn terminate_process_windows(pid: Pid) -> Result<(), String> {
    use std::process::Command;
    
    // 使用taskkill命令终止进程树
    let output = Command::new("taskkill")
        .args(["/PID", &pid.to_string(), "/T", "/F"])
        .output()
        .map_err(|e| format!("执行taskkill命令失败: {}", e))?;
    
    if output.status.success() {
        Ok(())
    } else {
        let error_msg = String::from_utf8_lossy(&output.stderr);
        Err(format!("taskkill执行失败: {}", error_msg))
    }
}

/// Unix/Linux/macOS平台进程终止
#[cfg(unix)]
fn terminate_process_unix(pid: Pid) -> Result<(), String> {
    use std::process::Command;
    
    // 首先尝试SIGTERM（优雅终止）
    let term_result = Command::new("kill")
        .args(["-TERM", &pid.to_string()])
        .status();
    
    if let Ok(status) = term_result {
        if status.success() {
            // 等待一小段时间让进程优雅退出
            std::thread::sleep(std::time::Duration::from_millis(500));
            
            // 检查进程是否还存在
            let check_result = Command::new("kill")
                .args(["-0", &pid.to_string()])
                .status();
            
            if let Ok(check_status) = check_result {
                if !check_status.success() {
                    // 进程已经退出
                    return Ok(());
                }
            }
        }
    }
    
    // 如果SIGTERM失败，使用SIGKILL强制终止
    let kill_result = Command::new("kill")
        .args(["-KILL", &pid.to_string()])
        .status()
        .map_err(|e| format!("执行kill命令失败: {}", e))?;
    
    if kill_result.success() {
        Ok(())
    } else {
        Err(format!("kill命令执行失败"))
    }
}

/// 检查是否有IDE进程正在运行
/// 
/// Returns:
///     bool: 如果有IDE进程正在运行则返回true
pub fn has_running_ide_processes() -> bool {
    let mut system = System::new_all();
    system.refresh_all();
    
    for (_pid, process) in system.processes() {
        if is_target_process(process) {
            return true;
        }
    }
    
    false
}

/// 检查是否有JetBrains进程正在运行
pub fn has_running_jetbrains_processes() -> bool {
    let mut system = System::new_all();
    system.refresh_all();
    
    for (_pid, process) in system.processes() {
        if is_jetbrains_process(process) {
            return true;
        }
    }
    
    false
}

/// 检查是否有VSCode进程正在运行
pub fn has_running_vscode_processes() -> bool {
    let mut system = System::new_all();
    system.refresh_all();
    
    for (_pid, process) in system.processes() {
        if is_vscode_process(process) {
            return true;
        }
    }
    
    false
}
