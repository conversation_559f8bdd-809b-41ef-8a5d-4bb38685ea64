// YAugment Rust 版本主程序
// 对应原版 Python 的 YAugment.py

// Windows 子系统配置 - 关键：防止显示终端窗口
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use yaugment::utils::console::{hide_console_window, should_hide_console};

fn main() {
    // 读取版本配置以确定是否隐藏终端
    let hide_console = should_hide_console();

    // 跨平台终端隐藏
    if hide_console {
        hide_console_window();
    }

    // 初始化日志
    tracing_subscriber::fmt::init();

    if !hide_console {
        println!("YAugment 启动中...");
    }

    // ============================================================================
    // 应用启动流程 - 完全对应原版Python的AppManager.run()
    // ============================================================================

    // 启动Tauri应用，但是要先显示版本检查窗口
    // 版本检查完成后会自动调用start_main_app命令启动主程序
    yaugment::run_with_version_check();
}
