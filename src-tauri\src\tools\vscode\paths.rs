/// VSCode 路径工具
///
/// 对应原版 Python 的 augment_tools/vscode/utils/paths.py
/// 获取 VSCode 相关文件和目录的跨平台路径

#[cfg(target_os = "windows")]
use std::env;



/// 获取用户主目录
/// 对应原版 Python 的 get_home_dir 函数
pub fn get_home_dir() -> String {
    dirs::home_dir()
        .map(|path| path.to_string_lossy().to_string())
        .unwrap_or_default()
}

/// 获取应用数据目录
/// 对应原版 Python 的 get_app_data_dir 函数
/// 
/// 平台特定路径：
/// - Windows: %APPDATA% (通常是 C:\Users\<USER>\AppData\Roaming)
/// - macOS: ~/Library/Application Support
/// - Linux: ~/.local/share
pub fn get_app_data_dir() -> String {
    #[cfg(target_os = "windows")]
    {
        env::var("APPDATA").unwrap_or_default()
    }
    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        format!("{}/Library/Application Support", home)
    }
    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        format!("{}/.local/share", home)
    }
}

/// 获取 storage.json 文件路径
/// 对应原版 Python 的 get_storage_path 函数
/// 
/// 平台特定路径：
/// - Windows: %APPDATA%/Code/User/globalStorage/storage.json
/// - macOS: ~/Library/Application Support/Code/User/globalStorage/storage.json
/// - Linux: ~/.config/Code/User/globalStorage/storage.json
pub fn get_storage_path() -> String {
    #[cfg(target_os = "windows")]
    {
        let base_path = get_app_data_dir();
        format!("{}\\Code\\User\\globalStorage\\storage.json", base_path)
    }
    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        format!("{}/Library/Application Support/Code/User/globalStorage/storage.json", home)
    }
    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        format!("{}/.config/Code/User/globalStorage/storage.json", home)
    }
}

/// 获取 state.vscdb 数据库文件路径
/// 对应原版 Python 的 get_db_path 函数
/// 
/// 平台特定路径：
/// - Windows: %APPDATA%/Code/User/globalStorage/state.vscdb
/// - macOS: ~/Library/Application Support/Code/User/globalStorage/state.vscdb
/// - Linux: ~/.config/Code/User/globalStorage/state.vscdb
pub fn get_db_path() -> String {
    #[cfg(target_os = "windows")]
    {
        let base_path = get_app_data_dir();
        format!("{}\\Code\\User\\globalStorage\\state.vscdb", base_path)
    }
    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        format!("{}/Library/Application Support/Code/User/globalStorage/state.vscdb", home)
    }
    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        format!("{}/.config/Code/User/globalStorage/state.vscdb", home)
    }
}

/// 获取机器 ID 文件路径（主路径）
/// 对应原版 Python 的 get_machine_id_path 函数
///
/// 平台特定路径：
/// - Windows: %APPDATA%/Code/User/machineid
/// - macOS: ~/Library/Application Support/Code/machineid
/// - Linux: ~/.config/Code/machineid
pub fn get_machine_id_path() -> String {
    #[cfg(target_os = "windows")]
    {
        let base_path = get_app_data_dir();
        format!("{}\\Code\\User\\machineid", base_path)
    }
    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        format!("{}/Library/Application Support/Code/machineid", home)
    }
    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        format!("{}/.config/Code/machineid", home)
    }
}

/// 获取所有可能的 VSCode 机器 ID 文件路径
/// 根据用户反馈，machineid文件可能存在于多个位置
///
/// 返回所有可能的路径，按优先级排序：
/// - Windows:
///   1. %APPDATA%/Code/User/machineid (标准路径)
///   2. %APPDATA%/Code/machineid (备选路径)
/// - macOS:
///   1. ~/Library/Application Support/Code/machineid (标准路径)
///   2. ~/Library/Application Support/Code/User/machineid (备选路径)
/// - Linux:
///   1. ~/.config/Code/machineid (标准路径)
///   2. ~/.config/Code/User/machineid (备选路径)
pub fn get_all_machine_id_paths() -> Vec<String> {
    #[cfg(target_os = "windows")]
    {
        let base_path = get_app_data_dir();
        vec![
            format!("{}\\Code\\User\\machineid", base_path),      // 标准路径
            format!("{}\\Code\\machineid", base_path),            // 备选路径
        ]
    }

    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        vec![
            format!("{}/Library/Application Support/Code/machineid", home),      // 标准路径
            format!("{}/Library/Application Support/Code/User/machineid", home), // 备选路径
        ]
    }

    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        vec![
            format!("{}/.config/Code/machineid", home),          // 标准路径
            format!("{}/.config/Code/User/machineid", home),     // 备选路径
        ]
    }
}

/// 获取工作区存储目录路径
/// 对应原版 Python 的 get_workspace_storage_path 函数
/// 
/// 平台特定路径：
/// - Windows: %APPDATA%/Code/User/workspaceStorage
/// - macOS: ~/Library/Application Support/Code/User/workspaceStorage
/// - Linux: ~/.config/Code/User/workspaceStorage
pub fn get_workspace_storage_path() -> String {
    #[cfg(target_os = "windows")]
    {
        let base_path = get_app_data_dir();
        format!("{}\\Code\\User\\workspaceStorage", base_path)
    }
    #[cfg(target_os = "macos")]
    {
        let home = get_home_dir();
        format!("{}/Library/Application Support/Code/User/workspaceStorage", home)
    }
    #[cfg(not(any(target_os = "windows", target_os = "macos")))]
    {
        let home = get_home_dir();
        format!("{}/.config/Code/User/workspaceStorage", home)
    }
}
