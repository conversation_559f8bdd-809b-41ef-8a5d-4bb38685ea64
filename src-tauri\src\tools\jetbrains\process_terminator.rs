/// JetBrains 进程终止工具
///
/// 负责终止正在运行的JetBrains IDE进程，确保文件修改不被占用
/// 对应 augment-vip-rust-master 的 terminate_ides 函数
///
/// 注意：这个模块现在主要是为了保持向后兼容性
/// 实际的进程终止功能已经移到了 tools::process_terminator 模块

use crate::tools::process_terminator::{
    terminate_jetbrains_processes as terminate_jetbrains_impl,
    has_running_jetbrains_processes as has_running_jetbrains_impl,
    ProcessTerminationResult,
};

/// 终止JetBrains IDE进程
/// 对应原始项目的 terminate_ides 函数
///
/// Returns:
///     Result<ProcessTerminationResult, String>: 终止结果或错误信息
pub fn terminate_jetbrains_processes() -> Result<ProcessTerminationResult, String> {
    terminate_jetbrains_impl()
}

/// 检查是否有JetBrains进程正在运行
///
/// Returns:
///     bool: 如果有JetBrains进程正在运行则返回true
pub fn has_running_jetbrains_processes() -> bool {
    has_running_jetbrains_impl()
}


