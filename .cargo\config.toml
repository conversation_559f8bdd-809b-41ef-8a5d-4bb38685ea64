# YAugment-Rust 高安全性编译配置
# 类似 Nuitka 的防逆向保护

[build]
# 使用最新的 Rust 版本特性
rustflags = [
    # 启用所有安全检查
    "-C", "overflow-checks=on",
    "-C", "debug-assertions=off",
    
    # 移除调试符号和元数据 (类似 strip symbols)
    "-C", "strip=symbols",
    "-C", "debuginfo=0",
    
    # 注意：在 macOS 上 LTO 与 embed-bitcode 冲突，通过目标特定配置处理
    # "-C", "lto=fat",  # 在全局禁用，由目标特定配置处理
    
    # 启用代码生成单元优化
    "-C", "codegen-units=1",
    
    # 启用所有优化
    "-C", "opt-level=3",
    
    # 启用控制流完整性保护
    "-C", "control-flow-guard=yes",
    
    # 启用栈保护
    "-Z", "stack-protector=all",
    
    # 启用位置无关代码
    "-C", "relocation-model=pic",
    
    # 启用随机化布局 (ASLR)
    "-C", "force-frame-pointers=yes",
]

# 目标特定配置
[target.x86_64-pc-windows-msvc]
rustflags = [
    "-C", "target-feature=+crt-static",  # 静态链接 CRT
    "-C", "link-args=/DYNAMICBASE /NXCOMPAT /GUARD:CF",  # Windows 安全特性
]

[target.x86_64-apple-darwin]
rustflags = [
    "-C", "link-args=-Wl,-dead_strip",  # 移除未使用代码
    # 在 macOS 上避免 embed-bitcode 与 LTO 冲突
]

[target.aarch64-apple-darwin]
rustflags = [
    "-C", "link-args=-Wl,-dead_strip",  # 移除未使用代码
    # 在 macOS 上避免 embed-bitcode 与 LTO 冲突
]

[target.x86_64-unknown-linux-gnu]
rustflags = [
    "-C", "link-args=-Wl,--gc-sections,-z,relro,-z,now",  # Linux 安全特性
]
