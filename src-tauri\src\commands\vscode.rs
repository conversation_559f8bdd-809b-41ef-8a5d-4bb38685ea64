/// VSCode 编辑器命令
/// 
/// 对应原版 Python 的 augment_tools/vscode 包的前端接口
/// 提供 Tauri 命令用于前端调用

use tauri::{command, State};
use crate::app_state::AppState;
use crate::tools::vscode::{
    json_modifier::{modify_telemetry_ids, ModifyResult},
    sqlite_modifier::{clean_augment_data, count_augment_records, CleanResult},
    workspace_cleaner::{clean_workspace_storage, CleanWorkspaceResult},
};
use crate::tools::process_terminator::{terminate_vscode_processes, ProcessTerminationResult};

/// 修改 VSCode 遥测 ID
/// 对应原版 Python 的 modify_telemetry_ids 函数
/// 
/// 此命令：
/// 1. 创建 storage.json 和机器 ID 文件的备份
/// 2. 生成新的机器和设备 ID
/// 3. 更新 storage.json 中的遥测 ID
/// 4. 更新机器 ID 文件
/// 
/// Returns:
///     Result<ModifyResult, String>: 包含操作结果的结构体或错误信息
#[command]
pub async fn vscode_modify_telemetry_ids(
    _state: State<'_, AppState>
) -> Result<ModifyResult, String> {
    modify_telemetry_ids()
}

/// 清理 VSCode SQLite 数据库中的 augment 相关数据
/// 对应原版 Python 的 clean_augment_data 函数
/// 
/// 此命令：
/// 1. 创建数据库文件的备份
/// 2. 删除 key 包含 'augment' 的记录
/// 
/// Returns:
///     Result<CleanResult, String>: 包含操作结果的结构体或错误信息
#[command]
pub async fn vscode_clean_augment_data(
    _state: State<'_, AppState>
) -> Result<CleanResult, String> {
    clean_augment_data()
}

/// 清理 VSCode 工作区存储目录
/// 对应原版 Python 的 clean_workspace_storage 函数
/// 
/// 此命令：
/// 1. 创建工作区目录的 zip 备份
/// 2. 删除目录中的所有文件
/// 
/// Returns:
///     Result<CleanWorkspaceResult, String>: 包含操作结果的结构体或错误信息
#[command]
pub async fn vscode_clean_workspace_storage(
    _state: State<'_, AppState>
) -> Result<CleanWorkspaceResult, String> {
    clean_workspace_storage()
}

/// 获取 VSCode 数据库中包含 'augment' 的记录数量
/// 用于验证清理操作的效果
/// 
/// Returns:
///     Result<usize, String>: 记录数量或错误信息
#[command]
pub async fn vscode_count_augment_records(
    _state: State<'_, AppState>
) -> Result<usize, String> {
    count_augment_records()
}

/// 终止VSCode相关进程
/// 对应 augment-vip-rust-master 的 terminate_ides 功能
///
/// Returns:
///     Result<ProcessTerminationResult, String>: 包含终止结果的结构体或错误信息
#[command]
pub async fn vscode_terminate_processes(
    _state: State<'_, AppState>
) -> Result<ProcessTerminationResult, String> {
    terminate_vscode_processes()
}

/// 执行完整的 VSCode 重置流程
/// 对应原版 Python 中的完整重置逻辑
///
/// 此命令按顺序执行：
/// 1. 终止正在运行的VSCode进程
/// 2. 修改遥测 ID
/// 3. 清理 augment 数据
/// 4. 清理工作区存储
///
/// Returns:
///     Result<VscodeResetResult, String>: 包含所有操作结果的结构体或错误信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct VscodeResetResult {
    pub termination_result: ProcessTerminationResult,
    pub telemetry_result: ModifyResult,
    pub database_result: CleanResult,
    pub workspace_result: CleanWorkspaceResult,
}

#[command]
pub async fn vscode_full_reset(
    _state: State<'_, AppState>
) -> Result<VscodeResetResult, String> {
    // 1. 终止正在运行的VSCode进程（对应原始项目的terminate_ides）
    let termination_result = terminate_vscode_processes()?;

    // 2. 修改遥测 ID
    let telemetry_result = modify_telemetry_ids()?;

    // 3. 清理 augment 数据
    let database_result = clean_augment_data()?;

    // 4. 清理工作区存储
    let workspace_result = clean_workspace_storage()?;

    Ok(VscodeResetResult {
        termination_result,
        telemetry_result,
        database_result,
        workspace_result,
    })
}
