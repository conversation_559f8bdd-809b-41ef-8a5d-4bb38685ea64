use std::sync::{Arc, Mutex};
use serde::{Deserialize, Serialize};
use tokio::sync::{RwLock, Mutex as TokioMutex};

use crate::core::{<PERSON>fi<PERSON><PERSON><PERSON><PERSON>, EditorDetector, EmailManager, AugmentAccountManager};
use crate::core::version::VersionChecker;

/// 应用全局状态
/// 对应原版 Python 的 AppManager 类
#[derive(Debug)]
pub struct AppState {
    /// 配置管理器
    pub config_manager: Arc<Mutex<ConfigManager>>,

    /// 编辑器检测器
    pub editor_detector: Arc<EditorDetector>,

    /// 邮箱管理器
    pub email_manager: Arc<Mutex<EmailManager>>,

    /// Augment 账号管理器
    pub augment_account_manager: Arc<Mutex<AugmentAccountManager>>,

    /// 重置状态跟踪
    pub reset_status: Arc<Mutex<ResetStatus>>,

    /// 验证码获取状态跟踪
    pub verification_status: Arc<RwLock<VerificationStatus>>,

    /// 窗口拖拽状态
    pub drag_state: Arc<Mutex<DragState>>,

    /// 版本检查器实例
    pub version_checker: Arc<TokioMutex<Option<VersionChecker>>>,

    /// QQ登录状态
    pub qq_login_state: Arc<TokioMutex<QQLoginState>>,

    /// QQ API实例 - 保持登录状态
    pub qq_api: Arc<TokioMutex<Option<crate::core::qq_api::QQGroupAPI>>>,

    /// 版本验证完成状态 - 防止重复验证
    pub verification_completed: Arc<TokioMutex<bool>>,

    /// QQ状态检查任务句柄 - 用于取消重复任务
    pub qq_status_check_task: Arc<TokioMutex<Option<tokio::task::JoinHandle<()>>>>,

    /// 时效检查任务句柄 - 全局时效倒计时
    pub expiry_check_task: Arc<TokioMutex<Option<tokio::task::JoinHandle<()>>>>,

    /// 时效状态 - 记录当前认证的过期时间
    pub expiry_status: Arc<TokioMutex<ExpiryStatus>>,

    /// 网络优化器状态
    pub network_optimizer_state: Arc<TokioMutex<crate::commands::network_optimizer::NetworkOptimizerState>>,
}

/// 重置状态
/// 对应原版 Python 的 reset_status 字典
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResetStatus {
    pub is_running: bool,
    pub completed: bool,
    pub success: bool,
    pub message: String,
}

/// 验证码获取状态
/// 对应原版 Python 的 verification_result 字典
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerificationStatus {
    pub is_running: bool,
    pub completed: bool,
    pub success: bool,
    pub message: String,
    pub code: String,
    pub progress: f64,
}

/// 窗口拖拽状态
/// 对应原版 Python 的拖拽相关变量
#[derive(Debug, Clone)]
pub struct DragState {
    pub drag_start_pos: Option<(i32, i32)>,
    pub drag_window_start_pos: Option<(i32, i32)>,
}

/// QQ登录状态
/// 对应原版Python VerificationDialog中的QQ登录状态管理
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QQLoginState {
    pub is_logged_in: bool,
    pub current_qq: String,
    pub qr_code_data: Option<Vec<u8>>,
    pub login_status: String, // "waiting", "scanned", "confirmed", "expired", "error"
    pub status_message: String,
}

/// 时效状态
/// 管理认证时效的全局状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExpiryStatus {
    /// 是否启用时效检查
    pub enabled: bool,
    /// 认证过期时间戳（Unix时间戳）
    pub expiry_timestamp: u64,
    /// 认证方式（"code_verification" 或 "vip_qq_verification"）
    pub verification_method: String,
    /// 剩余时间（秒）
    pub remaining_seconds: u64,
}

impl Default for ResetStatus {
    fn default() -> Self {
        Self {
            is_running: false,
            completed: false,
            success: false,
            message: String::new(),
        }
    }
}

impl Default for VerificationStatus {
    fn default() -> Self {
        Self {
            is_running: false,
            completed: false,
            success: false,
            message: String::new(),
            code: String::new(),
            progress: 0.0,
        }
    }
}

impl Default for DragState {
    fn default() -> Self {
        Self {
            drag_start_pos: None,
            drag_window_start_pos: None,
        }
    }
}

impl Default for QQLoginState {
    fn default() -> Self {
        Self {
            is_logged_in: false,
            current_qq: String::new(),
            qr_code_data: None,
            login_status: "waiting".to_string(),
            status_message: "请扫码登录QQ".to_string(),
        }
    }
}

impl Default for ExpiryStatus {
    fn default() -> Self {
        Self {
            enabled: false,
            expiry_timestamp: 0,
            verification_method: String::new(),
            remaining_seconds: 0,
        }
    }
}

impl AppState {
    /// 创建新的应用状态
    pub fn new() -> anyhow::Result<Self> {
        let config_manager = Arc::new(Mutex::new(ConfigManager::new()?));
        let editor_detector = Arc::new(EditorDetector::new());

        // 从配置管理器获取配置来初始化邮箱管理器
        let config = {
            let config_manager_guard = config_manager.lock().unwrap();
            config_manager_guard.get_config().clone()
        };

        let email_manager = Arc::new(Mutex::new(EmailManager::new(config)?));
        let augment_account_manager = Arc::new(Mutex::new(AugmentAccountManager::new()?));
        let reset_status = Arc::new(Mutex::new(ResetStatus::default()));
        let verification_status = Arc::new(RwLock::new(VerificationStatus::default()));
        let drag_state = Arc::new(Mutex::new(DragState::default()));
        let version_checker = Arc::new(TokioMutex::new(None));
        let qq_login_state = Arc::new(TokioMutex::new(QQLoginState::default()));
        let qq_api = Arc::new(TokioMutex::new(None));
        let verification_completed = Arc::new(TokioMutex::new(false));
        let qq_status_check_task = Arc::new(TokioMutex::new(None));
        let expiry_check_task = Arc::new(TokioMutex::new(None));
        let expiry_status = Arc::new(TokioMutex::new(ExpiryStatus::default()));
        let network_optimizer_state = Arc::new(TokioMutex::new(crate::commands::network_optimizer::NetworkOptimizerState::default()));

        Ok(Self {
            config_manager,
            editor_detector,
            email_manager,
            augment_account_manager,
            reset_status,
            verification_status,
            drag_state,
            version_checker,
            qq_login_state,
            qq_api,
            verification_completed,
            qq_status_check_task,
            expiry_check_task,
            expiry_status,
            network_optimizer_state,
        })
    }

    /// 创建包含版本检查器的应用状态
    pub fn new_with_version_checker(checker: VersionChecker) -> anyhow::Result<Self> {
        let config_manager = Arc::new(Mutex::new(ConfigManager::new()?));
        let editor_detector = Arc::new(EditorDetector::new());

        // 从配置管理器获取配置来初始化邮箱管理器
        let config = {
            let config_manager_guard = config_manager.lock().unwrap();
            config_manager_guard.get_config().clone()
        };

        let email_manager = Arc::new(Mutex::new(EmailManager::new(config)?));
        let augment_account_manager = Arc::new(Mutex::new(AugmentAccountManager::new()?));
        let reset_status = Arc::new(Mutex::new(ResetStatus::default()));
        let verification_status = Arc::new(RwLock::new(VerificationStatus::default()));
        let drag_state = Arc::new(Mutex::new(DragState::default()));
        let version_checker = Arc::new(TokioMutex::new(Some(checker)));
        let qq_login_state = Arc::new(TokioMutex::new(QQLoginState::default()));
        let qq_api = Arc::new(TokioMutex::new(None));
        let verification_completed = Arc::new(TokioMutex::new(true)); // 已验证过
        let qq_status_check_task = Arc::new(TokioMutex::new(None));
        let expiry_check_task = Arc::new(TokioMutex::new(None));
        let expiry_status = Arc::new(TokioMutex::new(ExpiryStatus::default()));
        let network_optimizer_state = Arc::new(TokioMutex::new(crate::commands::network_optimizer::NetworkOptimizerState::default()));

        Ok(Self {
            config_manager,
            editor_detector,
            email_manager,
            augment_account_manager,
            reset_status,
            verification_status,
            drag_state,
            version_checker,
            qq_login_state,
            qq_api,
            verification_completed,
            qq_status_check_task,
            expiry_check_task,
            expiry_status,
            network_optimizer_state,
        })
    }

    /// 更新邮箱管理器配置
    /// 对应原版 Python 的 save_config 方法中的配置更新逻辑
    pub fn update_email_manager_config(&self, config: serde_json::Value) -> anyhow::Result<()> {
        let mut email_manager = self.email_manager.lock().unwrap();
        email_manager.update_config(config)?;
        Ok(())
    }

    /// 获取重置状态的副本
    /// 对应原版 Python 的 get_reset_status 方法
    pub fn get_reset_status(&self) -> ResetStatus {
        let reset_status = self.reset_status.lock().unwrap();
        reset_status.clone()
    }

    /// 更新重置状态
    /// 对应原版 Python 中重置状态的更新逻辑
    pub fn update_reset_status(&self, status: ResetStatus) {
        let mut reset_status = self.reset_status.lock().unwrap();
        *reset_status = status;
    }

    /// 获取验证码状态的副本
    pub async fn get_verification_status(&self) -> VerificationStatus {
        let verification_status = self.verification_status.read().await;
        verification_status.clone()
    }

    /// 更新验证码状态
    pub async fn update_verification_status(&self, status: VerificationStatus) {
        let mut verification_status = self.verification_status.write().await;
        *verification_status = status;
    }

    /// 获取拖拽状态
    pub fn get_drag_state(&self) -> DragState {
        let drag_state = self.drag_state.lock().unwrap();
        drag_state.clone()
    }

    /// 更新拖拽状态
    pub fn update_drag_state(&self, state: DragState) {
        let mut drag_state = self.drag_state.lock().unwrap();
        *drag_state = state;
    }

    /// 设置版本检查器
    pub async fn set_version_checker(&self, checker: VersionChecker) {
        let mut version_checker = self.version_checker.lock().await;
        *version_checker = Some(checker);
    }

    /// 获取版本检查器的引用
    pub async fn get_version_checker(&self) -> Option<VersionChecker> {
        let version_checker = self.version_checker.lock().await;
        version_checker.clone()
    }
}
