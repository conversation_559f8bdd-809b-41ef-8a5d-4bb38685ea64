/// 进度报告工具
/// 对应原版 Python 的 _report_progress 模式和进度回调机制

/// 进度回调函数类型
/// 对应原版 Python: Callable[[str, float], None]
/// 参数顺序：(message: &str, progress: f64)
pub type ProgressCallback = Box<dyn Fn(&str, f64) + Send + Sync>;

/// 进度报告器
/// 对应原版 Python 中各个模块的进度报告机制
pub struct ProgressReporter {
    callback: Option<ProgressCallback>,
}

impl std::fmt::Debug for ProgressReporter {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ProgressReporter")
            .field("has_callback", &self.callback.is_some())
            .finish()
    }
}

impl ProgressReporter {
    /// 创建新的进度报告器
    pub fn new() -> Self {
        Self {
            callback: None,
        }
    }

    /// 设置进度回调函数
    /// 对应原版 Python 的 set_progress_callback 方法
    pub fn set_callback(&mut self, callback: ProgressCallback) {
        self.callback = Some(callback);
    }

    /// 报告进度
    /// 对应原版 Python 的 _report_progress 方法
    ///
    /// Args:
    ///     message: 进度消息
    ///     progress: 进度值 (0.0 - 1.0)
    pub fn report(&self, message: &str, progress: f64) {
        if let Some(ref callback) = self.callback {
            callback(message, progress);
        }
    }

    /// 检查是否有回调函数
    pub fn has_callback(&self) -> bool {
        self.callback.is_some()
    }
}

impl Default for ProgressReporter {
    fn default() -> Self {
        Self::new()
    }
}
