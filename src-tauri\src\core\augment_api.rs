/// 新API客户端
/// 
/// 创建AugmentApiClient，实现与https://yaugmentapi.yan.vin的加密通信
/// 包括获取Portal Token、查询加固状态、执行加固三个API端点

use serde::{Deserialize, Serialize};
use serde_json::Value;
use reqwest;
use std::time::Duration;
use anyhow::Result;
use crate::core::crypto_aes256::CryptoUtils;

/// API请求数据结构
/// 对应API_Documentation.md中的请求数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiRequestData {
    /// 会话token
    pub session_token: String,
    /// 主板sMBIOS UUID
    pub uuid: String,
}

/// 加密请求格式
/// 对应API_Documentation.md中的请求格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptedRequest {
    /// Base64编码的加密数据
    pub encrypted_data: String,
}

/// 加密响应格式
/// 对应API_Documentation.md中的响应格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptedResponse {
    /// Base64编码的加密响应数据
    pub encrypted_data: String,
}

/// Portal Token响应数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PortalTokenData {
    /// Portal Token
    pub portal_token: String,
    /// 原始订阅数据
    pub raw_subscription_data: Option<Value>,
}

/// 简单API响应结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SimpleApiResponse {
    /// 是否成功
    pub success: bool,
    /// 消息
    pub message: String,
    /// 响应类型
    pub response_type: String,
    /// 数据
    pub data: Option<Value>,
}



/// API响应基础结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    /// 是否成功
    pub success: bool,
    /// 消息
    pub message: String,
    /// 时间戳
    pub timestamp: Option<String>,
    /// 数据
    pub data: Option<T>,
}

/// Augment API客户端
/// 对应api_caller.py中的AugmentAPIService类
#[derive(Debug)]
pub struct AugmentApiClient {
    /// 基础URL
    base_url: String,
    /// HTTP客户端
    http_client: reqwest::Client,
    /// 是否显示详细日志
    verbose: bool,
}

impl AugmentApiClient {
    /// 创建新的Augment API客户端
    /// 对应api_caller.py的__init__方法
    pub fn new(base_url: Option<String>, verbose: bool) -> Result<Self> {
        let base_url = base_url.unwrap_or_else(|| "https://yaugmentapi.yan.vin".to_string());
        
        // 创建HTTP客户端 - 复用现有模式
        let http_client = reqwest::Client::builder()
            .timeout(Duration::from_secs(30))
            .build()?;

        Ok(Self {
            base_url,
            http_client,
            verbose,
        })
    }

    /// 加密请求数据
    /// 对应api_caller.py的_encrypt_request方法
    fn encrypt_request(&self, data: &ApiRequestData) -> Result<String> {
        let json_data = serde_json::to_string(data)?;
        if self.verbose {
            println!("原始请求数据: {}", json_data);
        }

        let encrypted = CryptoUtils::encrypt_with_fixed_key(&json_data)
            .map_err(|e| anyhow::anyhow!("加密失败: {}", e))?;
        if self.verbose {
            println!("加密后请求数据: {}", encrypted);
        }

        Ok(encrypted)
    }

    /// 解密响应数据
    /// 对应api_caller.py的_decrypt_response方法
    fn decrypt_response(&self, encrypted_data: &str, uuid: &str) -> Result<Value> {
        if self.verbose {
            println!("接收到加密响应: {}", encrypted_data);
        }

        let decrypted_json = CryptoUtils::decrypt_with_uuid_key(encrypted_data, uuid)
            .map_err(|e| anyhow::anyhow!("解密失败: {}", e))?;
        if self.verbose {
            println!("解密后响应数据: {}", decrypted_json);
        }

        let response: Value = serde_json::from_str(&decrypted_json)?;
        Ok(response)
    }

    /// 调用API的通用方法
    /// 对应api_caller.py的_call_api方法
    async fn call_api(&self, endpoint: &str, session_token: &str, uuid: &str) -> Result<Value> {
        // 准备请求数据
        let request_data = ApiRequestData {
            session_token: session_token.to_string(),
            uuid: uuid.to_string(),
        };

        // 加密请求
        let encrypted_request = self.encrypt_request(&request_data)?;
        
        // 构建请求体
        let request_body = EncryptedRequest {
            encrypted_data: encrypted_request,
        };

        // 发送请求
        let url = format!("{}{}", self.base_url, endpoint);
        if self.verbose {
            println!("发送到端点: {}", url);
        }

        let response = self.http_client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&request_body)
            .send()
            .await?;

        // 检查HTTP状态码
        if !response.status().is_success() {
            let status = response.status();
            let text = response.text().await.unwrap_or_default();
            if self.verbose {
                println!("HTTP错误: {}", status);
            }
            return Err(anyhow::anyhow!("HTTP {}: {}", status, text));
        }

        // 解析响应
        let response_data: EncryptedResponse = response.json().await?;
        
        // 解密响应
        let decrypted_response = self.decrypt_response(&response_data.encrypted_data, uuid)?;
        
        Ok(decrypted_response)
    }

    /// 获取Portal Token
    /// 对应api_caller.py的get_portal_token方法
    pub async fn get_portal_token(&self, session_token: &str, uuid: &str) -> Result<ApiResponse<PortalTokenData>> {
        if self.verbose {
            println!("=== 获取Token ===");
            println!("UUID: {}", uuid);
            println!("Token: {}...", &session_token[..50.min(session_token.len())]);
            println!("\n--- 开始加密通信过程 ---");
        }

        let result = self.call_api("/api/portal-token", session_token, uuid).await?;
        
        if self.verbose {
            println!("--- 加密通信过程结束 ---\n");
        }

        let response: ApiResponse<PortalTokenData> = serde_json::from_value(result)?;
        
        if response.success {
            if let Some(ref data) = response.data {
                if self.verbose {
                    println!("Token: {}", data.portal_token);
                    println!("获取成功!");
                }
            }
        } else {
            if self.verbose {
                println!("获取失败: {}", response.message);
            }
        }

        Ok(response)
    }

    /// 通过subscription接口获取Portal Token
    /// 对应subscription_fetcher.py的get_subscription_info方法
    pub async fn get_portal_token_from_subscription(&self, session_token: &str) -> Result<String> {
        let url = "https://app.augmentcode.com/api/subscription";

        if self.verbose {
            println!("=== 通过Subscription接口获取Portal Token ===");
            println!("URL: {}", url);
            println!("Session Token: {}...", &session_token[..50.min(session_token.len())]);
        }

        // 设置请求头，完全按照subscription_fetcher.py
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert("accept", "*/*".parse()?);
        headers.insert("accept-language", "zh-CN,zh;q=0.9".parse()?);
        headers.insert("priority", "u=1, i".parse()?);
        headers.insert("referer", "https://app.augmentcode.com/account/subscription".parse()?);
        headers.insert("sec-ch-ua", r#""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138""#.parse()?);
        headers.insert("sec-ch-ua-mobile", "?0".parse()?);
        headers.insert("sec-ch-ua-platform", r#""Windows""#.parse()?);
        headers.insert("sec-fetch-dest", "empty".parse()?);
        headers.insert("sec-fetch-mode", "cors".parse()?);
        headers.insert("sec-fetch-site", "same-origin".parse()?);
        headers.insert("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36".parse()?);

        // 设置Cookie，使用_session
        let cookie_value = format!("_session={}", session_token);
        headers.insert("cookie", cookie_value.parse()?);

        let client = reqwest::Client::new();
        let response = client
            .get(url)
            .headers(headers)
            .send()
            .await?;

        if response.status().is_success() {
            let data: serde_json::Value = response.json().await?;

            if self.verbose {
                println!("✅ 请求成功!");
                println!("响应数据: {}", serde_json::to_string_pretty(&data)?);
            }

            // 提取portalUrl中的token
            if let Some(portal_url) = data.get("portalUrl").and_then(|v| v.as_str()) {
                if self.verbose {
                    println!("🔗 Portal URL: {}", portal_url);
                }

                // 解析URL获取token参数
                let url = reqwest::Url::parse(portal_url)?;
                if let Some(token) = url.query_pairs().find(|(key, _)| key == "token").map(|(_, value)| value.to_string()) {
                    if self.verbose {
                        println!("🎯 提取的Token: {}", token);
                        println!("获取成功!");
                    }
                    return Ok(token);
                } else {
                    return Err(anyhow::anyhow!("未找到token参数"));
                }
            } else {
                return Err(anyhow::anyhow!("响应中未找到portalUrl"));
            }
        } else {
            return Err(anyhow::anyhow!("请求失败，状态码: {}", response.status()));
        }
    }

    /// 切换到社区计划
    /// 对应api_request.py的send_api_request方法
    pub async fn switch_to_community_plan(&self, session_token: &str) -> Result<SimpleApiResponse> {
        let url = "https://app.augmentcode.com/api/put-user-on-plan";

        if self.verbose {
            println!("=== 切换到社区计划 ===");
            println!("URL: {}", url);
            println!("Session Token: {}...", &session_token[..50.min(session_token.len())]);
        }

        // 设置请求头，完全按照api_request.py
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert("accept", "*/*".parse()?);
        headers.insert("accept-language", "zh-CN,zh;q=0.9".parse()?);
        headers.insert("content-type", "application/json".parse()?);
        headers.insert("origin", "https://app.augmentcode.com".parse()?);
        headers.insert("priority", "u=1, i".parse()?);
        headers.insert("referer", "https://app.augmentcode.com/account/subscription".parse()?);
        headers.insert("sec-ch-ua", r#""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138""#.parse()?);
        headers.insert("sec-ch-ua-mobile", "?0".parse()?);
        headers.insert("sec-ch-ua-platform", r#""Windows""#.parse()?);
        headers.insert("sec-fetch-dest", "empty".parse()?);
        headers.insert("sec-fetch-mode", "cors".parse()?);
        headers.insert("sec-fetch-site", "same-origin".parse()?);
        headers.insert("user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36".parse()?);

        // 设置Cookie
        let cookie_value = format!("_session={}", session_token);
        headers.insert("cookie", cookie_value.parse()?);

        // 请求数据
        let request_data = serde_json::json!({
            "planId": "orb_community_plan"
        });

        if self.verbose {
            println!("请求数据: {}", serde_json::to_string_pretty(&request_data)?);
        }

        let client = reqwest::Client::new();
        let response = client
            .post(url)
            .headers(headers)
            .json(&request_data)
            .send()
            .await?;

        if self.verbose {
            println!("响应状态码: {}", response.status());
        }

        if response.status().is_success() {
            let data: serde_json::Value = response.json().await?;

            if self.verbose {
                println!("✅ 请求成功!");
                println!("响应数据: {}", serde_json::to_string_pretty(&data)?);
            }

            // 解析响应
            let success = data.get("success").and_then(|v| v.as_bool()).unwrap_or(false);
            let message = data.get("message").and_then(|v| v.as_str()).unwrap_or("").to_string();
            let response_type = data.get("type").and_then(|v| v.as_str()).unwrap_or("").to_string();

            Ok(SimpleApiResponse {
                success,
                message,
                response_type,
                data: Some(data),
            })
        } else {
            let error_text = response.text().await.unwrap_or_else(|_| "未知错误".to_string());
            if self.verbose {
                println!("❌ 请求失败: {}", error_text);
            }

            Ok(SimpleApiResponse {
                success: false,
                message: format!("请求失败: {}", error_text),
                response_type: "error".to_string(),
                data: None,
            })
        }
    }

}
