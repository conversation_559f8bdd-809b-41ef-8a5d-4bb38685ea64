
// 防止重复初始化的标志
let pageAnimationsInitialized = false;

// 页面动画初始化
function initPageAnimations() {
    // 防止重复初始化
    if (pageAnimationsInitialized) {
        console.log('页面动画已经初始化，跳过重复初始化');
        return;
    }

    pageAnimationsInitialized = true;
    console.log('开始初始化页面动画');

    // 初始化粒子背景
    createParticleField();

    // Hero动画 - 检查元素是否存在
    if (document.querySelector('.hero-title')) {
        const timeline = gsap.timeline();

        timeline.to('.hero-title', {
            duration: 1,
            y: 0,
            opacity: 1,
            ease: 'power3.out'
        });

        if (document.querySelector('.hero-subtitle')) {
            timeline.to('.hero-subtitle', {
                duration: 1,
                y: 0,
                opacity: 1,
                ease: 'power3.out'
            }, '-=0.7');
        }

        if (document.querySelector('.selected-editor')) {
            timeline.to('.selected-editor', {
                duration: 1,
                scale: 1,
                opacity: 1,
                ease: 'back.out(1.7)'
            }, '-=0.5');
        }

        timeline.call(() => {
            // 创建并显示滚动提示
            createScrollHint();
        }, null, '+=1');
    }

    // 新的功能区域动画 - 重置容器和邮箱容器
    if (document.querySelector('.reset-container')) {
        gsap.to('.reset-container', {
            duration: 1.2,
            y: 0,
            opacity: 1,
            ease: 'power3.out',
            scrollTrigger: {
                trigger: '.features-section',
                start: 'top 60%',
                toggleActions: 'play none none reverse'
            }
        });
    }

    // 新的邮箱工作流动画 - 使用timeline精确控制时序
    if (document.querySelector('.email-workflow-container')) {
        const emailWorkflowTimeline = gsap.timeline({
            scrollTrigger: {
                trigger: '.features-section',
                start: 'top 60%',
                toggleActions: 'play none none reverse'
            }
        });

        // 首先让邮箱工作流容器入场
        emailWorkflowTimeline.to('.email-workflow-container', {
            duration: 0.8,
            y: 0,
            opacity: 1,
            delay: 0.1,
            ease: 'power3.out'
        })
        // 等待0.5秒后，浮动卡片开始淡入
        .to('.floating-card.card-1', {
            duration: 0.8,
            opacity: 1,
            ease: 'power2.out',
            delay: 0.5,  // 等待0.5秒
            onComplete: function() {
                // 动画完成后启用CSS浮动动画
                gsap.set('.floating-card.card-1', { animation: 'float1 3s ease-in-out infinite' });
            }
        })
        // 第二个卡片稍后出现
        .to('.floating-card.card-2', {
            duration: 0.8,
            opacity: 1,
            ease: 'power2.out',
            delay: 0.15,  // 第一个卡片后0.15秒
            onComplete: function() {
                // 动画完成后启用CSS浮动动画
                gsap.set('.floating-card.card-2', { animation: 'float2 3s ease-in-out infinite 1.5s' });
            }
        });
    }

    // 兼容旧版邮箱容器动画
    if (document.querySelector('.email-container')) {
        gsap.to('.email-container', {
            duration: 1.2,
            y: 0,
            opacity: 1,
            delay: 0.2,
            ease: 'power3.out',
            scrollTrigger: {
                trigger: '.features-section',
                start: 'top 60%',
                toggleActions: 'play none none reverse'
            }
        });
    }

    // Augment账号管理组件动画
    if (document.querySelector('.augment-account-container')) {
        gsap.fromTo('.augment-account-container',
            {
                y: 100,
                opacity: 0
            },
            {
                duration: 1,
                y: 0,
                opacity: 1,
                delay: 0.3, // 在邮箱工作流之后显示
                ease: 'power3.out',
                scrollTrigger: {
                    trigger: '.features-section',
                    start: 'top 60%',
                    toggleActions: 'play none none reverse'
                }
            }
        );
    }

    // 网络优化器组件动画
    if (document.querySelector('.network-optimizer-container')) {
        gsap.fromTo('.network-optimizer-container',
            {
                y: 100,
                opacity: 0
            },
            {
                duration: 1,
                y: 0,
                opacity: 1,
                delay: 0.5, // 在账号管理组件之后显示
                ease: 'power3.out',
                scrollTrigger: {
                    trigger: '.features-section',
                    start: 'top 60%',
                    toggleActions: 'play none none reverse'
                }
            }
        );
    }

    // 兼容旧的功能卡片动画
    if (document.querySelector('.feature-card')) {
        gsap.to('.feature-card', {
            duration: 1,
            y: 0,
            opacity: 1,
            stagger: 0.2,
            ease: 'power3.out',
            scrollTrigger: {
                trigger: '.features-section',
                start: 'top 60%',
                toggleActions: 'play none none reverse'
            }
        });
    }

    // 滚动视差效果
    setupScrollParallax();
}

// 创建粒子背景
function createParticleField() {
    const particleField = document.getElementById('particleField');
    if (!particleField) return;

    const particleCount = 25; // 减少粒子数量
    const particles = [];

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';

        // 随机大小
        const size = Math.random() * 4 + 1;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;

        // 随机位置
        particle.style.left = `${Math.random() * 100}%`;
        particle.style.top = `${Math.random() * 100}%`;

        particleField.appendChild(particle);
        particles.push(particle);

        // 随机动画
        animateParticle(particle);
    }

    // 优化的鼠标交互 - 使用节流
    let mouseX = 0;
    let mouseY = 0;
    let isMouseInteracting = false;

    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;

        // 节流处理，避免过度计算
        if (!isMouseInteracting) {
            isMouseInteracting = true;
            requestAnimationFrame(() => {
                updateParticleInteraction(particles, mouseX, mouseY);
                isMouseInteracting = false;
            });
        }
    });
}

// 优化的粒子交互函数
function updateParticleInteraction(particles, mouseX, mouseY) {
    particles.forEach((particle, index) => {
        // 只处理部分粒子以提高性能
        if (index % 2 !== 0) return;

        const rect = particle.getBoundingClientRect();
        const particleX = rect.left + rect.width / 2;
        const particleY = rect.top + rect.height / 2;

        const distance = Math.sqrt(
            Math.pow(mouseX - particleX, 2) +
            Math.pow(mouseY - particleY, 2)
        );

        if (distance < 100) { // 减小交互范围
            const angle = Math.atan2(mouseY - particleY, mouseX - particleX);
            const force = (100 - distance) / 100;

            gsap.to(particle, {
                duration: 0.5,
                x: -Math.cos(angle) * force * 30,
                y: -Math.sin(angle) * force * 30,
                ease: 'power2.out'
            });
        } else {
            gsap.to(particle, {
                duration: 1.5,
                x: 0,
                y: 0,
                ease: 'power2.out'
            });
        }
    });
}

// 粒子动画
function animateParticle(particle) {
    const duration = Math.random() * 20 + 10;
    const delay = Math.random() * 5;
    
    gsap.timeline({ repeat: -1 })
        .to(particle, {
            duration: duration,
            x: `+=${Math.random() * 200 - 100}`,
            y: `+=${Math.random() * 200 - 100}`,
            opacity: Math.random() * 0.5 + 0.3,
            ease: 'none',
            delay: delay
        })
        .to(particle, {
            duration: duration,
            x: `+=${Math.random() * 200 - 100}`,
            y: `+=${Math.random() * 200 - 100}`,
            opacity: Math.random() * 0.5 + 0.3,
            ease: 'none'
        });
}

// 优化的滚动视差效果
function setupScrollParallax() {
    // 简化的Hero背景视差 - 使用更轻量的实现
    if (document.querySelector('.hero-background')) {
        gsap.to('.hero-background', {
            yPercent: -30, // 减少移动距离
            ease: 'none',
            scrollTrigger: {
                trigger: '.hero-section',
                start: 'top top',
                end: 'bottom top',
                scrub: 1 // 使用数值而不是true，提供更好的性能
            }
        });
    }

    // 简化的功能卡片效果 - 移除3D变换
    document.querySelectorAll('.feature-card').forEach((card) => {
        if (card) {
            gsap.to(card, {
                yPercent: -10, // 减少移动距离
                ease: 'none',
                scrollTrigger: {
                    trigger: card,
                    start: 'top bottom',
                    end: 'bottom top',
                    scrub: 2 // 增加scrub值以减少频繁更新
                }
            });
        }
    });

    // 关于区域动画 - 创意分散式设计
    if (document.querySelector('.about-section')) {
        // 关于区域动画 - 使用timeline确保动画顺序和最终状态
        const aboutTimeline = gsap.timeline({
            scrollTrigger: {
                trigger: '.about-section',
                start: 'top 80%',
                once: true
            }
        });

        // 关于标题 - 从左边放大进入
        aboutTimeline.to('.about-header', {
            duration: 1,
            x: 0,
            scale: 1,
            opacity: 1,
            ease: 'power2.out'
        })
        // 主应用名 - 从右上旋转进入
        .to('.app-main-info', {
            duration: 1,
            x: 0,
            y: 0,
            rotation: -3,
            opacity: 1,
            ease: 'power2.out'
        }, '-=0.8')
        // 版权信息 - 从左往右插入淡入
        .fromTo('.copyright-item', {
            x: -100,
            opacity: 0,
            rotation: 3
        }, {
            duration: 1.0,
            x: 0,
            opacity: 1,
            rotation: 3,
            ease: 'power2.out'
        }, '-=0.6')
        // 邮箱信息 - 从右往左插入淡入
        .fromTo('.email-item', {
            x: 100,
            opacity: 0,
            rotation: -2.5
        }, {
            duration: 1.0,
            x: 0,
            opacity: 1,
            rotation: -2.5,
            ease: 'power2.out'
        }, '-=0.7')
        // QQ群信息 - 从左往右插入淡入
        .fromTo('.qq-item', {
            x: -100,
            opacity: 0,
            rotation: -10
        }, {
            duration: 1.0,
            x: 0,
            opacity: 1,
            rotation: -10,
            ease: 'power2.out'
        }, '-=0.7')
        // 感谢信息 - 从右下旋转进入
        .to('.thanks-message', {
            duration: 0.8,
            x: 0,
            y: 0,
            rotation: 2,
            opacity: 1,
            ease: 'power2.out'
        }, '-=0.6')
        // 编程工作环境 - 延迟0.5秒后淡入
        .to('.coding-workspace', {
            duration: 4.0,
            opacity: 1,
            ease: 'power2.out',
            delay: 0.3
        }, '-=0.8')
        // 动画完成后启用hover效果
        .call(() => {
            // 为所有关于区域的元素添加hover transition
            const aboutItems = document.querySelectorAll('.copyright-item, .email-item, .qq-item, .thanks-message');
            aboutItems.forEach(item => {
                item.style.transition = 'transform 0.3s ease, background 0.3s ease, border-color 0.3s ease';
            });
        });
    }
}

// 重置动画初始化标志（用于调试或重新初始化）
function resetPageAnimations() {
    pageAnimationsInitialized = false;
    console.log('页面动画初始化标志已重置');
}

// 编辑器选择页面动画
document.addEventListener('DOMContentLoaded', () => {
    // 如果在编辑器选择页面
    const editorSelectPage = document.getElementById('editorSelectPage');
    if (editorSelectPage && !editorSelectPage.classList.contains('hidden')) {
        // 标题动画 - 检查元素是否存在
        if (document.querySelector('.page-title')) {
            gsap.from('.page-title', {
                duration: 1,
                y: -50,
                opacity: 0,
                ease: 'power3.out'
            });
        }

        if (document.querySelector('.page-subtitle')) {
            gsap.from('.page-subtitle', {
                duration: 1,
                y: -30,
                opacity: 0,
                delay: 0.2,
                ease: 'power3.out'
            });
        }

        // 编辑器卡片动画 - 检查元素是否存在
        if (document.querySelector('.editor-card')) {
            gsap.from('.editor-card', {
                duration: 1,
                scale: 0.8,
                opacity: 0,
                stagger: 0.2,
                delay: 0.5,
                ease: 'back.out(1.7)'
            });
        }
    }
});

// 添加粒子样式
const particleStyle = document.createElement('style');
particleStyle.textContent = `
    .particle {
        position: absolute;
        background: radial-gradient(circle, var(--purple-primary), transparent);
        border-radius: 50%;
        pointer-events: none;
        opacity: 0.3;
        will-change: transform;
        transform: translateZ(0); /* 强制GPU加速 */
        backface-visibility: hidden; /* 优化3D变换 */
    }
    
    /* 加载动画 */
    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }
    
    /* 脉冲动画 */
    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }
    }
    
    /* 旋转动画 */
    @keyframes rotate {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }
    
    /* 浮动动画 */
    @keyframes float {
        0%, 100% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-20px);
        }
    }
    
    /* 优化的应用动画 */
    .glitch-text {
        animation: pulse 3s ease-in-out infinite; /* 减慢动画速度 */
    }

    .card-icon {
        /* 移除持续动画，只在悬停时启用 */
        transition: transform 0.3s ease;
    }

    .loading-spinner {
        animation: rotate 1s linear infinite;
    }

    /* 优化的悬停效果 */
    .feature-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        will-change: transform; /* 优化GPU加速 */
    }

    .feature-card:hover {
        transform: translateY(-5px) scale(1.01); /* 减少变换幅度 */
    }

    .feature-card:hover .card-icon {
        animation: float 2s ease-in-out infinite; /* 悬停时才启用动画 */
    }
    
    /* 按钮悬停效果 */
    .action-btn {
        position: relative;
        overflow: hidden;
    }
    
    .action-btn::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }
    
    .action-btn:hover::before {
        width: 300px;
        height: 300px;
    }
    
    /* 卡片光效 */
    .card-glow {
        animation: pulse 3s ease-in-out infinite;
    }
    
    /* 滚动提示动画 */
    @keyframes scrollHint {
        0%, 100% {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
        }
        50% {
            transform: translateX(-50%) translateY(10px);
            opacity: 0.5;
        }
    }
    
    .scroll-hint {
        position: absolute;
        bottom: 60px;
        left: 50%;
        transform: translateX(-50%) translateY(20px);
        opacity: 0;
        color: var(--purple-light);
        font-size: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        /* 不立即播放动画，等入场动画完成后再启用 */
    }

    .scroll-hint:hover {
        color: var(--purple-primary);
        transform: translateX(-50%) translateY(15px);
    }

    .scroll-hint.animate {
        animation: scrollHint 2s ease-in-out infinite;
    }
`;
document.head.appendChild(particleStyle);

// 创建滚动提示
function createScrollHint() {
    // 检查是否已经存在滚动提示，避免重复创建
    if (document.querySelector('.scroll-hint')) {
        return;
    }

    const hint = document.createElement('div');
    hint.className = 'scroll-hint';
    hint.innerHTML = `
        <svg width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 5v14M19 12l-7 7-7-7"/>
        </svg>
    `;

    // 添加点击滚动功能
    hint.addEventListener('click', () => {
        const featuresSection = document.querySelector('.features-section');
        if (featuresSection) {
            featuresSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });

    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        heroSection.appendChild(hint);

        // 添加平滑的入场动画
        gsap.to(hint, {
            duration: 1.5,
            opacity: 1,
            y: 0,
            ease: 'power3.out',
            delay: 0.3,
            onComplete: () => {
                // 入场动画完成后，启用浮动动画
                hint.classList.add('animate');
            }
        });
    }
}

// 滚动提示现在在主页动画序列中创建，不需要延迟创建

// 性能优化：检测设备性能并调整动画
function optimizeForPerformance() {
    // 检测是否为低性能设备
    const isLowPerformance = navigator.hardwareConcurrency <= 4 ||
                            window.devicePixelRatio > 2;

    if (isLowPerformance) {
        // 禁用部分动画效果
        const style = document.createElement('style');
        style.textContent = `
            .particle {
                display: none !important;
            }
            .feature-card:hover {
                transform: translateY(-2px) !important;
            }
            .glitch-text {
                animation: none !important;
            }
        `;
        document.head.appendChild(style);

        // 禁用ScrollTrigger的scrub效果
        if (typeof ScrollTrigger !== 'undefined') {
            ScrollTrigger.config({
                autoRefreshEvents: "visibilitychange,DOMContentLoaded,load"
            });
        }
    }
}

// 在页面加载完成后执行性能优化
document.addEventListener('DOMContentLoaded', optimizeForPerformance);