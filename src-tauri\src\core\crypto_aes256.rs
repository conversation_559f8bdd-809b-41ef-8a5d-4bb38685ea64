/// AES-256-CBC 加密模块
/// 
/// 对应 API_Documentation.md 中的加密要求
/// 实现固定Key加密请求和UUID Key解密响应的功能

use aes::Aes256;
use cbc::{Decryptor, Encryptor};
use cbc::cipher::{BlockDecryptMut, BlockEncryptMut, KeyIvInit};
use sha2::{Sha256, Digest};
use base64::{Engine as _, engine::general_purpose};
use rand::RngCore;
use machine_uid;

type Aes256CbcEnc = Encryptor<Aes256>;
type Aes256CbcDec = Decryptor<Aes256>;

/// 固定密钥，用于加密请求数据
/// 对应 API_Documentation.md 第9行的固定Key
const FIXED_KEY: &[u8] = b"K7m9#Qp2$Vx8&Nz5!Rt6@Wy4^Lu3*Jh1";

/// AES-256-CBC 加密工具
/// 对应 API_Documentation.md 中的 CryptoUtils 结构体
pub struct CryptoUtils;

impl CryptoUtils {
    /// 标准化UUID密钥为32字节
    /// 对应 API_Documentation.md 第189-193行的 normalize_key 方法
    /// 
    /// # 参数
    /// - `uuid`: 主板sMBIOS UUID字符串
    /// 
    /// # 返回
    /// - `[u8; 32]`: 标准化的32字节密钥
    fn normalize_key(uuid: &str) -> [u8; 32] {
        let mut hasher = Sha256::new();
        hasher.update(uuid.as_bytes());
        hasher.finalize().into()
    }
    
    /// 使用固定密钥加密数据
    /// 对应 API_Documentation.md 第196-211行的 encrypt_with_fixed_key 方法
    /// 
    /// # 参数
    /// - `plaintext`: 要加密的明文字符串
    /// 
    /// # 返回
    /// - `Ok(String)`: Base64编码的加密数据（包含IV）
    /// - `Err(Box<dyn std::error::Error>)`: 加密失败的错误信息
    pub fn encrypt_with_fixed_key(plaintext: &str) -> Result<String, Box<dyn std::error::Error>> {
        // 生成随机IV
        let mut iv = [0u8; 16];
        rand::thread_rng().fill_bytes(&mut iv);
        
        // 创建加密器
        let cipher = Aes256CbcEnc::new(FIXED_KEY.into(), &iv.into());
        
        // 准备数据并添加PKCS7填充
        let mut buffer = plaintext.as_bytes().to_vec();
        let original_len = buffer.len();
        let padding_len = 16 - (buffer.len() % 16);
        buffer.extend(vec![padding_len as u8; padding_len]);

        // 执行加密
        let encrypted = cipher.encrypt_padded_mut::<cbc::cipher::block_padding::Pkcs7>(&mut buffer, original_len)
            .map_err(|e| format!("AES加密失败: {:?}", e))?;
        
        // 组合IV和密文
        let mut result = iv.to_vec();
        result.extend_from_slice(&encrypted);
        
        // Base64编码
        Ok(general_purpose::STANDARD.encode(&result))
    }
    
    /// 使用UUID密钥解密数据
    /// 对应 API_Documentation.md 第214-226行的 decrypt_with_uuid_key 方法
    /// 
    /// # 参数
    /// - `encrypted_data`: Base64编码的加密数据
    /// - `uuid`: 主板sMBIOS UUID字符串
    /// 
    /// # 返回
    /// - `Ok(String)`: 解密后的明文字符串
    /// - `Err(Box<dyn std::error::Error>)`: 解密失败的错误信息
    pub fn decrypt_with_uuid_key(encrypted_data: &str, uuid: &str) -> Result<String, Box<dyn std::error::Error>> {
        // 标准化UUID为32字节密钥
        let key = Self::normalize_key(uuid);
        
        // Base64解码
        let data = general_purpose::STANDARD.decode(encrypted_data)?;
        
        // 分离IV和密文
        if data.len() < 16 {
            return Err("加密数据太短，无法提取IV".into());
        }
        let (iv, encrypted) = data.split_at(16);
        
        // 创建解密器
        let cipher = Aes256CbcDec::new(&key.into(), iv.into());
        
        // 执行解密
        let mut buffer = encrypted.to_vec();
        let decrypted = cipher.decrypt_padded_mut::<cbc::cipher::block_padding::Pkcs7>(&mut buffer)
            .map_err(|e| format!("AES解密失败: {:?}", e))?;
        
        // 转换为UTF-8字符串
        Ok(String::from_utf8(decrypted.to_vec())?)
    }
    
    /// 获取主板sMBIOS UUID
    /// 重用现有的machine_uid库获取UUID
    /// 对应版本验证中的get_machine_uuid方法
    /// 
    /// # 返回
    /// - `Ok(String)`: 主板UUID字符串
    /// - `Err(String)`: 获取失败的错误信息
    pub fn get_machine_uuid() -> Result<String, String> {
        machine_uid::get()
            .map_err(|e| format!("获取机器UUID失败: {}", e))
    }
}


