// use std::collections::HashMap;
use std::env;
use std::path::Path;
use std::process::Command;
// use anyhow::Result;
use serde_json::{json, Value};

/// 创建隐藏终端窗口的命令
/// 防止在执行系统命令时弹出终端窗口
fn create_hidden_command(program: &str) -> Command {
    #[cfg(windows)]
    let mut cmd = Command::new(program);
    #[cfg(not(windows))]
    let cmd = Command::new(program);

    #[cfg(windows)]
    {
        use std::os::windows::process::CommandExt;
        // Windows: 使用 CREATE_NO_WINDOW 标志隐藏窗口
        cmd.creation_flags(0x08000000); // CREATE_NO_WINDOW
    }

    cmd
}

/// 编辑器检测器
/// 对应原版 Python 的 EditorDetector 类
#[derive(Debug)]
pub struct EditorDetector {
    platform: String,
}

impl EditorDetector {
    /// 创建新的编辑器检测器实例
    /// 对应原版 Python 的 __init__ 方法
    pub fn new() -> Self {
        let platform = if cfg!(target_os = "windows") {
            "win32".to_string()
        } else if cfg!(target_os = "macos") {
            "darwin".to_string()
        } else {
            "linux".to_string()
        };

        Self { platform }
    }

    /// 检测 VS Code 是否安装
    /// 对应原版 Python 的 detect_vscode 方法
    /// 返回: (是否安装, 路径, 版本)
    pub fn detect_vscode(&self) -> (bool, Option<String>, Option<String>) {
        match self.platform.as_str() {
            "win32" => self.detect_vscode_windows(),
            "darwin" => self.detect_vscode_macos(),
            _ => self.detect_vscode_linux(),
        }
    }

    /// Windows 系统下检测 VS Code
    fn detect_vscode_windows(&self) -> (bool, Option<String>, Option<String>) {
        // 检查常见安装路径 - 修复路径拼接问题
        let possible_paths = vec![
            // LOCALAPPDATA\Programs\Microsoft VS Code\Code.exe
            format!("{}\\Programs\\Microsoft VS Code\\Code.exe",
                env::var("LOCALAPPDATA").unwrap_or_else(|_| String::new())),
            // PROGRAMFILES\Microsoft VS Code\Code.exe
            format!("{}\\Microsoft VS Code\\Code.exe",
                env::var("PROGRAMFILES").unwrap_or_else(|_| String::new())),
            // PROGRAMFILES(X86)\Microsoft VS Code\Code.exe
            format!("{}\\Microsoft VS Code\\Code.exe",
                env::var("PROGRAMFILES(X86)").unwrap_or_else(|_| String::new())),
        ];

        // 过滤掉空路径并检查文件是否存在
        for path_str in possible_paths {
            if !path_str.starts_with("\\") && !path_str.is_empty() {
                let path = Path::new(&path_str);
                if path.exists() {
                    println!("Found VS Code at: {}", path_str);
                    return (true, Some(path_str), Some("Unknown".to_string()));
                }
            }
        }

        // 尝试 where 命令
        if let Ok(output) = create_hidden_command("where")
            .arg("code")
            .output()
        {
            if output.status.success() {
                let stdout = String::from_utf8_lossy(&output.stdout);
                if let Some(path) = stdout.lines().next() {
                    let path = path.trim();
                    if !path.is_empty() {
                        println!("Found VS Code via where command: {}", path);
                        return (true, Some(path.to_string()), Some("Unknown".to_string()));
                    }
                }
            }
        }

        println!("VS Code not found");
        (false, None, None)
    }

    /// macOS 系统下检测 VS Code
    fn detect_vscode_macos(&self) -> (bool, Option<String>, Option<String>) {
        let app_path = "/Applications/Visual Studio Code.app/Contents/Resources/app/bin/code";
        if Path::new(app_path).exists() {
            (true, Some(app_path.to_string()), Some("Unknown".to_string()))
        } else {
            (false, None, None)
        }
    }

    /// Linux 系统下检测 VS Code
    fn detect_vscode_linux(&self) -> (bool, Option<String>, Option<String>) {
        if let Ok(output) = create_hidden_command("which")
            .arg("code")
            .output()
        {
            if output.status.success() {
                let stdout = String::from_utf8_lossy(&output.stdout);
                let path = stdout.trim();
                if !path.is_empty() {
                    return (true, Some(path.to_string()), Some("Unknown".to_string()));
                }
            }
        }
        (false, None, None)
    }

    /// 检测 Cursor 是否安装
    /// 对应原版 Python 的 detect_cursor 方法
    /// 返回: (是否安装, 路径, 版本)
    pub fn detect_cursor(&self) -> (bool, Option<String>, Option<String>) {
        match self.platform.as_str() {
            "win32" => self.detect_cursor_windows(),
            "darwin" => self.detect_cursor_macos(),
            _ => self.detect_cursor_linux(),
        }
    }

    /// Windows 系统下检测 Cursor
    fn detect_cursor_windows(&self) -> (bool, Option<String>, Option<String>) {
        // 检查常见安装路径 - 修复路径拼接问题
        let possible_paths = vec![
            // LOCALAPPDATA\Programs\Cursor\Cursor.exe
            format!("{}\\Programs\\Cursor\\Cursor.exe",
                env::var("LOCALAPPDATA").unwrap_or_else(|_| String::new())),
            // APPDATA\..\Local\Programs\Cursor\Cursor.exe (等同于 LOCALAPPDATA\Programs\Cursor\Cursor.exe)
            format!("{}\\..\\Local\\Programs\\Cursor\\Cursor.exe",
                env::var("APPDATA").unwrap_or_else(|_| String::new())),
        ];

        // 过滤掉空路径并检查文件是否存在
        for path_str in possible_paths {
            if !path_str.starts_with("\\") && !path_str.is_empty() {
                let path = Path::new(&path_str);
                if path.exists() {
                    println!("Found Cursor at: {}", path_str);
                    return (true, Some(path_str), Some("Unknown".to_string()));
                }
            }
        }

        // 尝试 where 命令
        if let Ok(output) = create_hidden_command("where")
            .arg("cursor")
            .output()
        {
            if output.status.success() {
                let stdout = String::from_utf8_lossy(&output.stdout);
                if let Some(path) = stdout.lines().next() {
                    let path = path.trim();
                    if !path.is_empty() {
                        println!("Found Cursor via where command: {}", path);
                        return (true, Some(path.to_string()), Some("Unknown".to_string()));
                    }
                }
            }
        }

        println!("Cursor not found");
        (false, None, None)
    }

    /// macOS 系统下检测 Cursor
    fn detect_cursor_macos(&self) -> (bool, Option<String>, Option<String>) {
        let app_path = "/Applications/Cursor.app/Contents/Resources/app/bin/cursor";
        if Path::new(app_path).exists() {
            (true, Some(app_path.to_string()), Some("Unknown".to_string()))
        } else {
            (false, None, None)
        }
    }

    /// Linux 系统下检测 Cursor
    fn detect_cursor_linux(&self) -> (bool, Option<String>, Option<String>) {
        if let Ok(output) = create_hidden_command("which")
            .arg("cursor")
            .output()
        {
            if output.status.success() {
                let stdout = String::from_utf8_lossy(&output.stdout);
                let path = stdout.trim();
                if !path.is_empty() {
                    return (true, Some(path.to_string()), Some("Unknown".to_string()));
                }
            }
        }
        (false, None, None)
    }

    /// 安全地获取 VS Code 版本
    /// 对应原版 Python 的 _get_vscode_version 方法
    fn _get_vscode_version(&self, code_path: &str) -> String {
        // 使用 --help 参数避免启动编辑器
        if let Ok(output) = create_hidden_command(code_path)
            .arg("--help")
            .output()
        {
            if output.status.success() {
                let stdout = String::from_utf8_lossy(&output.stdout);
                return self._extract_version_from_help(&stdout);
            }
        }
        "Unknown".to_string()
    }

    /// 从 help 文本中提取版本信息
    /// 对应原版 Python 的 _extract_version_from_help 方法
    fn _extract_version_from_help(&self, help_text: &str) -> String {
        use regex::Regex;

        for line in help_text.lines() {
            if line.to_lowercase().contains("version") {
                // 尝试提取版本号
                if let Ok(re) = Regex::new(r"\d+\.\d+\.\d+") {
                    if let Some(captures) = re.find(line) {
                        return captures.as_str().to_string();
                    }
                }
            }
        }
        "Unknown".to_string()
    }

    /// 检测IntelliJ IDEA是否安装
    /// 返回: (是否安装, 配置路径, 版本信息)
    pub fn detect_intellij(&self) -> (bool, Option<String>, Option<String>) {
        use crate::tools::jetbrains::paths::get_jetbrains_info;

        let (installed, config_path) = get_jetbrains_info();
        if installed {
            (true, config_path, Some("IntelliJ IDEA".to_string()))
        } else {
            (false, None, None)
        }
    }

    /// 检测PyCharm是否安装
    /// 返回: (是否安装, 配置路径, 版本信息)
    pub fn detect_pycharm(&self) -> (bool, Option<String>, Option<String>) {
        use crate::tools::jetbrains::paths::get_jetbrains_info;

        let (installed, config_path) = get_jetbrains_info();
        if installed {
            (true, config_path, Some("PyCharm".to_string()))
        } else {
            (false, None, None)
        }
    }

    /// 检测WebStorm是否安装
    /// 返回: (是否安装, 配置路径, 版本信息)
    pub fn detect_webstorm(&self) -> (bool, Option<String>, Option<String>) {
        use crate::tools::jetbrains::paths::get_jetbrains_info;

        let (installed, config_path) = get_jetbrains_info();
        if installed {
            (true, config_path, Some("WebStorm".to_string()))
        } else {
            (false, None, None)
        }
    }

    /// 检测PhpStorm是否安装
    pub fn detect_phpstorm(&self) -> (bool, Option<String>, Option<String>) {
        use crate::tools::jetbrains::paths::get_jetbrains_info;
        let (installed, config_path) = get_jetbrains_info();
        if installed { (true, config_path, Some("PhpStorm".to_string())) } else { (false, None, None) }
    }

    /// 检测RubyMine是否安装
    pub fn detect_rubymine(&self) -> (bool, Option<String>, Option<String>) {
        use crate::tools::jetbrains::paths::get_jetbrains_info;
        let (installed, config_path) = get_jetbrains_info();
        if installed { (true, config_path, Some("RubyMine".to_string())) } else { (false, None, None) }
    }

    /// 检测CLion是否安装
    pub fn detect_clion(&self) -> (bool, Option<String>, Option<String>) {
        use crate::tools::jetbrains::paths::get_jetbrains_info;
        let (installed, config_path) = get_jetbrains_info();
        if installed { (true, config_path, Some("CLion".to_string())) } else { (false, None, None) }
    }

    /// 检测GoLand是否安装
    pub fn detect_goland(&self) -> (bool, Option<String>, Option<String>) {
        use crate::tools::jetbrains::paths::get_jetbrains_info;
        let (installed, config_path) = get_jetbrains_info();
        if installed { (true, config_path, Some("GoLand".to_string())) } else { (false, None, None) }
    }

    /// 检测Rider是否安装
    pub fn detect_rider(&self) -> (bool, Option<String>, Option<String>) {
        use crate::tools::jetbrains::paths::get_jetbrains_info;
        let (installed, config_path) = get_jetbrains_info();
        if installed { (true, config_path, Some("Rider".to_string())) } else { (false, None, None) }
    }

    /// 检测DataGrip是否安装
    pub fn detect_datagrip(&self) -> (bool, Option<String>, Option<String>) {
        use crate::tools::jetbrains::paths::get_jetbrains_info;
        let (installed, config_path) = get_jetbrains_info();
        if installed { (true, config_path, Some("DataGrip".to_string())) } else { (false, None, None) }
    }

    /// 检测Android Studio是否安装
    pub fn detect_androidstudio(&self) -> (bool, Option<String>, Option<String>) {
        use crate::tools::jetbrains::paths::get_jetbrains_info;
        let (installed, config_path) = get_jetbrains_info();
        if installed { (true, config_path, Some("Android Studio".to_string())) } else { (false, None, None) }
    }



    /// 检测所有编辑器
    /// 对应原版 Python 的 detect_all 方法
    pub fn detect_all(&self) -> Value {
        let (vscode_installed, vscode_path, vscode_version) = self.detect_vscode();
        let (cursor_installed, cursor_path, cursor_version) = self.detect_cursor();
        let (intellij_installed, intellij_path, intellij_version) = self.detect_intellij();
        let (pycharm_installed, pycharm_path, pycharm_version) = self.detect_pycharm();
        let (webstorm_installed, webstorm_path, webstorm_version) = self.detect_webstorm();
        let (phpstorm_installed, phpstorm_path, phpstorm_version) = self.detect_phpstorm();
        let (rubymine_installed, rubymine_path, rubymine_version) = self.detect_rubymine();
        let (clion_installed, clion_path, clion_version) = self.detect_clion();
        let (goland_installed, goland_path, goland_version) = self.detect_goland();
        let (rider_installed, rider_path, rider_version) = self.detect_rider();
        let (datagrip_installed, datagrip_path, datagrip_version) = self.detect_datagrip();
        let (androidstudio_installed, androidstudio_path, androidstudio_version) = self.detect_androidstudio();

        json!({
            "vscode": {
                "installed": vscode_installed,
                "path": vscode_path.unwrap_or_default(),
                "version": vscode_version.unwrap_or_default()
            },
            "cursor": {
                "installed": cursor_installed,
                "path": cursor_path.unwrap_or_default(),
                "version": cursor_version.unwrap_or_default()
            },
            "intellij": {
                "installed": intellij_installed,
                "path": intellij_path.unwrap_or_default(),
                "version": intellij_version.unwrap_or_default()
            },
            "pycharm": {
                "installed": pycharm_installed,
                "path": pycharm_path.unwrap_or_default(),
                "version": pycharm_version.unwrap_or_default()
            },
            "webstorm": {
                "installed": webstorm_installed,
                "path": webstorm_path.unwrap_or_default(),
                "version": webstorm_version.unwrap_or_default()
            },
            "phpstorm": {
                "installed": phpstorm_installed,
                "path": phpstorm_path.unwrap_or_default(),
                "version": phpstorm_version.unwrap_or_default()
            },
            "rubymine": {
                "installed": rubymine_installed,
                "path": rubymine_path.unwrap_or_default(),
                "version": rubymine_version.unwrap_or_default()
            },
            "clion": {
                "installed": clion_installed,
                "path": clion_path.unwrap_or_default(),
                "version": clion_version.unwrap_or_default()
            },
            "goland": {
                "installed": goland_installed,
                "path": goland_path.unwrap_or_default(),
                "version": goland_version.unwrap_or_default()
            },
            "rider": {
                "installed": rider_installed,
                "path": rider_path.unwrap_or_default(),
                "version": rider_version.unwrap_or_default()
            },
            "datagrip": {
                "installed": datagrip_installed,
                "path": datagrip_path.unwrap_or_default(),
                "version": datagrip_version.unwrap_or_default()
            },
            "androidstudio": {
                "installed": androidstudio_installed,
                "path": androidstudio_path.unwrap_or_default(),
                "version": androidstudio_version.unwrap_or_default()
            }
        })
    }
}
