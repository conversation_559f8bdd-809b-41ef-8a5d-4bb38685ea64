/// JetBrains 路径管理工具
/// 
/// 负责获取JetBrains配置目录和相关文件路径
/// 支持Windows、macOS、Linux三个平台

use std::path::PathBuf;
use dirs;

/// 获取JetBrains配置目录
/// 对应 augment-vip-rust-master 的 get_jetbrains_config_dir 函数
///
/// 与原始项目完全一致的查找逻辑：
/// 1. dirs::config_dir()/JetBrains
/// 2. dirs::home_dir()/JetBrains
/// 3. dirs::data_dir()/JetBrains
///
/// Returns:
///     Option<PathBuf>: JetBrains配置目录路径，如果不存在则返回None
pub fn get_jetbrains_config_dir() -> Option<PathBuf> {
    // 与原始项目完全一致的逻辑
    [dirs::config_dir(), dirs::home_dir(), dirs::data_dir()]
        .into_iter()
        .filter_map(|base_dir| base_dir)
        .map(|base_dir| base_dir.join("JetBrains"))
        .find(|path| path.exists())
}

/// 获取永久设备ID文件路径
/// 对应JetBrains的PermanentDeviceId文件
/// 
/// Returns:
///     Option<PathBuf>: 设备ID文件路径
pub fn get_permanent_device_id_path() -> Option<PathBuf> {
    get_jetbrains_config_dir().map(|dir| dir.join("PermanentDeviceId"))
}

/// 获取永久用户ID文件路径
/// 对应JetBrains的PermanentUserId文件
/// 
/// Returns:
///     Option<PathBuf>: 用户ID文件路径
pub fn get_permanent_user_id_path() -> Option<PathBuf> {
    get_jetbrains_config_dir().map(|dir| dir.join("PermanentUserId"))
}

/// 获取所有JetBrains ID文件路径
/// 
/// Returns:
///     Vec<PathBuf>: 所有存在的ID文件路径列表
pub fn get_all_id_file_paths() -> Vec<PathBuf> {
    let mut paths = Vec::new();
    
    if let Some(device_id_path) = get_permanent_device_id_path() {
        paths.push(device_id_path);
    }
    
    if let Some(user_id_path) = get_permanent_user_id_path() {
        paths.push(user_id_path);
    }
    
    paths
}

/// 检查JetBrains是否已安装
/// 通过检查配置目录是否存在来判断
/// 
/// Returns:
///     bool: 如果JetBrains已安装返回true，否则返回false
pub fn is_jetbrains_installed() -> bool {
    get_jetbrains_config_dir().is_some()
}

/// 获取JetBrains安装信息
/// 
/// Returns:
///     (bool, Option<String>): (是否安装, 配置目录路径)
pub fn get_jetbrains_info() -> (bool, Option<String>) {
    if let Some(config_dir) = get_jetbrains_config_dir() {
        (true, Some(config_dir.to_string_lossy().to_string()))
    } else {
        (false, None)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_jetbrains_config_dir_detection() {
        // 这个测试在没有安装JetBrains的环境中会返回None
        let config_dir = get_jetbrains_config_dir();
        println!("JetBrains config dir: {:?}", config_dir);
    }

    #[test]
    fn test_id_file_paths() {
        let paths = get_all_id_file_paths();
        println!("JetBrains ID file paths: {:?}", paths);
    }
}
