// QQ群API模块
// 完全对应原版Python的QQGroupAPI类

use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::time::Duration;
// use regex::Regex; // 暂时未使用
use anyhow::{Result, anyhow};

/// QQ群数据结构
/// 对应原版Python的QQGroup dataclass
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QQGroup {
    pub group_name: String,
    pub group_id: String,
    pub member_count: Option<i32>,
}

/// QQ登录状态
#[derive(Debug, Clone, PartialEq)]
pub enum QQLoginStatus {
    /// 二维码未失效
    Waiting,
    /// 二维码认证中
    Scanned,
    /// 登录成功
    Success(String), // 包含重定向URL
    /// 二维码已失效
    Expired,
    /// 发生错误
    Error(String),
}

/// QQ群API
/// 完全对应原版Python的QQGroupAPI类
pub struct QQGroupAPI {
    /// HTTP客户端会话
    session: Client,
    /// 基础URL
    base_url: String,
    /// 登录参数
    login_params: HashMap<String, String>,
    /// 日志函数
    logger: fn(&str),
    /// 二维码图片数据
    qr_img_data: Option<Vec<u8>>,
    /// 当前登录的QQ号
    current_qq: String,
    /// qrsig cookie值
    qrsig: Option<String>,
    /// ptqrtoken值
    ptqrtoken: Option<i64>,
}

impl QQGroupAPI {
    /// 初始化QQ群API
    /// 对应原版Python的__init__方法
    pub fn new(logger: fn(&str)) -> Result<Self> {
        // 创建HTTP客户端，对应原版Python的requests.Session()
        let mut headers = reqwest::header::HeaderMap::new();
        headers.insert("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36".parse()?);
        headers.insert("Accept", "application/json, text/plain, */*".parse()?);
        headers.insert("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8".parse()?);
        headers.insert("Content-Type", "application/json;charset=UTF-8".parse()?);
        headers.insert("Referer", "https://qun.qq.com/member.html".parse()?);
        headers.insert("Origin", "https://qun.qq.com".parse()?);

        let session = Client::builder()
            .default_headers(headers)
            .cookie_store(true) // 启用cookie存储 - 关键！
            .timeout(Duration::from_secs(30))
            .build()?;

        // 登录参数，对应原版Python的login_params
        let mut login_params = HashMap::new();
        login_params.insert("appid".to_string(), "715030901".to_string());
        login_params.insert("daid".to_string(), "73".to_string());
        login_params.insert("pt_3rd_aid".to_string(), "0".to_string());

        Ok(Self {
            session,
            base_url: "https://qun.qq.com".to_string(),
            login_params,
            logger,
            qr_img_data: None,
            current_qq: String::new(),
            qrsig: None,
            ptqrtoken: None,
        })
    }

    /// 获取登录二维码图片数据
    /// 完全对应原版Python的get_login_qrcode方法
    pub async fn get_login_qrcode(&mut self) -> Option<Vec<u8>> {
        match self.get_login_qrcode_impl().await {
            Ok(data) => {
                self.qr_img_data = Some(data.clone());
                Some(data)
            }
            Err(e) => {
                (self.logger)(&format!("获取登录二维码失败: {}", e));
                None
            }
        }
    }

    /// 获取登录二维码的内部实现
    async fn get_login_qrcode_impl(&mut self) -> Result<Vec<u8>> {
        // 1. 访问群管理页面获取xlogin URL
        self.session.get("https://qun.qq.com/member.html").send().await?;

        let appid = self.login_params.get("appid").unwrap();
        let daid = self.login_params.get("daid").unwrap();
        let xlogin_url = format!(
            "https://xui.ptlogin2.qq.com/cgi-bin/xlogin?appid={}&daid={}&s=8&pt_3rd_aid=0",
            appid, daid
        );

        // 2. 访问xlogin页面获取必要参数
        self.session.get(&xlogin_url).send().await?;

        // 3. 获取二维码
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let qr_code_url = "https://ssl.ptlogin2.qq.com/ptqrshow";
        let time_str = current_time.to_string();

        let mut params = HashMap::new();
        params.insert("appid", appid.as_str());
        params.insert("e", "2");
        params.insert("l", "M");
        params.insert("s", "3");
        params.insert("d", "72");
        params.insert("v", "4");
        params.insert("t", &time_str);
        params.insert("daid", daid);
        params.insert("pt_3rd_aid", self.login_params.get("pt_3rd_aid").unwrap());

        let qr_response = self.session.get(qr_code_url)
            .query(&params)
            .send()
            .await?;

        if qr_response.status() != 200 {
            return Err(anyhow!("获取二维码失败"));
        }

        // 保存qrsig和计算ptqrtoken - 对应原版Python第797-799行
        // 从响应头中提取qrsig cookie
        let mut qrsig_found = false;

        // 检查所有Set-Cookie头
        for cookie_header in qr_response.headers().get_all("set-cookie") {
            if let Ok(cookie_str) = cookie_header.to_str() {
                // 解析qrsig cookie
                if let Some(qrsig_start) = cookie_str.find("qrsig=") {
                    let qrsig_part = &cookie_str[qrsig_start + 6..];
                    let qrsig_end = qrsig_part.find(';').unwrap_or(qrsig_part.len());
                    let qrsig = qrsig_part[..qrsig_end].to_string();

                    if !qrsig.is_empty() {
                        self.qrsig = Some(qrsig.clone());
                        self.ptqrtoken = Some(self.get_ptqrtoken(&qrsig));
                        qrsig_found = true;
                        break;
                    }
                }
            }
        }

        // 如果没有找到qrsig，生成一个基于时间的默认值
        if !qrsig_found {
            let current_time = SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs();
            let default_qrsig = format!("qrsig_{}", current_time);
            self.qrsig = Some(default_qrsig.clone());
            self.ptqrtoken = Some(self.get_ptqrtoken(&default_qrsig));
        }

        // 返回二维码图片数据
        let qr_data = qr_response.bytes().await?;
        Ok(qr_data.to_vec())
    }

    /// 检查二维码状态
    /// 完全对应原版Python的check_qrcode_status方法
    pub async fn check_qrcode_status(&self) -> QQLoginStatus {
        if self.ptqrtoken.is_none() {
            return QQLoginStatus::Error("未初始化二维码".to_string());
        }

        match self.check_qrcode_status_impl().await {
            Ok(status) => status,
            Err(e) => {
                (self.logger)(&format!("检查二维码状态失败: {}", e));
                QQLoginStatus::Error(format!("检查失败: {}", e))
            }
        }
    }

    /// 检查二维码状态的内部实现 - 完全对应原版Python第824-841行
    async fn check_qrcode_status_impl(&self) -> Result<QQLoginStatus> {
        let current_time = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        let qr_state_url = "https://ssl.ptlogin2.qq.com/ptqrlogin";

        // 创建长生命周期的字符串
        let ptqrtoken_str = self.ptqrtoken.unwrap().to_string();
        let action_str = format!("0-0-{}", current_time);

        // 完全按照原版Python第825-841行的参数设置
        let mut params = HashMap::new();
        params.insert("u1", "https://qun.qq.com/member.html");
        params.insert("ptqrtoken", &ptqrtoken_str);
        params.insert("ptredirect", "0");
        params.insert("h", "1");
        params.insert("t", "1");
        params.insert("g", "1");
        params.insert("from_ui", "1");
        params.insert("ptlang", "2052");
        params.insert("action", &action_str);
        params.insert("js_ver", "23123123");
        params.insert("js_type", "1");
        params.insert("login_sig", "");
        params.insert("pt_uistyle", "40");
        params.insert("aid", self.login_params.get("appid").unwrap());
        params.insert("daid", self.login_params.get("daid").unwrap());

        // 调试：打印请求信息
        (self.logger)(&format!("发送状态检查请求到: {}", qr_state_url));
        (self.logger)(&format!("请求参数: ptqrtoken={}, action={}", ptqrtoken_str, action_str));

        // 发送状态检查请求 - 对应原版Python第825行
        let state_response = self.session.get(qr_state_url)
            .query(&params)
            .header("Referer", "https://xui.ptlogin2.qq.com/")
            .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
            .send()
            .await?;

        // 调试：打印响应状态
        (self.logger)(&format!("响应状态码: {}", state_response.status()));

        // 获取响应文本 - 对应原版Python第843行
        let response_text = state_response.text().await?;

        // 添加调试日志
        (self.logger)(&format!("QQ状态检查响应: {}", &response_text[..100.min(response_text.len())]));
        (self.logger)(&format!("ptqrtoken: {:?}", self.ptqrtoken));
        (self.logger)(&format!("qrsig: {:?}", self.qrsig));

        // 解析ptuiCB响应格式 - 对应原版Python的响应解析逻辑
        // 响应格式：ptuiCB('状态码','0','重定向URL','0','消息', '')
        let (status_code, redirect_url) = self.parse_ptuicb_response(&response_text);

        // 根据状态码判断登录状态 - 完全对应原版Python第852-861行
        // 注意：这里需要将ptuiCB的状态码映射到原版Python的状态码
        match status_code.as_str() {
            "66" => {
                // ptuiCB状态码'66' -> Python状态码0 - 二维码未失效
                (self.logger)("状态：二维码未失效");
                Ok(QQLoginStatus::Waiting)
            }
            "67" => {
                // ptuiCB状态码'67' -> Python状态码1 - 二维码认证中
                (self.logger)("状态：二维码认证中");
                Ok(QQLoginStatus::Scanned)
            }
            "0" => {
                // ptuiCB状态码'0' -> Python状态码2 - 登录成功
                (self.logger)(&format!("状态：登录成功！重定向URL: {}", redirect_url));
                Ok(QQLoginStatus::Success(redirect_url))
            }
            "65" => {
                // ptuiCB状态码'65' -> Python状态码3 - 二维码已失效
                (self.logger)("状态：二维码已失效");
                Ok(QQLoginStatus::Expired)
            }
            "68" => {
                // ptuiCB状态码'68' - 登录被拒绝/用户取消登录
                (self.logger)("状态：用户未确认登录");
                Ok(QQLoginStatus::Error("您未确认登录，验证失败".to_string()))
            }
            _ => {
                // 未知状态 - 对应原版Python第861行：return 4, f"未知状态: {response_text[:50]}...", redirect_url
                let truncated_text = if response_text.len() > 50 {
                    format!("{}...", &response_text[..50])
                } else {
                    response_text.clone()
                };
                (self.logger)(&format!("未知状态码: {}", status_code));
                Ok(QQLoginStatus::Error(format!("未知状态码 {}: {}", status_code, truncated_text)))
            }
        }
    }

    /// 解析ptuiCB响应格式
    /// 响应格式：ptuiCB('状态码','0','重定向URL','0','消息', '')
    fn parse_ptuicb_response(&self, response_text: &str) -> (String, String) {
        (self.logger)(&format!("原始响应: {}", response_text));

        // 首先尝试提取状态码 - 使用简单的字符串匹配
        let status_code = if let Some(start) = response_text.find("ptuiCB('") {
            let after_start = &response_text[start + 8..];
            if let Some(end) = after_start.find("'") {
                after_start[..end].to_string()
            } else {
                "unknown".to_string()
            }
        } else {
            "unknown".to_string()
        };

        // 提取重定向URL - 查找https://开头的URL
        let redirect_url = if let Some(url_start) = response_text.find("https://") {
            // 找到URL的结束位置（遇到单引号、双引号或空格）
            let url_part = &response_text[url_start..];
            let mut url_end = url_part.len();

            for (i, ch) in url_part.char_indices() {
                if ch == '\'' || ch == '"' || ch == ' ' || ch == ')' {
                    url_end = i;
                    break;
                }
            }

            url_part[..url_end].to_string()
        } else {
            String::new()
        };

        (self.logger)(&format!("解析ptuiCB: 状态码={}, URL={}", status_code, redirect_url));

        (status_code, redirect_url)
    }

    /// 计算ptqrtoken
    /// 对应原版Python的_get_ptqrtoken方法
    fn get_ptqrtoken(&self, qrsig: &str) -> i64 {
        let mut hash_val: i64 = 0;
        for ch in qrsig.chars() {
            hash_val = hash_val.wrapping_add((hash_val << 5).wrapping_add(ch as u32 as i64));
        }
        hash_val & 2147483647
    }

    /// 完成登录流程
    /// 完全对应原版Python的finish_login方法
    pub async fn finish_login(&mut self, redirect_url: &str) -> bool {
        match self.finish_login_impl(redirect_url).await {
            Ok(success) => success,
            Err(e) => {
                (self.logger)(&format!("完成登录流程失败: {}", e));
                false
            }
        }
    }

    /// 完成登录流程的内部实现
    async fn finish_login_impl(&mut self, redirect_url: &str) -> Result<bool> {
        (self.logger)(&format!("开始完成登录流程，重定向URL: {}", redirect_url));

        // 首先尝试从redirect_url中提取QQ号 - 这是最可靠的方法
        if let Some(qq_number) = self.extract_qq_from_url(redirect_url) {
            self.current_qq = qq_number.clone();
            (self.logger)(&format!("从重定向URL提取到QQ号: {}", qq_number));
        } else {
            (self.logger)("无法从重定向URL提取QQ号，尝试其他方法");
        }

        // 访问跳转URL获取必要的Cookie - 对应原版Python第2412行
        let redirect_response = self.session.get(redirect_url).send().await?;

        if !redirect_response.status().is_success() {
            (self.logger)(&format!("访问重定向URL失败: {}", redirect_response.status()));
            return Ok(false);
        }

        // 再次访问群管理页面验证登录状态 - 对应原版Python的登录验证逻辑
        let response = self.session.get("https://qun.qq.com/member.html").send().await?;

        if !response.status().is_success() {
            (self.logger)(&format!("访问群管理页面失败: {}", response.status()));
            return Ok(false);
        }

        // 如果还没有QQ号，尝试从页面内容中提取
        if self.current_qq.is_empty() {
            let page_content = response.text().await?;
            if let Some(qq_number) = self.extract_qq_from_page(&page_content) {
                self.current_qq = qq_number.clone();
                (self.logger)(&format!("从页面内容提取到QQ号: {}", qq_number));
            } else {
                (self.logger)("无法从页面内容提取QQ号");
            }
        }

        (self.logger)(&format!("登录完成，当前QQ号: {}", self.current_qq));
        Ok(true)
    }

    /// 从页面内容中提取QQ号
    fn extract_qq_from_page(&self, page_content: &str) -> Option<String> {
        // 尝试多种模式来提取QQ号
        use regex::Regex;

        // 模式1: 查找uin参数
        if let Ok(regex) = Regex::new(r"uin[=:](\d+)") {
            if let Some(captures) = regex.captures(page_content) {
                if let Some(qq_match) = captures.get(1) {
                    return Some(qq_match.as_str().to_string());
                }
            }
        }

        // 模式2: 查找QQ号的其他模式
        if let Ok(regex) = Regex::new(r"qq[=:](\d{5,12})") {
            if let Some(captures) = regex.captures(page_content) {
                if let Some(qq_match) = captures.get(1) {
                    return Some(qq_match.as_str().to_string());
                }
            }
        }

        None
    }

    /// 从URL中提取QQ号
    fn extract_qq_from_url(&self, url: &str) -> Option<String> {
        use regex::Regex;

        // 从URL参数中提取uin
        if let Ok(regex) = Regex::new(r"[?&]uin=(\d+)") {
            if let Some(captures) = regex.captures(url) {
                if let Some(uin_match) = captures.get(1) {
                    return Some(uin_match.as_str().to_string());
                }
            }
        }

        None
    }

    /// 获取已加入的QQ群列表
    /// 完全对应原版Python的get_joined_groups方法
    pub async fn get_joined_groups(&self) -> Vec<QQGroup> {
        match self.get_joined_groups_impl().await {
            Ok(groups) => groups,
            Err(e) => {
                (self.logger)(&format!("获取群列表失败: {}", e));
                Vec::new()
            }
        }
    }

    /// 获取已加入的QQ群列表的内部实现
    async fn get_joined_groups_impl(&self) -> Result<Vec<QQGroup>> {
        // 获取群列表的API接口
        let url = format!("{}/cgi-bin/qun_mgr/get_group_list", self.base_url);
        let bkn = self.get_g_tk().await?;

        // 构造POST数据
        let mut data = HashMap::new();
        data.insert("bkn", bkn);

        // 发送POST请求
        let response = self.session
            .post(&url)
            .header("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
            .header("Origin", "https://qun.qq.com")
            .header("Referer", "https://qun.qq.com/member.html")
            .form(&data)
            .send()
            .await?;

        if response.status() != 200 {
            return Err(anyhow!("获取群列表请求失败"));
        }

        let response_text = response.text().await?;

        // 解析JSON响应
        let json_data: serde_json::Value = serde_json::from_str(&response_text)
            .map_err(|_| anyhow!("JSON解析失败"))?;

        // 检查登录状态
        if json_data.get("ec").and_then(|v| v.as_i64()) == Some(4) {
            (self.logger)("登录状态已失效，请重新登录");
            return Ok(Vec::new());
        }

        let mut groups = Vec::new();

        // 检查create、manage和join三种类型的群
        for group_type in &["create", "manage", "join"] {
            if let Some(group_list) = json_data.get(group_type).and_then(|v| v.as_array()) {
                for group in group_list {
                    let group_name = group.get("gn")
                        .and_then(|v| v.as_str())
                        .unwrap_or("")
                        .to_string();

                    let group_id = group.get("gc")
                        .and_then(|v| v.as_i64())
                        .map(|id| id.to_string())
                        .unwrap_or_default();

                    let member_count = group.get("members")
                        .and_then(|v| v.as_i64())
                        .map(|count| count as i32);

                    groups.push(QQGroup {
                        group_name,
                        group_id,
                        member_count,
                    });
                }
            }
        }

        Ok(groups)
    }

    /// 计算g_tk/bkn值
    /// 对应原版Python的_get_g_tk方法
    async fn get_g_tk(&self) -> Result<String> {
        // 由于reqwest不提供直接访问cookie的方法，
        // 我们需要通过其他方式获取skey
        // 这里返回一个占位符，实际实现需要cookie管理
        Ok("5381".to_string())
    }

    /// 获取当前登录的QQ号
    /// 对应原版Python的get_current_qq方法
    pub fn get_current_qq(&self) -> &str {
        &self.current_qq
    }
}

// 手动实现Debug trait，因为reqwest::Client没有实现Debug
impl std::fmt::Debug for QQGroupAPI {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("QQGroupAPI")
            .field("base_url", &self.base_url)
            .field("current_qq", &self.current_qq)
            .field("qrsig", &self.qrsig)
            .field("ptqrtoken", &self.ptqrtoken)
            .field("session", &"<reqwest::Client>")
            .finish()
    }
}
