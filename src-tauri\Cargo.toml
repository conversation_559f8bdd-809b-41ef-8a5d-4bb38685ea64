[package]
name = "yaugment"
version = "2.4.1"
description = "YAugment - Augment插件重置和邮箱验证工具"
authors = ["Yan <<EMAIL>>"]
license = "MIT"
repository = "https://github.com/yan/YAugment"
edition = "2021"
rust-version = "1.70"
default-run = "yaugment"

# 定义二进制文件
[[bin]]
name = "yaugment"
path = "src/bin/main.rs"


# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
tauri = { version = "2.0", features = [] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "stream", "cookies"] }
rusqlite = { version = "0.29", features = ["bundled"] }
dirs = "5.0"
uuid = { version = "1.0", features = ["v4", "serde"] }
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
log = "0.4"
env_logger = "0.10"
anyhow = "1.0"
chrono = { version = "0.4", features = ["serde"] }
regex = "1.0"
base64 = "0.21"
sha2 = "0.10"
rand = "0.8"
hex = "0.4"
quick-xml = { version = "0.31", features = ["serialize"] }
md5 = "0.7"
async-trait = "0.1"
imap = "2.4"
native-tls = "0.2"
zip = "0.6"
# 版本检查相关依赖
flate2 = "1.0"  # 用于 zlib 压缩/解压缩
aes-gcm = "0.10"  # 用于加密/解密
pbkdf2 = "0.12"  # 用于密钥派生
hmac = "0.12"  # 用于 HMAC
machine-uid = "0.3"  # 用于获取机器唯一标识
toml = "0.8"  # 用于解析配置文件
notify-rust = "4.10"  # 用于系统原生通知
# Fernet兼容解密所需依赖
aes = "0.8"  # AES加密算法
cbc = "0.1"  # CBC模式
# 剪贴板功能
arboard = "3.2"
# 系统信息和进程管理
sysinfo = "0.30"

# Windows API (仅在Windows平台编译)
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["processthreadsapi", "securitybaseapi", "winnt", "handleapi", "wincon", "winuser", "shellapi"] }

# Unix系统API (仅在Unix平台编译)
[target.'cfg(unix)'.dependencies]
libc = "0.2"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]



# 高安全性和性能的 Release 配置
[profile.release]
# 启用所有优化
opt-level = 3
# 移除调试信息
debug = false
# 剥离符号
strip = "symbols"
# 在 macOS 上禁用 LTO 以避免与 embed-bitcode=no 冲突
# LTO 在其他平台通过 .cargo/config.toml 启用
lto = false
# 减少代码生成单元以提高优化效果
codegen-units = 1
# 启用 panic = abort 以减少二进制大小和提高安全性
panic = "abort"
# 启用溢出检查
overflow-checks = true

[dev-dependencies]
tempfile = "3.8"  # 用于测试中创建临时文件
