use serde_json::Value;
use std::sync::Arc;
use tokio::sync::Mutex;
use reqwest;
use regex::Regex;
use rand::{thread_rng, Rng};
use rand::seq::SliceRandom;
// use std::time::{SystemTime, UNIX_EPOCH}; // 暂时未使用
use crate::utils::ProgressReporter;
use anyhow;

/// 连接测试结果
#[derive(Debug)]
pub struct ConnectionTestResult {
    pub success: bool,
    pub message: String,
}

/// 验证码获取结果
#[derive(Debug)]
pub enum CodeResult {
    Success(String),
    NoCode,
    ConnectionError(String),
    OtherError(String),
}

/// 邮件消息结构
/// 对应原版 Python 的邮件数据结构
#[derive(Debug, Clone)]
pub struct EmailMessage {
    pub mail_id: String,
    pub from_mail: String,
    pub subject: String,
    pub content: String,
    pub is_new: bool,
}

/// 邮箱管理器
/// 对应原版 Python 的 EmailManager 类
#[derive(Debug)]
pub struct EmailManager {
    // 基本配置
    domain: String,
    use_temp_mail: bool,

    // 临时邮箱配置
    temp_mail_email: String,
    temp_mail_pin: String,

    // IMAP配置
    imap_server: String,
    imap_port: u16,
    imap_user: String,
    imap_password: String,
    imap_folder: String,

    // 生成配置
    username_length: usize,
    include_numbers: bool,
    number_probability: f64,
    min_numbers: usize,
    max_numbers: usize,
    no_digit_prefix_length: usize,
    include_uppercase: bool,
    uppercase_probability: f64,
    min_uppercase: usize,
    max_uppercase: usize,

    // 重试配置
    max_retries: usize,
    retry_interval: u64,

    // 其他配置
    auto_copy: bool,

    // 状态管理
    last_error: Option<String>,
    stop_requested: Arc<Mutex<bool>>,
    progress_reporter: Arc<Mutex<ProgressReporter>>,

    // HTTP客户端
    http_client: reqwest::Client,
}

impl EmailManager {
    /// 创建新的邮箱管理器
    /// 对应原版 Python 的 __init__ 方法
    pub fn new(config: Value) -> Result<Self, anyhow::Error> {
        let mut manager = Self {
            // 默认值
            domain: "xx.com".to_string(),
            use_temp_mail: false,
            temp_mail_email: "<EMAIL>".to_string(),
            temp_mail_pin: String::new(),
            imap_server: "imap.qq.com".to_string(),
            imap_port: 993,
            imap_user: String::new(),
            imap_password: String::new(),
            imap_folder: "INBOX".to_string(),
            username_length: 9,
            include_numbers: true,
            number_probability: 0.7,
            min_numbers: 1,
            max_numbers: 3,
            no_digit_prefix_length: 3,
            include_uppercase: false,
            uppercase_probability: 0.3,
            min_uppercase: 1,
            max_uppercase: 2,
            max_retries: 30,
            retry_interval: 1,
            auto_copy: true,
            last_error: None,
            stop_requested: Arc::new(Mutex::new(false)),
            progress_reporter: Arc::new(Mutex::new(ProgressReporter::new())),
            http_client: reqwest::Client::builder()
                .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .timeout(std::time::Duration::from_secs(15))
                .build()
                .map_err(|e| anyhow::anyhow!("Failed to create HTTP client: {}", e))?,
        };

        manager.update_config(config)?;
        Ok(manager)
    }

    /// 更新配置
    /// 对应原版 Python 的 update_config 方法
    pub fn update_config(&mut self, config: Value) -> Result<(), anyhow::Error> {
        let email_config = config.get("email").unwrap_or(&Value::Null);

        // 基本配置
        if let Some(domain) = email_config.get("domain").and_then(|v| v.as_str()) {
            self.domain = domain.to_string();
        }
        if let Some(use_temp_mail) = email_config.get("use_temp_mail").and_then(|v| v.as_bool()) {
            self.use_temp_mail = use_temp_mail;
        }

        // 临时邮箱配置
        if let Some(temp_mail_config) = email_config.get("temp_mail") {
            if let Some(email) = temp_mail_config.get("email").and_then(|v| v.as_str()) {
                self.temp_mail_email = email.to_string();
            }
            if let Some(pin) = temp_mail_config.get("pin").and_then(|v| v.as_str()) {
                self.temp_mail_pin = pin.to_string();
            }
        }

        // IMAP配置
        if let Some(imap_config) = email_config.get("imap") {
            if let Some(server) = imap_config.get("server").and_then(|v| v.as_str()) {
                self.imap_server = server.to_string();
            }
            if let Some(port) = imap_config.get("port").and_then(|v| v.as_u64()) {
                self.imap_port = port as u16;
            }
            if let Some(user) = imap_config.get("user").and_then(|v| v.as_str()) {
                self.imap_user = user.to_string();
            }
            if let Some(password) = imap_config.get("password").and_then(|v| v.as_str()) {
                self.imap_password = password.to_string();
            }
            if let Some(folder) = imap_config.get("folder").and_then(|v| v.as_str()) {
                self.imap_folder = folder.to_string();
            }
        }

        // 生成配置
        if let Some(gen_config) = email_config.get("generation") {
            if let Some(length) = gen_config.get("username_length").and_then(|v| v.as_u64()) {
                self.username_length = length as usize;
            }
            if let Some(include_numbers) = gen_config.get("include_numbers").and_then(|v| v.as_bool()) {
                self.include_numbers = include_numbers;
            }
            if let Some(prob) = gen_config.get("number_probability").and_then(|v| v.as_f64()) {
                self.number_probability = prob;
            }
            if let Some(min_nums) = gen_config.get("min_numbers").and_then(|v| v.as_u64()) {
                self.min_numbers = min_nums as usize;
            }
            if let Some(max_nums) = gen_config.get("max_numbers").and_then(|v| v.as_u64()) {
                self.max_numbers = max_nums as usize;
            }
            if let Some(prefix_len) = gen_config.get("no_digit_prefix_length").and_then(|v| v.as_u64()) {
                self.no_digit_prefix_length = prefix_len as usize;
            }
            if let Some(include_upper) = gen_config.get("include_uppercase").and_then(|v| v.as_bool()) {
                self.include_uppercase = include_upper;
            }
            if let Some(upper_prob) = gen_config.get("uppercase_probability").and_then(|v| v.as_f64()) {
                self.uppercase_probability = upper_prob;
            }
            if let Some(min_upper) = gen_config.get("min_uppercase").and_then(|v| v.as_u64()) {
                self.min_uppercase = min_upper as usize;
            }
            if let Some(max_upper) = gen_config.get("max_uppercase").and_then(|v| v.as_u64()) {
                self.max_uppercase = max_upper as usize;
            }
        }

        // 重试配置
        if let Some(retry_config) = email_config.get("retry") {
            if let Some(max_retries) = retry_config.get("max_retries").and_then(|v| v.as_u64()) {
                self.max_retries = max_retries as usize;
            }
            if let Some(interval) = retry_config.get("retry_interval").and_then(|v| v.as_u64()) {
                self.retry_interval = interval;
            }
        }

        // 其他配置
        if let Some(auto_copy) = email_config.get("auto_copy").and_then(|v| v.as_bool()) {
            self.auto_copy = auto_copy;
        }

        Ok(())
    }

    /// 生成随机邮箱
    /// 对应原版 Python 的 generate_random_email 方法
    pub fn generate_random_email(&self) -> String {
        let mut rng = thread_rng();

        // 生成基础小写字母用户名
        let mut username: Vec<char> = (0..self.username_length)
            .map(|_| rng.gen_range('a'..='z'))
            .collect();

        // 添加数字
        if self.include_numbers && rng.gen::<f64>() < self.number_probability {
            let num_count = rng.gen_range(self.min_numbers..=self.max_numbers);
            let num_count = num_count.min(username.len());

            // 确定可以放置数字的位置（跳过前几位）
            let start_pos = self.no_digit_prefix_length.min(username.len() - 1);
            let available_positions: Vec<usize> = (start_pos..username.len()).collect();

            if !available_positions.is_empty() {
                let positions_to_change: Vec<usize> = available_positions
                    .choose_multiple(&mut rng, num_count.min(available_positions.len()))
                    .cloned()
                    .collect();

                for pos in positions_to_change {
                    username[pos] = rng.gen_range('0'..='9');
                }
            }
        }

        // 添加大写字母
        if self.include_uppercase && rng.gen::<f64>() < self.uppercase_probability {
            let upper_count = rng.gen_range(self.min_uppercase..=self.max_uppercase);
            let upper_count = upper_count.min(username.len());

            let lowercase_positions: Vec<usize> = username
                .iter()
                .enumerate()
                .filter(|(_, &c)| c.is_ascii_lowercase())
                .map(|(i, _)| i)
                .collect();

            if !lowercase_positions.is_empty() {
                let positions_to_change: Vec<usize> = lowercase_positions
                    .choose_multiple(&mut rng, upper_count.min(lowercase_positions.len()))
                    .cloned()
                    .collect();

                for pos in positions_to_change {
                    username[pos] = username[pos].to_ascii_uppercase();
                }
            }
        }

        // 确保至少有一个字母
        if !username.iter().any(|&c| c.is_ascii_alphabetic()) {
            let pos = rng.gen_range(0..username.len());
            username[pos] = rng.gen_range('a'..='z');
        }

        let username_str: String = username.into_iter().collect();
        format!("{}@{}", username_str, self.domain)
    }

    /// 设置进度回调函数
    /// 对应原版 Python 的 set_progress_callback 方法
    pub async fn set_progress_callback(&self, callback: Option<crate::utils::ProgressCallback>) {
        let mut reporter = self.progress_reporter.lock().await;
        if let Some(cb) = callback {
            reporter.set_callback(cb);
        }
    }

    /// 停止验证码获取
    /// 对应原版 Python 的 stop_verification 方法
    pub async fn stop_verification(&self) {
        let mut stop_flag = self.stop_requested.lock().await;
        *stop_flag = true;
    }

    /// 检查是否应该停止
    /// 对应原版 Python 的 _should_stop 方法
    async fn should_stop(&self) -> bool {
        let stop_flag = self.stop_requested.lock().await;
        *stop_flag
    }

    /// 报告进度
    /// 对应原版 Python 的 _report_progress 方法
    async fn report_progress(&self, message: &str, progress: f64) {
        let reporter = self.progress_reporter.lock().await;
        reporter.report(message, progress);
    }

    /// 可中断的睡眠
    /// 对应原版 Python 的 _interruptible_sleep 方法
    async fn interruptible_sleep(&self, seconds: u64) {
        for _ in 0..(seconds * 10) {
            if self.should_stop().await {
                break;
            }
            tokio::time::sleep(std::time::Duration::from_millis(100)).await;
        }
    }

    /// 获取验证码
    /// 对应原版 Python 的 get_verification_code 方法
    pub async fn get_verification_code(&mut self) -> Option<String> {
        self.report_progress("开始获取验证码...", 0.0).await;

        // 重置停止标志
        {
            let mut stop_flag = self.stop_requested.lock().await;
            *stop_flag = false;
        }

        // 清除之前的错误
        self.last_error = None;

        // 检查是否应该停止
        if self.should_stop().await {
            return None;
        }

        // 首先进行连接测试
        self.report_progress("测试邮箱连接...", 0.1).await;

        let connection_test = self.test_connection().await;
        if !connection_test.success {
            let error_msg = format!("邮箱连接失败: {}", connection_test.message);
            self.last_error = Some(connection_test.message.clone());
            self.report_progress(&error_msg, 1.0).await;
            return None;
        }

        // 再次检查是否应该停止
        if self.should_stop().await {
            return None;
        }

        self.report_progress("连接测试通过，开始获取验证码...", 0.2).await;

        for attempt in 0..self.max_retries {
            // 检查是否应该停止
            if self.should_stop().await {
                println!("🛑 收到停止信号，终止验证码获取");
                return None;
            }

            let progress = 0.2 + (attempt + 1) as f64 / self.max_retries as f64 * 0.8;
            let attempt_msg = format!("第 {}/{} 次尝试...", attempt + 1, self.max_retries);
            println!("⏳ {}", attempt_msg);
            self.report_progress(&attempt_msg, progress).await;

            let result = if self.use_temp_mail {
                self.get_code_from_temp_mail().await
            } else {
                // 暂时保持IMAP的原有逻辑，返回CodeResult::NoCode
                match self.get_code_from_imap().await {
                    Some(code) => CodeResult::Success(code),
                    None => CodeResult::NoCode,
                }
            };

            match result {
                CodeResult::Success(code) => {
                    let success_msg = format!("成功获取验证码: {}", code);
                    println!("✅ {}", success_msg);
                    self.report_progress("成功获取验证码", 1.0).await;

                    // 自动复制到剪贴板
                    if self.auto_copy {
                        if let Err(_) = self.copy_to_clipboard(&code).await {
                            println!("⚠️ 自动复制失败");
                        } else {
                            println!("📋 验证码已自动复制到剪贴板");
                        }
                    }

                    return Some(code);
                }
                CodeResult::ConnectionError(error_msg) => {
                    // 连接相关错误，立即停止重试
                    let final_error = format!("网络连接问题，停止重试: {}", error_msg);
                    println!("🚫 {}", final_error);
                    self.last_error = Some(error_msg);
                    self.report_progress(&final_error, 1.0).await;
                    return None;
                }
                CodeResult::OtherError(error_msg) => {
                    // 其他错误，记录错误信息
                    let attempt_error = format!("第 {} 次尝试失败: {}", attempt + 1, error_msg);
                    println!("❌ {}", attempt_error);
                    self.last_error = Some(error_msg);
                }
                CodeResult::NoCode => {
                    // 没有找到验证码，继续重试
                }
            }

            if attempt < self.max_retries - 1 {
                let retry_msg = format!("未获取到验证码，{}秒后重试...", self.retry_interval);
                println!("⏰ {}", retry_msg);
                self.interruptible_sleep(self.retry_interval).await;
            }
        }

        let final_msg = format!("经过 {} 次尝试后仍未获取到验证码", self.max_retries);
        println!("❌ {}", final_msg);
        self.last_error = Some(final_msg.clone());
        self.report_progress(&final_msg, 1.0).await;
        None
    }

    /// 从临时邮箱获取验证码
    /// 对应原版 Python 的 _get_code_from_temp_mail 方法
    async fn get_code_from_temp_mail(&mut self) -> CodeResult {
        println!("📧 正在连接临时邮箱服务...");

        // 获取邮件列表
        let mail_list_url = format!(
            "https://tempmail.plus/api/mails?email={}&limit=20&epin={}",
            self.temp_mail_email, self.temp_mail_pin
        );
        println!("🌐 请求邮件列表: {}", self.temp_mail_email);

        let response = match self.http_client
            .get(&mail_list_url)
            .timeout(std::time::Duration::from_secs(8))
            .send()
            .await
        {
            Ok(resp) => resp,
            Err(e) => {
                let error_msg = if e.is_timeout() {
                    let msg = "连接超时".to_string();
                    println!("⏰ {}", msg);
                    msg
                } else if e.is_connect() {
                    let msg = "连接错误".to_string();
                    println!("🌐 {}", msg);
                    msg
                } else {
                    let msg = "请求失败".to_string();
                    println!("❌ {}", msg);
                    msg
                };
                // 设置last_error字段
                self.last_error = Some(error_msg.clone());
                return CodeResult::ConnectionError(error_msg);
            }
        };

        if response.status() != 200 {
            let error_msg = format!("获取邮件列表失败，HTTP状态码: {}", response.status());
            println!("❌ {}", error_msg);
            self.last_error = Some(error_msg.clone());
            return CodeResult::OtherError(error_msg);
        }

        println!("✅ 成功获取邮件列表");

        let data: Value = match response.json().await {
            Ok(data) => data,
            Err(_) => {
                let error_msg = "解析JSON失败".to_string();
                println!("❌ {}", error_msg);
                self.last_error = Some(error_msg.clone());
                return CodeResult::OtherError(error_msg);
            }
        };

        if !data.get("result").and_then(|v| v.as_bool()).unwrap_or(false) {
            println!("❌ API返回失败结果");
            return CodeResult::NoCode;
        }

        let mail_list = match data.get("mail_list").and_then(|v| v.as_array()) {
            Some(list) => list,
            None => {
                println!("📭 暂无新邮件");
                return CodeResult::NoCode;
            }
        };

        let mail_count = mail_list.len();
        println!("📬 找到 {} 封邮件", mail_count);

        for (i, mail) in mail_list.iter().enumerate() {
            println!("📄 检查第 {}/{} 封邮件", i + 1, mail_count);

            // 调试：打印完整的邮件JSON结构
            println!("🔍 邮件JSON结构: {}", serde_json::to_string_pretty(mail).unwrap_or_else(|_| "无法序列化".to_string()));

            // 检查是否为新邮件
            if !mail.get("is_new").and_then(|v| v.as_bool()).unwrap_or(false) {
                println!("⏭️ 跳过已读邮件");
                continue;
            }

            let from_mail = mail.get("from_mail").and_then(|v| v.as_str()).unwrap_or("");
            println!("📧 外层发件人: {}", from_mail);

            // 获取邮件ID（可能是数字或字符串类型）
            let mail_id = mail.get("mail_id")
                .and_then(|v| {
                    // 尝试作为字符串
                    if let Some(s) = v.as_str() {
                        Some(s.to_string())
                    }
                    // 尝试作为数字
                    else if let Some(n) = v.as_u64() {
                        Some(n.to_string())
                    }
                    else if let Some(n) = v.as_i64() {
                        Some(n.to_string())
                    }
                    else {
                        None
                    }
                })
                .or_else(|| {
                    // 尝试其他可能的字段名称
                    mail.get("id").and_then(|v| {
                        if let Some(s) = v.as_str() {
                            Some(s.to_string())
                        } else if let Some(n) = v.as_u64() {
                            Some(n.to_string())
                        } else if let Some(n) = v.as_i64() {
                            Some(n.to_string())
                        } else {
                            None
                        }
                    })
                });

            let mail_id = match mail_id {
                Some(id) => id,
                None => {
                    println!("⚠️ 邮件ID为空，跳过");
                    continue;
                }
            };

            println!("🔍 获取邮件详情 (ID: {})", mail_id);
            let detail_url = format!(
                "https://tempmail.plus/api/mails/{}?email={}&epin={}",
                mail_id, self.temp_mail_email, self.temp_mail_pin
            );

            let detail_response = match self.http_client
                .get(&detail_url)
                .timeout(std::time::Duration::from_secs(8))
                .send()
                .await
            {
                Ok(resp) => resp,
                Err(_) => {
                    println!("⚠️ 获取邮件详情失败");
                    continue;
                }
            };

            if detail_response.status() != 200 {
                println!("⚠️ 获取邮件详情失败，状态码: {}", detail_response.status());
                continue;
            }

            let detail_data: Value = match detail_response.json().await {
                Ok(data) => data,
                Err(_) => continue,
            };

            if !detail_data.get("result").and_then(|v| v.as_bool()).unwrap_or(false) {
                continue;
            }

            // 检查真实发件人（从邮件详情中获取）
            let real_from_mail = detail_data.get("from_mail").and_then(|v| v.as_str()).unwrap_or("");
            let real_from_name = detail_data.get("from_name").and_then(|v| v.as_str()).unwrap_or("");
            let subject = detail_data.get("subject").and_then(|v| v.as_str()).unwrap_or("");
            println!("📧 真实发件人: {} <{}>", real_from_name, real_from_mail);
            println!("📝 邮件主题: {}", subject);

            // 验证是否为Augment验证码邮件
            let is_augment_mail = real_from_mail.to_lowercase().contains("<EMAIL>") ||
                real_from_name.to_lowercase().contains("augment") ||
                subject.to_lowercase().contains("augment") ||
                subject.to_lowercase().contains("verification") ||
                subject.contains("验证码");

            if !is_augment_mail {
                println!("⏭️ 跳过非Augment验证码邮件");
                continue;
            }

            // 只有通过发件人验证的Augment邮件才进行后续处理和标记已读
            let mail_text = detail_data.get("text").and_then(|v| v.as_str()).unwrap_or("");
            println!("📝 邮件内容长度: {} 字符", mail_text.len());

            // 验证邮件内容是否包含验证码相关信息
            let mail_text_lower = mail_text.to_lowercase();
            let is_verification_content = mail_text_lower.contains("verification code") ||
                mail_text_lower.contains("验证码") ||
                mail_text_lower.contains("your code") ||
                mail_text_lower.contains("code is:") ||
                mail_text_lower.contains("verification");

            if !is_verification_content {
                println!("🔍 Augment邮件但不包含验证码相关信息，标记为已读并跳过");
                self.mark_mail_as_read(&mail_id).await;
                continue;
            }

            // 查找6位数字验证码
            // 注意：Rust的regex不支持负向后查找，需要手动验证前后文
            let re = Regex::new(r"\b\d{6}\b").unwrap();
            let mut found_code = None;

            for code_match in re.find_iter(mail_text) {
                let start = code_match.start();
                let code = code_match.as_str();

                // 检查前一个字符是否为字母、@或点号
                let prev_char_ok = if start == 0 {
                    true
                } else {
                    let prev_char = mail_text.chars().nth(start - 1).unwrap_or(' ');
                    !prev_char.is_ascii_alphabetic() && prev_char != '@' && prev_char != '.'
                };

                if prev_char_ok {
                    found_code = Some(code.to_string());
                    break;
                }
            }

            if let Some(code) = found_code {
                println!("🎯 在邮件中找到验证码: {}", code);
                self.mark_mail_as_read(&mail_id).await;
                return CodeResult::Success(code);
            } else {
                println!("🔍 Augment验证码邮件但未找到有效6位数字验证码，标记为已读");
                self.mark_mail_as_read(&mail_id).await;
            }
        }

        println!("📭 所有邮件检查完毕，未找到验证码");
        CodeResult::NoCode
    }

    /// 标记邮件为已读（通过删除实现）
    /// 对应原版 Python 的 mark_mail_as_read 函数
    async fn mark_mail_as_read(&self, mail_id: &str) {
        let delete_url = "https://tempmail.plus/api/mails/";
        let payload = serde_json::json!({
            "email": self.temp_mail_email,
            "first_id": mail_id,
            "epin": self.temp_mail_pin,
        });

        let _ = self.http_client
            .delete(delete_url)
            .json(&payload)
            .timeout(std::time::Duration::from_secs(10))
            .send()
            .await;
        // 删除失败不影响验证码获取，所以忽略错误
    }

    /// 从IMAP邮箱获取验证码
    /// 对应原版 Python 的 _get_code_from_imap 方法
    async fn get_code_from_imap(&self) -> Option<String> {
        // use imap::Session; // 暂时未使用
        use native_tls::TlsConnector;

        // 连接IMAP服务器
        let tls = match TlsConnector::builder().build() {
            Ok(tls) => tls,
            Err(e) => {
                println!("从IMAP获取验证码失败: TLS连接器创建失败: {}", e);
                return None;
            }
        };

        let client = match imap::connect(
            (self.imap_server.as_str(), self.imap_port),
            &self.imap_server,
            &tls,
        ) {
            Ok(client) => client,
            Err(e) => {
                println!("从IMAP获取验证码失败: 连接失败: {}", e);
                return None;
            }
        };

        let mut session = match client.login(&self.imap_user, &self.imap_password) {
            Ok(session) => session,
            Err((e, _)) => {
                println!("从IMAP获取验证码失败: 登录失败: {}", e);
                return None;
            }
        };

        if let Err(e) = session.select(&self.imap_folder) {
            println!("从IMAP获取验证码失败: 选择文件夹失败: {}", e);
            let _ = session.logout();
            return None;
        }

        // 搜索未读邮件
        let messages = match session.search("UNSEEN") {
            Ok(messages) => messages,
            Err(e) => {
                println!("从IMAP获取验证码失败: 搜索邮件失败: {}", e);
                let _ = session.logout();
                return None;
            }
        };

        if messages.is_empty() {
            let _ = session.logout();
            return None;
        }

        // 限制检查邮件数量，只检查最新的20封未读邮件
        let max_check_count = 20;
        let mut mail_ids_to_check: Vec<u32> = messages.into_iter().collect();
        mail_ids_to_check.reverse(); // 最新的邮件在前
        mail_ids_to_check.truncate(max_check_count);

        let total_unread = mail_ids_to_check.len();
        let check_count = mail_ids_to_check.len();

        println!("📬 找到 {} 封未读邮件，检查最新 {} 封", total_unread, check_count);

        // 检查最新未读邮件
        for (i, mail_id) in mail_ids_to_check.iter().enumerate() {
            println!("📄 检查第 {}/{} 封邮件", i + 1, check_count);

            let messages = match session.fetch(mail_id.to_string(), "RFC822") {
                Ok(messages) => messages,
                Err(_) => continue,
            };

            if let Some(message) = messages.iter().next() {
                if let Some(body) = message.body() {
                    match std::str::from_utf8(body) {
                        Ok(email_str) => {
                            if let Some(code) = self.process_imap_email(&mut session, *mail_id, email_str).await {
                                let _ = session.logout();
                                return Some(code);
                            }
                        }
                        Err(_) => continue,
                    }
                }
            }
        }

        let _ = session.logout();
        None
    }

    /// 处理IMAP邮件
    /// 对应原版 Python 中IMAP邮件处理的逻辑
    async fn process_imap_email(&self, session: &mut imap::Session<native_tls::TlsStream<std::net::TcpStream>>, mail_id: u32, email_str: &str) -> Option<String> {
        // 简单的邮件解析，检查发件人
        let lines: Vec<&str> = email_str.lines().collect();
        let mut from_line = "";
        let mut in_body = false;
        let mut body_lines = Vec::new();

        for line in &lines {
            if line.starts_with("From:") {
                from_line = line;
            } else if line.is_empty() && !in_body {
                in_body = true;
            } else if in_body {
                body_lines.push(*line);
            }
        }

        // 检查发件人
        let sender = from_line.to_lowercase();
        let is_augment_sender = sender.contains("<EMAIL>") ||
            sender.contains("augment") ||
            sender.contains("noreply");

        if !is_augment_sender {
            return None;
        }

        // 提取邮件正文
        let body = body_lines.join("\n");

        // 验证邮件内容是否包含验证码相关信息
        let body_lower = body.to_lowercase();
        let is_verification_content = body_lower.contains("verification code") ||
            body_lower.contains("验证码") ||
            body_lower.contains("your code") ||
            body_lower.contains("code is:") ||
            body_lower.contains("verification");

        if !is_verification_content {
            println!("🔍 Augment邮件但不包含验证码相关信息，标记为已读并跳过");
            let _ = session.store(mail_id.to_string(), "+FLAGS (\\Seen)");
            return None;
        }

        // 查找验证码 - 使用简单正则表达式然后手动验证前缀
        let re = Regex::new(r"\b\d{6}\b").unwrap();
        for code_match in re.find_iter(&body) {
            let code = code_match.as_str();
            let start_pos = code_match.start();

            // 检查前一个字符是否为字母、@或点号
            let is_valid = if start_pos == 0 {
                true // 如果在开头，则有效
            } else {
                let prev_char = body.chars().nth(start_pos - 1).unwrap_or(' ');
                !prev_char.is_ascii_alphabetic() && prev_char != '@' && prev_char != '.'
            };

            if is_valid {
                let _ = session.store(mail_id.to_string(), "+FLAGS (\\Seen)");
                return Some(code.to_string());
            }
        }

        println!("🔍 Augment验证码邮件但未找到有效6位数字验证码，标记为已读");
        let _ = session.store(mail_id.to_string(), "+FLAGS (\\Seen)");

        None
    }



    /// 测试当前配置的连接
    /// 对应原版 Python 的 test_connection 方法
    pub async fn test_connection(&self) -> ConnectionTestResult {
        if self.use_temp_mail {
            // 测试临时邮箱连接
            println!("🔧 测试临时邮箱连接: {}", self.temp_mail_email);

            let test_url = format!(
                "https://tempmail.plus/api/mails?email={}&limit=1&epin={}",
                self.temp_mail_email, self.temp_mail_pin
            );

            match self.http_client
                .get(&test_url)
                .timeout(std::time::Duration::from_secs(5))
                .send()
                .await
            {
                Ok(response) => {
                    if response.status() == 200 {
                        println!("✅ 临时邮箱连接测试成功");
                        ConnectionTestResult {
                            success: true,
                            message: "临时邮箱连接成功".to_string(),
                        }
                    } else {
                        let error_msg = format!("临时邮箱连接失败: HTTP {}", response.status());
                        println!("❌ {}", error_msg);
                        ConnectionTestResult {
                            success: false,
                            message: error_msg,
                        }
                    }
                }
                Err(e) => {
                    let error_msg = if e.is_timeout() {
                        "临时邮箱连接超时".to_string()
                    } else if e.is_connect() {
                        "临时邮箱连接错误".to_string()
                    } else {
                        "临时邮箱连接失败".to_string()
                    };
                    println!("❌ {}", error_msg);
                    ConnectionTestResult {
                        success: false,
                        message: error_msg,
                    }
                }
            }
        } else {
            // 测试IMAP连接
            println!("🔧 测试IMAP连接...");
            self.test_imap_connection().await
        }
    }

    /// 测试IMAP连接
    /// 对应原版 Python 的 _test_imap_connection 方法
    async fn test_imap_connection(&self) -> ConnectionTestResult {
        // use imap::Session; // 暂时未使用
        use native_tls::TlsConnector;

        // 1. 测试服务器连接
        println!("📡 连接服务器: {}:{}", self.imap_server, self.imap_port);

        let tls = match TlsConnector::builder().build() {
            Ok(tls) => tls,
            Err(_) => {
                let error_msg = "TLS连接器创建失败";
                println!("❌ {}", error_msg);
                return ConnectionTestResult {
                    success: false,
                    message: "IMAP服务器连接失败".to_string(),
                };
            }
        };

        let client = match imap::connect(
            (self.imap_server.as_str(), self.imap_port),
            &self.imap_server,
            &tls,
        ) {
            Ok(client) => {
                println!("✅ 服务器连接成功");
                client
            }
            Err(e) => {
                let error_str = e.to_string().to_lowercase();

                let error_msg = if error_str.contains("timeout") || error_str.contains("timed out") {
                    "IMAP服务器连接超时"
                } else if error_str.contains("connection refused") || error_str.contains("refused") {
                    "IMAP服务器连接被拒绝"
                } else if error_str.contains("name or service not known") || error_str.contains("nodename nor servname") {
                    "IMAP地址或端口配置错误"
                } else {
                    "IMAP服务器连接失败"
                };

                println!("❌ {}", error_msg);

                return ConnectionTestResult {
                    success: false,
                    message: error_msg.to_string(),
                };
            }
        };

        // 2. 测试登录认证
        println!("🔐 登录验证: {}", self.imap_user);
        let mut session = match client.login(&self.imap_user, &self.imap_password) {
            Ok(session) => {
                println!("✅ 登录验证成功");
                session
            }
            Err((e, _)) => {
                let error_str = e.to_string().to_lowercase();

                let error_msg = if error_str.contains("authentication failed") || error_str.contains("invalid credentials") {
                    "IMAP用户名或密码错误"
                } else {
                    "IMAP登录验证失败"
                };

                println!("❌ {}", error_msg);

                return ConnectionTestResult {
                    success: false,
                    message: error_msg.to_string(),
                };
            }
        };

        // 3. 测试文件夹选择
        println!("📁 选择文件夹: {}", self.imap_folder);
        match session.select(&self.imap_folder) {
            Ok(_) => {
                println!("✅ 文件夹选择成功");
                let _ = session.logout();
                ConnectionTestResult {
                    success: true,
                    message: "IMAP连接成功".to_string(),
                }
            }
            Err(e) => {
                println!("❌ 文件夹选择失败: {}", e);
                let _ = session.logout();
                ConnectionTestResult {
                    success: false,
                    message: "IMAP文件夹选择失败".to_string(),
                }
            }
        }
    }

    /// 复制文本到剪贴板
    /// 对应原版 Python 的 copy_to_clipboard 函数
    async fn copy_to_clipboard(&self, text: &str) -> Result<(), anyhow::Error> {
        if !self.auto_copy {
            return Err(anyhow::anyhow!("自动复制已禁用"));
        }

        use arboard::Clipboard;

        match Clipboard::new() {
            Ok(mut clipboard) => {
                match clipboard.set_text(text) {
                    Ok(_) => {
                        println!("📋 成功复制到剪贴板: {}", text);
                        Ok(())
                    }
                    Err(_) => {
                        Err(anyhow::anyhow!("复制到剪贴板失败"))
                    }
                }
            }
            Err(_) => {
                Err(anyhow::anyhow!("创建剪贴板实例失败"))
            }
        }
    }

    /// 获取最后的错误信息
    pub fn get_last_error(&self) -> Option<String> {
        self.last_error.clone()
    }
}
